using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.Shared.Constants;

namespace RTP.StatusMonitor.Domain.EngUnits;

public static class VelocityUnitConverter
{
    public static Velocity ToMilesPerHour(this Velocity velocity)
    => velocity.EngUnits.Name switch
    {
        nameof(VelocityEngUnit.MilesPerHour) => velocity,
        nameof(VelocityEngUnit.MetersPerSecond)
            => new Velocity(
                Value: velocity.Value * UnitConversionFactor.MPS_TO_MPH,
                EngUnits: VelocityEngUnit.MilesPerHour),
        nameof(VelocityEngUnit.KilometersPerHour)
            => new Velocity(
                Value: velocity.Value * UnitConversionFactor.KMH_TO_MPH,
                EngUnits: VelocityEngUnit.MilesPerHour),
        _ => throw new InvalidOperationException("Unknown velocity unit"),
    };

    public static Velocity ToMetersPerSecond(this Velocity velocity)
    => velocity.EngUnits.Name switch
    {
        nameof(VelocityEngUnit.MetersPerSecond) => velocity,
        nameof(VelocityEngUnit.MilesPerHour)
            => new Velocity(
                Value: velocity.Value / UnitConversionFactor.MPS_TO_MPH,
                EngUnits: VelocityEngUnit.MetersPerSecond),
        nameof(VelocityEngUnit.KilometersPerHour)
            => new Velocity(
                Value: velocity.Value / UnitConversionFactor.METER_PER_SEC_TO_KM_PER_HR,
                EngUnits: VelocityEngUnit.MetersPerSecond),
        _ => throw new InvalidOperationException("Unknown velocity unit"),
    };
}