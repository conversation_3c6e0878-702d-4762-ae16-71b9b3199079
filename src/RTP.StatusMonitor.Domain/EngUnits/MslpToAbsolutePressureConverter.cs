using RTP.StatusMonitor.Domain.EngUnits.Types;

namespace RTP.StatusMonitor.Domain.EngUnits;

public static class MslpToAbsolutePressureConverter
{
    const double PRESS_CALC_FACTOR = 29.263;

    /// <summary>
    /// Convert from mean sea level pressure to absolute pressure
    /// Always parameters must be in metric units
    /// </summary>
    /// 
    /// <param name="mslp">mean sea level pressure in mbar</param>
    /// <param name="altitude">elevation in meters</param>
    /// <param name="temperature">temperature</param>
    /// <param name="isMetric">weather to return press in metric (mbar) or imperial (inHg) units</param>
    /// 
    /// <returns>absolute pressure in mbar or inHg</returns>
    public static AbsolutePressure ToAbsolutePressure(
        this MeanSeaLevelPressure mslp,
        double altitude,
        Temperature temp)
    {
        double mslpMbar = mslp.ToMBar().Value;

        double kelvinTemp = temp.ToKelvin().Value;

        double absolutePressMbar = mslpMbar * Math.Exp(-altitude / (kelvinTemp * PRESS_CALC_FACTOR));

        return (absolutePressMbar is < 800 or > 1200)
            ? new(0, PressureEngUnit.Millibar)
            : new(absolutePressMbar, PressureEngUnit.Millibar);
    }
}
