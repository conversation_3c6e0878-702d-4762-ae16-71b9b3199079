using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.Shared.Constants;

namespace RTP.StatusMonitor.Domain.EngUnits;

/// <summary>
/// Pressure unit converter
/// NOTE
/// - 1 hPa = 1mbar = 0.02953 inHg
/// - 1 hPa = 100 Pa
/// - 1 hPa = 1mbar
/// </summary>
public static class PressureUnitConverter
{
    public static Pressure ToHectoPascal(this Pressure pressure) =>
        pressure.EngUnits.Name switch
        {
            nameof(PressureEngUnit.HectoPascal) => pressure,
            nameof(PressureEngUnit.InchOfMercury) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.MBAR_TO_INHG,
                EngUnits: PressureEngUnit.HectoPascal
            ),
            nameof(PressureEngUnit.Millibar) => new Pressure(
                Value: pressure.Value,
                EngUnits: PressureEngUnit.HectoPascal
            ),
            nameof(PressureEngUnit.Pascal) => new Pressure(
                Value: pressure.Value / UnitConversionFactor.HPA_TO_PA,
                EngUnits: PressureEngUnit.HectoPascal
            ),
            nameof(PressureEngUnit.Psi) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.PSI_TO_HPA,
                EngUnits: PressureEngUnit.HectoPascal
            ),
            _ => throw new InvalidOperationException("Unknown pressure unit"),
        };

    public static Pressure ToInHg(this Pressure pressure) =>
        pressure.EngUnits.Name switch
        {
            nameof(PressureEngUnit.InchOfMercury) => pressure,
            nameof(PressureEngUnit.HectoPascal) or nameof(PressureEngUnit.Millibar) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.MBAR_TO_INHG,
                EngUnits: PressureEngUnit.InchOfMercury
            ),
            nameof(PressureEngUnit.Pascal) => new Pressure(
                Value: pressure.Value
                    / UnitConversionFactor.HPA_TO_PA
                    * UnitConversionFactor.MBAR_TO_INHG,
                EngUnits: PressureEngUnit.InchOfMercury
            ),
            nameof(PressureEngUnit.Psi) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.PSI_TO_INHG,
                EngUnits: PressureEngUnit.InchOfMercury
            ),
            _ => throw new InvalidOperationException("Unknown pressure unit"),
        };

    public static Pressure ToMBar(this Pressure pressure) =>
        pressure.EngUnits.Name switch
        {
            nameof(PressureEngUnit.Millibar) => pressure,
            nameof(PressureEngUnit.HectoPascal) => new Pressure(
                Value: pressure.Value,
                EngUnits: PressureEngUnit.Millibar
            ),
            nameof(PressureEngUnit.InchOfMercury) => new Pressure(
                Value: pressure.Value / UnitConversionFactor.MBAR_TO_INHG,
                EngUnits: PressureEngUnit.Millibar
            ),
            nameof(PressureEngUnit.Pascal) => new Pressure(
                Value: pressure.Value / UnitConversionFactor.HPA_TO_PA,
                EngUnits: PressureEngUnit.Millibar
            ),
            nameof(PressureEngUnit.Psi) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.PSI_TO_HPA,
                EngUnits: PressureEngUnit.Millibar
            ),
            _ => throw new InvalidOperationException("Unknown pressure unit"),
        };

    public static Pressure ToPsi(this Pressure pressure) =>
        pressure.EngUnits.Name switch
        {
            nameof(PressureEngUnit.Psi) => pressure,
            nameof(PressureEngUnit.HectoPascal) or nameof(PressureEngUnit.Millibar) => new Pressure(
                Value: pressure.Value * UnitConversionFactor.MBAR_TO_PSI,
                EngUnits: PressureEngUnit.Psi
            ),
            nameof(PressureEngUnit.Pascal) => new Pressure(
                Value: pressure.Value
                    / UnitConversionFactor.HPA_TO_PA
                    * UnitConversionFactor.MBAR_TO_PSI,
                EngUnits: PressureEngUnit.Psi
            ),
            nameof(PressureEngUnit.InchOfMercury) => new Pressure(
                Value: pressure.Value / UnitConversionFactor.PSI_TO_INHG,
                EngUnits: PressureEngUnit.Psi
            ),
            _ => throw new InvalidOperationException("Unknown pressure unit"),
        };
}
