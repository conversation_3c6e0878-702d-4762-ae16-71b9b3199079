using Ardalis.SmartEnum;

namespace RTP.StatusMonitor.Domain.EngUnits.Types;

public record Precipitation(double Value, PrecipitationEngUnit EngUnits);

public class PrecipitationEngUnit : SmartEnum<PrecipitationEngUnit>
{
    // in
    public static readonly PrecipitationEngUnit Inches = new(
        nameof(Inches), 0);
    // mm
    public static readonly PrecipitationEngUnit Millimeters = new(
        nameof(Millimeters), 1);

    public PrecipitationEngUnit(string name, int value) : base(name, value)
    { }
}