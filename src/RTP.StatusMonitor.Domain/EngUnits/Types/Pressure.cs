using Ardalis.SmartEnum;

namespace RTP.StatusMonitor.Domain.EngUnits.Types;

public class PressureEngUnit(string name, int value) : SmartEnum<PressureEngUnit>(name, value)
{
    public static readonly PressureEngUnit HectoPascal = new(
        nameof(HectoPascal), 0);
    public static readonly PressureEngUnit InchOfMercury = new(
        nameof(InchOfMercury), 1);
    public static readonly PressureEngUnit Millibar = new(
        nameof(Millibar), 2);
    public static readonly PressureEngUnit Pascal = new(
        nameof(Pascal), 3);
    public static readonly PressureEngUnit Psi = new(
        nameof(Psi), 4);
}

public record Pressure(double Value, PressureEngUnit EngUnits);

public record MeanSeaLevelPressure(double Value, PressureEngUnit EngUnits)
    : Pressure(Value, EngUnits);
public record AbsolutePressure(double Value, PressureEngUnit EngUnits)
    : Pressure(Value, EngUnits);

