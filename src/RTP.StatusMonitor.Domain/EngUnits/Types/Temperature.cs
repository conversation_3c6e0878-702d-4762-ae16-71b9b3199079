using Ardalis.SmartEnum;

namespace RTP.StatusMonitor.Domain.EngUnits.Types;
public class TemperatureEngUnit(string name, int value) : SmartEnum<TemperatureEngUnit>(name, value)
{
    public static readonly TemperatureEngUnit Celsius = new(nameof(Celsius), 0);
    public static readonly TemperatureEngUnit Fahrenheit = new(nameof(Fahrenheit), 1);
    public static readonly TemperatureEngUnit Kelvin = new(nameof(<PERSON><PERSON>), 2);
}

public record Temperature(double Value, TemperatureEngUnit EngUnits);
