using Ardalis.SmartEnum;

namespace RTP.StatusMonitor.Domain.EngUnits.Types;

public class VelocityEngUnit : SmartEnum<VelocityEngUnit>
{
    // mph
    public static readonly VelocityEngUnit MilesPerHour = new(
        nameof(MilesPerHour), 0);
    // m/s
    public static readonly VelocityEngUnit MetersPerSecond = new(
        nameof(MetersPerSecond), 1);
    // km/h
    public static readonly VelocityEngUnit KilometersPerHour = new(
        nameof(KilometersPerHour), 2);

    public VelocityEngUnit(string name, int value) : base(name, value)
    { }
}

public record Velocity(double Value, VelocityEngUnit EngUnits);