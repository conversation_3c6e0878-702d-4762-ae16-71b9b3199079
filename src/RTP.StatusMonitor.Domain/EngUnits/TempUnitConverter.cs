using RTP.StatusMonitor.Domain.EngUnits.Types;

namespace RTP.StatusMonitor.Domain.EngUnits;

public static class TempUnitConverter
{
    public static Temperature ToCelcius(this Temperature temperature)
    {
        if (temperature.EngUnits == TemperatureEngUnit.Celsius)
            return temperature;

        if (temperature.EngUnits == TemperatureEngUnit.Fahrenheit)
        {
            return new Temperature(
                Value: (temperature.Value - 32) * 5 / 9,
                EngUnits: TemperatureEngUnit.Celsius);
        }

        return new Temperature(
            Value: temperature.Value - 273.15,
            EngUnits: TemperatureEngUnit.Celsius);
    }

    public static Temperature ToFahrenheit(this Temperature temperature)
    {
        if (temperature.EngUnits == TemperatureEngUnit.Fahrenheit)
            return temperature;

        if (temperature.EngUnits == TemperatureEngUnit.Celsius)
        {
            return new Temperature(
                Value: (temperature.Value * 9 / 5) + 32,
                EngUnits: TemperatureEngUnit.Fahrenheit);
        }

        return new Temperature(
            Value: temperature.Value * 9 / 5 - 459.67,
            EngUnits: TemperatureEngUnit.Fahrenheit);
    }

    public static Temperature ToKelvin(this Temperature temperature)
    {
        if (temperature.EngUnits == TemperatureEngUnit.Kelvin)
            return temperature;

        if (temperature.EngUnits == TemperatureEngUnit.Celsius)
        {
            return new Temperature(
                Value: temperature.Value + 273.15,
                EngUnits: TemperatureEngUnit.Kelvin);
        }

        return new Temperature(
            Value: (temperature.Value + 459.67) * 5 / 9, EngUnits: TemperatureEngUnit.Kelvin);
    }
}
