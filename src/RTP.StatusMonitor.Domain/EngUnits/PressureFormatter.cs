using RTP.StatusMonitor.Domain.EngUnits.Types;

namespace RTP.StatusMonitor.Domain.EngUnits;

public static class PressureFormatter
{
    public const int PRESS_PRECISION = 2;
    public static Pressure Format(this Pressure pressure)
    => pressure switch
    {
        MeanSeaLevelPressure mslp => new MeanSeaLevelPressure(Math.Round(mslp.Value, PRESS_PRECISION), mslp.EngUnits),
        AbsolutePressure ap => new AbsolutePressure(Math.Round(ap.Value, PRESS_PRECISION), ap.EngUnits),
        _ => new Pressure(Math.Round(pressure.Value, PRESS_PRECISION), pressure.EngUnits)
    };
}
