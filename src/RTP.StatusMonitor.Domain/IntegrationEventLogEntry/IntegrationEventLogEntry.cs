using RTP.StatusMonitor.Domain.Abstractions;
using Newtonsoft.Json;

namespace RTP.StatusMonitor.Domain.IntegrationEventLogEntry;

public class IntegrationEventLogEntry : Entity
{
    public string EventType { get; private set; } = string.Empty; // (e.g., ForecastUpdateEvent)
    public string Content { get; private set; } = string.Empty; // Serialized content of the event
    public string State { get; private set; } = string.Empty; // (e.g., Pending, Processed)
    public string TriggeredBy { get; private set; } = string.Empty;
    public DateTime CreationDate { get; private set; }

    private IntegrationEventLogEntry() { }

    public IntegrationEventLogEntry(
        IntegrationEvent eventItem,
        string state)
    {
        Id = Guid.NewGuid();
        EventType = eventItem.GetType().Name;
        CreationDate = DateTime.UtcNow;
        Content = JsonConvert.SerializeObject(eventItem);
        State = state;
        TriggeredBy = eventItem.CreatedBy;
    }
}
