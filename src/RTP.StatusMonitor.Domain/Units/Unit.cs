using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.Domain.Units;

public class Unit
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public DateTime DateCreated { get; set; } = DateTime.Now;
    public DateTime DateModified { get; set; } = DateTime.Now;

    // 1-to-many relationship with Block
    public Guid BlockId { get; set; }
    public Block Block { get; set; } = null!;

    public string GetAirsonicHistoricalTable()
    {
        string siteName = Block.Site.Name.Replace(" ", string.Empty);

        return siteName switch
        {
            "7FA" => "airsonicVandolah",
            "GT24" => "airsonicLakeRoad",
            "W501g" => "airsonicEnnis",
            _ => $"airsonic{siteName}",
        };
    }

    /// <summary>
    /// Get the airsonic snapshot partition key for the unit
    /// </summary>
    ///
    /// <returns>
    /// The partition key for the unit
    /// </returns>
    public string GetAirsonicSnapshotPartition()
    {
        string siteName = Block.Site.Name.Replace(" ", string.Empty);

        return siteName switch
        {
            "7FA" => "Vandolah-4-1-Snapshot",
            "GT24" => "Lake Road-3-1-Snapshot",
            "W501g" => "Ennis-1-1-Snapshot",
            _ => $"{Block.Site.Name}-{Block.Name}-{Name}-Snapshot",
        };
    }
}
