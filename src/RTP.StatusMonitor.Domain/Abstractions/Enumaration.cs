using System.Reflection;

namespace RTP.StatusMonitor.Domain.Abstractions;

public abstract class Enumeration<T>
    : IEquatable<Enumeration<T>> where T : Enumeration<T>
{
    private static readonly Dictionary<int, T> _enumerations = CreateEnumerations();

    /// <summary>
    /// Create a dictionary of all possible enumerations of type T
    /// using reflection to find all public static fields of type T.
    /// </summary>
    /// 
    /// <returns>A dictionary of all possible enumerations of type T.</returns>
    private static Dictionary<int, T> CreateEnumerations()
        => typeof(T)
            .GetFields(
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.FlattenHierarchy)
            .Where(fi => typeof(T).IsAssignableFrom(fi.FieldType))
            .Select(fi => (T)fi.GetValue(default)!)
            .ToDictionary(e => e.Value);

    public int Value { get; protected init; }
    public string Name { get; protected set; } = string.Empty;

    protected Enumeration(int value, string name)
    {
        Value = value;
        Name = name;
    }

    public static T? FromValue(int value)
    => _enumerations.TryGetValue(value, out T? result)
        ? result : default;

    public static T? FromName(string name)
    => _enumerations.Values.SingleOrDefault(e => e.Name == name);

    /// <summary>
    /// Two enumerations are equal if they have the same type and value.
    /// </summary>
    /// 
    /// <param name="other"></param>
    /// 
    /// <returns>Whether the two enumerations are equal.</returns>
    public bool Equals(Enumeration<T>? other)
    => other is not null && GetType() == other.GetType() && Value.Equals(other.Value);

    /// <summary>
    /// Compare this enumeration against another object.
    /// They are equal if the other object is an enumeration of the same type and value.
    /// </summary>
    /// 
    /// <param name="obj"></param>
    /// 
    /// <returns>Whether the two objects are equal.</returns>
    public override bool Equals(object? obj)
    => obj is Enumeration<T> other && Equals(other);

    public override int GetHashCode()
    => HashCode.Combine(GetType(), Value);
}