namespace RTP.StatusMonitor.Domain.Abstractions;

public abstract class IntegrationEvent
{
    public Guid Id { get; }
    public long Timestamp { get; }
    public string CreatedBy { get; }
    public string EventType { get; }
    protected IntegrationEvent(string createdBy, string eventType)
    {
        Id = Guid.NewGuid();
        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        CreatedBy = createdBy;
        EventType = eventType;
    }
}