namespace RTP.StatusMonitor.Domain.Abstractions;

public static class MaybeExtensions
{
    /// <summary>
    /// Applies a transformation function to the value inside a <see cref="Maybe{T}"/> and returns a new <see cref="Maybe{T}"/> with the transformed value.
    /// </summary>
    /// <typeparam name="TIn">The type of the value inside the input <see cref="Maybe{T}"/>.</typeparam>
    /// <typeparam name="TOut">The type of the value inside the output <see cref="Maybe{T}"/>.</typeparam>
    /// <param name="this">The input <see cref="Maybe{T}"/> instance.</param>
    /// <param name="func">The transformation function to apply to the value inside the <see cref="Maybe{T}"/>.</param>
    /// <returns>A new <see cref="Maybe{T}"/> instance with the transformed value.</returns>
    public static Maybe<TOut> Bind<TIn, TOut>(
        this Maybe<TOut> @this,
        Func<TIn, TOut> func)
    {
        try
        {
            return @this switch
            {
                // Apply the transformation if the value is not null.
                Something<TIn> s when s.Value is not null
                    => new Something<TOut>(func(s.Value)),
                // Return Nothing<TOut> on all other cases.
                _ => new Nothing<TOut>(),
            };
        }
        catch (Exception)
        {
            return new Nothing<TOut>();
        }
    }


    /// <summary>
    /// Binds a function to the value of a <see cref="Maybe{T}"/> asynchronously.
    /// </summary>
    /// <typeparam name="TIn">The type of the input value.</typeparam>
    /// <typeparam name="TOut">The type of the output value.</typeparam>
    /// <param name="this">The <see cref="Maybe{T}"/> instance.</param>
    /// <param name="func">The function to bind.</param>
    /// <returns>A new <see cref="Maybe{T}"/> instance with the result of the bound function.</returns>
    /// <remarks>
    /// If the input <see cref="Maybe{T}"/> is a <see cref="Something{T}"/> with a non-null value, the function is applied to the value asynchronously.
    /// If the input <see cref="Maybe{T}"/> is a <see cref="Nothing{T}"/> or the function throws an exception, a new <see cref="Nothing{T}"/> instance is returned.
    /// </remarks>
    public static async Task<Maybe<TOut>> BindAsync<TIn, TOut>(
        this Maybe<TOut> @this,
        Func<TIn, Task<TOut>> func)
    {
        try
        {
            return @this switch
            {
                Something<TIn> s when s.Value is not null
                    => new Something<TOut>(await func(s.Value)),
                _ => new Nothing<TOut>(),
            };
        }
        catch (Exception)
        {
            return new Nothing<TOut>();
        }
    }

    /// <summary>
    /// Binds a function to the current Maybe instance, transforming it into a new Maybe instance.
    /// </summary>
    /// <typeparam name="TIn">The type of the input value.</typeparam>
    /// <typeparam name="TOut">The type of the output value.</typeparam>
    /// <param name="this">The current Maybe instance.</param>
    /// <param name="func">The function to bind.</param>
    /// <returns>A new Maybe instance resulting from the binding operation.</returns>
    public static Maybe<TOut> Bind<TIn, TOut>(
        this Maybe<TOut> @this,
        Func<TIn, Maybe<TOut>> func)
    {
        try
        {
            var returnValue = @this switch
            {
                Something<TIn> s when s is not null => func(s.Value),
                _ => new Nothing<TOut>()
            };
            return returnValue;
        }
        catch (Exception)
        {
            return new Nothing<TOut>();
        }
    }

    public static Maybe<T> OnSomeThing<T>(
        this Maybe<T> @this,
        Action<T> action)
    {
        if (@this is Something<T> something)
        {
            action(something.Value);
        }

        return @this;
    }

    public static void OnSomething<T>(
        this Maybe<T> @this,
        Action<T> action)
    {
        if (@this is Something<T> something)
        {
            action(something.Value);
        }
    }

    public static Maybe<T> OnNothing<T>(
        this Maybe<T> @this,
        Action action)
    {
        if (@this is Nothing<T>)
        {
            action();
        }

        return @this;
    }
}