namespace RTP.StatusMonitor.Domain.Abstractions.Extensions;

public static class FunctionalExtensions
{
    /// <summary>
    /// Converts the specified object to a double value or returns a default value if the conversion fails.
    /// </summary>
    /// <param name="this">The object to convert.</param>
    /// <param name="defaultValue">The default value to return if the conversion fails. Default value is NaN.</param>
    /// <returns>The double value of the object, or the default value if the conversion fails.</returns>
    public static double ToDoubleOrDefault(
        this object @this,
        double defaultValue = double.NaN)
        => double.TryParse(@this?.ToString(), out double result)
            ? result
            : defaultValue;
}