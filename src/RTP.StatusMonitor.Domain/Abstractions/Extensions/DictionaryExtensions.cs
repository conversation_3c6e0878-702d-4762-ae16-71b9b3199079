namespace RTP.StatusMonitor.Domain.Abstractions.Extensions;

public static class DictionaryExtesions
{
    /// <summary>
    /// Add or update a dictionary of lists
    /// </summary>
    /// 
    /// <typeparam name="T<PERSON><PERSON>"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dictionary"></param>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public static IDictionary<TKey, List<TValue>> AddOrUpdate<TKey, TValue>(
        this IDictionary<TKey, List<TValue>> dictionary,
        TKey key,
        TValue value)
    {
        if (dictionary.ContainsKey(key))
        {
            dictionary[key].Add(value);
        }
        else
        {
            dictionary.Add(key, new List<TValue> { value });
        }

        return dictionary;
    }

    /// <summary>
    /// Get the value of a dictionary by key, or return default (null for reference types) if the key does not exist
    /// </summary>
    /// <typeparam name="<PERSON><PERSON><PERSON>"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dictionary">The dictionary to get the value from</param>
    /// <param name="key">The key to get the value for</param>
    /// <returns>The value of the dictionary at the given key, or default if the key does not exist</returns>
    public static TValue? ToLookUp<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, TKey key)
    => dictionary.ContainsKey(key)
        ? dictionary[key]
        : default;
}