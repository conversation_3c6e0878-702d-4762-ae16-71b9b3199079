using System.Globalization;

namespace RTP.StatusMonitor.Domain.Abstractions.Extensions;

public static class DateTimeExtensions
{
    /// <summary>
    /// Get the start of the day for a given date
    /// </summary>
    /// <param name="date"></param>
    /// <returns></returns>
    public static DateTime StartOfDay(this DateTime date)
    {
        return new DateTime(
            year: date.Year,
            month: date.Month,
            day: date.Day,
            hour: 0,
            minute: 0,
            second: 0);
    }

    /// <summary>
    /// Get the end of the day for a given date
    /// </summary>
    /// <param name="date"></param>
    /// <returns></returns>
    public static DateTime EndOfDay(this DateTime date)
    {
        return new DateTime(
            year: date.Year,
            month: date.Month,
            day: date.Day,
            hour: 23,
            minute: 59,
            second: 59);
    }

    /// <summary>
    /// Check if two dates are in the same week
    /// </summary>
    /// <param name="date"></param>
    /// <param name="otherDate"></param>
    /// <returns>Return true if the two dates are in the same week</returns>
    public static bool IsSameWeekAs(this DateTime date, DateTime otherDate)
    {
        Calendar cal = DateTimeFormatInfo.CurrentInfo.Calendar;

        // Get the first day of the week for the given date
        DateTime d1 = date.Date
            .AddDays(-1 * (int)cal.GetDayOfWeek(date));
        DateTime d2 = otherDate.Date
            .AddDays(-1 * (int)cal.GetDayOfWeek(otherDate));

        return d1 == d2;
    }
}