using ErrorOr;

namespace RTP.StatusMonitor.Domain.Abstractions.Extensions;

public static class ErrorOrExt
{
    public static ErrorOr<Success> Combine<T>(IEnumerable<ErrorOr<T>> errorsOrValues)
    {
        IEnumerable<ErrorOr<T>> errors = errorsOrValues
            .Where(e => e.IsError);

        if (errors.Any())
        {
            return errors
                .Where(e => e.IsError)
                .SelectMany(e => e.Errors)
                .ToList();
        }

        return Result.Success;
    }

    /// <summary>
    /// Combine two ErrorOr instances into a single ErrorOr instance.
    /// </summary>
    public static ErrorOr<Success> Combine<T1, T2>(
        ErrorOr<T1> errorOr1, ErrorOr<T2> errorOr2)
    {
        if (errorOr1.IsError || errorOr2.IsError)
        {
            return errorOr1.Errors.Concat(errorOr2.Errors).ToList();
        }

        return Result.Success;
    }

    /// <summary>
    /// Combine three ErrorOr instances into a single ErrorOr instance.
    /// </summary>
    public static ErrorOr<Success> Combine<T1, T2, T3>(
        ErrorOr<T1> errorOr1,
        ErrorOr<T2> errorOr2,
        ErrorOr<T3> errorOr3)
    {
        if (errorOr1.IsError || errorOr2.IsError || errorOr3.IsError)
        {
            return errorOr1.Errors
                .Concat(errorOr2.Errors)
                .Concat(errorOr3.Errors).ToList();
        }

        return Result.Success;
    }

    /// <summary>
    /// Combine four ErrorOr instances into a single ErrorOr instance.
    /// </summary>
    public static ErrorOr<Success> Combine<T1, T2, T3, T4>(
        ErrorOr<T1> errorOr1,
        ErrorOr<T2> errorOr2,
        ErrorOr<T3> errorOr3,
        ErrorOr<T4> errorOr4)
    {
        if (errorOr1.IsError || errorOr2.IsError || errorOr3.IsError || errorOr4.IsError)
        {
            return errorOr1.Errors
                .Concat(errorOr2.Errors)
                .Concat(errorOr3.Errors)
                .Concat(errorOr4.Errors)
                .ToList();
        }

        return Result.Success;
    }
}