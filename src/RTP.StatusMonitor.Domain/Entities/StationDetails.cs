using ErrorOr;
using RTP.StatusMonitor.Domain.Errors;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.Entities;

public sealed class StationDetails
{
  public Guid Id { get; private set; }
  public string Name { get; private set; } = string.Empty;
  public WeatherApi Api { get; private set; }
  public double Latitude { get; private set; }
  public double Longitude { get; private set; }
  public double Elevation { get; private set; }
  public string State { get; private set; } = string.Empty;
  public string City { get; private set; } = string.Empty;
  public double DistanceFrom { get; private set; }
  public double DirectionFrom { get; private set; }
  public string DirectionFromTag { get; private set; } = string.Empty;
  public Guid SiteId { get; private set; }
  public Site.Site Site { get; private set; } = null!;

  public StationDetails()
  { }
  private StationDetails(
      Guid id,
      string name,
      WeatherApi api,
      double latitude,
      double longitude,
      double elevation,
      string state,
      string city,
      double distanceFrom,
      double directionFrom,
      string directionFromTag,
      Site.Site site)
  {
    Id = id;
    Name = name;
    Api = api;
    Latitude = latitude;
    Longitude = longitude;
    Elevation = elevation;
    State = state;
    City = city;
    DistanceFrom = distanceFrom;
    DirectionFrom = directionFrom;
    DirectionFromTag = directionFromTag;
    Site = site;
    SiteId = site.Id;
  }

  /// <summary>
  /// This is the factory method to create a new instance of the weather station for the site
  ///  It will validate that the site does not already have a weather station with the same name.
  /// </summary>
  internal static ErrorOr<StationDetails> Create(
      Guid id,
      string name,
      WeatherApi api,
      double latitude,
      double longitude,
      double elevation,
      string state,
      string city,
      double distanceFrom,
      double directionFrom,
      string directionFromTag,
      Site.Site site)
  {
    // Before create a new instance of the weather station for the site
    // We need to check if the site already has a weather station with the same name
    if (site.StationDetails.Any(sws => sws.Name == name))
    {
      return SiteWeatherStationDomainError.DuplicateStationName;
    }

    return new StationDetails(
        id: id,
        name: name,
        api: api,
        latitude: latitude,
        longitude: longitude,
        elevation: elevation,
        state: state,
        city: city,
        distanceFrom: distanceFrom,
        directionFrom: directionFrom,
        directionFromTag: directionFromTag,
        site: site);
  }
}