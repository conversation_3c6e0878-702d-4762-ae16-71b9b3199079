using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.Domain.Entities;

public class CurrentWeatherEntity : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public DateTimeOffset? Timestamp { get; set; }
    public string Source { get; set; } = string.Empty;
    public string LocalTimestamp { get; set; } = string.Empty;
    public int HourEnding { get; set; }
    public double Temp { get; set; }
    public double Press { get; set; }
    public double RH { get; set; }
    public double TempBias { get; set; }
    public double PressBias { get; set; }
    public double RHBias { get; set; }
    public ETag ETag { get; set; }
}
