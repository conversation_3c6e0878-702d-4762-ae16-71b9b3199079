namespace RTP.StatusMonitor.Domain.Entities;

public class Display
{
  public Guid Id { get; set; } = Guid.NewGuid();
  public string Name { get; set; } = null!;
  public string Alias { get; set; } = string.Empty;
  public string Description { get; set; } = string.Empty;
  public string Layout { get; set; } = null!;
  public string DisplayComponent { get; set; } = null!;
  public DateTime DateCreated { get; set; } = DateTime.Now;
  public DateTime DateModified { get; set; } = DateTime.Now;
  // many-to-many rel with display through DisplayViewPermission
  public ICollection<DisplayViewPermission> DisplayViewPermissions { get; set; } = Enumerable.Empty<DisplayViewPermission>().ToList();
}