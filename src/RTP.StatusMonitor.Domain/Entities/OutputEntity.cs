using Azure;
using Azure.Data.Tables;
namespace RTP.StatusMonitor.Domain.Entities;

public class OutputEntity : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public DateTimeOffset? Timestamp { get; set; }
    public int BatchId { get; set; }
    public string LastUpdateLocal { get; set; } = null!;
    public int LastUpdateUtc { get; set; }
    public string Tag { get; set; } = null!;
    public double Value { get; set; }
    public string Quality { get; set; } = string.Empty;
    public int Hour { get; set; }
    public int Month { get; set; }
    public int DayOfYear { get; set; }
    public string BatchIdLocal { get; set; } = null!;
    public ETag ETag { get; set; }
}