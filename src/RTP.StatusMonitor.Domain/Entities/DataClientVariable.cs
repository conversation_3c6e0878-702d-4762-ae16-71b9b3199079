namespace RTP.StatusMonitor.Domain.Entities;

public class DataClientVariable
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Tag { get; set; } = null!;
    public string Alias { get; set; } = string.Empty; // code name for RTP
    public string EngUnits { get; set; } = string.Empty;
    public double DefaultValue { get; set; }
    // 1-to-many rel with DataClient (dependent)
    public DataClient DataClient { get; set; } = null!; // must belong to a DataClient of a block
    // many-to-many rel with group through DataClientGroupVariable
    public ICollection<DataClientGroupVariable> DataClientGroupVariables { get; set; } = Enumerable.Empty<DataClientGroupVariable>().ToList();
    // 1-to-1 rel with DataClientStatusVariable (parent)
    public DataClientStatusVariable? DataClientStatusVariable { get; set; } // may be null if not set as a status variable
}