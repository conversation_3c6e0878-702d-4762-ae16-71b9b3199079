namespace RTP.StatusMonitor.Domain.Entities;

public class DataClientGroup
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string? Alias { get; set; }
    public bool IsCustomerAccess { get; set; } = false;
    // 1-to-many relationship with DataClient (dependent)
    public DataClient DataClient { get; set; } = null!; // must belong to a DataClient of a block

    // many-to-many rel with variable through DataClientGroupVariable
    public ICollection<DataClientGroupVariable> DataClientGroupVariables { get; set; } = null!; // must have at least one variable
}