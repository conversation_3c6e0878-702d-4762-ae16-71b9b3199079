using RTP.StatusMonitor.Domain.UserGroups;

namespace RTP.StatusMonitor.Domain.Entities;

public class GroupPermission
{
    public Guid GroupId { get; private set; }
    public Guid SiteId { get; private set; }
    public UserGroup Group { get; private set; } = null!;
    public Site.Site Site { get; private set; } = null!;
    public string Tag { get; private set; } = null!;

    private GroupPermission() { }

    // Can only be called internally within the domain project
    internal GroupPermission(UserGroup group, Site.Site site)
    {
        Group = group;
        Site = site;

        GroupId = group.UserGroupId;
        SiteId = site.Id;

        // Tag is made up of customer name and site name
        Tag = $"{group.Name}-{site.Name}";
    }
}