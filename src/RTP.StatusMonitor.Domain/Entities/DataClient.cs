using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.Domain.Entities;

public class DataClient
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string? Title { get; set; }
    // 1-to-1 rel with Block (dependent)
    public Guid BlockId { get; set; }
    public Block Block { get; set; } = null!; // must belong to a block
    // 1-to-many rel with DataClientGroup (parent)
    public ICollection<DataClientGroup> DataClientGroups { get; set; } = Enumerable.Empty<DataClientGroup>().ToList();
    // 1-to-many rel with DataClientVariable (parent)
    public ICollection<DataClientVariable> DataClientVariables { get; set; } = Enumerable.Empty<DataClientVariable>().ToList();
    // 1-to-many rel with DataClientStatusVariable (parent)
    public ICollection<DataClientStatusVariable> DataClientStatusVariables { get; set; } = Enumerable.Empty<DataClientStatusVariable>().ToList();
}