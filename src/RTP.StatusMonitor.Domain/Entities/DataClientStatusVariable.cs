namespace RTP.StatusMonitor.Domain.Entities;

public enum DataClientStatusType
{
    Ambient, // 0
    Status, // 1
    Info // 2
}
// depend on DataClientClient -> variable must exist in DataClientVariable
public class DataClientStatusVariable
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Tag { get; set; } = null!; // same as DataClientVariable.Tag
    public string? Abbreviation { get; set; } // UI display title
    public int NoDecimals { get; set; }
    public string? Expression { get; set; }
    public string? Color { get; set; }
    // 0: ambient, 1: status, 2: info
    public DataClientStatusType Type { get; set; }
    // 1-to-many rel with DataClient
    public DataClient DataClient { get; set; } = null!; // must belong to a DataClient of a block
    // 1-to-1 rel with DataClientVariable (dependent)
    public Guid DataClientVariableId { get; set; }
    public DataClientVariable DataClientVariable { get; set; } = null!; // must be a variable in DataClientVariable
}