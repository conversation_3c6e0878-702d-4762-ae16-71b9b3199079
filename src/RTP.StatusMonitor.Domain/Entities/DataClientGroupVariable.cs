namespace RTP.StatusMonitor.Domain.Entities;
// join table between DataClientGroup and DataClientVariable
public class DataClientGroupVariable
{
    public int NoDecimals { get; set; }
    public double Min { get; set; }
    public double Max { get; set; }
    public string? Color { get; set; } // hex color
    public string LineType { get; set; } = "line";
    public int Thickness { get; set; }
    public Guid DataClientGroupId { get; set; }
    public DataClientGroup DataClientGroup { get; set; } = null!;
    public Guid DataClientVariableId { get; set; }
    public DataClientVariable DataClientVariable { get; set; } = null!;
}
