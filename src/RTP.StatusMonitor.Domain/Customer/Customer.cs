using ErrorOr;
using RTP.StatusMonitor.Domain.UserGroups;

namespace RTP.StatusMonitor.Domain.Customer;

public class Customer
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = null!;
    public string Title { get; set; } = string.Empty;
    public string CompanyLogoUrl { get; set; } = string.Empty;
    public string BlobContainer { get; set; } = string.Empty;
    public DateTime DateCreated { get; set; } = DateTime.Now;
    public DateTime DateModified { get; set; } = DateTime.Now;
    public ICollection<Site.Site> Sites { get; set; } = Enumerable.Empty<Site.Site>().ToList();

    private Customer()
    { }

    public static ErrorOr<Customer> Create(
        Guid id,
        string name,
        string title,
        string companyLogoUrl,
        string blobContainer)
    => new Customer()
    {
        Id = id,
        Name = name,
        Title = title,
        CompanyLogoUrl = companyLogoUrl,
        BlobContainer = blobContainer
    };

    public ErrorOr<Site.Site> AddSite(
        string name,
        string alias,
        string description,
        string location,
        double altitude,
        bool isMetric,
        double latitude,
        double longitude,
        string timeZone,
        int locationKey,
        List<UserGroup> userGroups)
    {
        // Any site with the same name is not allowed
        if (Sites.Any(s => s.Name == name))
        {
            return CustomerErrors.DuplicateSite;
        }

        // Create the site
        Site.Site site = new
        (
            id: Guid.NewGuid(),
            name: name,
            alias: alias,
            description: description,
            location: location,
            altitude: altitude,
            isMetric: isMetric,
            latitude: latitude,
            longitude: longitude,
            timeZone: timeZone,
            locationKey: locationKey,
            userGroups: userGroups
        );

        // Add the site to the customer
        Sites.Add(site);

        // Return the site  
        return site;
    }
}
