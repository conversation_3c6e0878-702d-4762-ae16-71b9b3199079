using ErrorOr;

namespace RTP.StatusMonitor.Domain.Customer;

public static class CustomerErrors
{
  public static Error NotFound => Error.NotFound(
    code: "Customer.NotFound",
    description: "Customer not found"
  );

  public static Error AlreadyExists => Error.Conflict(
    code: "Customer.AlreadyExists",
    description: "Customer already exists"
  );

  public static Error DuplicateSite => Error.Conflict(
    code: "Customer.DuplicateSite",
    description: "Customer already has a site with this name"
  );

  public static Error Unauthorized => Error.Failure(
    code: "Customer.Unauthorized",
    description: "User is not authorized to modify a customer"
  );
}
