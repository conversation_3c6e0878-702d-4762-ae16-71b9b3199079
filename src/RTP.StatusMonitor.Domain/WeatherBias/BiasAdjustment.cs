using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.Domain.WeatherBias;

public static class BiasAdjustment
{
    /// <summary>
    /// Apply the bias to the observed weather to adjust it to the actual instrumentation
    /// </summary>
    /// 
    /// <param name="observed">The observed weather data point from external API</param>
    /// 
    /// <returns>The adjusted weather data point taking into account site instrumentation bias</returns>
    public static AdjustedWeatherDataPoint? ApplyBias(
        this ObservedWeatherDataPoint observed,
        AmbientBiasDataPoint? bias)
        => bias is null || observed.Value is null
            ? new AdjustedWeatherDataPoint(observed.BiasType, observed.Value).Format()
            : new AdjustedWeatherDataPoint(observed.BiasType, observed.Value + bias.Value).Format();
}
