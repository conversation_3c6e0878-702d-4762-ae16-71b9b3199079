using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.WeatherBias.Types;

/// <summary>
/// The adjusted weather data point is the observed weather data point adjusted by the bias between the observed and actual data 
/// </summary>
/// <param name="Value">The adjusted weather value taking into the account the bias</param>
public record AdjustedWeatherDataPoint(
    WeatherBiasType BiasType,
    double? Value) : WeatherDataPoint(BiasType, Value);
