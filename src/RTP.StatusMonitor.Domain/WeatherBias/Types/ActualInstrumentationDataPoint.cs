using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.WeatherBias.Types;

/// <summary>
/// The actual weather data point is data from the actual instrumentation from sensor/equipment
/// </summary>
/// <param name="TagSource">The source of the instrumentation (either RTP or Customer)</param>
/// <param name="BiasType"></param>
/// <param name="Value">The actual value of the data point from the instrumentation</param>
public record ActualInstrumentationDataPoint(
    WeatherTagSource TagSource,
    WeatherBiasType BiasType,
    double? Value) : WeatherDataPoint(BiasType, Value);
