using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.WeatherBias.Types;

/// <summary>
/// The ambient bias data point is the difference between actual data from instrumentation and the observed from weather API
/// </summary>
/// <param name="BiasType"></param>
/// <param name="ApiSource">The external API used in computation of this bias (e.g. AccuWeather, IBM, Tomorrow.io)</param>
/// <param name="TagSource">The source of the instrumentation (either RTP or Customer)</param>
/// <param name="Value">The difference between actual data from instrumentation and the observed from weather API (actual - observed)</param>
public record AmbientBiasDataPoint(
    WeatherService ApiSource,
    WeatherTagSource TagSource,
    WeatherBiasType BiasType,
    double? Value
) : WeatherDataPoint(BiasType, Value);
