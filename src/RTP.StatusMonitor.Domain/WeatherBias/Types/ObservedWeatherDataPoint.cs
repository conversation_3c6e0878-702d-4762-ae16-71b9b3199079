using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.WeatherBias.Types;

/// <summary>
/// The observed weather data point is data from external weather API sources (e.g. AccuWeather, IBM, Tomorrow.io)
/// </summary>
/// <param name="ApiSource">The source of the weather data</param>
/// <param name="BiasType">The type of weather data (e.g. temperature, humidity, pressure, etc.) to bias</param>
/// <param name="Value">The observed value of the weather data point from the API source</param>
public record ObservedWeatherDataPoint(
    WeatherService ApiSource,
    WeatherBiasType BiasType,
    double? Value
) : WeatherDataPoint(BiasType, Value);
