using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.WeatherBias.Types;

/// <summary>
/// The type of weather data (e.g. temperature, humidity, pressure, etc.) to bias
/// </summary>
/// <param name="BiasType">The type of weather data (e.g. temperature, humidity, pressure, etc.) to bias</param>
/// <param name="Value">The value of the weather data point</param>
public abstract record WeatherDataPoint(WeatherBiasType BiasType, double? Value);
