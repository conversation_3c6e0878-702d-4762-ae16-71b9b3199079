using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.Domain.WeatherBias;

public static class BiasComputation
{
    /// <summary>
    /// Get the difference between actual data from instrumentation and the observed from weather API
    /// </summary>
    /// <param name="actual">The actual weather data point from the instrumentation</param>
    /// <param name="observed">The observed weather data point from external API</param>
    /// <returns>The ambient bias data representing the difference between actual data from instrumentation and the observed from weather API</returns>
    public static AmbientBiasDataPoint? Bias(
        this ActualInstrumentationDataPoint? actual,
        ObservedWeatherDataPoint? observed
    )
    {
        if (observed == null || actual == null || observed.Value == null || actual.Value == null)
        {
            return null;
        }

        if (!IsBiasTypeMatch(observed, actual))
        {
            return null;
        }

        // NOTE - Handle edge case if bias type press has different units
        // If value of actual press is between 29-31 => assuming inHg => convert to psi
        if (actual.BiasType == WeatherBiasType.Press && actual.Value >= 25 && actual.Value <= 35)
        {
            actual = actual with { Value = actual.Value / UnitConversionFactor.PSI_TO_INHG };
        }

        return new(
            ApiSource: observed.ApiSource,
            TagSource: actual.TagSource,
            BiasType: actual.BiasType,
            Value: actual.Value - observed.Value
        );
    }

    /// <summary>
    /// Check if the bias type of the observed and actual weather data points match
    /// </summary>
    ///
    /// <param name="observed">The observed weather data point from external API</param>
    /// <param name="actual">The actual weather data point from the instrumentation</param>
    ///
    /// <returns>True if the bias type of the observed and actual weather data points match, otherwise false</returns>
    private static bool IsBiasTypeMatch(WeatherDataPoint observed, WeatherDataPoint actual) =>
        observed.BiasType == actual.BiasType;
}
