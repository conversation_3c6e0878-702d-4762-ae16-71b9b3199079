using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.Domain.WeatherBias;

/// <summary>
/// Round the ambient data to the correct precision
/// </summary>
public static class WeatherDataPointFormatter
{
    public static ObservedWeatherDataPoint? Format(
        this ObservedWeatherDataPoint? observedData)
        => observedData is null ? null : observedData with
        {
            Value = RoundAmbientData(observedData.BiasType, observedData.Value)
        };

    public static AdjustedWeatherDataPoint? Format(
        this AdjustedWeatherDataPoint? data)
        => data is null ? null : data with
        {
            Value = RoundAmbientData(data.BiasType, data.Value)
        };

    public static AmbientBiasDataPoint? Format(
        this AmbientBiasDataPoint? data)
        => data is null ? null : data with
        {
            Value = RoundAmbientData(data.BiasType, data.Value)
        };

    public static ActualInstrumentationDataPoint? Format(
        this ActualInstrumentationDataPoint? data)
        => data is null ? null : data with
        {
            Value = RoundAmbientData(data.BiasType, data.Value)
        };

    private static double? RoundAmbientData(
        WeatherBiasType biasType,
        double? rawValue)
        => biasType switch
        {
            WeatherBiasType.Temp => rawValue.HasValue
                ? Math.Round(rawValue.Value, 1)
                : null,
            WeatherBiasType.RH => rawValue.HasValue
                ? Math.Round(rawValue.Value, 0)
                : null,
            WeatherBiasType.Press => rawValue.HasValue
                ? Math.Round(rawValue.Value, 2)
                : null,
            WeatherBiasType.CIT => rawValue.HasValue
                ? Math.Round(rawValue.Value, 1)
                : null,
            _ => rawValue
        };
}