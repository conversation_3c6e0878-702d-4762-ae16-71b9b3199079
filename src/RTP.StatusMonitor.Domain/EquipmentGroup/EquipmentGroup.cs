using ErrorOr;

namespace RTP.StatusMonitor.Domain.EquipmentGroup;

public sealed class EquipmentGroup
{
    public Guid Id { get; private set; }
    public string Name { get; private set; } = null!;
    public double Width { get; private set; }
    public int Position { get; private set; } // the order of the group on the page
    public string Notes { get; private set; } = string.Empty;
    public DateTime? DateCreated { get; private set; }
    public DateTime? DateModified { get; private set; }
    public string? CreatedBy { get; private set; }
    public string? ModifiedBy { get; private set; }

    // SECTION - RELATIONSHIPS
    //**********************************************
    public List<EquipmentSection.EquipmentSection> EquipmentSections { get; private set; } = new();
    public Guid SiteId { get; private set; }
    public Site.Site Site { get; private set; } = null!;
    //**********************************************

    // NOTE - used by EF Core
    private EquipmentGroup()
    { }
    private EquipmentGroup(
        Guid id,
        string name,
        double width,
        int position,
        string notes,
        Site.Site site,
        List<EquipmentSection.EquipmentSection> equipmentSections,
        DateTime? dateCreated,
        DateTime? dateModified,
        string? createdBy,
        string? modifiedBy)
    {
        Id = id;
        Name = name;
        Width = width;
        Position = position;
        Notes = notes;
        SiteId = site.Id;
        Site = site;
        EquipmentSections = equipmentSections;
        DateCreated = dateCreated;
        DateModified = dateModified;
        CreatedBy = createdBy;
        ModifiedBy = modifiedBy;
    }

    public static ErrorOr<EquipmentGroup> Create(
        List<Guid> userGroupsId,
        Guid id,
        string name,
        double width,
        int position,
        string notes,
        Site.Site site,
        List<EquipmentSection.EquipmentSection> equipmentSections,
        DateTime? dateCreated,
        DateTime? dateModified,
        string? createdBy,
        string? modifiedBy)
    {
        // In order to create equipment group
        // User must be allowed to do
        if (IsAllowedToModify(userGroupsId, site) is false)
            return EquipmentGroupErrors.SaveUnauthorized;

        // Grid system between 0-12
        bool isWidthValid = width > 0 && width <= 12;
        if (isWidthValid is false)
            return EquipmentGroupErrors.WidthOutOfRange;

        // The position (order) must not be negative
        if (position < 0)
            return EquipmentGroupErrors.PositionOutOfRange;

        // Otherwise, we create the new equipment group
        EquipmentGroup equipmentGroup = new(
            id: id,
            name: name,
            width: width,
            position: position,
            notes: notes,
            site: site,
            equipmentSections: equipmentSections,
            dateCreated: dateCreated,
            dateModified: dateModified,
            createdBy: createdBy,
            modifiedBy: modifiedBy);

        return equipmentGroup;
    }

    /// <summary>
    /// User must have access to the site and have admin privileges
    /// in order to modify/create equipment group 
    /// </summary>
    private static bool IsAllowedToModify(
    List<Guid> userGroupsId,
    Site.Site site)
    {
        // In order to modify equipment group
        // User must first have access to the site
        bool hasAccessToSite = site.HasAccessToSite(userGroupsId);
        if (hasAccessToSite is false)
            return false;

        return true;
    }
}


