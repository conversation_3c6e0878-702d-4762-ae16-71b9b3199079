using ErrorOr;

namespace RTP.StatusMonitor.Domain.EquipmentGroup;

public static class EquipmentGroupErrors
{
  public static Error WidthOutOfRange => Error.Failure(
    code: "EquipmentGroup.WidthOutOfRange",
    description: "Equipment group width must be between 0 and 12 columns"
  );

  public static Error PositionOutOfRange => Error.Failure(
  code: "EquipmentGroup.PositionOutOfRange",
  description: "Equipment group position must be greater than 0"
  );

  public static Error SaveUnauthorized => Error.Failure(
    code: "EquipmentGroup.SaveUnauthorized",
    description: "User is not authorized to save equipment groups"
  );

  public static Error PersistenceErrors => Error.Unexpected(
    code: "EquipmentGroup.PersistenceErrors",
    description: "An unexpected error occurred while saving the equipment group"
  );
}