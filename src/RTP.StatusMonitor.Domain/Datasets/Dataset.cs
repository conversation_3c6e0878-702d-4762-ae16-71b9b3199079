using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.Datasets;
public enum DatasetType { SqlQuery, ApiEndpoint }

public class Dataset : Entity
{
    [JsonProperty]
    public string Name { get; private set; }

    [JsonProperty]
    public string Description { get; private set; }

    [JsonProperty]
    public DatasetType Type { get; private set; }

    [JsonProperty]
    public string Configuration { get; private set; }

    [JsonProperty]
    public string CreatedBy { get; private set; }

    [JsonConstructor]
    private Dataset(
        Guid id,
        string name,
        string description,
        DatasetType type,
        string configuration,
        string createdBy)
        : base(id)
    {
        Name = name;
        Description = description;
        Type = type;
        Configuration = configuration;
        CreatedBy = createdBy;
    }

    public static Dataset Create(
        string name,
        string description,
        DatasetType type,
        string configuration,
        string createdBy) => new(Guid.NewGuid(), name, description, type, configuration, createdBy);
}