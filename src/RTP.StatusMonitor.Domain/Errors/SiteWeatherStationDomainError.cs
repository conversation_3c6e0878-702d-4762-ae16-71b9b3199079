using ErrorOr;

namespace RTP.StatusMonitor.Domain.Errors;

public static class SiteWeatherStationDomainError
{
  public static Error DuplicateStationName => Error.Conflict(
      code: "SiteWeatherStation.DuplicateStationName",
      description: "Weather station name already exists for this site"
  );
  public static Error PersistenceError => Error.Unexpected(
      code: "SiteWeatherStation.PersistenceError",
      description: "Failed to add weather station to database"
  );
}