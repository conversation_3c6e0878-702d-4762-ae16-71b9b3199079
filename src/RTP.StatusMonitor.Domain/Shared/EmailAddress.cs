using System.Net.Mail;

namespace RTP.StatusMonitor.Domain.Shared;

public record EmailAddress
{
    public string Value { get; init; } = string.Empty;

    /// <summary>
    /// Parse a string into an EmailAddress.
    /// Returns null if the email address is invalid.
    /// </summary>
    /// <param name="email">The email address to parse.</param>
    /// <returns>An EmailAddress if the email address is valid, otherwise null.</returns>
    public static EmailAddress? From(string email)
    {
        // Try to parse the email address
        try
        {
            var addr = new MailAddress(email);
            return new EmailAddress { Value = addr.Address };
        }
        catch
        {
            // If the email address is invalid, return null
            return null;
        }
    }
}