namespace RTP.StatusMonitor.Domain.Shared.Constants;

public static class UnitConversionFactor
{
    /// <summary>
    /// Conversion factor from mbar to inHg for pressure
    /// </summary>
    public const double MBAR_TO_INHG = 0.02953;

    /// <summary>
    /// Conversion factor from hPa to Pa for pressure
    /// </summary>
    public const double HPA_TO_PA = 100;

    /// <summary>
    /// Conversion factor from mbar to psi for pressure
    /// 1 mbar = 1 hPa = 0.014503773773 psi
    /// </summary>
    public const float MBAR_TO_PSI = 0.014503773773f;

    /// <summary>
    /// Conversion factor from psi to mbar for pressure
    /// 1 psi = 68.9476 hPa
    /// </summary>
    public const double PSI_TO_HPA = 68.9476;
    /// <summary>
    /// Conversion factor from psi to inHg for pressure
    /// 1 psi = 2.03602068 inHg
    /// </summary>
    public const double PSI_TO_INHG = 2.03602068;

    /// <summary>
    /// The offset to convert celsius to kelvi n for temperature
    /// </summary>
    public const double KELVIN_OFFSET_FROM_CELSIUS = 273.15;

    /// <summary>
    /// Conversion factor from m/s to km/hr for velocity (metric)
    /// 1 m/s = 3.6 km/hr
    /// </summary>
    public const double METER_PER_SEC_TO_KM_PER_HR = 3.6;

    /// <summary>
    /// Conversion factor from m/s to mph for velocity
    /// 1 m/s = 2.23694 mph
    /// </summary>
    public const double MPS_TO_MPH = 2.23694;

    /// <summary>
    /// Conversion factor from km/hr to mph for velocity
    /// 1 km/hr = 0.621371 mph
    /// </summary>
    public const double KMH_TO_MPH = 0.621371;
}