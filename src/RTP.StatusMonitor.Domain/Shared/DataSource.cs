namespace RTP.StatusMonitor.Domain.Shared;

public enum DataSource
{
    /// <summary>
    /// Get data from dataClient table
    /// </summary>
    Actual,

    /// <summary>
    /// Get data from day ahead forecase blob file
    /// </summary>
    DayAhead,

    /// <summary>
    /// Get data from real time forecast blob file
    /// </summary>
    RealTime,

    /// <summary>
    /// Get data from airsonic table
    /// </summary>
    Airsonic
}
