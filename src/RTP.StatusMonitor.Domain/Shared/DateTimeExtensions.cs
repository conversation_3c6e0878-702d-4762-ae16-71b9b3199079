namespace RTP.StatusMonitor.Domain.Shared;

public static class DateTimeExtensions
{
    private static readonly DateTime epoch = new(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    /// <summary>
    /// Converts a UTC DateTime to Unix time in seconds
    /// </summary>
    /// 
    /// <param name="utcDateTime">The UTC DateTime to convert</param>
    /// 
    /// <returns>The Unix time in seconds</returns>
    public static int ToEpoch(this DateTime utcDateTime)
        => (int)(utcDateTime - epoch).TotalSeconds;

    /// <summary>
    /// Converts a UTC DateTime to Unix time in milliseconds
    /// </summary>
    /// 
    /// <param name="utcDateTime">The UTC DateTime to convert</param>
    /// 
    /// <returns>The Unix time in milliseconds</returns>
    public static double ToEpochInMs(this DateTime utcDateTime)
        => utcDateTime.ToEpoch() * 1000;
}