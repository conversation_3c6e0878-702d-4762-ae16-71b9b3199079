// REFERENCE - https://stackoverflow.com/questions/246498/creating-a-datetime-in-a-specific-time-zone-in-c-sharp

namespace RTP.StatusMonitor.Domain.Shared;

/// <summary>
/// This class is used to store a DateTime of a specific TimeZoneInfo and its utc time
/// It has 3 properties: 
/// - UtcDateTime: the DateTime of the instance converted to UTC
/// - UnixTimeInSeconds: the Unix time in seconds of the instance
/// - TimeZoneInfo: the TimeZoneInfo
/// Whenever a new instance is created, the datetime is converted to UTC and stored
/// </summary>
public record DateTimeWithZone
{
    public DateTime UtcDateTime { get; init; }
    public int UnixTimeInSeconds { get; init; }
    public long UnixTimeInMs { get; init; }
    public TimeZoneInfo TimeZoneInfo { get; init; }

    public DateTimeWithZone(
        DateTime dateTime,
        TimeZoneInfo timeZoneInfo)
    {
        DateTime dateTimeUnspec = DateTime
            .SpecifyKind(dateTime, DateTimeKind.Unspecified);

        UtcDateTime = TimeZoneInfo.ConvertTimeToUtc(dateTimeUnspec, timeZoneInfo);

        UnixTimeInSeconds = UtcDateTime.ToEpoch();

        UnixTimeInMs = (long)UnixTimeInSeconds * 1000;

        TimeZoneInfo = timeZoneInfo;
    }
}