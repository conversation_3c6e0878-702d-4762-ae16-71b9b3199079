namespace RTP.StatusMonitor.Domain.EquipmentSection;

public sealed class EquipmentSection
{
  public Guid Id { get; private set; }
  public string Name { get; private set; } = null!;
  public SectionType SectionType { get; private set; }
  public int Position { get; private set; }

  // SECTION - RELATIONSHIPS
  //**********************************************
  public Guid EquipmentGroupId { get; private set; }
  public EquipmentGroup.EquipmentGroup EquipmentGroup { get; private set; } = null!;

  public List<EquipmentContent.EquipmentContent> EquipmentContents { get; private set; } = new();
  //**********************************************

  // For EF Core
  public EquipmentSection()
  { }

  public EquipmentSection(
    Guid id,
    string name,
    SectionType sectionType,
    int position,
    List<EquipmentContent.EquipmentContent> equipmentContents
    )
  {
    Id = id;
    Name = name;
    SectionType = sectionType;
    Position = position;
    EquipmentContents = equipmentContents;
  }
}

