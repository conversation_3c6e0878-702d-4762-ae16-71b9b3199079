using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.Domain.Blocks;

public class Block
{
    // NOTE - for EF Core
    public Block()
    {
        Id = Guid.NewGuid();
        // create a new DataClient for a new Block
        DataClient = new DataClient
        {
            Id = Guid.NewGuid(),
            BlockId = Id,
            DataClientGroups = Enumerable.Empty<DataClientGroup>().ToList(),
            DataClientVariables = Enumerable.Empty<DataClientVariable>().ToList(),
            DataClientStatusVariables = Enumerable.Empty<DataClientStatusVariable>().ToList(),
        };
        DateCreated = DateTime.Now;
        DateModified = DateTime.Now;
        Units = Enumerable.Empty<Unit>().ToList();
    }

    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public int? Port { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }

    // SECTION - RELATIONSHIP
    // **********************************************

    // 1-to-many rel with Site
    public Guid SiteId { get; set; }
    public Site.Site Site { get; set; } = null!; // must belong to a Site

    // 1-to-many rel with Unit
    public ICollection<Unit> Units { get; set; }

    // 1-to-1 rel with DataClient (parent)
    public DataClient DataClient { get; set; } = null!;

    public List<EquipmentContent.EquipmentContent> EquipmentContents { get; private set; } = null!;

    // **********************************************

    /// <summary>
    /// This method will return the partition key for the block's input data in table storage
    ///
    /// Example: Bellingham-1-1
    /// </summary>
    public string GetInputDataPartitionKey() => $"{Site.Name}-{Name}-1";
}
