using ErrorOr;

namespace RTP.StatusMonitor.Domain.Reports;

public static class ReportColumnErrors
{
    public static Error InvalidReportColumnPosition => Error.Conflict(
        code: "Report.InvalidReportColumnPosition",
        description: "Report column position can't be negative");

    public static Error InvalidReportColumnSources => Error.Conflict(
        code: "Report.InvalidReportColumnSources",
        description: "Report column source is invalid");
    public static Error DuplicateColumnName => Error.Conflict(
        code: "Report.DuplicateColumnName",
        description: "Report column with the same name already exists");
}