namespace RTP.StatusMonitor.Domain.Reports;

public static class ReportColumnExtensions
{
    /// <summary>
    /// Groups the report columns by a specified key selector and returns a dictionary of grouped columns.
    /// </summary>
    /// <typeparam name="T">The type of the key.</typeparam>
    /// <param name="reportColumns">The collection of report columns.</param>
    /// <param name="keySelector">The function used to extract the key from each report column.</param>
    /// <returns>A dictionary containing the grouped report columns.</returns>
    public static Dictionary<T, List<ReportColumn>> GroupColumnsBy<T>(
        this IEnumerable<ReportColumn> reportColumns,
        Func<ReportColumn, T> keySelector) where T : notnull
        => reportColumns
            .GroupBy(keySelector)
            .ToDictionary(
                key => key.Key,
                value => value.ToList());
}
