using ErrorOr;

namespace RTP.StatusMonitor.Domain.Reports;

public record ReportSchedule
{
    /// <summary>
    /// Whether to enable the report or not. Defaults to false.
    /// </summary>
    public bool IsEnabled { get; private set; } = false;

    /// <summary>
    /// The interval at which the report should be processed (only meaning ful if report is enabled). Defaults to Hourly.
    /// </summary>
    public ReportProcessInterval ProcessInterval { get; private set; } = ReportProcessInterval.Hourly;

    public static ErrorOr<ReportSchedule> Create(
        bool isEnabled, string processInterval)
    {
        string[] validProcessIntervals = { "ThirtyMinutes", "Hourly", "Daily", "Weekly", "Monthly" };

        // Verify that the interval is one of the valid ones
        if (validProcessIntervals.Contains(processInterval) is false)
        {
            return ReportErrors.InvalidReportSchedule;
        }

        return new ReportSchedule
        {
            IsEnabled = isEnabled,
            ProcessInterval = Enum.Parse<ReportProcessInterval>(processInterval)
        };
    }
}
