using RTP.StatusMonitor.Domain.Site;

namespace RTP.StatusMonitor.Domain.Reports;

public abstract record ReportDateRangeInfo
{
    public Guid Id { get; init; }
    public Guid ReportId { get; init; }

    public (DateTime StartDate, DateTime EndDate) GetDateRange(
        Report report, SiteLocalTime localTime)
    {
        // Get the date range to query data
        (DateTime startDate, DateTime endDate) = this switch
        {
            FixedReportDateRange fixedDateRange => (fixedDateRange.StartDate, fixedDateRange.EndDate),
            RelativeReportDateRange relativeDateRange => relativeDateRange.ConvertToFixedDateRange(report, localTime),
            _ => throw new NotImplementedException(),
        };

        return (startDate, endDate);
    }
}
