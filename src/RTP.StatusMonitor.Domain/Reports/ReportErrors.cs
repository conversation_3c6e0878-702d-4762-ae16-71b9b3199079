using ErrorOr;

namespace RTP.StatusMonitor.Domain.Reports;

public static class ReportErrors
{
    public static Error NotFound => Error.NotFound(
        code: "Report.NotFound",
        description: "Report not found");

    public static Error DuplicateName => Error.Conflict(
        code: "Report.DuplicateName",
        description: "Report with the same name already exists");

    public static Error InsufficientWritePermission => Error.Unauthorized(
        code: "Report.InsufficientWritePermission",
        description: "User is not allowed to edit/delete report by others");

    public static Error InvalidReportSchedule => Error.Conflict(
        code: "Report.InvalidReportSchedule",
        description: "Report schedule is invalid");

    public static Error InvalidFixedDateRangeForForecast => Error.Conflict(
        code: "Report.InvalidFixedDateRangeForForecast",
        description: "Forecast report can only have relative date range type");

    public static Error InvalidDateRangeType => Error.Conflict(
        code: "Report.InvalidDateRangeType",
        description: "Report can only has fixed or relative date range");

    public static Error InvalidDateRange => Error.Conflict(
        code: "Report.InvalidDateRange",
        description: "Report has invalid date range to process");

    public static Error SingleSourceReportType => Error.Conflict(
        code: "Report.SingleSourceReportType",
        description: "This report type can only contain single source");

    public static Error InvalidReportGrouping => Error.Validation(
        code: "Report.InvalidReportGrouping",
        description: "Either the grouping interval or aggregate is invalid");

    public static Error InvalidReportFilter => Error.Validation(
        code: "Report.InvalidReportFilter",
        description: "Report filter must be either 'And' or 'Or' if provided");
    public static Error InvalidVariableGroup => Error.Conflict(
        code: "Report.InvalidVariableGroup",
        description: "Variable group is invalid");

    public static Error InvalidReportLayout => Error.Validation(
        code: "Report.InvalidReportLayout",
        description: "Invalid report layout");
}