using ErrorOr;
using RTP.StatusMonitor.Domain.Site;

namespace RTP.StatusMonitor.Domain.Reports;

/// <summary>
/// Represents a relative date range for a report.
/// For minute/hourly/daily reports, the date range is relative to the current date.
/// For weekly/monthly reports, the date range is relative to the current week/month.
/// </summary>
public record RelativeReportDateRange : ReportDateRangeInfo
{
    public int StartOffset { get; private set; }
    public int EndOffset { get; private set; }
    public RelativeReportDateRange() { }
    private RelativeReportDateRange(int startOffset, int endOffset)
    {
        Id = Guid.NewGuid();
        StartOffset = startOffset;
        EndOffset = endOffset;
    }

    public static ErrorOr<RelativeReportDateRange> Create(
        int? startOffset, int? endOffset)
    {
        if (startOffset is null || endOffset is null)
            return ReportErrors.InvalidDateRange;

        if (startOffset > endOffset)
            return ReportErrors.InvalidDateRange;

        return new RelativeReportDateRange(startOffset.Value, endOffset.Value);
    }

    /// <summary>
    /// Converts a relative date range to a fixed date range based on the provided site local time.
    /// </summary>
    /// <param name="report">The report in which the date range belongs to.</param>
    /// <param name="siteLocalTime">The site local time.</param>
    /// <returns>A tuple containing the start and end dates of the fixed date range.</returns>
    public (DateTime startDate, DateTime endDate) ConvertToFixedDateRange(
        Report report,
        SiteLocalTime siteLocalTime)
        => report.TimeGrouping.GroupingInterval switch
        {
            ReportGroupingInterval.Minute or
            ReportGroupingInterval.Hourly or
            ReportGroupingInterval.Daily => ConvertToFixedDateRangeForDaily(siteLocalTime),
            ReportGroupingInterval.Weekly => ConvertToFixedDateRangeForWeekly(siteLocalTime),
            ReportGroupingInterval.Monthly => ConvertToFixedDateRangeForMonthly(siteLocalTime),
            _ => throw new NotSupportedException("Unsupported interval for report date range"),
        };

    /// <summary>
    /// Convert the relative date range to the actual date range for daily reports
    /// </summary>
    /// <param name="siteLocalTime">The site local time.</param>
    /// <returns>A tuple containing the start and end dates of the fixed date range.</returns>
    private (DateTime startDate, DateTime endDate) ConvertToFixedDateRangeForDaily(
        SiteLocalTime siteLocalTime)
    {
        // Convert the relative date range to the actual date range
        // For example if current time is 2021-09-01 12:00:00 and offset is -1
        // The start date will be 2021-08-31 00:00:00
        // NOTE - Add 1 tick to filter out hour 00:00:00 
        DateTime startDate = siteLocalTime.Value.Date
            .AddDays(StartOffset)
            .AddTicks(1);

        // If index is 3, and current date is 2021-09-01
        // The end date will be 2021-09-05 00:00:00
        // NOTE - Need to get add 1 more days to get to hour 00:00:00 (end of date)
        DateTime endDate = siteLocalTime.Value.Date
            .AddDays(EndOffset)
            .AddDays(1);

        // Otherwise, filter the data by the date range
        return (startDate, endDate);
    }

    /// <summary>
    /// Convert the relative date range to the actual date range for weekly reports
    /// </summary>
    /// <param name="siteLocalTime">The site local time.</param>
    /// <returns>A tuple containing the start and end dates of the fixed date range.</returns>
    private (DateTime startDate, DateTime endDate) ConvertToFixedDateRangeForWeekly(
        SiteLocalTime siteLocalTime)
    {
        // Revert back n weeks and get the start date of the week (Monday)
        DateTime startDate = siteLocalTime.Value.Date
            .AddDays(StartOffset * 7)
            .AddDays(-(int)siteLocalTime.Value.DayOfWeek + (int)DayOfWeek.Monday);

        // Revert back n weeks 
        DateTime endWeek = siteLocalTime.Value.Date.AddDays(EndOffset * 7);

        // Get the start date of the end week (Monday)
        DateTime startOfEndWeek = endWeek
            .AddDays(-(int)endWeek.DayOfWeek + (int)DayOfWeek.Monday);

        // Then add 6 days to get the end date of the end week which is the last day of the week (Sunday)
        DateTime endDate = startOfEndWeek.AddDays(6);

        return (startDate, endDate);
    }

    /// <summary>
    /// Convert the relative date range to the actual date range for monthly reports
    /// </summary>
    /// <param name="siteLocalTime">The site local time.</param>
    /// <returns>A tuple containing the start and end dates of the fixed date range.</returns>
    private (DateTime startDate, DateTime endDate) ConvertToFixedDateRangeForMonthly(
        SiteLocalTime siteLocalTime)
    {
        // Start date will be first day of the month that is relative to current month
        DateTime startDate = new(
            siteLocalTime.Value.AddMonths(StartOffset).Year,
            siteLocalTime.Value.AddMonths(StartOffset).Month,
            1);

        // End date will be last day of the month that is relative to current month
        int endMonth = siteLocalTime.Value.AddMonths(EndOffset).Month;
        DateTime endDate = new(
            siteLocalTime.Value.AddMonths(EndOffset).Year,
            siteLocalTime.Value.AddMonths(EndOffset).Month,
            DateTime.DaysInMonth(startDate.Year, endMonth));

        return (startDate, endDate);
    }
}
