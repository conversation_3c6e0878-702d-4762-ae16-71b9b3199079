using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.Reports;

public interface IReportRepository
{
    /// <summary>
    /// Get all reports in db
    /// </summary>
    Task<List<Report>> GetReportsAsync(
        CancellationToken ct);

    /// <summary>
    /// Get report by id
    /// </summary>
    /// 
    /// <param name="reportId">The report id to get</param>
    /// <param name="ct"></param>
    /// 
    /// <returns>The report that matches the id, or null if not found</returns>
    // Task<Report?> GetReportByIdAsync(
    //     Guid reportId,
    //     CancellationToken ct);

    Task<Maybe<Report>> GetReportByIdAsync(
        Guid reportId,
        CancellationToken ct);

    /// <summary>
    ///  Get all reports that are enabled to run automatically in db
    /// </summary>
    /// 
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<List<Report>> GetEnabledReportsAsync(CancellationToken ct);

    /// <summary>
    /// Add a new report to the db
    /// </summary>
    /// 
    /// <param name="report">The new report to add</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task AddReportAsync(Report report, CancellationToken ct);

    /// <summary>
    /// Delete a report from the db
    /// </summary>
    /// 
    /// <param name="reportId">The id of the report to delete</param>
    /// <param name="ct"></param>
    /// 
    /// <returns></returns>
    void DeleteReport(Report report);
}