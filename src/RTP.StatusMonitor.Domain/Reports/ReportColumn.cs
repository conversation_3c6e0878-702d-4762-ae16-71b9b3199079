using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.Reports;

public class ReportColumn : Entity
{
    private readonly List<DataSource> _sources = [];

    /// <summary>
    /// site/block/unit id will be used to get the data for the report
    /// of the according site/block/unit
    /// </summary>
    public Guid SiteId { get; private set; }
    public Guid BlockId { get; private set; }
    public Guid UnitId { get; private set; }
    public string Name { get; private set; }

    /// <summary>
    /// Data source could be single or multiple if we are to process the data from multiple sources
    /// </summary>
    public IReadOnlyList<DataSource> Sources => _sources.AsReadOnly();
    public string Tag { get; private set; }
    public ComputedExpression Calculation { get; private set; }
    public FilterExpression Filter { get; private set; }
    public string EngUnits { get; private set; }

    /// <summary>
    /// The column position in the report
    /// </summary>
    public int Position { get; private set; }

    /// <summary>
    /// Color of the column
    /// </summary>
    public string Color { get; private set; }

    /// <summary>
    /// The no of decimals of analytics for the column (only applicable for numerical values)
    /// </summary>
    public int NoOfDecimals { get; private set; }

    private ReportColumn()
    {
        Name = string.Empty;
        Tag = string.Empty;
        Calculation = new ComputedExpression(string.Empty);
        Filter = new FilterExpression(string.Empty);
        EngUnits = string.Empty;
        Color = string.Empty;
    }

    private ReportColumn(
        Guid reportColumnId,
        Guid siteId,
        Guid blockId,
        Guid unitId,
        string name,
        List<DataSource> sources,
        string tag,
        ComputedExpression calculation,
        FilterExpression filter,
        int position,
        int noOfDecimals,
        string engUnits,
        string color) : base(reportColumnId)
    {
        SiteId = siteId;
        BlockId = blockId;
        UnitId = unitId;
        Name = name;
        _sources = sources;
        Tag = tag;
        Calculation = calculation;
        Filter = filter;
        Position = position;
        NoOfDecimals = noOfDecimals;
        EngUnits = engUnits;
        Color = color;
    }

    public static ErrorOr<ReportColumn> Create(
        Guid reportColumnId,
        Guid siteId,
        Guid blockId,
        Guid unitId,
        string name,
        List<string> sources,
        string tag,
        ComputedExpression calculation,
        FilterExpression filter,
        int position,
        int noOfDecimals,
        string engUnits,
        string color)
    {
        if (position < 0)
            return ReportColumnErrors.InvalidReportColumnPosition;

        // Make sure the sources are one of the following
        string[] validSources = { "RealTime", "DayAhead", "Actual", "Airsonic" };
        if (sources.Any(s => !validSources.Contains(s)))
        {
            return ReportColumnErrors.InvalidReportColumnSources;
        }

        return new ReportColumn(
            reportColumnId: reportColumnId,
            siteId: siteId,
            blockId: blockId,
            unitId: unitId,
            name: name,
            sources: sources
                .Select(s => Enum.Parse<DataSource>(s))
                .ToList(),
            tag: tag,
            calculation: calculation,
            filter: filter,
            position: position,
            noOfDecimals: noOfDecimals,
            engUnits: engUnits,
            color: color);
    }

    /// <summary>
    /// Check if the column should use raw data based on the report grouping interval
    /// </summary>
    /// <param name="interval">The report grouping interval</param>
    /// <returns>True if the column should use raw data, false otherwise</returns>
    public bool ShouldProcessWithRawData(ReportGroupingInterval interval)
    {
        // If there's a filter, we must use raw data
        if (!string.IsNullOrEmpty(Filter.Value))
            return true;

        // For hourly or daily intervals without a filter, we can use pre-aggregated data
        if (interval == ReportGroupingInterval.Hourly ||
            interval == ReportGroupingInterval.Daily)
        {
            return false;
        }

        // For all other cases (minute, weekly, monthly), use raw data
        return true;
    }

    /// <summary>
    /// Check if the column is a day ahead forecast column (1 source and it's day ahead)
    /// </summary>
    /// <returns>True if the column is a day ahead forecast column, false otherwise</returns>
    public bool IsDayAheadForecastColumn()
    => Sources.Count == 1 && Sources.Contains(DataSource.DayAhead);
}
