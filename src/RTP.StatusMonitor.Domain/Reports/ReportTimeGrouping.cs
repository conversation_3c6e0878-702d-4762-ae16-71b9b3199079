using ErrorOr;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.Reports;

/// <summary>
/// Indicate how the report is grouped
/// 1. GroupingInterval: The interval to group the data of the report by (e.g. Hourly, Daily, Weekly, etc...)
/// 2. GroupingAggregate: The aggregate function to group those data according to the specified interval (e.g. Sum, Average, Count, etc...)
/// </summary>
public record ReportTimeGrouping
{
    private readonly List<AggregateType> _groupingAggregates = new();
    public ReportGroupingInterval GroupingInterval { get; private set; }
    public IReadOnlyList<AggregateType> GroupingAggregates => _groupingAggregates.AsReadOnly();

    // Required by EF Core
    private ReportTimeGrouping()
    {
        // Default to hourly interval and grouped by average
        GroupingInterval = ReportGroupingInterval.Hourly;
        _groupingAggregates = [AggregateType.Avg];
    }

    private ReportTimeGrouping(
        string groupingInterval,
        List<string> groupingAggregates)
    {
        GroupingInterval = Enum.Parse<ReportGroupingInterval>(groupingInterval);
        _groupingAggregates = groupingAggregates
            .Select(Enum.Parse<AggregateType>)
            .ToList() ?? [];
    }

    public static ErrorOr<ReportTimeGrouping> Create(
        string interval,
        List<string> aggregateMethods)
    {
        string[] validGroupIntervals = ["Minute", "Hourly", "Daily", "Weekly", "Monthly"];
        string[] validGroupAggreagates = ["Min", "Max", "Avg", "StdDev", "Count"];

        // Verify that any of the aggregate methods must be one of the valid ones
        if (aggregateMethods.Any(aggregate => !validGroupAggreagates.Contains(aggregate)))
        {
            return ReportErrors.InvalidReportGrouping;
        }

        // Verify that the interval is one of the valid ones
        if (!validGroupIntervals.Contains(interval))
        {
            return ReportErrors.InvalidReportGrouping;
        }

        return new ReportTimeGrouping(interval, aggregateMethods);
    }
}
