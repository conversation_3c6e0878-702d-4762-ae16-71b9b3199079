using ErrorOr;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.Reports;

public record VariableGroup
{
    public string Name { get; private set; } = string.Empty;
    public string Tag { get; private set; } = string.Empty;
    public int Min { get; private set; }
    public int Max { get; private set; }
    public int Interval { get; private set; }

    private VariableGroup() { }

    [JsonConstructor]
    private VariableGroup(
        string tag,
        string name,
        int min,
        int max,
        int interval)
    {
        Tag = tag;
        Name = name;
        Min = min;
        Max = max;
        Interval = interval;
    }

    public static ErrorOr<VariableGroup> Create(
        string tag,
        string name,
        int min,
        int max,
        int interval)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return ReportErrors.InvalidVariableGroup;
        }

        if (min >= max)
        {
            return ReportErrors.InvalidVariableGroup;
        }

        return new VariableGroup(tag, name, min, max, interval);
    }
}

public record ReportVariableGrouping
{
    public DataSource Source { get; private set; }
    public VariableGroup Row { get; private set; }
    public VariableGroup Column { get; private set; }

    private ReportVariableGrouping() { }

    [JsonConstructor]
    private ReportVariableGrouping(
        DataSource source,
        VariableGroup row,
        VariableGroup column)
    {
        Source = source;
        Row = row;
        Column = column;
    }

    public static ErrorOr<ReportVariableGrouping> Create(
        string source,
        VariableGroup row,
        VariableGroup column)
    {
        List<string> validSources = new()
        { "Actual", "DayAhead", "RealTime", "Airsonic" };

        if (!validSources.Contains(source))
        {
            return ReportErrors.InvalidVariableGroup;
        }

        if (row.Min >= row.Max || column.Min >= column.Max)
        {
            return ReportErrors.InvalidVariableGroup;
        }

        return new ReportVariableGrouping(
            Enum.Parse<DataSource>(source), row, column);
    }
}

