using ErrorOr;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.Reports;

/// <summary>
/// The statistical filter to apply on the report.
/// Depending on the type of the report. The filter can be applied on the site, block, or unit. 
/// NOTE - Currently only supports stats for data client (block)
/// </summary>
public record ReportFilter
{
    public Guid? SiteId { get; private set; }
    public Guid? BlockId { get; private set; }
    public Guid? UnitId { get; private set; }
    public string Filter { get; private set; }
    public CombinationRule RuleGroup { get; private set; }

    // NOTE - Force Newtonsoft.Json to use this constructor to create the object
    [JsonConstructor]
    private ReportFilter(
        Guid? siteId,
        Guid? blockId,
        Guid? unitId,
        string filter,
        CombinationRule ruleGroup)
    {
        SiteId = siteId;
        BlockId = blockId;
        UnitId = unitId;
        Filter = filter;
        RuleGroup = ruleGroup;
    }

    /// <summary>
    /// Create a new report filter
    /// </summary>
    /// <param name="siteId"></param>
    /// <param name="blockId"></param>
    /// <param name="unitId"></param>
    /// <param name="filter"></param>
    /// <param name="ruleGroup"></param>
    /// <returns></returns>
    public static ErrorOr<ReportFilter> Create(
        Guid? siteId, Guid? blockId, Guid? unitId,
        string filter, string ruleGroup)
        => ruleGroup is not "And" and not "Or"
            ? ReportErrors.InvalidReportFilter
            : new ReportFilter(siteId, blockId, unitId, filter, Enum.Parse<CombinationRule>(ruleGroup));

    public List<DateTime> ApplyFilter(
        List<TimeSeriesData> data) =>
        // Put the time series data into a table for analytics to evaluate the filter
        // and get the dates that satisfy the filter
        SortedTimeSeriesTable
            .Create(data)
            .TryApplyFilter(new FilterExpression(Filter))
            .Match(
                table => table.GetColumnData(columnName: "Timestamp").Timestamps.ToList(),
                error => []);
}
