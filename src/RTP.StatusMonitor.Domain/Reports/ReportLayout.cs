using ErrorOr;

namespace RTP.StatusMonitor.Domain.Reports;

public enum ForecastLayoutType { VariableByHour, HourByVariable }
public enum HistoricalLayoutType { VariableByAggregate, AggregateByVariable }
public enum AccuracyLayoutType { Accuracy }
public enum LookupLayoutType { CombinedMatrix, SeparateMatrices }

// TODO - for now, temporary allow empty layout values (no layout selected)
public record ReportLayout
{
    public string Value { get; private set; } = string.Empty;
    public static implicit operator string(ReportLayout layout) => layout.Value;

    public static ErrorOr<ReportLayout> CreateForecastLayout(ReportType reportType, string layout)
    {
        // TODO - for now, temporary allow empty layout values (no layout selected)
        if (string.IsNullOrEmpty(layout))
            return new ReportLayout { Value = string.Empty };

        if (!Enum.TryParse(layout, out ForecastLayoutType forecastLayout))
        {
            return ReportErrors.InvalidReportLayout;
        }

        return reportType == ReportType.Forecast
        ? new ReportLayout { Value = forecastLayout.ToString() }
        : Error.Conflict(
            code: "Report.InvalidLayoutForReportType",
            description: "Report type must be Forecast to create a forecast layout");
    }


    public static ErrorOr<ReportLayout> CreateHistoricalLayout(ReportType reportType, string layout)
    {
        // TODO - for now, temporary allow empty layout values (no layout selected)
        if (string.IsNullOrEmpty(layout))
            return new ReportLayout { Value = string.Empty };

        if (!Enum.TryParse(layout, out HistoricalLayoutType historicalLayout))
        {
            return ReportErrors.InvalidReportLayout;
        }

        return reportType == ReportType.Historical
            ? new ReportLayout { Value = historicalLayout.ToString() }
         : Error.Conflict(
             code: "Report.InvalidLayoutForReportType",
             description: "Report type must be Historical to create a historical layout");
    }
    public static ErrorOr<ReportLayout> CreateLookupLayout(ReportType reportType, string layout)
    {
        // TODO - for now, temporary allow empty layout values (no layout selected)
        if (string.IsNullOrEmpty(layout))
            return new ReportLayout { Value = string.Empty };

        if (!Enum.TryParse(layout, out LookupLayoutType lookupLayout))
        {
            return ReportErrors.InvalidReportLayout;
        }

        return reportType == ReportType.Lookup
            ? new ReportLayout { Value = lookupLayout.ToString() }
            : Error.Conflict(
                code: "Report.InvalidLayoutForReportType",
                description: "Report type must be Lookup to create a lookup layout");
    }

    public static ErrorOr<ReportLayout> CreateAccuracyLayout(ReportType reportType, string layout)
    {
        // TODO - for now, temporary allow empty layout values (no layout selected)
        if (string.IsNullOrEmpty(layout))
            return new ReportLayout { Value = string.Empty };

        if (!Enum.TryParse(layout, out AccuracyLayoutType accuracyLayout))
        {
            return ReportErrors.InvalidReportLayout;
        }

        return reportType == ReportType.Accuracy
            ? new ReportLayout { Value = accuracyLayout.ToString() }
            : Error.Conflict(
                code: "Report.InvalidLayoutForReportType",
                description: "Report type must be Accuracy to create an accuracy layout");
    }
}
