// using ErrorOr;

// namespace RTP.StatusMonitor.Domain.Reports;

// public static class ReportColumnValidators
// {
//     public static Func<int, bool> IsColumnPositionValid => position => position >= 0;
//     public static Func<List<string>, bool> IsColumnDataSourceValid => sources
//         => Enumerable
//             .Range(0, sources.Count)
//             .All(i => Enum.TryParse<ReportDataSource>(sources[i], out _));

//     public static Func<int, List<string>, ErrorOr<bool>> IsReportColumnValid
//     => (int position, List<string> sources)
//     =>
//     {
//         if (!IsColumnPositionValid(position)) return ReportErrors.InvalidReportColumnPosition;
//         if (!IsColumnDataSourceValid(sources)) return ReportErrors.InvalidReportColumnSources;
//         return true;
//     };
// }