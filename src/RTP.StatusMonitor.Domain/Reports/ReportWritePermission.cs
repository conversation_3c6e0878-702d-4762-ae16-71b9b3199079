using RTP.StatusMonitor.Domain.Shared.Constants;

namespace RTP.StatusMonitor.Domain.Reports;

public static class ReportWritePermission
{

    /// <summary>
    /// Verifies if the user has write permission to the report.
    /// Must either be admin or the creator of the report.
    /// </summary>
    /// <param name="report">The report to delete/modify</param>
    /// <param name="requestedBy">The user requesting the delete/modify</param>
    /// <param name="appRoles">The roles of the user</param>
    /// <returns>True if the user has write permission, false otherwise</returns>
    public static bool HasWritePermission(
        this Report report,
        List<string> appRoles,
        string requestedBy)
    {
        // If user is admin, then they have permission
        if (appRoles.Contains(AppRole.Admin)) return true;

        // Otherwise, the user must be the creator of the report
        return report.AuditInfo.CreatedBy == requestedBy;
    }
}