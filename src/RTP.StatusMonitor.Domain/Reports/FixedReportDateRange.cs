using ErrorOr;

namespace RTP.StatusMonitor.Domain.Reports;

public record FixedReportDateRange : ReportDateRangeInfo
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public FixedReportDateRange() { }

    private FixedReportDateRange(DateTime startDate, DateTime endDate)
    {
        Id = Guid.NewGuid();
        StartDate = startDate.Date;
        EndDate = endDate.Date;
    }

    public static ErrorOr<FixedReportDateRange> Create(
        DateTime? startDate,
        DateTime? endDate)
    {
        if (startDate is null || endDate is null) return ReportErrors.InvalidDateRange;

        if (startDate > endDate) return ReportErrors.InvalidDateRange;

        return new FixedReportDateRange(startDate.Value, endDate.Value);
    }
}
