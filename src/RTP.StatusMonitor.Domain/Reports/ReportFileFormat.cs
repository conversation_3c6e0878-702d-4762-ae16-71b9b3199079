namespace RTP.StatusMonitor.Domain.Reports;

public enum ReportFileFormat
{
    Excel,
    CSV,
    HTML,
}

public static class ReportFileFormatExtensions
{
    /// <summary>
    /// Gets the file extension for the report file format
    /// </summary>
    /// <param name="fileFormat">The file format</param>
    /// <returns>The file extension</returns>
    /// <exception cref="InvalidOperationException">Thrown when the file format is unsupported</exception>
    public static string GetFileExtension(this ReportFileFormat fileFormat) =>
        fileFormat switch
        {
            ReportFileFormat.CSV => "csv",
            ReportFileFormat.Excel => "xlsx",
            _ => throw new InvalidOperationException($"Unsupported file format: {fileFormat}"),
        };

    /// <summary>
    /// Gets the MIME type for the report file format
    /// </summary>
    /// <param name="fileFormat">The file format</param>
    /// <returns>The MIME type</returns>
    /// <exception cref="InvalidOperationException">Thrown when the file format is unsupported</exception>
    public static string GetMimeType(this ReportFileFormat fileFormat) =>
        fileFormat switch
        {
            ReportFileFormat.HTML => "text/html",
            ReportFileFormat.CSV => "text/csv",
            ReportFileFormat.Excel =>
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            _ => throw new InvalidOperationException($"Unsupported file format: {fileFormat}"),
        };
}
