using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.Site;

namespace RTP.StatusMonitor.Domain.Reports;

public class Report : Entity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ReportType ReportType { get; private set; }

    /// <summary>
    /// Main filter will be used to filter against statistical historical data
    /// </summary>
    private readonly List<ReportFilter> _filters = [];
    public IReadOnlyList<ReportFilter> Filters => _filters.AsReadOnly();

    /// <summary>
    /// How data in the report should be grouped (how often and what method to group by)
    /// </summary>
    public ReportTimeGrouping TimeGrouping { get; private set; }

    public ReportVariableGrouping? VariableGrouping { get; private set; }

    /// <summary>
    /// The date range info of the report
    /// </summary>
    public ReportDateRangeInfo DateRangeInfo { get; private set; }
    public ReportSchedule Schedule { get; private set; }
    public AuditInfo AuditInfo { get; private set; }

    /// <summary>
    /// The location of the report in blob storage
    /// </summary>
    public string Container { get; private set; } = string.Empty;

    /// <summary>
    /// The layout of the report
    /// </summary>
    public ReportLayout ReportLayout { get; private set; }

    /// <summary>
    /// The format of the file
    /// </summary>
    public ReportFileFormat FileFormat { get; private set; }

    /// <summary>
    /// The last time the report was processed
    /// </summary>
    public DateTime? LastProcessedUtc { get; private set; }

    /// <summary>
    /// List of columns that are used in the report
    /// </summary>
    private readonly List<ReportColumn> _reportColumns = [];
    public IReadOnlyList<ReportColumn> ReportColumns => _reportColumns.AsReadOnly();

    /// <summary>
    /// List of block ids that are used in the report
    /// </summary>
    private readonly List<Guid> _blocksInReport = [];
    public IReadOnlyList<Guid> BlocksInReport => _blocksInReport.AsReadOnly();

    /// <summary>
    /// List of group ids that can access this report
    /// </summary>
    private readonly List<Guid> _userGroupsAllowed = [];
    public IReadOnlyList<Guid> UserGroupsAllowed => _userGroupsAllowed.AsReadOnly();

    internal Report()
    {
        Name = string.Empty;
        Description = string.Empty;
        _userGroupsAllowed = new List<Guid>();
        _blocksInReport = new List<Guid>();
        Schedule = ReportSchedule.Create(isEnabled: false, processInterval: string.Empty).Value;
        _filters = new List<ReportFilter>();
        TimeGrouping = ReportTimeGrouping
            .Create(interval: "Hourly", aggregateMethods: new List<string> { "Average" })
            .Value;
    }

    /// <summary>
    /// Record the latest processing time
    /// </summary>
    public void RecordLastProcessingTime(DateTime processingCompletionTime) =>
        LastProcessedUtc = processingCompletionTime;

    /// <summary>
    /// Create a new historical report
    /// </summary>
    public static ErrorOr<Report> CreateHistoricalOrAccuracyReport(
        Guid reportId,
        string name,
        string description,
        ReportType reportType,
        List<ReportFilter> filters,
        ReportTimeGrouping timeGrouping,
        List<ReportColumn> columns,
        ReportSchedule schedule,
        ReportLayout reportLayout,
        ReportFileFormat fileFormat,
        string container,
        List<Guid> userGroupsAllowed,
        List<Guid> blocksInReport,
        AuditInfo auditInfo,
        List<Report> existingReports,
        ReportDateRangeInfo dateRangeInfo
    )
    {
        // Check for duplicate report name
        if (existingReports.Any(r => r.Name == name))
        {
            return ReportErrors.DuplicateName;
        }

        // Make sure there is no duplicate column name
        if (new HashSet<string>(columns.Select(c => c.Name)).Count != columns.Count)
        {
            return ReportColumnErrors.DuplicateColumnName;
        }

        return new Report(
            reportId: reportId,
            name: name,
            description: description,
            reportType: reportType,
            filters: filters,
            grouping: timeGrouping,
            variableGrouping: null,
            reportColumns: columns,
            dateRangeInfo: dateRangeInfo,
            schedule: schedule,
            reportLayout: reportLayout,
            fileFormat: fileFormat,
            container: container,
            userGroupsAllowed: userGroupsAllowed,
            blocksInReport: blocksInReport,
            auditInfo: auditInfo
        );
    }

    /// <summary>
    /// Create a new lookup report
    /// </summary>
    public static ErrorOr<Report> CreateLookupReport(
        Guid reportId,
        string name,
        string description,
        List<ReportFilter> filters,
        ReportTimeGrouping timeGrouping,
        ReportVariableGrouping variableGrouping,
        List<ReportColumn> columns,
        ReportSchedule schedule,
        string container,
        ReportLayout reportLayout,
        ReportFileFormat fileFormat,
        List<Guid> userGroupsAllowed,
        List<Guid> blocksInReport,
        AuditInfo auditInfo,
        List<Report> existingReports,
        ReportDateRangeInfo dateRangeInfo
    )
    {
        // Check for duplicate report name
        if (existingReports.Any(r => r.Name == name))
        {
            return ReportErrors.DuplicateName;
        }

        // Make sure there is no duplicate column name
        if (new HashSet<string>(columns.Select(c => c.Name)).Count != columns.Count)
        {
            return ReportColumnErrors.DuplicateColumnName;
        }

        return new Report(
            reportId: reportId,
            name: name,
            description: description,
            reportType: ReportType.Lookup,
            filters: filters,
            grouping: timeGrouping,
            variableGrouping: variableGrouping,
            reportColumns: columns,
            dateRangeInfo: dateRangeInfo,
            schedule: schedule,
            container: container,
            reportLayout: reportLayout,
            fileFormat: fileFormat,
            userGroupsAllowed: userGroupsAllowed,
            blocksInReport: blocksInReport,
            auditInfo: auditInfo
        );
    }

    /// <summary>
    /// Create a new report
    /// </summary>
    public static ErrorOr<Report> CreateForecastReport(
        Guid reportId,
        string name,
        string description,
        List<ReportColumn> columns,
        ReportSchedule schedule,
        string container,
        List<Guid> userGroupsAllowed,
        List<Guid> blocksInReport,
        AuditInfo auditInfo,
        ReportDateRangeInfo dateRangeInfo,
        ReportLayout reportLayout,
        ReportFileFormat fileFormat,
        List<Report> existingReports
    )
    {
        // Check for duplicate report name
        if (existingReports.Any(r => r.Name == name))
        {
            return ReportErrors.DuplicateName;
        }

        // Make sure there is no duplicate column name
        if (new HashSet<string>(columns.Select(c => c.Name)).Count != columns.Count)
        {
            return ReportColumnErrors.DuplicateColumnName;
        }

        // Forecast report can only contain one source
        List<DataSource> dataSources = columns.SelectMany(col => col.Sources).Distinct().ToList();
        if (dataSources.Count > 1)
            return ReportErrors.SingleSourceReportType;

        ErrorOr<ReportTimeGrouping> timeGrouping = ReportTimeGrouping.Create(
            interval: "Hourly",
            aggregateMethods: new List<string> { "Avg" }
        );
        if (timeGrouping.IsError)
            return timeGrouping.Errors;

        return new Report(
            reportId: reportId,
            name: name,
            description: description,
            reportType: ReportType.Forecast,
            filters: Enumerable.Empty<ReportFilter>().ToList(),
            grouping: timeGrouping.Value,
            variableGrouping: null,
            reportColumns: columns,
            blocksInReport: blocksInReport,
            dateRangeInfo: dateRangeInfo,
            schedule: schedule,
            container: container,
            reportLayout: reportLayout,
            fileFormat: fileFormat,
            userGroupsAllowed: userGroupsAllowed,
            auditInfo: auditInfo
        );
    }

    /// <summary>
    /// Constructor
    /// </summary>
    private Report(
        Guid reportId,
        string name,
        string description,
        ReportType reportType,
        List<ReportFilter> filters,
        ReportTimeGrouping grouping,
        ReportVariableGrouping? variableGrouping,
        List<ReportColumn> reportColumns,
        ReportDateRangeInfo dateRangeInfo,
        ReportSchedule schedule,
        string container,
        ReportLayout reportLayout,
        ReportFileFormat fileFormat,
        List<Guid> userGroupsAllowed,
        List<Guid> blocksInReport,
        AuditInfo auditInfo
    )
        : base(reportId)
    {
        Name = name;
        Description = description;
        ReportType = reportType;
        _filters = filters;
        TimeGrouping = grouping;
        VariableGrouping = variableGrouping;
        _reportColumns = reportColumns;
        DateRangeInfo = dateRangeInfo;
        Schedule = schedule;
        Container = container;
        ReportLayout = reportLayout;
        FileFormat = fileFormat;
        // NOTE - always make sure RealTimePower is allowed
        _userGroupsAllowed = userGroupsAllowed
            .Append(UserGroupsConstants.RealTimePower)
            .Distinct()
            .ToList();
        _blocksInReport = blocksInReport;
        AuditInfo = auditInfo;
    }

    /// <summary>
    /// Apply the report filters sequentially to get the final list of filtered dates
    /// </summary>
    ///
    /// <param name="filteredDates">The dates obtained from evaluating each filtered on the approprivate data source</param>
    /// <returns></returns>
    public List<DateTime> ApplyReportFilters(List<List<DateTime>> filteredDates)
    {
        // If there is only 1 filter (there is no union/intersection to perform), then get all the timestamps
        if (_filters.Count == 1)
        {
            return filteredDates.SelectMany(x => x).Distinct().ToList();
        }

        // Otherwise, union/intersect the filtered dates based on the combination rule
        // Use the first filter as the initial to aggregate the results
        List<DateTime> results =
            filteredDates.Count > 0 ? filteredDates.First() : Enumerable.Empty<DateTime>().ToList();

        // Start from second filter since the first filter is already in the results
        for (int i = 1; i < _filters.Count; i++)
        {
            // NOTE - Use the combination rule of previous filter to combine the results
            CombinationRule previousCombinationRule = _filters[i - 1].RuleGroup;

            results = previousCombinationRule switch
            {
                CombinationRule.And => results.Intersect(filteredDates[i]).ToList(),
                CombinationRule.Or => results.Union(filteredDates[i]).ToList(),
                _ => results,
            };
        }

        return results;
    }

    /// <summary>
    /// Gets the date range for generating a report based on the specified site and UTC time.
    /// </summary>
    /// <param name="site">The site for which the report is being generated.</param>
    /// <param name="utcTime">The UTC time for which the report is being generated.</param>
    /// <returns>A tuple containing the start and end dates of the report.</returns>
    public (DateTime startDate, DateTime endDate) GetReportDateRange(
        Site.Site site,
        DateTime utcTime
    ) =>
        DateRangeInfo switch
        {
            FixedReportDateRange fixedDateRange => (
                fixedDateRange.StartDate,
                fixedDateRange.EndDate
            ),
            RelativeReportDateRange relativeDateRange => relativeDateRange.ConvertToFixedDateRange(
                this,
                SiteLocalTime.Create(site, utcTime)
            ),
            _ => throw new InvalidOperationException("Invalid date range type"),
        };
}
