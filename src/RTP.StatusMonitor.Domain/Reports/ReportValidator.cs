// namespace RTP.StatusMonitor.Domain.Reports;

// public static class ReportValidator
// {
//     public static readonly Func<ReportType, List<ReportColumn>, bool> ForecastOrHistoricalHasSingleDataSource =
//         (reportType, columns) => reportType switch
//         {
//             ReportType.Forecast => columns.SelectMany(col => col.Sources).Distinct().Count() == 1,
//             ReportType.Historical => columns.SelectMany(col => col.Sources).Distinct().Count() == 1,
//             _ => true
//         };

//     public static readonly Func<ReportType, ReportDateRangeType, bool> IsFixedDateRangeAllowed =
//         (reportType, dateRangeType) => reportType switch
//         {
//             ReportType.Forecast => dateRangeType == ReportDateRangeType.Fixed,
//             _ => true
//         };
// }