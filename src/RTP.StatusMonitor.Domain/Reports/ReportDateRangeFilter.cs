using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.Reports;

public static class ReportDateRangeFilter
{
    /// <summary>
    /// Filter the time series data by the date range of the report.
    /// If the report is relative date range, convert the relative date range to the actual date range and filter the data by the date range.
    /// </summary>
    /// 
    /// <param name="report">The report in which the data belong to and the date range to apply to filter</param>
    /// <param name="data">The time series data to filter by the date range of the report</param>
    /// <param name="siteLocalTime">The site local time to convert the relative date range to the actual date range</param>
    /// 
    /// <returns>The time series data after applying the date range filter</returns>
    public static TimeSeriesData FilterByReportDateRange(
        this Report report,
        TimeSeriesData data,
        SiteLocalTime siteLocalTime)
    => report.DateRangeInfo switch
    {
        RelativeReportDateRange relativeDateRange => FilterRelativeDateRange(
            report,
            relativeDateRange,
            data,
            siteLocalTime),
        FixedReportDateRange fixedDateRange => data
            .FilterByDateRange(fixedDateRange.StartDate, fixedDateRange.EndDate),
        _ => throw new NotSupportedException("Unsupported date range type")
    };

    /// <summary>
    /// Filter the data of the report by the relative date range.
    /// Convert the relative date range to the actual date range and filter the data by the date range.
    /// </summary>
    /// <param name="report">The report in which the data belong to and the date range to apply to filter</param>
    /// <param name="data">The time series data to filter by the date range of the report</param>
    /// <returns>The time series data after applying the date range filter</returns>
    private static TimeSeriesData FilterRelativeDateRange(
        Report report,
        RelativeReportDateRange relativeDateRange,
        TimeSeriesData data,
        SiteLocalTime localTime)
    {
        // Otherwise, convert the relative date range to the actual date range
        (DateTime startDate, DateTime endDate) = relativeDateRange
            .ConvertToFixedDateRange(report, localTime);

        // Otherwise, filter the data by the date range
        return data.FilterByDateRange(startDate, endDate);
    }
}
