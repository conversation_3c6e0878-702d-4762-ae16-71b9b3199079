using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.Domain.Reports;

/// <summary>
/// This will indicate how often data in the report will be grouped
/// (every minute, hourly, daily, etc.)
/// </summary>
public enum ReportGroupingInterval
{
    Minute = 0,
    Hourly = 1,
    Daily = 2,
    Weekly = 3,
    Monthly = 4,
}


public static class ReportGroupingIntervalExtensions
{
    /// <summary>
    /// Map the report grouping interval to the time series resampling interval
    /// </summary>
    /// <param name="interval">The report grouping interval</param>
    /// <returns>The time series resampling interval</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when the report grouping interval is invalid</exception>
    public static TimeSeriesResamplingInterval ToTimeSeriesResamplingInterval(this ReportGroupingInterval interval) => interval switch
    {
        ReportGroupingInterval.Minute => TimeSeriesResamplingInterval.Minute,
        ReportGroupingInterval.Hourly => TimeSeriesResamplingInterval.Hour,
        ReportGroupingInterval.Daily => TimeSeriesResamplingInterval.Day,
        ReportGroupingInterval.Weekly => TimeSeriesResamplingInterval.Week,
        ReportGroupingInterval.Monthly => TimeSeriesResamplingInterval.Month,
        _ => throw new ArgumentOutOfRangeException(nameof(interval), interval, "Invalid report grouping interval")
    };
}


