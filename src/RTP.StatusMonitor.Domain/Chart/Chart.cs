using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.Chart;

public sealed class Chart : Entity
{
	public string Name { get; private set; } = null!;
	public string Notes { get; private set; } = string.Empty;
	public bool IsDefault { get; private set; } = false;
	public string Type { get; private set; } = string.Empty;
	public string ChartStatuses { get; set; } = string.Empty;
	public string ChartAreas { get; set; } = string.Empty;
	public Site.Site Site { get; private set; } = null!;
	public Guid SiteId { get; private set; }

	private Chart() { }

	internal Chart(
		Guid id,
		string name,
		string notes,
		bool isDefault,
		string type,
		string chartStatuses,
		string chartAreas,
		Site.Site site)
	{
		Id = id;
		Name = name;
		Notes = notes;
		IsDefault = isDefault;
		Type = type;
		ChartStatuses = chartStatuses;
		ChartAreas = chartAreas;
		Site = site;
		SiteId = site.Id;
	}
}