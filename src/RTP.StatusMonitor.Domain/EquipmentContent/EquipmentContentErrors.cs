using ErrorOr;

namespace RTP.StatusMonitor.Domain.EquipmentContent;

public static class EquipmentContentErrors
{
  // Errors for saving group content to null block
  public static Error NullBlock => Error.Unexpected(
    code: "EquipmentGroupContent.NullBlock",
    description: "The block to save the equipment group content to was null"
  );

  public static Error InvalidEquipmentRange => Error.Unexpected(
    code: "EquipmentGroupContent.InvalidEquipmentRange",
    description: "The equipment range is invalid"
  );

  public static Error UpdateForecastFailed => Error.Unexpected(
    code: "EquipmentGroupContent.UpdateForecastFailed",
    description: "Failed to update the equipment value"
  );
}