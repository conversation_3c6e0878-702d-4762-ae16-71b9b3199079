using ErrorOr;

namespace RTP.StatusMonitor.Domain.EquipmentContent;

public record ContentRange
{
    public double? Min { get; private set; }
    public double? Max { get; private set; }
    public double? Interval { get; private set; }

    private ContentRange(double? min, double? max, double? interval)
    {
        Min = min;
        Max = max;
        Interval = interval;
    }

    public static ErrorOr<ContentRange> Create(double? min, double? max, double? interval)
    {
        if (min.HasValue && max.HasValue && min.Value > max.Value)
        {
            return EquipmentContentErrors.InvalidEquipmentRange;
        }

        return new ContentRange(min, max, interval);
    }
}