using ErrorOr;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.EquipmentContent;

public sealed class EquipmentContent
{
    public Guid Id { get; set; }
    public string Alias { get; private set; } = null!; // alias is user created abbreviation => different from alias tied to a specific tag
    public string Tag { get; private set; } = null!;
    public double Value { get; private set; }
    public string Suffix { get; private set; } = string.Empty;
    public ContentType Type { get; private set; }
    public ContentFormat Format { get; private set; }
    public ContentPosition Position { get; private set; } = null!;
    public ContentRange Range { get; private set; } = null!;
    public AuditInfo AuditInfo { get; set; } = null!;

    // SECTION - RELATIONSHIPS
    //*************************************************
    public Guid EquipmentSectionId { get; private set; }
    public EquipmentSection.EquipmentSection EquipmentSection { get; private set; } = null!;
    public Guid BlockId { get; private set; }
    public Block Block { get; private set; } = null!;
    //*************************************************

    private EquipmentContent()
    { }


    private EquipmentContent(
    Guid id,
    string alias,
    ContentType type,
    ContentFormat format,
    ContentRange range,
    ContentPosition position,
    string tag,
    double value,
    string? suffix,
    Block block,
    AuditInfo auditInfo
    )
    {
        Id = id;
        Alias = alias;
        Type = type;
        Format = format;
        Position = position;
        Range = range;
        Tag = tag;
        Value = value;
        Suffix = suffix ?? string.Empty;
        Block = block;
        BlockId = block.Id;
        AuditInfo = auditInfo;
    }

    public static ErrorOr<EquipmentContent> Create(
        Guid id,
        string alias,
        ContentType type,
        ContentFormat format,
        ContentRange range,
        ContentPosition position,
        string tag,
        double value,
        string? suffix,
        Block? block,
        AuditInfo auditInfo)
    {
        if (block is null)
            return EquipmentContentErrors.NullBlock;

        return new EquipmentContent(
            id: id,
            alias: alias,
            type: type,
            format: format,
            position: position,
            range: range,
            tag: tag,
            value: value,
            suffix: suffix,
            block: block,
            auditInfo: auditInfo);
    }

    /// <summary>
    /// Update the value of the content 
    /// if user has access to the equipment group
    /// </summary>
    public void UpdateEquipmentValue(double value) => Value = value;
}
