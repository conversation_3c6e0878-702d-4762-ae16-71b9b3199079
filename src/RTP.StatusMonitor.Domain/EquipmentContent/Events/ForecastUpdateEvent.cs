using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.EquipmentContent.Events;

public class ForecastUpdateEvent(
    List<ForecastUpdate> updates,
    UpdateAction action,
    bool isTest,
    string createdBy,
    string site,
    string note,
    Guid? alertId) : IntegrationEvent(createdBy, nameof(ForecastUpdateEvent))
{
    public string Site { get; } = site;
    public List<ForecastUpdate> Updates { get; } = updates;
    public UpdateAction Action { get; } = action;
    public bool IsTest { get; } = isTest;
    public string Note { get; } = note;
    public Guid? AlertId { get; init; } = alertId;
}
public enum UpdateAction { UpdateOnly, UpdateAndSend }
public record ForecastUpdate(string Block, List<UpdateVariable> Data);
public record UpdateVariable(string Name, double Value, long Timestamp);
