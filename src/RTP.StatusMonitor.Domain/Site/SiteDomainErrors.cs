using ErrorOr;

namespace RTP.StatusMonitor.Domain.Site;

public static class SiteDomainErrors
{
    public static Error Unauthorized => Error.Failure(
        code: "Site.Unauthorized",
        description: "User does not sufficient permissions to perform this action"
    );

    public static Error NotFound => Error.Failure(
        code: "Site.NotFound",
        description: "Site not found"
    );
    public static Error NameConflict => Error.Conflict(
        code: "Site.NameConflict",
        description: "This site name is already in use"
    );
}