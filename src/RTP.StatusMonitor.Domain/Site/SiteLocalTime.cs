namespace RTP.StatusMonitor.Domain.Site;

public record SiteLocalTime
{
    public DateTime Value { get; private set; }
    private SiteLocalTime(DateTime value) => Value = value;
    /// <summary>
    /// Implicitly convert a SiteLocalTime to a DateTime
    /// </summary>
    /// <param name="time">The SiteLocalTime to convert.</param>
    /// <returns>The DateTime value of the SiteLocalTime.</returns>
    public static implicit operator DateTime(SiteLocalTime time) => time.Value;
    public static SiteLocalTime Create(
        Site site,
        DateTime utcTime)
    {
        TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone);

        return new SiteLocalTime(TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone));
    }
}
