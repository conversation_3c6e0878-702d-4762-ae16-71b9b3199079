using ErrorOr;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.UserGroups;

namespace RTP.StatusMonitor.Domain.Site;

public sealed class Site
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public int LocationKey { get; set; }
    public double Altitude { get; set; }
    public bool IsMetric { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string TimeZone { get; set; } = string.Empty;
    public DateTime DateCreated { get; set; } = DateTime.Now;
    public DateTime DateModified { get; set; } = DateTime.Now;

    private readonly List<StationDetails> _stationDetails = [];
    private readonly List<Chart.Chart> _charts = [];

    public SiteWeatherSettings.SiteWeatherSettings SiteWeatherSettings { get; private set; } =
        null!;
    public IReadOnlyList<Chart.Chart> Charts => _charts.AsReadOnly();

    // TODO - need to push this to SiteWeatherSettings
    public IReadOnlyCollection<StationDetails> StationDetails => _stationDetails.AsReadOnly();

    // SECTION - RELATIONSHIPS
    //*******************************************
    // 1-to-many relationship with Customer
    public Guid CustomerId { get; set; }
    public Customer.Customer Customer { get; set; } = null!;

    // 1-to-many relationship with Block
    public ICollection<Block> Blocks { get; set; } = null!;

    // many-to-many relationship with UserGroup
    public List<GroupPermission> GroupPermissions { get; set; } = null!;

    // 1-to-many relationship with EquipmentGroup
    public List<EquipmentGroup.EquipmentGroup> EquipmentGroups { get; private set; } = [];

    //*******************************************

    private Site() { }

    internal Site(
        Guid id,
        string name,
        string alias,
        string description,
        string location,
        // string mapLocation,
        double altitude,
        bool isMetric,
        double latitude,
        double longitude,
        string timeZone,
        int locationKey,
        List<UserGroup> userGroups
    )
    {
        Id = id;
        Name = name;
        Alias = alias;
        Description = description;
        Location = location;
        // MapLocation = mapLocation;
        Altitude = altitude;
        IsMetric = isMetric;
        Latitude = latitude;
        Longitude = longitude;
        TimeZone = timeZone;
        LocationKey = locationKey;
        // Assign group permissions for the site
        GroupPermissions = userGroups.Select(ug => new GroupPermission(ug, this)).ToList();
    }

    /// <summary>
    /// Update the site with the new values
    /// </summary>
    /// <param name="name">The new name of the site</param>
    /// <param name="alias">The new alias of the site</param>
    /// <param name="description">The new description of the site</param>
    /// <param name="location">The new location of the site</param>
    /// <param name="mapLocation">The new map location of the site</param>
    /// <param name="altitude">The new altitude of the site</param>
    /// <param name="isMetric">The new metric status of the site</param>
    /// <param name="latitude">The new latitude of the site</param>
    /// <param name="longitude">The new longitude of the site</param>
    /// <param name="timeZone">The new time zone of the site</param>
    /// <param name="userGroupsAllowedAccess">The new user groups allowed access to the site</param>
    /// <param name="customerId">The new customer id of the site</param>
    /// <returns>An error or the updated site</returns>
    public ErrorOr<Updated> Update(
        string name,
        string alias,
        string description,
        string location,
        // string mapLocation,
        double altitude,
        bool isMetric,
        double latitude,
        double longitude,
        string timeZone,
        List<UserGroup> userGroupsAllowedAccess,
        Guid customerId
    )
    {
        Name = name;
        Alias = alias;
        Description = description;
        Location = location;
        // MapLocation = mapLocation;
        Altitude = altitude;
        IsMetric = isMetric;
        Latitude = latitude;
        Longitude = longitude;
        TimeZone = timeZone;
        CustomerId = customerId;
        GroupPermissions = userGroupsAllowedAccess
            .Select(ug => new GroupPermission(ug, this))
            .ToList();

        // Indicate success update
        return new Updated();
    }

    /// <summary>
    /// Given a list of group id of the current user
    /// When the user belongs to a group that allows access to the site
    /// Then return true indicate allow access
    /// </summary>
    /// <param name="userGroupsId">a list of groups id the user belongs to</param>
    /// <returns></returns>
    public bool HasAccessToSite(List<Guid> userGroupsId) =>
        GroupPermissions.Any(gp => userGroupsId.Contains(gp.GroupId));

    /// <summary>
    /// This method will remove all the current weather stations that are saved in the site
    /// </summary>
    public void RemoveSavedWeatherStations() => _stationDetails.Clear();

    /// <summary>
    /// Give a weather station to add to the site
    /// Then we should create the station instance and add it to the site
    /// <returns>a list of stations details</returns>
    /// </summary>
    public ErrorOr<List<StationDetails>> AddStationToSite(
        Guid id,
        string name,
        WeatherApi api,
        double latitude,
        double longitude,
        double elevation,
        string state,
        string city,
        double distanceFrom,
        double directionFrom,
        string directionFromTag
    )
    {
        ErrorOr<StationDetails> station = Entities.StationDetails.Create(
            id,
            name,
            api,
            latitude,
            longitude,
            elevation,
            state,
            city,
            distanceFrom,
            directionFrom,
            directionFromTag,
            this
        );

        if (station.IsError)
            return station.Errors;

        _stationDetails.Add(station.Value);

        return _stationDetails;
    }

    /// <summary>
    /// Given the user roles of current user
    /// We need to check if the user
    /// is allowed to save/update the chart.
    /// If so then save the charts for the site
    /// </summary>
    public ErrorOr<Chart.Chart> SaveChart(
        List<string> appRoles,
        Guid id,
        string name,
        string notes,
        bool isDefault,
        string type,
        string chartStatuses,
        string chartAreas
    )
    {
        // Must be admin to create chart
        if (appRoles.Contains(AppRole.Admin) is false)
        {
            return SiteDomainErrors.Unauthorized;
        }

        // Create and add the chart to the list
        Chart.Chart chart = new(
            id: id,
            name: name,
            notes: notes,
            isDefault: isDefault,
            type: type,
            chartStatuses: chartStatuses,
            chartAreas: chartAreas,
            this
        );

        return chart;
    }
}
