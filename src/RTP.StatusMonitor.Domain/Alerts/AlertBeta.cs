using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.Alerts;

public abstract record AlertTrigger(int ExpirationTimeInMinutes);

public class AlertBeta(
    Guid id,
    string name,
    string description,
    bool isEnabled,
    AlertTrigger trigger,
    string title,
    List<AlertType> alertTypes,
    List<EmailAddress> recipients,
    List<AlertContent> contents,
    List<AlertAttachment> attachments,
    Guid siteId,
    DateTime? lastSentUtc
) : Entity(id)
{
    public string Name { get; private set; } = name;
    public string Description { get; private set; } = description;
    public bool IsEnabled { get; private set; } = isEnabled;
    public AlertTrigger Trigger { get; private set; } = trigger;
    public string Title { get; private set; } = title;
    public List<EmailAddress> Recipients { get; private set; } = recipients;
    private readonly List<AlertType> _alertTypes = alertTypes;
    public IReadOnlyList<AlertType> AlertTypes => _alertTypes.AsReadOnly();
    private readonly List<AlertContent> _alertContents = contents;
    public IReadOnlyList<AlertContent> AlertContents => _alertContents.AsReadOnly();
    private readonly List<AlertAttachment> _alertAttachments = attachments;
    public IReadOnlyList<AlertAttachment> Attachments => _alertAttachments.AsReadOnly();
    public AuditInfo AuditInfo { get; private set; } = null!;
    public DateTime? LastSentUtc { get; private set; } = lastSentUtc;

    /// <summary>
    /// The scope of the alert (which site is this alert for)
    /// </summary>
    public Guid SiteId { get; private set; } = siteId;

    /// <summary>
    /// Keep track of the latest time when the alert is sent
    /// This will help make sure to over-sent alerts within a set period
    /// </summary>
    /// <param name="sentAtUtc">The utc time when alert was last sent</param>
    public ErrorOr<Updated> RecordTimeSent(DateTime sentAtUtc)
    {
        // Check the timezone of the sent at time
        if (sentAtUtc.Kind != DateTimeKind.Utc)
        {
            return AlertErrors.InvalidAlertSentTime;
        }

        // Check if the sent at time is before the last sent time
        if (LastSentUtc is not null && sentAtUtc < LastSentUtc)
        {
            return AlertErrors.InvalidAlertSentTime;
        }

        LastSentUtc = sentAtUtc;
        return Result.Updated;
    }

    /// <summary>
    /// Determine whether the alert should be sent now based on the type of trigger
    /// and if the time has expired since last sent
    /// </summary>
    /// <param name="currentTimeUtc">The current utc time to compare against last sent utc time</param>
    public bool ShouldBeSent(DateTime currentTimeUtc)
    {
        if (LastSentUtc.HasValue)
        {
            TimeSpan sinceLastAlert = currentTimeUtc - LastSentUtc.Value;
            if (sinceLastAlert.TotalMinutes < Trigger.ExpirationTimeInMinutes)
            {
                return false;
            }
        }

        // Convert utc to central time
        DateTime centralTime = TimeZoneInfo.ConvertTimeFromUtc(
            currentTimeUtc,
            TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time")
        );

        return Trigger switch
        {
            ScheduleTrigger scheduleTrigger => scheduleTrigger.IsScheduledToRun(centralTime),
            CriteriaTrigger criteriaTrigger => criteriaTrigger.IsTriggerConditionMet(),
            _ => throw new InvalidOperationException($"Invalid trigger {Trigger.GetType().Name}"),
        };
    }
}
