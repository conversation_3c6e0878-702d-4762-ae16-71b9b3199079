namespace RTP.StatusMonitor.Domain.Alerts;

public enum NotificationType
{
    Email,
}


// # Alerts
// - Id
// - Name
// - Description
// - Site (an entity in our domain/the power plant)
// - Block (an entity in our domain/ a block of a power plant)
// - AlertType (Forecast, Accuracy, etc... will dictate the format of the email)
// - Criteria (the trigger to send the alert - azure function will evaluate the criteria on a hourly basis and send the alerts if it meets the criteria)
// - UploadToBlob (whether to upload the artifacts of the alert for storage)
// - Frequency (how often this alert should be sent - used to limit how often alert is sent, ex if configured 30 minutes, then the alert wont be sent within 30 minutes of each other even if the criteria is met)
// - NotificationType (method of alert - currently only support email)
// - RunTimes (the exact times the alert will be sent out - ex: 12:00, 1:08, etc... )
// - AlertDetails (JSON string)
// - Reports (a list of reports that will be sent as attachments)

// # AlertDetails (this will be the configuration of the email)
// - Title (email title)
// - Recipient (email of receivers)
// - Body (this will be the predefined handlebar template specific to the report and alert with dynamic data)
// - Attachments (depending on the type of alert and the reports associated, will get the report attachments from blob storage and send together)