using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.Alerts;

public class Alert : Entity
{
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public bool IsEnabled { get; private set; }
    public AlertType AlertType { get; private set; }
    public FilterExpression Criteria { get; private set; } = null!;
    public bool UploadToBlob { get; private set; }
    public AlertFrequency Frequency { get; private set; }

    /// <summary>
    /// The minimum time in minutes that must elapse between alert sends.
    /// Used with LastSentUtc to prevent sending alerts too frequently.
    /// </summary>
    public int MinimumMinutesBetweenAlerts { get; private set; }
    public NotificationType NotificationType { get; private set; }
    public List<TimeOnly> RunTimes { get; private set; } = [];
    public AlertDetails AlertDetails { get; private set; } = null!;
    public AuditInfo AuditInfo { get; private set; } = null!;
    public DateTime? LastSentUtc { get; private set; }

    /// <summary>
    /// The scope of the alert (which site is this alert for)
    /// </summary>
    public Guid SiteId { get; private set; }
    public Site.Site Site { get; private set; } = null!;

    // For EF Core
    public Alert() { }

    private Alert(
        string name,
        string description,
        bool isEnabled,
        AlertType alertType,
        FilterExpression criteria,
        bool uploadToBlob,
        int minimumMinutesBetweenAlerts,
        AlertFrequency frequency,
        List<TimeOnly> runTimes,
        AlertDetails alertDetails,
        AuditInfo auditInfo,
        Site.Site site
    )
        : base(Guid.NewGuid())
    {
        Name = name;
        Description = description;
        IsEnabled = isEnabled;
        AlertType = alertType;
        Criteria = criteria;
        UploadToBlob = uploadToBlob;
        MinimumMinutesBetweenAlerts = minimumMinutesBetweenAlerts;
        Frequency = frequency;
        NotificationType = NotificationType.Email;
        RunTimes = runTimes;
        AlertDetails = alertDetails;
        SiteId = site.Id;
        Site = site;
        AuditInfo = auditInfo;
        LastSentUtc = null;
    }

    /// <summary>
    /// Creates a new alert
    /// </summary>
    public static async Task<ErrorOr<Alert>> Create(
        string name,
        string description,
        bool isEnabled,
        AlertType alertType,
        FilterExpression criteria,
        bool uploadToBlob,
        int minimumMinutesBetweenAlerts,
        AlertFrequency frequency,
        List<TimeOnly> runTimes,
        AlertDetails alertDetails,
        Site.Site site,
        Func<List<Guid>, Task<bool>> checkReportExists
    )
    {
        // Get all reports will be using for the alert
        List<Guid> reportIds = alertDetails
            .Attachments.Select(a => a.ReportId)
            .Concat(alertDetails.Body.ReportRenderConfigs.Select(r => r.ReportId))
            .ToList();

        // Make sure the reports included attached to the alert exist
        if (!await checkReportExists(reportIds))
        {
            return ReportErrors.NotFound;
        }

        // Create audit info
        AuditInfo auditInfo = new(
            CreatedBy: null,
            DateCreated: DateTime.Now,
            ModifiedBy: null,
            DateModified: DateTime.Now
        );

        // Create alert
        return new Alert(
            name,
            description,
            isEnabled,
            alertType,
            criteria,
            uploadToBlob,
            minimumMinutesBetweenAlerts,
            frequency,
            runTimes,
            alertDetails,
            auditInfo,
            site
        );
    }

    /// <summary>
    /// Updates an alert
    /// </summary>
    public async Task<ErrorOr<Updated>> Update(
        string name,
        string description,
        bool isEnabled,
        AlertType alertType,
        FilterExpression criteria,
        bool uploadToBlob,
        int minimumMinutesBetweenAlerts,
        AlertFrequency frequency,
        List<TimeOnly> runTimes,
        AlertDetails alertDetails,
        Site.Site site,
        Func<List<Guid>, Task<bool>> checkReportExists
    )
    {
        // Get all reports will be using for the alert
        List<Guid> reportIds = alertDetails
            .Attachments.Select(a => a.ReportId)
            .Concat(alertDetails.Body.ReportRenderConfigs.Select(r => r.ReportId))
            .Distinct()
            .ToList();

        // Make sure the reports included attached to the alert exist
        if (!await checkReportExists(reportIds))
            return ReportErrors.NotFound;

        // Update alert
        Name = name;
        Description = description;
        IsEnabled = isEnabled;
        AlertType = alertType;
        Criteria = criteria;
        UploadToBlob = uploadToBlob;
        MinimumMinutesBetweenAlerts = minimumMinutesBetweenAlerts;
        Frequency = frequency;
        RunTimes = runTimes ?? [];
        AlertDetails = alertDetails;
        Site = site;
        SiteId = site.Id;
        AuditInfo = new(
            CreatedBy: AuditInfo.CreatedBy,
            DateCreated: AuditInfo.DateCreated,
            ModifiedBy: null,
            DateModified: DateTime.Now
        );

        return Result.Updated;
    }

    /// <summary>
    /// Records that the alert was sent
    /// </summary>
    /// <param name="sentAtUtc">The date and time the alert was sent</param>
    public ErrorOr<Updated> RecordAlertSent(DateTime sentAtUtc)
    {
        // Check the timezone of the sent at time
        if (sentAtUtc.Kind != DateTimeKind.Utc)
        {
            return AlertErrors.InvalidAlertSentTime;
        }

        // Check if the sent at time is before the last sent time
        if (sentAtUtc < LastSentUtc)
        {
            return AlertErrors.InvalidAlertSentTime;
        }

        LastSentUtc = sentAtUtc;
        return Result.Updated;
    }

    /// <summary>
    /// Determines if the alert should be processed
    /// </summary>
    /// <param name="utcTime">The current date and time in UTC</param>
    /// <returns>True if the alert should be processed, false otherwise</returns>
    public bool ShouldProcessAlert(DateTime utcTime)
    {
        // Check if the time limit has elapsed since the last sent time
        if (LastSentUtc is not null)
        {
            // Get the total time between the last sent time and now (in UTC)
            TimeSpan elapsedTime = utcTime - LastSentUtc.Value;

            // Do not send the alert if the minimum time has not elapsed
            if (elapsedTime.TotalMinutes < MinimumMinutesBetweenAlerts)
            {
                return false;
            }
        }

        // TODO - Check if the criteria is met

        // Check the frequency of the alert
        if (Frequency == AlertFrequency.None)
            return false;

        DateTime centralTime = TimeZoneInfo.ConvertTimeFromUtc(
            utcTime,
            TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time")
        );

        // TODO - need to make sure to also process alert if it is missed its run time
        if (Frequency == AlertFrequency.Daily)
        {
            return RunTimes.Any(t => t.Hour == centralTime.Hour && t.Minute == centralTime.Minute);
        }

        if (Frequency == AlertFrequency.Weekly)
        {
            return RunTimes.Any(t =>
                t.Hour == centralTime.Hour
                && t.Minute == centralTime.Minute
                && centralTime.DayOfWeek == DayOfWeek.Monday
            );
        }

        if (Frequency == AlertFrequency.Monthly)
        {
            return RunTimes.Any(t =>
                t.Hour == centralTime.Hour && t.Minute == centralTime.Minute && centralTime.Day == 1
            );
        }

        if (Frequency == AlertFrequency.Yearly)
        {
            return RunTimes.Any(t =>
                t.Hour == centralTime.Hour
                && t.Minute == centralTime.Minute
                && centralTime.Day == 1
                && centralTime.Month == 1
            );
        }

        return true;
    }
}
