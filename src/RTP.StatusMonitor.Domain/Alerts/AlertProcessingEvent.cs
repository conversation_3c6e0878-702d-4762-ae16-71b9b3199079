using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Domain.Alerts;

/// <summary>
/// Create an AlertProcessingEvent.
/// </summary>
/// <param name="alertId">The ID of the alert to process.</param>
/// <param name="createdBy">The user who triggered the alert processing.</param>
/// <param name="sendTo">
/// The email address to send the alert to.
/// If null, the alert will be sent to default recipients.
/// </param>
public class AlertProcessingEvent(
    Guid alertId,
    string createdBy,
    EmailAddress? sendTo = null) : IntegrationEvent(createdBy, nameof(AlertProcessingEvent))
{
    public Guid AlertId { get; } = alertId;

    /// <summary>
    /// The email address to send the alert to.
    /// If null, the alert will be sent to default recipients.
    /// </summary>
    public EmailAddress? SendTo { get; init; } = sendTo;
}

