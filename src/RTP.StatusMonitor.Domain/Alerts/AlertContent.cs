using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Domain.Alerts;

public abstract record AlertContent(Guid AlertContentId);
public record AlertNoteContent(Guid AlertContentId, string Note)
    : <PERSON><PERSON><PERSON>ontent(AlertContentId);
public record AlertContactContent(Guid AlertContentId, string Contact)
    : <PERSON>ert<PERSON>ontent(AlertContentId);
public record AlertReportContent(Guid AlertContentId, List<Guid> Reports)
    : AlertContent(AlertContentId);
public record AlertEquipmentContent(
    Guid AlertContentId,
    bool IncludeAvailability,
    bool IncludeConstraint,
    int NumberOfEventsToInclude) : <PERSON><PERSON><PERSON>ontent(AlertContentId);

public enum ForecastInterval { Daily, Hourly }

public record AlertWeatherContent(
    Guid AlertContentId,
    Guid SiteId,
    WeatherService WeatherService,
    ForecastInterval ForecastInterval,
    int NumberOfDaysForecast) : <PERSON>ert<PERSON>ontent(AlertContentId);

public record AlertSqlContent(Guid AlertContentId, string Query)
    : AlertContent(AlertContentId);
