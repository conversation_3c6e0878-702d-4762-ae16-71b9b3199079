using ErrorOr;

namespace RTP.StatusMonitor.Domain.Alerts;

public static class AlertErrors
{
    public static Error NotFound => Error.NotFound(
        code: "Alert.NotFound",
        description: "The alert was not found");
    public static Error CannotProcessDisabledAlert => Error.Conflict(
        code: "Alert.CannotProcessDisabledAlert",
        description: "Cannot process and send an alert that is disabled");
    public static Error InvalidAlertSentTime => Error.Conflict(
        code: "Alert.InvalidAlertSentTime",
        description: "The alert sent time is either not in UTC or before the last sent time");

    public static Error InvalidTriggerType => Error.Validation(
        code: "Alert.InvalidTriggerType",
        description: "Unknown trigger type for alert");

}
