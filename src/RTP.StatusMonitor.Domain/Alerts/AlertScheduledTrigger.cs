namespace RTP.StatusMonitor.Domain.Alerts;

public abstract record ScheduleTrigger(int ExpirationTimeInMinutes, List<TimeOnly> RunTimes)
    : AlertTrigger(ExpirationTimeInMinutes)
{
    public abstract bool IsScheduledToRun(DateTime currentTime);
}

public record DailyFrequency : ScheduleTrigger
{
    public DailyFrequency(int expirationTimeInMinutes, List<TimeOnly> runTimes)
        : base(expirationTimeInMinutes, runTimes) { }

    /// <summary>
    /// Daily scheduled alert will run at specified time
    /// </summary>
    public override bool IsScheduledToRun(DateTime currentTime)
    {
        TimeOnly currentTimeOnly = TimeOnly.FromDateTime(currentTime);

        return RunTimes.Any(runTime =>
            runTime.Hour == currentTimeOnly.Hour && runTime.Minute == currentTimeOnly.Minute
        );
    }
}

/// <summary>
/// Which day of the week should the alert be run
/// </summary>
public record WeeklyFrequency : ScheduleTrigger
{
    public List<DayOfWeek> DaysOfWeeks { get; private set; }

    public WeeklyFrequency(
        int expirationTimeInMinutes,
        List<TimeOnly> runTimes,
        List<DayOfWeek> dayOfWeeks
    )
        : base(expirationTimeInMinutes, runTimes) => DaysOfWeeks = dayOfWeeks;

    /// <summary>
    /// Weekly scheduled alert will run on the scheduled days of the week (Mon, Tues, etc...) and at specified run times
    /// </summary>
    public override bool IsScheduledToRun(DateTime currentTime) =>
        DaysOfWeeks.Contains(currentTime.DayOfWeek)
        && RunTimes.Any(runTime =>
        {
            TimeOnly currentTimeOnly = TimeOnly.FromDateTime(currentTime);
            return runTime.Hour == currentTimeOnly.Hour && runTime.Minute == currentTimeOnly.Minute;
        });
}

public record MonthlyFrequency : ScheduleTrigger
{
    /// <summary>
    /// Which day of the month should the alert be run (1-31)
    /// </summary>
    public List<int> DaysOfMonth { get; private set; } = [];

    public MonthlyFrequency(
        int expirationTimeInMinutes,
        List<TimeOnly> runTimes,
        List<int> daysOfMonth
    )
        : base(expirationTimeInMinutes, runTimes) => DaysOfMonth = daysOfMonth;

    /// <summary>
    /// Monthly scheduled alert will run on the specified days of the month (1-31) and at specified run times
    /// </summary>
    public override bool IsScheduledToRun(DateTime currentTime) =>
        DaysOfMonth.Contains(currentTime.Day)
        && RunTimes.Any(runTime =>
        {
            TimeOnly currentTimeOnly = TimeOnly.FromDateTime(currentTime);
            return runTime.Hour == currentTimeOnly.Hour && runTime.Minute == currentTimeOnly.Minute;
        });
}

/// <summary>
/// Which day of the year should the alert be run (1-365)
/// </summary>
public record YearlyFrequency : ScheduleTrigger
{
    public List<int> DaysOfYear { get; private set; } = [];

    public YearlyFrequency(
        int expirationTimeInMinutes,
        List<TimeOnly> runTimes,
        List<int> daysOfYear
    )
        : base(expirationTimeInMinutes, runTimes) => DaysOfYear = daysOfYear;

    /// <summary>
    /// Yearly scheduled alert will run on the specified days of the year (1-365) and at specified run times
    /// </summary>
    public override bool IsScheduledToRun(DateTime currentTime) =>
        DaysOfYear.Contains(currentTime.DayOfYear)
        && RunTimes.Any(runTime =>
        {
            TimeOnly currentTimeOnly = TimeOnly.FromDateTime(currentTime);
            return runTime.Hour == currentTimeOnly.Hour && runTime.Minute == currentTimeOnly.Minute;
        });
}
