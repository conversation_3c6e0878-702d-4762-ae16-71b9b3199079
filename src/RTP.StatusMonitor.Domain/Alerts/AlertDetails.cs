namespace RTP.StatusMonitor.Domain.Alerts;

public record AlertDetails(
    string Title,
    List<string> Recipients,
    AlertBody Body,
    List<AlertAttachment> Attachments);

/// <summary>
/// An attachment to an alert (e.g. a report).
/// </summary>
/// <param name="ReportId">The ID of the report to attach.</param>
public record AlertAttachment(Guid ReportId);

// Dynamic parameters (also used for sections of report body - notes, contact info, etc.)
public record AlertBody(
    Dictionary<string, object> Parameters,
    List<ReportRenderConfig> ReportRenderConfigs);

/// <summary>
/// Configuration for rendering a report in an alert.
/// </summary>
/// <param name="ReportId">The ID of the report to render.</param>
/// <param name="Position">The position of the report in the alert.</param>
public record ReportRenderConfig(Guid ReportId, int Position);
