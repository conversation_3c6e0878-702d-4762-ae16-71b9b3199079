using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.Alerts;

public record CriteriaTrigger(int ExpirationTimeInMinutes, List<AlertCriteria> Criterias, CombinationRule CombinationRule)
    : AlertTrigger(ExpirationTimeInMinutes)
{
    public bool IsTriggerConditionMet()
    {
        // todo - need to implement this
        return false;
    }
}

public record AlertCriteria(Guid AlertCriteriaId, Guid BlockId, FilterExpression Filter);

