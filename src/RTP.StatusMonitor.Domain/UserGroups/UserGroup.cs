using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.Domain.UserGroups;

public class UserGroup
{
    public Guid UserGroupId { get; private set; }
    public string Name { get; private set; } = null!;
    public string Description { get; private set; } = string.Empty;
    public DateTime DateCreated { get; private set; }
    public DateTime DateModified { get; private set; }
    private readonly List<GroupPermission> _groupPermissions = [];
    private readonly List<DisplayViewPermission> _displayViewPermissions = [];
    public IReadOnlyCollection<GroupPermission> GroupPermissions
        => _groupPermissions.AsReadOnly();
    public IReadOnlyCollection<DisplayViewPermission> DisplayViewPermissions
        => _displayViewPermissions.AsReadOnly();

    // EF Core
    private UserGroup() { }

    public UserGroup(Guid groupId, string name, string description)
    {
        UserGroupId = groupId;
        Name = name;
        Description = description;

        DateCreated = DateTime.UtcNow;
        DateModified = DateTime.UtcNow;
    }

    public void UpdateUserGroup(
        string name,
        string description,
        List<Site.Site> sitesAllowedAccess)
    {
        Name = name;
        Description = description;

        // Remove all permissions
        _groupPermissions.Clear();

        // Add new permissions
        sitesAllowedAccess.ForEach(AddPermissionToSite);

        DateModified = DateTime.UtcNow;
    }

    /// <summary>
    /// Add a permission to a site
    /// </summary>
    /// <param name="site">The site to add the permission to</param>
    public void AddPermissionToSite(Site.Site site)
    {
        // Add logic to check if permission already exists, etc.
        GroupPermission permission = new(this, site);

        _groupPermissions.Add(permission);

        DateModified = DateTime.UtcNow;
    }

    /// <summary>
    /// Add a permission to a display
    /// </summary>
    /// <param name="display">The display to add the permission to</param>
    public void AddPermissionToDisplay(Display display, bool isDefault)
    {
        DisplayViewPermission permission = new()
        {
            DisplayId = display.Id,
            IsDefault = isDefault
        };

        _displayViewPermissions.Add(permission);

        DateModified = DateTime.UtcNow;
    }
}
