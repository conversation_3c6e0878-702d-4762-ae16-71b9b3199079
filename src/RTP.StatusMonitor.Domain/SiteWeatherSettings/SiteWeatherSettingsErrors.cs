using ErrorOr;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public static class SiteWeatherSettingsErrors
{
    public static Error NotFound => Error.NotFound(
        code: "SiteWeatherSettings.NotFound",
        description: "Site weather settings not found"
    );

    public static Error InvalidBiasLimit => Error.Validation(
        code: "SiteWeatherSettings.InvalidBiasLimit",
        description: "Invalid bias limit. Min value must be less than max value"
    );

    public static Error InvalidForecastDays => Error.Validation(
        code: "SiteWeatherSettings.InvalidForecastDays",
        description: "Forecast days must be greater than 0"
    );

    public static Error InvalidWeatherBiasType => Error.Validation(
        code: "SiteWeatherSettings.InvalidWeatherBiasType",
        description: "Weather bias must be Temp, RH, Pressure, or CIT"
    );

    public static Error InvalidWeatherTagSource => Error.Validation(
        code: "SiteWeatherSettings.InvalidWeatherTagSource",
        description: "Weather tag source must be RTP or Customer"
    );

    public static Error InvalidWeatherService => Error.Validation(
        code: "SiteWeatherSettings.InvalidWeatherService",
        description: "Unknown weather service"
    );
}