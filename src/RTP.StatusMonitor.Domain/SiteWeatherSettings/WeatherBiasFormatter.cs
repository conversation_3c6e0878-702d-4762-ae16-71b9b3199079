namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public static class WeatherBiasFormatter
{
    public static double Format(
        WeatherBiasType biasType,
        double? value)
    => biasType switch
    {
        WeatherBiasType.Temp => value.HasValue ? Math.Round(value.Value, 1) : 0,
        WeatherBiasType.RH => value.HasValue ? Math.Round(value.Value, 0) : 0,
        WeatherBiasType.Press => value.HasValue ? Math.Round(value.Value, 2) : 0,
        WeatherBiasType.CIT => value.HasValue ? Math.Round(value.Value, 1) : 0,
        _ => value.HasValue ? Math.Round(value.Value, 1) : 0
    };
}
