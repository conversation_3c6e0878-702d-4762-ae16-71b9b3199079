namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public record WindEffect
{
    /// <summary>
    /// If wind effect is enabled, then use the expression to evaluate the bias
    /// </summary>
    public bool IsWindEffectsEnabled { get; private set; }
    public string Expression { get; private set; } = string.Empty;

    public WindEffect(bool isWindEffectsEnabled, string expression)
    {
        IsWindEffectsEnabled = isWindEffectsEnabled;
        Expression = expression;
    }
}
