using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public class WeatherBiasSettings : Entity
{
    public Guid UnitId { get; private set; }

    /// <summary>
    /// Weather bias modelling should be applied to this unit or not
    /// </summary>
    public bool IsBiasEnabled { get; private set; }

    /// <summary>
    /// The type of the bias (Temperature, RH, Pressure, CIT)
    /// </summary>
    public WeatherBiasType BiasType { get; private set; }

    /// <summary>
    /// The source of the tags (RTP or customer)
    /// </summary>
    public WeatherTagSource TagSource { get; private set; }

    /// <summary>
    /// This is the weather service to bias against
    /// </summary>
    public WeatherService BiasServiceSource { get; private set; }

    /// <summary>
    /// The limit of the bias
    /// If the bias falls outside the limit, then it will be overridden by the limit
    /// </summary>
    public BiasLimit BiasLimit { get; private set; } = null!;

    /// <summary>
    /// The settings for the wind effect.
    /// If it is enabled, then use its expression to evaluate the bias
    /// (only applicable for Temperature variables)
    /// </summary>
    public WindEffect WindEffect { get; set; } = null!;

    public Guid SiteWeatherSettingsId { get; private set; }

    // For EF Core
    private WeatherBiasSettings() { }

    private WeatherBiasSettings(
        Guid id,
        Guid unitId,
        bool isBiasEnabled,
        WeatherBiasType weatherBiasType,
        WeatherTagSource tagSource,
        WeatherService biasServiceSource,
        BiasLimit biasLimit,
        WindEffect windEffect,
        Guid siteWeatherSettingsId
    )
        : base(id)
    {
        UnitId = unitId;
        IsBiasEnabled = isBiasEnabled;
        BiasType = weatherBiasType;
        TagSource = tagSource;
        BiasServiceSource = biasServiceSource;
        BiasLimit = biasLimit;
        WindEffect = windEffect;
        SiteWeatherSettingsId = siteWeatherSettingsId;
    }

    public static ErrorOr<WeatherBiasSettings> Create(
        Guid id,
        Guid unitId,
        bool isBiasEnabled,
        string weatherBiasType,
        string tagSource,
        string biasServiceSource,
        BiasLimit biasLimit,
        WindEffect windEffect,
        Guid siteWeatherSettingsId
    )
    {
        string[] validWeatherBiasTypes = Enum.GetNames(typeof(WeatherBiasType));
        if (!validWeatherBiasTypes.Contains(weatherBiasType))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherBiasType;
        }

        string[] validWeatherTagSources = Enum.GetNames(typeof(WeatherTagSource));
        if (!validWeatherTagSources.Contains(tagSource))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherTagSource;
        }

        string[] validWeatherServiceSources = Enum.GetNames(typeof(WeatherService));
        if (!validWeatherServiceSources.Contains(biasServiceSource))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherService;
        }

        return new WeatherBiasSettings(
            id: id,
            unitId: unitId,
            isBiasEnabled: isBiasEnabled,
            weatherBiasType: Enum.Parse<WeatherBiasType>(weatherBiasType),
            tagSource: Enum.Parse<WeatherTagSource>(tagSource),
            biasServiceSource: Enum.Parse<WeatherService>(biasServiceSource),
            biasLimit: biasLimit,
            windEffect: windEffect,
            siteWeatherSettingsId: siteWeatherSettingsId
        );
    }
}
