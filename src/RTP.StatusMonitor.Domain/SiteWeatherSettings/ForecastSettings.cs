using ErrorOr;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public record ForecastSettings
{
    public bool IsForecastEnabled { get; private set; }
    public int ForecastDays { get; private set; }
    public bool IsMetric { get; private set; }
    public WeatherService ForecastServiceSource { get; private set; }

    // Default forecast settings
    public ForecastSettings()
    {
        IsForecastEnabled = false;
        ForecastDays = 15;
        IsMetric = false;
        ForecastServiceSource = WeatherService.IBM;
    }

    private ForecastSettings(
        bool isForecastEnabled,
        int forecastDays,
        bool isMetric,
        WeatherService forecastServiceSource)
    {
        IsForecastEnabled = isForecastEnabled;
        ForecastDays = forecastDays;
        IsMetric = isMetric;
        ForecastServiceSource = forecastServiceSource;
    }

    public static ErrorOr<ForecastSettings> Create(
        bool isForecastEnabled,
        int forecastDays,
        bool isMetric,
        string forecastServiceSource)
    {
        string[] validWeatherServiceSources = Enum.GetNames(typeof(WeatherService));

        if (!validWeatherServiceSources.Contains(forecastServiceSource.ToString()))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherService;
        }

        if (forecastDays < 0) return SiteWeatherSettingsErrors.InvalidForecastDays;

        return new ForecastSettings(
            isForecastEnabled: isForecastEnabled,
            forecastDays: forecastDays,
            isMetric: isMetric,
            forecastServiceSource: Enum.Parse<WeatherService>(forecastServiceSource));
    }
}