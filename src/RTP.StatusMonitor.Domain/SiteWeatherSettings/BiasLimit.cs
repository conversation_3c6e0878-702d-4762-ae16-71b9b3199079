using ErrorOr;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public record BiasLimit
{
    public double? Min { get; private set; }
    public double? Max { get; private set; }

    private BiasLimit(double? min, double? max)
    {
        Min = min;
        Max = max;
    }

    public static ErrorOr<BiasLimit> Create(double? min, double? max)
    {
        if (min.HasValue && max.HasValue && min > max)
        {
            return SiteWeatherSettingsErrors.InvalidBiasLimit;
        }

        return new BiasLimit(min, max);
    }
}
