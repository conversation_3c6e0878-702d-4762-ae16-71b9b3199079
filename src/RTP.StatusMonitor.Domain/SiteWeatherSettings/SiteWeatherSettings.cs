using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public class SiteWeatherSettings : Entity
{
    /// <summary>
    /// Settings for the forecast service
    /// </summary>
    public ForecastSettings ForecastSettings { get; private set; } = null!;

    /// <summary>
    /// Settings for lightning strikes service
    /// </summary>
    public LightningStrikeSettings LightningStrikeSettings
    { get; private set; } = null!;

    /// <summary>
    /// The settings for the weather bias
    /// The site may or may not have weather bias service
    /// </summary>
    private readonly List<WeatherBiasSettings> _weatherBiasSettings = [];
    public IReadOnlyList<WeatherBiasSettings> WeatherBiasSettings
        => _weatherBiasSettings.AsReadOnly();

    /// <summary>
    /// The settings for the bias tags of each unit in the site
    /// </summary>
    private readonly List<BiasTagSettings> _biasTagSettings = [];
    public IReadOnlyList<BiasTagSettings> BiasTagSettings => _biasTagSettings;

    public Site.Site Site { get; private set; } = null!;
    public Guid SiteId { get; private set; }

    // For EF Core
    private SiteWeatherSettings() { }

    public SiteWeatherSettings(
        Guid id,
        ForecastSettings forecastSettings,
        LightningStrikeSettings lightningStrikeSettings,
        List<WeatherBiasSettings> weatherBiasSettings,
        List<BiasTagSettings> biasTagSettings,
        Site.Site site) : base(id)
    {
        ForecastSettings = forecastSettings;
        LightningStrikeSettings = lightningStrikeSettings;
        _weatherBiasSettings = weatherBiasSettings;
        _biasTagSettings = biasTagSettings;
        Site = site;
        SiteId = site.Id;
    }

    /// <summary>
    /// Update site weather settings with the new settings for forecast/lightning strike/weather bias/etc...
    /// If the bias settings is not found => new bias settings, add it to the list
    /// Otherwise, update the current one
    /// </summary>
    /// 
    /// <param name="updatedForecastSettings">The updated forecast settings</param>
    /// <param name="updatedLightningStrikeSettings">The updated lightning strike settings</param>
    /// <param name="updatedBiasSettings">The updated bias settings</param>
    public void UpdateSiteWeatherSettings(
        ForecastSettings updatedForecastSettings,
        LightningStrikeSettings updatedLightningStrikeSettings,
        IReadOnlyList<WeatherBiasSettings> updatedBiasSettings,
        IReadOnlyList<BiasTagSettings> updatedBiasTagSettings)
    {
        ForecastSettings = updatedForecastSettings;
        LightningStrikeSettings = updatedLightningStrikeSettings;

        // Overwrite the settings
        _weatherBiasSettings.Clear();
        _weatherBiasSettings.AddRange(updatedBiasSettings);

        _biasTagSettings.Clear();
        _biasTagSettings.AddRange(updatedBiasTagSettings);
    }
}
