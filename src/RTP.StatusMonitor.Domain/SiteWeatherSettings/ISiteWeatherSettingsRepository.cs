namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public interface ISiteWeatherSettingsRepository
{
    /// <summary>
    /// Get the weather settings by site id
    /// </summary>
    /// 
    /// <param name="siteId">The site id of the weather settings to get</param>
    /// <param name="ct"></param>
    /// 
    /// <returns>The weather settings for the site (null if not available yet)</returns>
    Task<SiteWeatherSettings?> GetWeatherSettingsBySiteId(
        Guid siteId, CancellationToken ct);

    /// <summary>
    /// Save the site weather settings.
    /// If the settings already exist, then update them.
    /// If the settings do not exist, then create them.
    /// </summary>
    /// 
    /// <param name="siteWeatherSettings">The site weather settings to save</param>
    /// <param name="ct"></param>
    Task SaveSiteWeatherSetttings(
        SiteWeatherSettings siteWeatherSettings, CancellationToken ct);
}
