using ErrorOr;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.Domain.SiteWeatherSettings;

public class BiasTagSettings : Entity
{
    public UnitId UnitId { get; set; }

    /// <summary>
    /// The actual tag name to be used for the bias.
    /// If null, then the bias for such tag does not exist
    /// </summary>
    public string Tag { get; private set; }

    public ComputedExpression ComputedExpression { get; private set; }

    /// <summary>
    /// The type of the bias (Temperature, RH, Pressure, CIT)
    /// </summary>
    public WeatherBiasType BiasType { get; private set; }

    /// <summary>
    /// The source of the tags (RTP or customer)
    /// </summary>
    public WeatherTagSource TagSource { get; private set; }
    public Guid SiteWeatherSettingsId { get; private set; }

    // NOTE - for EF Core
    private BiasTagSettings() { }

    private BiasTagSettings(
        Guid id,
        Guid unitId,
        string tag,
        ComputedExpression expression,
        WeatherBiasType biasType,
        WeatherTagSource tagSource,
        Guid siteWeatherSettingsId
    )
        : base(id)
    {
        UnitId = new(unitId);
        Tag = tag;
        ComputedExpression = expression;
        BiasType = biasType;
        TagSource = tagSource;
        SiteWeatherSettingsId = siteWeatherSettingsId;
    }

    public static ErrorOr<BiasTagSettings> Create(
        Guid id,
        Guid unitId,
        string tag,
        ComputedExpression expression,
        string biasType,
        string tagSource,
        Guid siteWeatherSettingsId
    )
    {
        List<string> validBiasTypes = Enum.GetNames(typeof(WeatherBiasType)).ToList();
        if (!validBiasTypes.Contains(biasType))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherBiasType;
        }

        List<string> validTagSources = Enum.GetNames(typeof(WeatherTagSource)).ToList();
        if (!validTagSources.Contains(tagSource))
        {
            return SiteWeatherSettingsErrors.InvalidWeatherTagSource;
        }

        return new BiasTagSettings(
            id: id,
            unitId: unitId,
            tag: tag,
            expression: expression,
            biasType: Enum.Parse<WeatherBiasType>(biasType),
            tagSource: Enum.Parse<WeatherTagSource>(tagSource),
            siteWeatherSettingsId: siteWeatherSettingsId
        );
    }
}
