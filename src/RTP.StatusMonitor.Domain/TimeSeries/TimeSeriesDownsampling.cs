using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.TimeSeries;

public static class TimeSeriesDownsampling
{
    public static readonly Func<IEnumerable<double>, double> StdDev = values =>
    {
        double mean = values.Average();
        double sumOfSquaresOfDifferences = values.Select(val => (val - mean) * (val - mean)).Sum();
        return Math.Sqrt(sumOfSquaresOfDifferences / values.Count());
    };
    public static readonly Func<IEnumerable<double>, double> Average = values => values.Average();
    public static readonly Func<IEnumerable<double>, double> Sum = values => values.Sum();
    public static readonly Func<IEnumerable<double>, double> Max = values => values.Max();
    public static readonly Func<IEnumerable<double>, double> Min = values => values.Min();
    public static readonly Func<IEnumerable<double>, double> Median = values =>
    {
        var sortedValues = values.OrderBy(v => v).ToArray();
        int n = sortedValues.Length;
        if (n % 2 == 0)
        {
            return (sortedValues[n / 2 - 1] + sortedValues[n / 2]) / 2;
        }
        return sortedValues[n / 2];
    };

    // Aggregate functions for the different types
    public static Dictionary<
        AggregateType,
        Func<IEnumerable<double>, double>
    > AggregateFunctionsLookup = new()
    {
        { AggregateType.Min, Min },
        { AggregateType.Max, Max },
        { AggregateType.Avg, Average },
        { AggregateType.StdDev, StdDev },
    };

    /// <summary>
    /// This method will downsample the data to the specified interval using the specified resampling method
    /// </summary>
    ///
    /// <param name="data"></param>
    /// <param name="interval"></param>
    /// <param name="Func<IEnumerable<double>"></param>
    /// <param name="resamlingMethod"></param>
    /// <returns></returns>
    public static TimeSeriesData Downsample(
        this TimeSeriesData data,
        TimeSeriesResamplingInterval interval,
        Func<IEnumerable<double>, double> resamlingMethod
    )
    {
        // Do not perform any resampling if the interval is None
        if (interval == TimeSeriesResamplingInterval.None)
            return data;

        // First resample the timestamps to the desired interval
        DateTime[] resampledTimestamps = data.Timestamps.Downsample(interval);

        // Then use it to resample to the data
        List<(DateTime, double)> resampledData = data
            .Values.Zip(
                resampledTimestamps,
                (value, time) =>
                    (
                        time,
                        double.TryParse(value.ToString(), out double result) ? result : double.NaN
                    )
            )
            .GroupBy(tv => tv.time)
            .Where(grp => grp.All(tv => !double.IsNaN(tv.Item2)))
            .Select(grp => (grp.Key, resamlingMethod(grp.Select(tv => tv.Item2))))
            .ToList();

        return new TimeSeriesData(
            Tag: data.Tag,
            Values: resampledData.Select(tv => (object)tv.Item2).ToArray(),
            Timestamps: resampledData.Select(tv => tv.Item1).ToArray()
        );
    }

    /// <summary>
    /// Resample the data to the specified interval using multiple resampling methods
    /// </summary>
    ///
    /// <param name="data">The time series data to resample</param>
    /// <param name="interval">The interval to resample the data to</param>
    /// <param name="resamplingMethods">The resampling methods to use</param>
    ///
    /// <returns>A list of tuples with the timestamp and the resampled values based on the resampling methods</returns>
    public static List<(DateTime, double[])> Downsample(
        this TimeSeriesData data,
        TimeSeriesResamplingInterval interval,
        Func<IEnumerable<double>, double>[] resamplingMethods
    )
    {
        try
        {
            // First resample the timestamps to the desired interval
            DateTime[] resampledTimestamps = data.Timestamps.Downsample(interval);

            Dictionary<DateTime, List<double>> dataGroupedByResampledTimestamp = data
                .Values.Zip(resampledTimestamps, (value, time) => (time, value.ToDoubleOrDefault()))
                .GroupBy(tv => tv.time)
                .Where(grp => grp.All(tv => !double.IsNaN(tv.Item2)))
                .ToDictionary(tv => tv.Key, tv => tv.Select(tv => tv.Item2).ToList());

            // Then compute the stats for each resampled timestamp
            List<(DateTime, double[])> stats = [];
            foreach (KeyValuePair<DateTime, List<double>> item in dataGroupedByResampledTimestamp)
            {
                List<double> values = [];
                for (int i = 0; i < resamplingMethods.Length; i++)
                {
                    values.Add(resamplingMethods[i](item.Value));
                }
                stats.Add((item.Key, values.ToArray()));
            }

            return stats;
        }
        catch (Exception ex)
        {
            throw new Exception("Error resampling data", ex);
        }
    }

    /// <summary>
    /// Given a list of date time values, downsample the timestamps to the specified interval
    /// </summary>
    public static DateTime[] Downsample(
        this DateTime[] timestamps,
        TimeSeriesResamplingInterval interval
    ) =>
        interval switch
        {
            // Round timestamps to the start of each minute
            TimeSeriesResamplingInterval.Minute =>
            [
                .. timestamps.Select(dt => new DateTime(
                    dt.Year,
                    dt.Month,
                    dt.Day,
                    dt.Hour,
                    dt.Minute,
                    0
                )),
            ],
            // Round timestamps to the start of each 5 minutes
            TimeSeriesResamplingInterval.FiveMinutes =>
            [
                .. timestamps.Select(dt => new DateTime(
                    dt.Year,
                    dt.Month,
                    dt.Day,
                    dt.Hour,
                    dt.Minute - (dt.Minute % 5),
                    0
                )),
            ],
            // Round timestamps to the start of each 15 minutes
            TimeSeriesResamplingInterval.FifteenMinutes =>
            [
                .. timestamps.Select(dt => new DateTime(
                    dt.Year,
                    dt.Month,
                    dt.Day,
                    dt.Hour,
                    dt.Minute - dt.Minute % 15,
                    0
                )),
            ],
            // Round timestamps to the start of each 30 minutes
            TimeSeriesResamplingInterval.ThirtyMinutes =>
            [
                .. timestamps.Select(dt => new DateTime(
                    dt.Year,
                    dt.Month,
                    dt.Day,
                    dt.Hour,
                    dt.Minute - dt.Minute % 30,
                    0
                )),
            ],
            // Round timestamps to the start of each hour
            TimeSeriesResamplingInterval.Hour =>
            [
                .. timestamps.Select(dt => new DateTime(dt.Year, dt.Month, dt.Day, dt.Hour, 0, 0)),
            ],
            // Round timestamps to the start of each day
            TimeSeriesResamplingInterval.Day =>
            [
                .. timestamps.Select(dt => new DateTime(dt.Year, dt.Month, dt.Day, 0, 0, 0)),
            ],
            // Round timestamps to the start of each week
            TimeSeriesResamplingInterval.Week =>
            [
                .. timestamps
                    .Select(dt => new DateTime(dt.Year, dt.Month, dt.Day, 0, 0, 0))
                    .Select(date => date.AddDays(-(int)date.DayOfWeek)),
            ],
            // Round timestamps to the start of each month (1st day of month)
            TimeSeriesResamplingInterval.Month =>
            [
                .. timestamps.Select(date => new DateTime(date.Year, date.Month, 1)),
            ],
            _ => timestamps, // no operation
        };
}
