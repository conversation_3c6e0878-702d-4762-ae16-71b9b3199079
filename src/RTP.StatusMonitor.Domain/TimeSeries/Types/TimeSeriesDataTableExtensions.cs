using System.Data;

namespace RTP.StatusMonitor.Domain.TimeSeries.Types;

public static class TimeSeriesDataTableExtensions
{
    /// <summary>
    /// Add rows to the time series data table.
    /// </summary>
    /// <param name="dataTable">The time series data table to add rows</param>
    /// <param name="timeSeriesData">The list of time series data to add to the table</param>
    /// <returns>The time series data table with the added rows</returns>
    internal static SortedTimeSeriesTable AddRows(
    this SortedTimeSeriesTable dataTable, List<TimeSeriesData> timeSeriesData)
    {
        // Create a dictionary to hold all the data, keyed by timestamp
        Dictionary<DateTime, Dictionary<string, object>> dataIndexByTimestamp = [];

        // First pass: Collect all data into the dictionary
        foreach (TimeSeriesData tag in timeSeriesData)
        {
            for (int i = 0; i < tag.Timestamps.Length; i++)
            {
                // If the timestamp does not exist in the dictionary, add it
                if (!dataIndexByTimestamp.TryGetValue(tag.Timestamps[i], out var rowData))
                {
                    rowData = [];
                    dataIndexByTimestamp[tag.Timestamps[i]] = rowData;
                }

                // Attempt to parse the values to double
                if (double.TryParse(tag.Values[i]?.ToString(), out double d))
                {
                    rowData[tag.Tag] = d;
                }
                else
                {
                    rowData[tag.Tag] = tag.Values[i];
                }
            }
        }

        // Sort timestamps to ensure ordered insertion
        List<DateTime> timestamps = [.. dataIndexByTimestamp.Keys];

        // Create a list to hold all the new DataRows
        List<DataRow> newRows = [];

        // Create DataRows
        for (int i = 0; i < timestamps.Count; i++)
        {
            DateTime timestamp = timestamps[i];

            // Get the row data for the current timestamp
            Dictionary<string, object> rowData = dataIndexByTimestamp[timestamp];

            // Create a new row with the timestamp
            DataRow row = dataTable.DataTable.NewRow();
            row["Timestamp"] = timestamp;

            // Add the values to the row
            foreach (KeyValuePair<string, object> tagValue in rowData)
            {
                row[tagValue.Key] = tagValue.Value;
            }

            // Add the row to the list
            newRows.Add(row);
        }

        // Bulk insert all new rows
        dataTable.DataTable.BeginLoadData();
        for (int i = 0; i < newRows.Count; i++)
        {
            dataTable.DataTable.Rows.Add(newRows[i]);
        }
        dataTable.DataTable.EndLoadData();

        return new SortedTimeSeriesTable(dataTable.DataTable);
    }

    /// <summary>
    /// Initialize the columns of the time series data table.
    /// </summary>
    /// <param name="timeSeriesData">The list of time series data used to build the table
    /// <returns>The constructed time series data table</returns>
    internal static SortedTimeSeriesTable AddColumns(
        this SortedTimeSeriesTable dataTable,
        IEnumerable<string> columns)
    {
        // Add timestamp column and set it as primary key
        dataTable.DataTable.Columns.Add("Timestamp", typeof(DateTime));
        dataTable.DataTable.PrimaryKey = [dataTable.DataTable.Columns["Timestamp"]!];


        // Add each variable as a column
        dataTable.DataTable.Columns.AddRange(
            [.. columns.Select(x => new DataColumn(x, typeof(object)))]);

        return new SortedTimeSeriesTable(dataTable.DataTable);
    }

    /// <summary>
    /// Sort the data table by Timestamp column in ascending order
    /// </summary>
    /// <param name="dataTable">The time series data table to sort</param>
    /// <returns>The sorted time series data table</returns>
    internal static SortedTimeSeriesTable SortByTimestamp(
        this SortedTimeSeriesTable dataTable)
    {
        dataTable.DataTable.DefaultView.Sort = "Timestamp ASC";
        return new SortedTimeSeriesTable(
            dataTable.DataTable.DefaultView.ToTable());
    }

    /// <summary>
    /// Get the data of a column from the data table
    /// </summary>
    /// <param name="dataTable">The data table from which to extract the data</param>
    /// <param name="tag">The tag of the time series data</param>
    ///  NOTE - This is required in case of computated column then the column name will be "Calculation"
    /// <param name="columnName">The column to extract the data from</param>
    /// <returns>The list of time series data points</returns>
    public static TimeSeriesData GetColumnData(
        this SortedTimeSeriesTable dataTable,
        string columnName)
    {
        // When the column to extract is empty, return an empty time series data
        if (string.IsNullOrEmpty(columnName))
        {
            return new TimeSeriesData(
            Tag: columnName,
            Timestamps: [],
            Values: []);
        }

        // Extract the data from the column
        List<DateTime> timestamps = [];
        List<object> values = [];
        for (int i = 0; i < dataTable.DataTable.Rows.Count; i++)
        {
            if (dataTable.DataTable.Rows[i]["Timestamp"] is DBNull ||
                dataTable.DataTable.Rows[i][columnName] is DBNull)
            { continue; }

            timestamps.Add((DateTime)dataTable.DataTable.Rows[i]["Timestamp"]);
            values.Add(dataTable.DataTable.Rows[i][columnName]);
        }

        // Return the time series data
        return new TimeSeriesData(
            Tag: columnName,
            Timestamps: [.. timestamps],
            Values: [.. values]);
    }

    public static TimeSeriesData GetColumnData(
        this DataTable dataTable,
        string columnName)
    {
        // When the column to extract is empty, return an empty time series data
        if (string.IsNullOrEmpty(columnName))
        {
            return new TimeSeriesData(
            Tag: columnName,
            Timestamps: [],
            Values: []);
        }

        // Extract the data from the column
        List<DateTime> timestamps = [];
        List<object> values = [];
        for (int i = 0; i < dataTable.Rows.Count; i++)
        {
            if (dataTable.Rows[i]["Timestamp"] is DBNull ||
                dataTable.Rows[i][columnName] is DBNull)
            { continue; }

            timestamps.Add((DateTime)dataTable.Rows[i]["Timestamp"]);
            values.Add(dataTable.Rows[i][columnName]);
        }

        // Return the time series data
        return new TimeSeriesData(
            Tag: columnName,
            Timestamps: [.. timestamps],
            Values: [.. values]);
    }
};
