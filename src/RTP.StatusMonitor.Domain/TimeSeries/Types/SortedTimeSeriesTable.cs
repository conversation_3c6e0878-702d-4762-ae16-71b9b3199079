using System.Data;

namespace RTP.StatusMonitor.Domain.TimeSeries.Types;

/// <summary>
/// Time series data table will have a timestamp column and each variable as a column.
/// </summary>
public record SortedTimeSeriesTable
{
    public DataTable DataTable { get; private set; } = new DataTable();
    public SortedTimeSeriesTable(DataTable table) { DataTable = table; }

    /// <summary>
    /// Factory method to create a time series data table from a list of time series data.
    /// Each tag will be a column in the table.
    /// Each row will have the timestamp and the value of the corresponding tag.
    /// </summary>
    /// <param name="data">The list of time series data to create the table</param>
    /// <returns>The time series data table</returns>
    public static SortedTimeSeriesTable Create(
        IEnumerable<TimeSeriesData> data)
    => new SortedTimeSeriesTable(new DataTable())
        .AddColumns([.. data.Select(i => i.Tag).Distinct()])
        .AddRows([.. data])
        .SortByTimestamp();
}
