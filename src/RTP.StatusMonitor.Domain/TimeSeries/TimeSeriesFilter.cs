using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.TimeSeries;

public static class TimeSeriesFilter
{
    /// <summary>
    /// Filter the time series data by the given date range.
    /// </summary>
    /// 
    /// <param name="data">The time series data to filter</param>
    /// <param name="startDate">The start date of the filter</param>
    /// <param name="endDate">The end date of the filter</param>
    /// 
    /// <returns>The filtered time series data</returns>
    public static TimeSeriesData FilterByDateRange(
        this TimeSeriesData data,
        DateTime startDate,
        DateTime endDate)
    {
        // Iterate through each timestamp and make sure it is within the date range
        // If it is, then add it to the filtered list
        List<DateTime> filteredTimestamps = [];
        List<object> filteredValues = [];
        for (int i = 0; i < data.Timestamps.Length; i++)
        {
            // Verify if the timestamp is within the date range
            if (data.Timestamps[i] >= startDate &&
                data.Timestamps[i] <= endDate)
            {
                filteredTimestamps.Add(data.Timestamps[i]);
                filteredValues.Add(data.Values[i]);
            }
        }

        // Return the filtered data
        return new TimeSeriesData(
            data.Tag,
            Timestamps: [.. filteredTimestamps],
            Values: [.. filteredValues]);
    }

    /// <summary>
    /// Given a list of dates to filter, filter the time series data by the timestamp given a filter function
    /// </summary>
    /// <param name="data">The time series data to filter</param>
    /// <param name="datesToFilter">The list of dates to filter</param>
    /// <param name="filterFunc">The filter function to use</param>
    /// <returns>The filtered time series data</returns>
    public static TimeSeriesData FilterByDateCondition(
        this TimeSeriesData data,
        List<DateTime> datesToFilter,
        Func<DateTime, DateTime, bool> filterFunc)
    {

        List<DateTime> filteredTimestamps = [];
        List<object> filteredValues = [];

        // Iterate through each timestamp and make sure it satisfies the filter function
        // If it is, then add it to the filtered list
        for (int i = 0; i < datesToFilter.Count; i++)
        {
            for (int j = 0; j < data.Timestamps.Length; j++)
            {
                if (filterFunc(data.Timestamps[j], datesToFilter[i]))
                {
                    filteredTimestamps.Add(data.Timestamps[j]);
                    filteredValues.Add(data.Values[j]);
                }
            }
        }

        // Return the filtered data
        return new TimeSeriesData(
            data.Tag,
            Timestamps: [.. filteredTimestamps],
            Values: [.. filteredValues]);
    }
}
