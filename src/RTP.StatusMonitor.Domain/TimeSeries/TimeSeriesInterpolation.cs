using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.TimeSeries;

public static class TimeSeriesInterpolation
{
    /// <summary>
    /// Linear interpolation of the time series data
    /// </summary>
    /// <param name="data">The time series data to interpolate</param>
    /// <param name="interval">The resampling interval</param>
    /// <returns>The interpolated time series data</returns>
    public static TimeSeriesData LinearInterpolate(
        this TimeSeriesData data,
        TimeSeriesResamplingInterval interval)
    {
        if (interval == TimeSeriesResamplingInterval.None)
        {
            return data;
        }

        DateTime[] timestamps = data.Timestamps;
        double[] numericValues = [.. data.Values
            .Select(
                v => double.TryParse(v.ToString(), out double result)
                    ? result
                    : double.NaN)];

        List<Tuple<DateTime, double>> interpolatedPoints = [];
        for (int i = 0; i < timestamps.Length - 1; i++)
        {
            // Get the timestamp and value of the 2 points
            long time0 = timestamps[i].Ticks;
            long time1 = timestamps[i + 1].Ticks;

            double value0 = numericValues[i];
            double value1 = numericValues[i + 1];

            // Compute the slope of the line between the 2 points
            double slope = (value1 - value0) / (time1 - time0);

            // Then use the slope to interpolate the values between the 2 points
            long intervalInTicks = GetTicksPerMinsFromInterval(interval);

            for (long t = time0; t <= time1; t += intervalInTicks)
            {
                // Use the slope to compute the value at the current timestamp
                double y = value0 + (slope * (t - time0));

                interpolatedPoints.Add(new Tuple<DateTime, double>(new DateTime(t), y));
            }
        }

        // NOTE This operation convert everything to utc => use offset from utc to convert back to local time
        interpolatedPoints = [.. interpolatedPoints.Select(p => new Tuple<DateTime, double>(p.Item1, p.Item2))];

        // Return the interpolated data
        return new(
            Tag: data.Tag,
            Timestamps: [.. interpolatedPoints.Select(p => p.Item1)],
            Values: [.. interpolatedPoints.Select(p => (object)p.Item2)]);
    }

    /// <summary>
    /// Determine the number of ticks per minute for the given interval
    /// </summary>
    /// <param name="interval">The resampling interval (minute/5 minute/hour/etc...)</param>
    /// <returns>The number of ticks in the given interval</returns>
    private static long GetTicksPerMinsFromInterval(
        TimeSeriesResamplingInterval interval)
        => interval switch
        {
            TimeSeriesResamplingInterval.Minute => TimeSpan.TicksPerMinute,
            TimeSeriesResamplingInterval.FiveMinutes => 5 * TimeSpan.TicksPerMinute,
            TimeSeriesResamplingInterval.FifteenMinutes => 15 * TimeSpan.TicksPerMinute,
            TimeSeriesResamplingInterval.ThirtyMinutes => 30 * TimeSpan.TicksPerMinute,
            TimeSeriesResamplingInterval.Hour => 60 * TimeSpan.TicksPerMinute,
            TimeSeriesResamplingInterval.Day => 24 * 60 * TimeSpan.TicksPerMinute,
            _ => throw new ArgumentOutOfRangeException(nameof(interval), interval, null)
        };
}
