using System.Text.RegularExpressions;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.TimeSeries;


public static class ExpressionParser
{
    const string FILTER_PATTERN = @"\[(.*?)\]";

    /// <summary>
    /// Return unique tags from expression. 
    /// Anything inside the square brackets is considered a variable
    /// </summary>
    /// 
    /// <param name="expression">the expression to extract</param>
    /// 
    /// <returns>a list of distinct variables from the expression</returns>
    /// 
    /// <example>
    /// filter = "[tag1] > 10 AND [tag2] < 20"
    /// return ["tag1", "tag2"]
    /// </example>
    public static List<string> Parse(Expression expression)
    {
        if (string.IsNullOrEmpty(expression.Value)) return new List<string>();

        // Find matching item from the expression using regex 
        MatchCollection? matches = Regex.Matches(expression.Value, FILTER_PATTERN);

        return new HashSet<string>(matches.Select(m => m.Groups[1].Value)).ToList();
    }

    /// <summary>
    /// Return unique tags from expression. 
    /// Anything inside the square brackets is considered a variable and also group the tags
    /// by their prefix (ACT/DA/RT or stats prefix MAX-MIN-AVG etc...)
    /// For example: filter = "[MAX-tag1] > 10 AND [AVG-tag2] < 20 AND [AVG-tag3] > 5"
    /// return 
    /// { 
    ///     "Max" : [tag1, tag3],
    ///     "Avg" : [tag2]
    /// }
    /// </summary>
    /// <param name="expression">the expression to extract</param>
    /// <returns>a list of distinct variables from the expression</returns>
    public static Dictionary<string, List<string>> ParseWithPrefix(
        Expression expression)
    {
        if (string.IsNullOrEmpty(expression.Value)) return [];

        // Find matching item from the expression using regex 
        MatchCollection? matches = Regex.Matches(expression.Value, FILTER_PATTERN);

        // Get all the unique tags from the expression
        List<string> uniqueTags = new HashSet<string>(
            matches
                .Select(m => m.Groups[1].Value))
                .ToList();

        // Group the unique tags by prefix and remove the prefix (MAX, MIN, AVG, etc...)
        return uniqueTags
            .GroupBy(
                // Use the prefix as the key (ACT, DA, etc...)
                x => x.Split('-')[0],
                // join the rest of the elements back together in case the variable name has - in it
                x => x.Split('-')[1..].Aggregate((x, y) => $"{x}-{y}"))
            .ToDictionary(x => x.Key, x => x.ToList());
    }
}
