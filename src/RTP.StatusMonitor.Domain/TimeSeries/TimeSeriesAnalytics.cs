using System.Data;
using ErrorOr;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.Domain.TimeSeries;

public static class TimeSeriesAnalytics
{
    /// <summary>
    /// Perform analytics on time series data table and return the time series data
    /// </summary>
    /// <param name="dataTable">The time series data table to perform analytics</param>
    /// <param name="tag">The tag (column) of the table</param>
    /// <param name="filter">The filter expression (if any)</param>
    /// <param name="calculation">The computation expression (if any)</param>
    /// <returns>The time series data after performing analytics</returns>
    public static TimeSeriesData TryEvaluateExpression(
        this SortedTimeSeriesTable dataTable,
        string tag,
        FilterExpression filter,
        ComputedExpression calculation)
    {
        // If there is no calculation, just extract the data column
        if (string.IsNullOrEmpty(calculation.Value))
        {
            // No calculation or tag => nothing to be done
            if (!dataTable.DataTable.Columns.Contains(tag))
            {
                return new TimeSeriesData(
                    Tag: tag,
                    Timestamps: [],
                    Values: []);
            }

            // If there is a filter, apply the filter and return the filtered data
            return dataTable
                .TryApplyFilter(filter)
                .Match(
                    table => table.GetColumnData(columnName: tag),
                    error => new TimeSeriesData(
                        Tag: tag,
                        Timestamps: [],
                        Values: []));
        }

        // Otherwise, compute the expression and return the time series data of the computed value
        return dataTable
            .TryApplyFilter(filter)
            .Match(
                value => value
                    .TryApplyExpression(calculation)
                    .Match(
                        value => value,
                        error => new TimeSeriesData(
                            Tag: tag,
                            Timestamps: [],
                            Values: [])),
                error => new TimeSeriesData(
                    Tag: tag,
                    Timestamps: [],
                    Values: []));
    }


    /// <summary>
    /// Apply the expression on the data table and return the time series data for the computation of the expression
    /// </summary>
    /// <param name="dataTable">The time series data table to apply the expression</param>
    /// <param name="column"></param>
    /// <param name="calculation">The expression to compute</param>
    /// <returns>The time series data after applying the expression</returns>
    public static ErrorOr<TimeSeriesData> TryApplyExpression(
        this SortedTimeSeriesTable dataTable,
        ComputedExpression computedExpression)
    {
        try
        {
            SortedTimeSeriesTable newDataTable = new(dataTable.DataTable);

            // Add a placeholder column to hold the computed value of the expression
            newDataTable.DataTable.Columns.Add(
                columnName: "Calculation",
                type: typeof(object),
                expression: computedExpression.Value);

            // Then get the data of the "Calculation" column for the filtered row
            TimeSeriesData timeSeriesData = newDataTable
                .GetColumnData(columnName: "Calculation");

            // Remove the calculation column once done
            newDataTable.DataTable.Columns.Remove("Calculation");

            return timeSeriesData;
        }
        catch
        { return TimeSeriesDataTableErrors.InvalidExpression; }
    }

    /// <summary>
    /// Apply filter on the data table to filter out the rows
    /// and return the filtered time series data
    /// </summary>
    /// <param name="dataTable">The time series data table</param>
    /// <param name="filter">The filter expression</param>
    /// <returns>The filtered time series data</returns>
    public static ErrorOr<SortedTimeSeriesTable> TryApplyFilter(
        this SortedTimeSeriesTable dataTable,
        FilterExpression filter)
    {
        try
        {
            // If the filter is empty, return the data table as is
            if (string.IsNullOrEmpty(filter.Value))
                return dataTable;

            DataRow[] filteredRows = dataTable
                .DataTable
                .Select(filterExpression: filter.Value);

            // NOTE - CopyToDataTable only works if rows are not empty
            return filteredRows.Length == 0
                ? new SortedTimeSeriesTable(dataTable.DataTable.Clone())
                .SortByTimestamp()
                : new SortedTimeSeriesTable(filteredRows.CopyToDataTable())
                    .SortByTimestamp();
        }
        catch
        { return TimeSeriesDataTableErrors.InvalidExpression; }
    }
}
