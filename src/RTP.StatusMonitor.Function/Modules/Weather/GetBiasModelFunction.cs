using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherBiasModel;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public record GetWeatherBiasModelRequest(
    ForecastRequest ForecastSettings,
    List<WeatherBiasRequest> WeatherBiasSettings);

public record ForecastRequest(
    int ForecastDays,
    string ForecastServiceSource);

public record WeatherBiasRequest(
    Guid Id,
    Guid UnitId,
    bool IsBiasEnabled,
    string BiasType,
    string TagSource,
    string BiasServiceSource,
    double? Min,
    double? Max,
    bool IsWindEffectEnabled,
    string Expression);

public class GetBiasModelFunction(ILogger<GetBiasModelFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetBiasModelFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetBiasModel))]
    public async Task<IActionResult> GetBiasModel(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "unit/{unitId}/forecast")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body)
                            .ReadToEndAsync();

                        GetWeatherBiasModelRequest? request = JsonConvert
                            .DeserializeObject<GetWeatherBiasModelRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new GetBiasModelCommand(
                                UnitId: unitId,
                                WeatherSettings: new WeatherSettingsCommand(
                                    ForecastSettings: new ForecastSettingsCommand(
                                        IsForecastEnabled: true,
                                        ForecastDays: request.ForecastSettings.ForecastDays,
                                        ForecastServiceSource: request.ForecastSettings.ForecastServiceSource),
                                    WeatherBiasSettings: request.WeatherBiasSettings.Select(wbs => new WeatherBiasSettingsCommand(
                                        Id: wbs.Id,
                                        UnitId: wbs.UnitId,
                                        IsBiasEnabled: wbs.IsBiasEnabled,
                                        BiasType: wbs.BiasType,
                                        TagSource: wbs.TagSource,
                                        BiasServiceSource: wbs.BiasServiceSource,
                                        Min: wbs.Min,
                                        Max: wbs.Max,
                                        IsWindEffectEnabled: wbs.IsWindEffectEnabled,
                                        Expression: wbs.Expression)).ToList())), ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get forecast data and bias for unit. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

