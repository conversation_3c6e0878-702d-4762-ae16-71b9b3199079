using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherBias;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public record WeatherBiasSettingsRequest(
    string BiasServiceSource,
    string TagSource,
    string BiasType
);

public class GetWeatherBiasMinMaxFunction(
    ILogger<GetWeatherBiasMinMaxFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<GetWeatherBiasMinMaxFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetWeatherBiasMinMax))]
    public async Task<IActionResult> GetWeatherBiasMinMax(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "unit/{unitId}/weather/bias"
        )]
            HttpRequest req,
        Guid unitId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        List<WeatherBiasSettingsRequest>? biasSettings =
                            JsonConvert.DeserializeObject<List<WeatherBiasSettingsRequest>>(
                                requestBody
                            );

                        if (biasSettings is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(
                                new GetWeatherBiasMinMaxCommand(
                                    UnitId: unitId,
                                    WeatherBiasSettings: biasSettings
                                        .Select(bs => new WeatherBiasSettingsCommand(
                                            BiasServiceSource: Enum.Parse<WeatherService>(
                                                bs.BiasServiceSource
                                            ),
                                            TagSource: Enum.Parse<WeatherTagSource>(bs.TagSource),
                                            BiasType: Enum.Parse<WeatherBiasType>(bs.BiasType)
                                        ))
                                        .ToList()
                                ),
                                ct
                            )
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()
                            );
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error while getting weather bias min/max. Error: {message}",
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }
}
