using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetForecastByLatLng;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetForecastByLatLngFunction(ILogger<GetForecastByLatLngFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetForecastByLatLngFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;
    [Function(nameof(GetForecastByLatLng))]
    public async Task<IActionResult> GetForecastByLatLng(
        [HttpTrigger(
        authLevel: AuthorizationLevel.Anonymous,
        methods: "get",
        Route = "weather/forecast")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetForecastByLatLngQuery
                        (
                            Latitude: double.Parse(req.Query["latitude"]!),
                            Longitude: double.Parse(req.Query["longitude"]!),
                            Days: int.Parse(req.Query["days"]!)
                        ), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get forecast by lat/lng. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
