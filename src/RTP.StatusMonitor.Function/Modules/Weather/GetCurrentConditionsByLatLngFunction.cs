using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditionByLatLng;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetCurrentConditionByLatLngFunction(
    ILogger<GetCurrentConditionByLatLngFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetCurrentConditionByLatLngFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetCurrentConditionsByLatLng))]
    public async Task<IActionResult> GetCurrentConditionsByLatLng(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "weather/current")] HttpRequest req,
            CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetCurrentConditionsByLatLngQuery
                        (
                            Latitude: double.Parse(req.Query["latitude"]!),
                            Longitude: double.Parse(req.Query["longitude"]!),
                            Api: req.Query["api"]!
                        ), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current weather conditions by lat/lng");
            return ex.ToErrorResult();
        }
    }
}
