using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeather;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Modules.Weather.Requests;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class SaveSiteWeatherSettingsFunction(
    ILogger<SaveSiteWeatherSettingsFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<SaveSiteWeatherSettingsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(SaveSiteWeatherSettings))]
    public async Task<IActionResult> SaveSiteWeatherSettings(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "site/{siteId}/weather/settings"
        )]
            HttpRequest req,
        Guid siteId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        SaveSiteWeatherRequest? request =
                            JsonConvert.DeserializeObject<SaveSiteWeatherRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(
                                new SaveSiteWeatherCommand(
                                    SiteWeatherSettingId: request.Id,
                                    SiteId: siteId,
                                    ForecastSettings: new(
                                        IsForecastEnabled: request
                                            .ForecastSettings
                                            .IsForecastEnabled,
                                        ForecastDays: request.ForecastSettings.ForecastDays,
                                        IsMetric: request.ForecastSettings.IsMetric,
                                        ForecastServiceSource: request
                                            .ForecastSettings
                                            .ForecastServiceSource
                                    ),
                                    LightningStrikeSettings: new(
                                        IsLightningStrikeEnabled: request
                                            .LightningStrikeSettings
                                            .IsLightningStrikeEnabled,
                                        LightningRadius: request
                                            .LightningStrikeSettings
                                            .LightningRadius
                                    ),
                                    WeatherBiasSettings: request
                                        .WeatherBiasSettings.Select(
                                            wbs => new WeatherBiasSettingsCommand(
                                                Id: wbs.Id,
                                                UnitId: wbs.UnitId,
                                                IsBiasEnabled: wbs.IsBiasEnabled,
                                                BiasType: wbs.BiasType,
                                                TagSource: wbs.TagSource,
                                                BiasServiceSource: wbs.BiasServiceSource,
                                                Min: wbs.Min,
                                                Max: wbs.Max,
                                                IsWindEffectEnabled: wbs.IsWindEffectEnabled,
                                                Expression: wbs.Expression
                                            )
                                        )
                                        .ToList(),
                                    BiasTagSettings: request.BiasTagSettings.ConvertAll(
                                        bts => new BiasTagSettingsCommand(
                                            Id: bts.Id,
                                            UnitId: bts.UnitId,
                                            Tag: bts.Tag,
                                            BiasType: bts.BiasType,
                                            TagSource: bts.TagSource,
                                            Expression: bts.Expression
                                        )
                                    )
                                ),
                                ct
                            )
                            .Match(_ => new NoContentResult(), errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error while configuring site weather. Error: {Message}",
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }
}
