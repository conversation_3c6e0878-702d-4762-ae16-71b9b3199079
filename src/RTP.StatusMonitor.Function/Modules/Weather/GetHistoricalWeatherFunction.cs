using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetHistoricalWeatherFunction(
    ILogger<GetHistoricalWeatherFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetHistoricalWeatherFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function("GetHistoricalWeather")]
    public async Task<IActionResult> GetHistoricalWeather(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "site/weather/historical/{siteId}")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetHistoricalWeatherQuery
                        (
                            SiteId: siteId,
                            StartDate: long.Parse(req.Query["startDate"].ToString()),
                            EndDate: long.Parse(req.Query["endDate"].ToString())
                        ), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get historical weather data. Error {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
