using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetCurrentConditionsBySiteFunction(
    ILogger<GetCurrentConditionsBySiteFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetCurrentConditionsBySiteFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    // FIXME - DEPRECATE THIS CONTROLLER IN FAVOR OF GetCurrentConditionByLatLngController
    [Function(nameof(GetCurrentConditions))]
    public async Task<IActionResult> GetCurrentConditions(
        [HttpTrigger(
        authLevel: AuthorizationLevel.Anonymous,
        methods: "get",
        Route = "sites/weather/current")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetCurrentWeatherConditionsQuery(
                            req.Query["ids"].ToString().Split(',').Select(Guid.Parse).ToList()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current weather conditions. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
