using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetForecastBySite;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetForecastBySiteFunction(ILogger<GetForecastBySiteFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetForecastBySiteFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetForecastBySite))]
    public async Task<IActionResult> GetForecastBySite(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/forecast")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetForecastBySiteQuery(siteId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get forecast for the sites. Error {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
