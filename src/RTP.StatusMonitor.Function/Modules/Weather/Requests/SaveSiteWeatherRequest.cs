namespace RTP.StatusMonitor.Function.Modules.Weather.Requests;

public record SaveSiteWeatherRequest(
    Guid Id,
    ForecastSettingsRequest ForecastSettings,
    LightningStrikeSettingsRequest LightningStrikeSettings,
    List<WeatherBiasSettingsRequest> WeatherBiasSettings,
    List<BiasTagSettingsRequest> BiasTagSettings);

public record ForecastSettingsRequest(
    bool IsForecastEnabled,
    int ForecastDays,
    bool IsMetric,
    string ForecastServiceSource);

public record LightningStrikeSettingsRequest(
    bool IsLightningStrikeEnabled,
    int LightningRadius);

public record WeatherBiasSettingsRequest(
    Guid Id,
    Guid UnitId,
    bool IsBiasEnabled,
    string BiasType,
    string TagSource,
    string BiasServiceSource,
    double? Min,
    double? Max,
    bool IsWindEffectEnabled,
    string Expression);

public record BiasTagSettingsRequest(
    Guid Id,
    Guid UnitId,
    string Tag,
    string BiasType,
    string TagSource,
    string Expression);
