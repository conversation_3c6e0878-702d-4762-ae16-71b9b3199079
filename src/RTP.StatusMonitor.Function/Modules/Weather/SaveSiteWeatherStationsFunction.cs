using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeatherStations;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class SaveSiteWeatherStationsFunction(
    ILogger<SaveSiteWeatherStationsFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<SaveSiteWeatherStationsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(SaveSiteWeatherStations))]
    public async Task<IActionResult> SaveSiteWeatherStations(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "site/{siteId}/weather/stations"
        )]
            HttpRequest req,
        Guid siteId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        List<StationDetailsCommand>? stations = JsonConvert.DeserializeObject<
                            List<StationDetailsCommand>
                        >(requestBody);

                        if (stations is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(
                                new SaveSiteWeatherStationsCommand
                                {
                                    UserGroupsIds = req.GetUserGroups(),
                                    Stations = stations,
                                    SiteId = siteId,
                                },
                                ct
                            )
                            .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error while saving site weather stations. Error: {message}",
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }
}
