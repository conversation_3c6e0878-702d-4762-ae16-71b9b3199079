using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.RunWeatherBiasModel;
using RTP.StatusMonitor.App.Shared.Constants;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class RunWeatherBiasModelTimerFunction(
    IMediator mediator,
    ILogger<RunWeatherBiasModelTimerFunction> logger
)
{
    private readonly IMediator _mediator = mediator;
    private readonly ILogger<RunWeatherBiasModelTimerFunction> _logger = logger;

    [Function("RunWeatherBiasModelTimerFunction")]
    public async Task Run([TimerTrigger("*/30 * * * *")] TimerInfo myTimer)
    {
        try
        {
            if (Environment.GetEnvironmentVariable("AppEnvironment") == AppEnvironment.Development)
                return;

            // Send the request to configure the site weather settings
            await _mediator.Send(new RunWeatherBiasModelCommand());

            _logger.LogInformation("Weather bias model updated at: {time}", DateTime.Now);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running bias model");
        }
    }
}
