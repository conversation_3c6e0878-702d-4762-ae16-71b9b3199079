using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetSiteWeatherSettings;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetSiteWeatherSettingsFunction(
    ILogger<GetSiteWeatherSettingsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetSiteWeatherSettingsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetSiteWeatherSettings))]
    public async Task<IActionResult> GetSiteWeatherSettings(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/weather/settings")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetSiteWeatherSettingsQuery
                        {
                            UserGroupsId = req.GetUserGroups(),
                            SiteId = siteId
                        }, ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get site weather settings. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
