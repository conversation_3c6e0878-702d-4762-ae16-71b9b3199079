using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsBySite;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetWeatherStationsBySiteFunction(
    ILogger<GetWeatherStationsBySiteFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetWeatherStationsBySiteFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetWeatherStationsBySite))]
    public async Task<IActionResult> GetWeatherStationsBySite(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/weather/stations")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetWeatherStationBySiteQuery(
                            SiteId: siteId,
                            UserGroupsId: req.GetUserGroups()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying weather stations. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
