using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class GetWeatherStationsByLatLngFunction(
    ILogger<GetWeatherStationsByLatLngFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetWeatherStationsByLatLngFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetWeatherStationsByLatLng))]
    public async Task<IActionResult> GetWeatherStationsByLatLng(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "weather/stations")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetWeatherStationsByLatLngQuery
                        (
                            Latitude: double.Parse(req.Query["latitude"]!),
                            Longitude: double.Parse(req.Query["longitude"]!),
                            IncludeCurrentCondition: bool.Parse(req.Query["includeCurrentCondition"]!),
                            IncludeForecast: bool.Parse(req.Query["includeForecast"]!)
                        ), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying weather stations. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
