using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Weather.UpdateWeatherBias;
using RTP.StatusMonitor.App.Shared.Constants;

namespace RTP.StatusMonitor.Function.Modules.Weather;

public class UpdateWeatherBiasTimerFunction(
    IMediator mediator,
    ILogger<UpdateWeatherBiasTimerFunction> logger
)
{
    private readonly IMediator _mediator = mediator;
    private readonly ILogger<UpdateWeatherBiasTimerFunction> _logger = logger;

    [Function("UpdateWeatherBiasTimerFunction")]
    public async Task Run([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer)
    {
        try
        {
            if (Environment.GetEnvironmentVariable("AppEnvironment") == AppEnvironment.Development)
                return;

            // Send the request to configure the site weather settings
            await _mediator.Send(new UpdateWeatherBiasCommand());

            _logger.LogInformation("Weather bias updated at: {Time}", DateTime.Now);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running bias model");
        }
    }

    //[Function(nameof(Test))]
    //public async Task Test(
    //    [HttpTrigger(authLevel: AuthorizationLevel.Function, methods: "post", Route = "test")] HttpRequest req,
    //    CancellationToken ct
    //)
    //{
    //    try
    //    {
    //        // Send the request to configure the site weather settings
    //        await _mediator.Send(new UpdateWeatherBiasCommand());

    //        _logger.LogInformation("Weather bias updated at: {Time}", DateTime.Now);
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, "Error running bias model");
    //    }
    //}
}
