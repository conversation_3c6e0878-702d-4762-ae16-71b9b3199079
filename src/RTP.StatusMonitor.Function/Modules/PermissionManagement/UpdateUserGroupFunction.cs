using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Groups.UpdateUserGroup;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.PermissionManagement;

public record UpdateUserGroupRequest(
    string Name,
    string Description,
    List<Guid> SitesAllowedAccess);

public class UpdateUserGroupFunction(
    ILogger<UpdateUserGroupFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateUserGroupFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateUserGroup))]
    public async Task<IActionResult> UpdateUserGroup(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "groups/{userGroupId}")] HttpRequest req,
        Guid userGroupId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateUserGroupRequest? request = JsonSerializer.Deserialize<UpdateUserGroupRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(new UpdateUserGroupCommand(
                                userGroupId,
                                request.Name,
                                request.Description,
                                request.SitesAllowedAccess), ct)
                            .Match(
                                _ => new NoContentResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating user group");
            return ex.ToErrorResult();
        }
    }
}
