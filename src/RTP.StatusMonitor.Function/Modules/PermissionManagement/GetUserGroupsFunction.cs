using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Groups;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.PermissionManagement;

public class GetUserGroupsFunction(
    ILogger<GetUserGroupsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetUserGroupsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetUserGroups))]
    public async Task<IActionResult> GetUserGroups(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "groups")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetUserGroupsQuery(), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user groups. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
