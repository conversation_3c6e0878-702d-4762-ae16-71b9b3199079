using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Groups.DeleteUserGroup;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.PermissionManagement;

public class DeleteUserGroupFunction(
    ILogger<DeleteUserGroupFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteUserGroupFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteUserGroup))]
    public async Task<IActionResult> DeleteUserGroup(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "groups/{userGroupId}")] HttpRequest req,
        Guid userGroupId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new DeleteUserGroupCommand(userGroupId), ct)
                        .Match(
                            _ => new OkResult(),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting user group");
            return ex.ToErrorResult();
        }
    }
}
