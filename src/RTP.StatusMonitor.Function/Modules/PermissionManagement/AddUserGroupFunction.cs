using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Groups.AddUserGroup;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.PermissionManagement;

public record AddUserGroupRequest(
    Guid UserGroupId,
    string Name,
    string Description,
    List<Guid> SitesAllowedAccess
);

public class AddUserGroupFunction(ILogger<AddUserGroupFunction> logger, IMediator mediator)
{
    private readonly ILogger<AddUserGroupFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(AddUserGroup))]
    public async Task<IActionResult> AddUserGroup(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "groups")] HttpRequest req,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        AddUserGroupRequest? request =
                            JsonConvert.DeserializeObject<AddUserGroupRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(
                                new AddUserGroupCommand(
                                    request.UserGroupId,
                                    request.Name,
                                    request.Description,
                                    request.SitesAllowedAccess
                                ),
                                ct
                            )
                            .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while adding user group");
            return ex.ToErrorResult();
        }
    }
}
