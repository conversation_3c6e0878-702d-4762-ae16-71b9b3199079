using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Airsonic;

public class MigrateAirsonicStorageFunction(IMediator mediator)
{
    private readonly IMediator _mediator = mediator;

    [Function(nameof(MigrateAirsonicStorage))]
    public async Task<IActionResult> MigrateAirsonicStorage(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Function,
            methods: "post",
            Route = "unit/{unitId}/airsonic/migrate"
        )]
            HttpRequest req,
        Guid unitId,
        CancellationToken ct
    )
    {
        try
        {
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            List<string> tags = JsonConvert.DeserializeObject<List<string>>(requestBody) ?? [];

            return await _mediator
                .Send(
                    new MigrateAirsonicStorageCommand(
                        UnitId: unitId,
                        Tags: tags,
                        Date: DateOnly.Parse(req.Query["date"]!)
                    ),
                    ct
                )
                .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(MigrateAirsonicStatistics))]
    public async Task<IActionResult> MigrateAirsonicStatistics(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Function,
            methods: "post",
            Route = "unit/{unitId}/airsonic/migrate/statistics"
        )]
            HttpRequest req,
        Guid unitId,
        CancellationToken ct
    )
    {
        try
        {
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            List<string> tags = JsonConvert.DeserializeObject<List<string>>(requestBody) ?? [];

            return await _mediator
                .Send(
                    new MigrateAirsonicStatisticsCommand(
                        UnitId: unitId,
                        Tags: tags,
                        Date: DateOnly.Parse(req.Query["date"]!)
                    ),
                    ct
                )
                .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            return ex.ToErrorResult();
        }
    }
}
