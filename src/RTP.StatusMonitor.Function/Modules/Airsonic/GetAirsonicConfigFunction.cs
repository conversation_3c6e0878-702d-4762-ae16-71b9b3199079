using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicConfig;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Airsonic;

public class GetAirsonicConfigFunction(
    ILogger<GetAirsonicConfigFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<GetAirsonicConfigFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAirsonicConfig))]
    public async Task<IActionResult> GetAirsonicConfig(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "airsonic/config/{unitId}")]
            HttpRequest req,
        Guid unitId
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                        await _mediator
                            .Send(new GetAirsonicConfigQuery(unitId, req.GetUserGroups()))
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()
                            ),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        // When there is an exception
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get airsonic config. Error: {error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
