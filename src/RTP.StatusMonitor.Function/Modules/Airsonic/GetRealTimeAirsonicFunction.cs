using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using ErrorOr;
using RTP.StatusMonitor.App.Modules.Airsonic;
using RTP.StatusMonitor.Function.Modules.Airsonic.Requests;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Dto;

namespace RTP.StatusMonitor.Function.Modules.Airsonic;
public class GetRealtimeAirsonicFunction(
    ILogger<GetRealtimeAirsonicFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetRealtimeAirsonicFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAirsonicLatest))]
    public async Task<IActionResult> GetAirsonicLatest(
            [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "airsonic/latest/{unitId}")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetRealTimeAirsonicDataQuery
                        (
                            UnitId: unitId,
                            UserGroups: req.GetUserGroups()
                        ), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest airsonic snapshot. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(GetAirsonicSnapshot))]
    public async Task<IActionResult> GetAirsonicSnapshot(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "unit/{unitId}/airsonic/latest")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        List<AirsonicSeriesRequest>? seriesRequested = JsonConvert.DeserializeObject<List<AirsonicSeriesRequest>>(requestBody);

                        if (seriesRequested is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new GetRealTimeAirsonicQueryVersion2
                            (seriesRequested.ConvertAll(x => new AirsonicSeries
                                (
                                    Id: x.Id,
                                    Name: x.Name,
                                    Alias: x.Alias,
                                    Calculation: new(x.Calculation),
                                    Filter: new(x.Filter)
                                )))
                            {
                                UnitId = unitId,
                                UserGroupsId = req.GetUserGroups()
                            }, ct)
                                .Match(
                                    value => new OkObjectResult(value),
                                    errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest airsonic snapshot. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
