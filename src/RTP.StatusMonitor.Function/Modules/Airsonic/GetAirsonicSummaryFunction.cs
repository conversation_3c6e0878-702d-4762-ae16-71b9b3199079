using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using ErrorOr;
using RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicSummary;

namespace RTP.StatusMonitor.Function.Modules.Airsonic;
public class GetAirsonicSummaryFunction(
    ILogger<GetAirsonicSummaryFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetAirsonicSummaryFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAirsonicSummary))]
    public async Task<IActionResult> GetAirsonicSummary(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "airsonic/summary")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetAirsonicSummaryQuery(req.GetUserGroups()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                        errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get airsonic summary. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
