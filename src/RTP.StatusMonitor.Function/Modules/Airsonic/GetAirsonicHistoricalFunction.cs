using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using MediatR;
using RTP.StatusMonitor.Domain.TimeSeries;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using ErrorOr;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;
using RTP.StatusMonitor.Function.Modules.Airsonic.Requests;
using RTP.StatusMonitor.App.Modules.Airsonic;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Dto;

namespace RTP.StatusMonitor.Function.Modules.Airsonic;

public class GetAirsonicHistoricalFunction(ILogger<GetAirsonicHistoricalFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetAirsonicHistoricalFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    // NOTE - Still needed for Airsonic on 3D model
    [Function(nameof(GetAirsonicHistorical))]
    public async Task<IActionResult> GetAirsonicHistorical(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "unit/airsonic/historical/")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetHistoricalAirsonicQuery()
                        {
                            UnitId = Guid.Parse(req.Query["id"]!),
                            UserGroups = req.GetUserGroups(),
                            Aliases = [.. req.Query["aliases"].ToString().Split(',')],
                            Filter = req.Query["filter"].ToString().Length == 0 ? "" : req.Query["filter"].ToString(),
                            SkipInterval = req.Query["skipInterval"].ToString().Length == 0 ? 1 : int.Parse(req.Query["skipInterval"]!),
                            StartDate = long.Parse(req.Query["startDate"].ToString()),
                            EndDate = long.Parse(req.Query["endDate"].ToString()),
                        }, ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get historical data for airsonic. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(GetAirsonicHistoricalData))]
    public async Task<IActionResult> GetAirsonicHistoricalData(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "unit/{unitId}/airsonic/historical/v2")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body)
                            .ReadToEndAsync();
                        List<AirsonicSeriesRequest>? seriesRequested = JsonConvert
                               .DeserializeObject<List<AirsonicSeriesRequest>>(requestBody);

                        if (seriesRequested is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new GetAirsonicHistoricalQuery
                                (
                                    Series: seriesRequested.Select(e => new AirsonicSeries(
                                        Id: e.Id,
                                        Name: e.Name,
                                        Alias: e.Alias,
                                        Calculation: new(e.Calculation),
                                        Filter: new(e.Filter))).ToList(),
                                    StartDate: DateTime.SpecifyKind(
                                        DateTime.Parse(req.Query["startTime"]!),
                                        DateTimeKind.Unspecified),
                                    EndDate: DateTime.SpecifyKind(
                                        DateTime.Parse(req.Query["endTime"]!),
                                        DateTimeKind.Unspecified),
                                    Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!)
                                )
                            {
                                UnitId = unitId,
                                UserGroupsId = req.GetUserGroups()
                            }, ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query historical data for airsonic. Error: {message}", ex.Message);

            return ex.ToErrorResult();
        }
    }

    [Function(nameof(GetAirsonicHistoricalInternal))]
    public async Task<IActionResult> GetAirsonicHistoricalInternal(
        [HttpTrigger(
        authLevel: AuthorizationLevel.Function,
        methods: "post",
        Route = "internal/unit/{unitId}/historical/airsonic")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            string requestBody = await new StreamReader(req.Body)
                                        .ReadToEndAsync();
            List<AirsonicSeriesRequest>? seriesRequested = JsonConvert
                   .DeserializeObject<List<AirsonicSeriesRequest>>(requestBody);

            if (seriesRequested is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            return await _mediator
                 .Send(new GetAirsonicHistoricalQuery
                     (
                         Series: seriesRequested.Select(e => new AirsonicSeries(
                             Id: e.Id,
                             Name: e.Name,
                             Alias: e.Alias,
                             Calculation: new(e.Calculation),
                             Filter: new(e.Filter))).ToList(),
                         StartDate: DateTime.SpecifyKind(
                             DateTime.Parse(req.Query["startTime"]!),
                             DateTimeKind.Unspecified),
                         EndDate: DateTime.SpecifyKind(
                             DateTime.Parse(req.Query["endTime"]!),
                             DateTimeKind.Unspecified),
                         Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!)
                     )
                 {
                     UnitId = unitId,
                     IsAuthorizationEnabled = false
                 }, ct)
                 .Match(
                     value => new OkObjectResult(value),
                     errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query historical data for airsonic. Error: {message}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
