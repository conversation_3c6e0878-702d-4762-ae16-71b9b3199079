using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Monitoring.MonitorStatus;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Monitoring;

public class GetStatusFunction(ILogger<GetStatusFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetStatusFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetStatus))]
    public async Task<IActionResult> GetStatus(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "status")] HttpRequest req,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                        await _mediator
                            .Send(new MonitorStatusQuery(), ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()
                            ),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get status");
            return ex.ToErrorResult();
        }
    }
}
