using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Monitoring.GetInputData;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Monitoring;

public class GetInputDataFunction(
    ILogger<GetInputDataFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetInputDataFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetInputData))]
    public async Task<IActionResult> GetInputData(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "unit/{unitId}/input")] HttpRequest req,
        Guid unitId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                            .Send(new GetInputDataQuery(
                                UserGroupsId: req.GetUserGroups(),
                                UnitId: unitId), ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get input data");
            return ex.ToErrorResult();
        }
    }
}
