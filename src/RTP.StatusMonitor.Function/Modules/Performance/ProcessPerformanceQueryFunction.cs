using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Performance.GetPerformanceData;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Performance;

public record PerformanceQuery(string Query);

public class ProcessPerformanceQueryFunction(
    ILogger<ProcessPerformanceQueryFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<ProcessPerformanceQueryFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(ProcessPerformanceQuery))]
    public async Task<IActionResult> ProcessPerformanceQuery(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "post",
            Route = "performance")]
        HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            PerformanceQuery? query = JsonConvert.DeserializeObject<PerformanceQuery>(requestBody);

            if (query is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            return await _mediator
                .Send(new GetPerformanceDataQuery(query.Query), ct)
                .Match(
                    res => new OkObjectResult(res),
                    errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query performance data. Error: {ErrorMessage}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
