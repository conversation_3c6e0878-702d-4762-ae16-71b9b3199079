using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Charts;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Charts;
public record CreateChartRequest(List<ChartRequest> Charts);
public record ChartRequest(
    Guid Id,
    string Name,
    string Notes,
    bool IsDefault,
    string Type,
    object[] ChartStatuses,
    object[] ChartAreas);
public class CreateChartsFunction(ILogger<CreateChartsFunction> logger, IMediator mediator)
{
    private readonly ILogger<CreateChartsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateCharts))]
    public async Task<IActionResult> CreateCharts(
        [HttpTrigger(AuthorizationLevel.Anonymous,"post",
        Route = "site/{siteId}/forecast-chart")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        CreateChartRequest? request = JsonConvert.DeserializeObject<CreateChartRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator.Send(new CreateChartCommand(
                            AppRoles: req.GetUserAppRoles(),
                            SiteId: siteId,
                            Charts: request.Charts
                                .Select(i => new ChartCommand(
                                    Id: i.Id,
                                    Name: i.Name,
                                    Notes: i.Notes,
                                    IsDefault: i.IsDefault,
                                    Type: i.Type,
                                    ChartStatuses: i.ChartStatuses,
                                    ChartAreas: i.ChartAreas))
                                .ToList()), ct)
                            .Match(
                                value => new NoContentResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create charts. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
