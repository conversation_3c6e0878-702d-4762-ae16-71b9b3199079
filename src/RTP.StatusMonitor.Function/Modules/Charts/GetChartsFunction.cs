using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Charts;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Charts;

public class GetChartsFunction(
    ILogger<GetChartsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetChartsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetCharts))]
    public async Task<IActionResult> GetCharts(
        [HttpTrigger(AuthorizationLevel.Anonymous,
        "get",
        Route = "site/{siteId}/forecast-chart")] HttpRequest req,
        Guid siteId, CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetChartsQuery(
                            UserGroupsId: req.GetUserGroups(),
                            SiteId: siteId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get charts. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

