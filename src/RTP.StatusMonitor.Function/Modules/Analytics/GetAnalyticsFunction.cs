using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using MediatR;
using RTP.StatusMonitor.Domain.TimeSeries;
using Microsoft.AspNetCore.Http;
using RTP.StatusMonitor.Function.Shared;
using ErrorOr;
using RTP.StatusMonitor.App.Modules.Analytics;

namespace RTP.StatusMonitor.Function.Modules.Analytics;

public record AnalyticsSeriesRequest(
    Guid Id,
    string Name,
    string Calculation,
    string Filter);

public class GetHistoricalAnalyticsFunction(ILogger<GetHistoricalAnalyticsFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetHistoricalAnalyticsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAnalytics))]
    public async Task<IActionResult> GetAnalytics(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "block/{blockId}/analytics")] HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(async _ =>
                {
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

                    List<AnalyticsSeriesRequest>? seriesRequested = JsonConvert
                        .DeserializeObject<List<AnalyticsSeriesRequest>>(requestBody);

                    if (seriesRequested is null)
                    {
                        return new BadRequestObjectResult("Invalid request");
                    }

                    // Send the request to the appropriate handler to get analytics data
                    return await _mediator
                        .Send(new GetAnalyticsQuery(
                            Series: seriesRequested.ConvertAll(e => new AnalyticsSeries(
                                Id: e.Id,
                                Name: e.Name,
                                Calculation: new(e.Calculation),
                                Filter: new(e.Filter))),
                            StartTime: DateTime.Parse(req.Query["startTime"]!),
                            EndTime: DateTime.Parse(req.Query["endTime"]!),
                            Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!))
                        {
                            BlockId = blockId,
                            UserGroupsId = req.GetUserGroups()
                        }, ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails());
                },
                errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get analytics. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(GetAnalyticsInternal))]
    public async Task<IActionResult> GetAnalyticsInternal(
    [HttpTrigger(
        authLevel: AuthorizationLevel.Function,
        methods: "post",
        Route = "internal/block/{blockId}/historical/multisource")] HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {
            // Get the request body
            string payload = await new StreamReader(req.Body).ReadToEndAsync();

            List<AnalyticsSeriesRequest>? seriesRequested = JsonConvert
                .DeserializeObject<List<AnalyticsSeriesRequest>>(payload);

            if (seriesRequested is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            // Send the request to the appropriate handler to get analytics data
            return await _mediator
                .Send(new GetAnalyticsQuery(
                    Series: seriesRequested
                        .ConvertAll(e => new AnalyticsSeries(
                            Id: e.Id,
                        Name: e.Name,
                        Calculation: new(e.Calculation),
                        Filter: new(e.Filter))),
                    StartTime: DateTime.Parse(req.Query["startTime"]!),
                    EndTime: DateTime.Parse(req.Query["endTime"]!),
                    Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!))
                {
                    IsAuthorizationEnabled = false,
                    BlockId = blockId,
                    UserGroupsId = [],
                }, ct)
                .Match(
                    value => new OkObjectResult(value),
                    errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get analytics. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
