using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentUpdatesLog;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class GetEquipmentUpdatesLogFunction(
    ILogger<GetEquipmentUpdatesLogFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetEquipmentUpdatesLogFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetEquipmentUpdatesLog))]
    public async Task<IActionResult> GetEquipmentUpdatesLog(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/equipment/updates/logs")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetEquipmentUpdatesLogQuery(req.GetUserGroups(), siteId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while retrieving event logs. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

