using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.CancelScheduledUpdates;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class CancelScheduledUpdatesFunction(
    ILogger<CancelScheduledUpdatesFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<CancelScheduledUpdatesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CancelScheduledUpdates))]
    public async Task<IActionResult> CancelScheduledUpdates(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "site/{siteId}/equipment/updates")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin, AppRole.Operator, AppRole.Production])
                .MatchAsync(
                    async _ =>
                    {
                        if (!long.TryParse(req.Query["sequenceNumber"], out long sequenceNumber))
                        {
                            return new BadRequestObjectResult("Invalid sequence number");
                        }

                        return await _mediator
                            .Send(new CancelScheduledUpdatesCommand(sequenceNumber)
                            {
                                UserGroupsId = req.GetUserGroups(),
                                ResourceId = new SiteId(siteId)
                            }, ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling scheduled updates. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

