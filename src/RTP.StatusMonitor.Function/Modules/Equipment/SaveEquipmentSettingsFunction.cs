using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.SaveEquipmentSettings;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class SaveEquipmentSettingsFunction(
    ILogger<SaveEquipmentSettingsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<SaveEquipmentSettingsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(SaveEquipmentGroup))]
    public async Task<IActionResult> SaveEquipmentGroup(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "site/{siteId}/equipment-group")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        List<GroupSettings>? equipmentGroups = JsonConvert
                            .DeserializeObject<List<GroupSettings>>(requestBody);

                        if (equipmentGroups is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(new SaveEquipmentSettingsCommand(
                                UserGroupsId: req.GetUserGroups(),
                                SiteId: siteId,
                                CreatedBy: req.GetUserEmailAddress(),
                                EquipmentGroups: equipmentGroups), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while saving equipment group for site. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
