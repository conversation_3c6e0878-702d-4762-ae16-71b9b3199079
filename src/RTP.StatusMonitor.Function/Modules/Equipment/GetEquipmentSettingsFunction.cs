using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentSettings;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class GetEquipmentSettingsFunction(
    ILogger<GetEquipmentSettingsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetEquipmentSettingsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetEquipmentGroups))]
    public async Task<IActionResult> GetEquipmentGroups(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/equipment-group")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetEquipmentSettingsQuery(
                                req.GetUserGroups(),
                                siteId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while getting equipment group. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
