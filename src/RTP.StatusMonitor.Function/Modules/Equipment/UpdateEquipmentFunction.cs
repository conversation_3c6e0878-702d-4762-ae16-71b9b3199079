using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.UpdateEquipment;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class UpdateEquipmentFunction(
    ILogger<UpdateEquipmentFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateEquipmentFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateEquipment))]
    public async Task<IActionResult> UpdateEquipment(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "site/{siteId}/equipment/update")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin, AppRole.Operator, AppRole.Production])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdatedEquipment? updatedEquipment = JsonConvert
                            .DeserializeObject<UpdatedEquipment>(requestBody);

                        if (updatedEquipment is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(new UpdateEquipmentCommand(
                                UpdatedEquipment: updatedEquipment,
                                ActorEmail: req.GetUserEmailAddress(),
                                CreatedBy: req.GetUserEmailAddress())
                            {
                                UserGroupsId = req.GetUserGroups(),
                                ResourceId = new SiteId(siteId)
                            }, ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating equipment value. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
