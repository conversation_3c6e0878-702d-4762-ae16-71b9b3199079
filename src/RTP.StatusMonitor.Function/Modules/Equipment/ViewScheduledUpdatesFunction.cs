using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.EquipmentSettings.ViewScheduledUpdates;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Equipment;

public class ViewScheduledUpdatesFunction(
    ILogger<ViewScheduledUpdatesFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<ViewScheduledUpdatesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(ViewScheduledUpdates))]
    public async Task<IActionResult> ViewScheduledUpdates(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "site/{siteId}/equipment/updates")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new ViewScheduledUpdatesQuery
                        {
                            UserGroupsId = req.GetUserGroups(),
                            ResourceId = new SiteId(siteId)
                        }, ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving scheduled updates. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
