using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Alerts.GetAlertSummariesBeta;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;
public class GetAlertSummariesFunction(ILogger<GetAlertSummariesFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetAlertSummariesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAlertSummaries))]
    public async Task<IActionResult> GetAlertSummaries(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "alerts")]
        HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                async _ => await _mediator
                    .Send(new GetAlertBetaSummariesQuery(), ct)
                    .Match(
                        res => new OkObjectResult(res),
                        errors => errors.ToProblemDetails()),
                errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alerts. Error: {ErrorMessage}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
