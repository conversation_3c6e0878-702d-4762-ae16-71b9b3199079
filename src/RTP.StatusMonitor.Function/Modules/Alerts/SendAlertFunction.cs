using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Alerts.ProcessAlertBeta;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class SendAlertFunction(ILogger<SendAlertFunction> logger, IMediator mediator)
{
    private readonly ILogger<SendAlertFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(TestAlert))]
    public async Task<IActionResult> TestAlert(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "alerts/{alertId}/test"
        )]
            HttpRequest req,
        Guid alertId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                    async success =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

                        // Get the email address to send if specified
                        TestAlertRequest? request = JsonConvert.DeserializeObject<TestAlertRequest>(
                            requestBody
                        );

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid test alert request");
                        }

                        // Send the request to create appropriate report type
                        return await _mediator
                            .Send(new SendAlertBetaCommand(alertId, request), ct)
                            .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to test alert {AlertId}. Error: {Error}",
                alertId,
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(SendAlert))]
    public async Task<IActionResult> SendAlert(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "alerts/{alertId}/send"
        )]
            HttpRequest req,
        Guid alertId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                    async success =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

                        // Get the email address to send if specified
                        SendAlertRequest? request = JsonConvert.DeserializeObject<SendAlertRequest>(
                            requestBody
                        );

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid send alert request");
                        }

                        // Send the request to create appropriate report type
                        return await _mediator
                            .Send(new SendAlertBetaCommand(alertId, request), ct)
                            .Match(_ => new OkResult(), errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to send alert {AlertId}. Error: {Error}",
                alertId,
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }
}
