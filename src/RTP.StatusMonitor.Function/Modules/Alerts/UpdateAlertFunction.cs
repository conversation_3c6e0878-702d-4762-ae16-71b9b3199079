using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Alerts.UpdateAlertBeta;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Modules.Alerts.Requests;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class UpdateAlertFunction(
    ILogger<UpdateAlertFunction> logger, IMediator mediator)
{
    private readonly ILogger<UpdateAlertFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateAlert))]
    public async Task<IActionResult> UpdateAlert(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            methods: "put",
            Route = "alerts/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            ErrorOr<Success> authResult = await req.AuthenticateRequestAsync([AppRole.Admin]);
            if (authResult.IsError)
            {
                return authResult.Errors.ToProblemDetails();
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            JsonSerializerSettings settings = new();
            settings.Converters.Add(new AlertTriggerJsonConverter());

            settings.Converters.Add(new AlertContentJsonConverter());

            AlertBetaUpdateRequest? request = JsonConvert
                .DeserializeObject<AlertBetaUpdateRequest>(requestBody, settings);

            if (request is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            return await _mediator
                .Send(new UpdateAlertBetaCommand(AlertId: id, UpdateAlert: request), ct)
                .Match(
                    _ => new OkResult(),
                    errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update alert. Error: {ErrorMessage}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

