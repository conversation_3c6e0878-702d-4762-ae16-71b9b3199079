using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Alerts.DeleteAlertBeta;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class DeleteAlertFunction(
    ILogger<DeleteAlertFunction> logger, IMediator mediator)
{
    private readonly ILogger<DeleteAlertFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteAlert))]
    public async Task<IActionResult> DeleteAlert(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "alerts/{alertId}")] HttpRequest req,
        Guid alertId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                     async _ => await _mediator.Send(new DeleteAlertBetaCommand(alertId), ct)
                             .Match(
                                 result => new OkResult(),
                                 errors => errors.ToProblemDetails()),
                     errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete alert. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
