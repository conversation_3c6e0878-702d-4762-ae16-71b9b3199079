using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.Domain.Shared.Constants;
using ErrorOr;
using RTP.StatusMonitor.Function.Shared;
using RTP.StatusMonitor.App.Modules.Alerts.PreviewAlertBeta;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class PreviewAlertFunction(
    ILogger<PreviewAlertFunction> logger, IMediator mediator)
{
    private readonly ILogger<PreviewAlertFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(PreviewAlert))]
    public async Task<IActionResult> PreviewAlert(
        [HttpTrigger(
             authLevel: AuthorizationLevel.Anonymous,
             methods: "post",
             Route = "alerts/{alertId}/preview")] HttpRequest req,
        Guid alertId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                    async success => await _mediator
                        .Send(new PreviewAlertBetaCommand(alertId), ct)
                        .Match(
                            res => new OkObjectResult(res),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to preview alert {AlertId}. Error: {Error}", alertId, ex.Message);
            return ex.ToErrorResult();
        }
    }
}
