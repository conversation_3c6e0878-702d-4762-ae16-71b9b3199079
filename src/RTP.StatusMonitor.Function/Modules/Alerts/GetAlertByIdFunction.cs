using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Alerts;
using RTP.StatusMonitor.App.Modules.Alerts.GetAlertBeta;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;
public class GetAlertByIdFunction(ILogger<GetAlertByIdFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetAlertByIdFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetAlertById))]
    public async Task<IActionResult> GetAlertById(
            [HttpTrigger(
                AuthorizationLevel.Anonymous,
                "get",
                Route = "alerts/{id}")]
            HttpRequest req,
            Guid id,
            CancellationToken ct)
    {
        try
        {
            ErrorOr<Success> authResult = await req.AuthenticateRequestAsync([AppRole.Admin]);
            if (authResult.IsError)
            {
                return authResult.Errors.ToProblemDetails();
            }

            ErrorOr<AlertBetaResponse> result = await _mediator
                .Send(new GetAlertBetaQuery(id), ct);

            return result.Match(
                res => new OkObjectResult(res),
                errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alert by id. Error: {ErrorMessage}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}

