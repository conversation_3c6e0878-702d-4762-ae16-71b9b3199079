using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Modules.Alerts.Requests;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class CreateAlertFunction(ILogger<CreateAlertFunction> logger, IMediator mediator)
{
    private readonly ILogger<CreateAlertFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateAlert))]
    public async Task<IActionResult> CreateAlert(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "alerts")] HttpRequest req,
        CancellationToken ct
    )
    {
        try
        {
            ErrorOr<Success> authResult = await req.AuthenticateRequestAsync([AppRole.Admin]);
            if (authResult.IsError)
            {
                return authResult.Errors.ToProblemDetails();
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            JsonSerializerSettings settings = new();
            settings.Converters.Add(new AlertTriggerJsonConverter());

            settings.Converters.Add(new AlertContentJsonConverter());

            AlertBetaCommand? request = JsonConvert.DeserializeObject<AlertBetaCommand>(
                requestBody,
                settings
            );

            if (request is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            ErrorOr<Unit> result = await _mediator.Send(request, ct);

            return result.Match(_ => new NoContentResult(), errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create alert. Error: {ErrorMessage}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
