using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Alerts.PublishAlert;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class PublishAlertTimerFunction(
    ILogger<PublishAlertTimerFunction> logger,
    IMediator mediator)
{
    private readonly IMediator _mediator = mediator;
    private readonly ILogger<PublishAlertTimerFunction> _logger = logger;
    [Function(nameof(PublishAlertTimerFunction))]
    public async Task Run(
        [TimerTrigger("* * * * *")] TimerInfo myTimer,
        CancellationToken ct)
    {
        if (Environment.GetEnvironmentVariable("AppEnvironment") == "Development")
        {
            return;
        }

        try
        {
            await _mediator.Send(new PublishAlertCommand(), ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish alert processing events. Error: {ex.Message}", ex.Message);
        }
    }
}
