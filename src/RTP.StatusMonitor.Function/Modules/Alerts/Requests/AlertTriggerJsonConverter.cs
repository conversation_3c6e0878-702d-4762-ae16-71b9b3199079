using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;

namespace RTP.StatusMonitor.Function.Modules.Alerts.Requests;

public class AlertTriggerJsonConverter
    : JsonConverter<AlertTriggerCommand>
{
    public override AlertTriggerCommand? ReadJson(
        JsonReader reader,
        Type objectType,
        AlertTriggerCommand? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        // Read the JSON object into a JObject
        JObject jsonObject = JObject.Load(reader);

        // Get the type property
        string? triggerType = jsonObject["type"]?.Value<string>();

        // Create a new serializer without this converter to avoid recursion
        JsonSerializer serializerWithoutConverter = new();
        foreach (JsonConverter converter in serializer.Converters)
        {
            if (converter is not AlertTriggerJsonConverter)
            {
                serializerWithoutConverter.Converters.Add(converter);
            }
        }

        // Based on the type, deserialize to the appropriate concrete class
        return triggerType switch
        {
            AlertTriggerConstants.Criteria => jsonObject.ToObject<CriteriaTriggerCommand>(serializerWithoutConverter),
            AlertTriggerConstants.Daily => jsonObject.ToObject<DailyScheduleTriggerCommand>(serializerWithoutConverter),
            AlertTriggerConstants.Weekly => jsonObject.ToObject<WeeklyScheduleTriggerCommand>(serializerWithoutConverter),
            AlertTriggerConstants.Monthly => jsonObject.ToObject<MonthlyScheduleTriggerCommand>(serializerWithoutConverter),
            AlertTriggerConstants.Yearly => jsonObject.ToObject<YearlyScheduleTriggerCommand>(serializerWithoutConverter),
            _ => throw new JsonSerializationException($"Unknown trigger type: {triggerType}")
        };
    }

    public override void WriteJson(
        JsonWriter writer,
        AlertTriggerCommand? value,
        JsonSerializer serializer)
    {
        if (value is null)
        {
            writer.WriteNull();
            return;
        }

        // Create a new serializer without this converter to avoid recursion
        JsonSerializer serializerWithoutConverter = new();
        foreach (var converter in serializer.Converters)
        {
            if (converter is not AlertTriggerJsonConverter)
            {
                serializerWithoutConverter.Converters.Add(converter);
            }
        }

        // Serialize the object normally, the Type property will be included automatically
        JObject.FromObject(value, serializerWithoutConverter).WriteTo(writer);
    }
}
