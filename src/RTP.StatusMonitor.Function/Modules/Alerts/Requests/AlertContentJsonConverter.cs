using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;

namespace RTP.StatusMonitor.Function.Modules.Alerts.Requests;

public class AlertContentJsonConverter
    : JsonConverter<AlertContentCommand>
{
    public override AlertContentCommand? ReadJson(
        JsonReader reader,
        Type objectType,
        AlertContentCommand? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        // Read the JSON object into a JObject
        JObject jsonObject = JObject.Load(reader);

        // Get the type property
        string? contentType = jsonObject["type"]?.Value<string>();

        // Create a new serializer without this converter to avoid recursion
        JsonSerializer serializerWithoutConverter = new();
        foreach (JsonConverter converter in serializer.Converters)
        {
            if (converter is not AlertContentJsonConverter)
            {
                serializerWithoutConverter.Converters.Add(converter);
            }
        }

        // Based on the type, deserialize to the appropriate concrete class
        return contentType switch
        {
            AlertContentConstants.Note => jsonObject.ToObject<AlertNoteContentCommand>(serializerWithoutConverter),
            AlertContentConstants.Contact => jsonObject.ToObject<AlertContactContentCommand>(serializerWithoutConverter),
            AlertContentConstants.Report => jsonObject.ToObject<AlertReportContentCommand>(serializerWithoutConverter),
            AlertContentConstants.Equipment => jsonObject.ToObject<AlertEquipmentContentCommand>(serializerWithoutConverter),
            AlertContentConstants.Weather => jsonObject.ToObject<AlertWeatherContentCommand>(serializerWithoutConverter),
            AlertContentConstants.SqlQuery => jsonObject.ToObject<AlertSqlContentCommand>(serializerWithoutConverter),
            _ => throw new JsonSerializationException($"Unknown trigger type: {contentType}")
        };
    }

    public override void WriteJson(
        JsonWriter writer,
        AlertContentCommand? value,
        JsonSerializer serializer)
    {
        if (value is null)
        {
            writer.WriteNull();
            return;
        }

        // Create a new serializer without this converter to avoid recursion
        JsonSerializer serializerWithoutConverter = new();
        foreach (var converter in serializer.Converters)
        {
            if (converter is not AlertContentJsonConverter)
            {
                serializerWithoutConverter.Converters.Add(converter);
            }
        }

        // Serialize the object normally, the Type property will be included automatically
        JObject.FromObject(value, serializerWithoutConverter).WriteTo(writer);
    }
}

