using Azure.Messaging.ServiceBus;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RTP.StatusMonitor.App.Modules.Alerts.ProcessAlertBeta;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.Function.Modules.Alerts;

public class ProcessPublishedAlertBetaServiceBusFunction(
    IMediator mediator,
    IOptions<AzureServiceBusOptions> serviceBusOptions,
    ILogger<ProcessPublishedAlertBetaServiceBusFunction> logger
)
{
    private readonly IMediator _mediator = mediator;
    public const string ALERT_PROCESSING_QUEUE = "alert-processing-queue-beta";
    private readonly AzureServiceBusOptions _serviceBusOptions = serviceBusOptions.Value;
    private readonly ILogger<ProcessPublishedAlertBetaServiceBusFunction> _logger = logger;

    [Function(nameof(ProcessPublishedAlertBeta))]
    public async Task ProcessPublishedAlertBeta(
        [ServiceBusTrigger(
            ALERT_PROCESSING_QUEUE,
            Connection = "ServiceBusConfig:ConnectionString",
            AutoCompleteMessages = false
        )]
            ServiceBusReceivedMessage message,
        CancellationToken ct
    )
    {
        if (Environment.GetEnvironmentVariable("AppEnvironment") == "Development")
        {
            return;
        }

        ServiceBusReceiver receiver = new ServiceBusClient(
            _serviceBusOptions.ConnectionString
        ).CreateReceiver(ALERT_PROCESSING_QUEUE);

        try
        {
            AlertProcessingEvent? alertProcessingEvent =
                JsonConvert.DeserializeObject<AlertProcessingEvent>(
                    message.Body.ToString(),
                    new JsonSerializerSettings
                    {
                        ContractResolver = new CamelCasePropertyNamesContractResolver(),
                        Formatting = Formatting.Indented,
                    }
                );

            if (alertProcessingEvent is null)
            {
                _logger.LogError(
                    "Invalid alert processing event, {message}",
                    message.Body.ToString()
                );
                await receiver.AbandonMessageAsync(message);
            }
            else
            {
                await _mediator.Send(
                    new SendAlertBetaCommand(
                        alertProcessingEvent.AlertId,
                        new SendAlertRequest(Note: null)
                    ),
                    ct
                );

                await receiver.CompleteMessageAsync(message);
            }
        }
        catch (Exception ex)
        {
            await receiver.AbandonMessageAsync(message);

            _logger.LogError(
                "Error processing alert, {exceptionMessage}, for message {message}",
                ex.Message,
                message.Body.ToString()
            );
        }
    }
}
