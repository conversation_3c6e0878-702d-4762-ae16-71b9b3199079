using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Forecast;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Forecasting;

public class GetDayAheadForecastFunction(ILogger<GetDayAheadForecastFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetDayAheadForecastFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    // NOTE - StartDate and EndDate must be in format "yyyy-MM-dd"
    // to work correctly
    // For example:
    // 1) StartDate = "2022-01-01"; EndDate = "2022-01-02" will
    // get forecast from 2022-01-01 00:00:00 to 2022-01-03 00:00:00
    // 2) StartDate = "2022-01-01"; EndDate = "2022-01-01" will
    // get forecast from 2022-01-01 00:00:00 to 2022-01-02 00:00:00
    [Function(nameof(GetDayAheadForecast))]
    public async Task<IActionResult> GetDayAheadForecast(
        [HttpTrigger(
        authLevel: AuthorizationLevel.Anonymous,
        methods: "post",
        Route = "block/{blockId}/forecast/dayahead")]
        HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        List<ForecastSeriesRequestBody>? seriesRequested = JsonConvert
                            .DeserializeObject<List<ForecastSeriesRequestBody>>(requestBody);

                        if (seriesRequested is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(new GetDayAheadForecastQuery(
                                StartDate: DateOnly.Parse(req.Query["startDate"]!),
                                EndDate: DateOnly.Parse(req.Query["endDate"]!),
                                Series: seriesRequested.ConvertAll(s => new ForecastSeries
                                (
                                    Id: s.Id,
                                    Name: s.Name,
                                    Tag: s.Tag,
                                    Calculation: new ComputedExpression(s.Calculation),
                                    Filter: new FilterExpression(s.Filter)
                                )),
                                Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!))
                            {
                                BlockId = blockId,
                                UserGroupsId = req.GetUserGroups()
                            }, ct)
                            .Match(
                                res => new OkObjectResult(res),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get day ahead forecast. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    /// <summary>
    /// Retrieves day ahead forecast data for a block.
    /// This is used internally (no authentication required but requires a valid API key)
    /// </summary>
    /// <param name="req">The HTTP request.</param>
    /// <param name="blockId">The ID of the block.</param>
    /// <param name="log">The logger.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>An <see cref="IActionResult"/> representing the result of the asynchronous operation.</returns>
    [Function(nameof(GetInternalDayAheadForecast))]
    public async Task<IActionResult> GetInternalDayAheadForecast(
            [HttpTrigger(
                authLevel: AuthorizationLevel.Function,
                methods: "post",
                Route = "internal/block/{blockId}/forecast/dayahead")] HttpRequest req,
            Guid blockId,
            CancellationToken ct)
    {
        try
        {
            // Get the series from the request body
            string payload = await new StreamReader(req.Body).ReadToEndAsync();
            List<ForecastSeriesRequestBody>? seriesRequested = JsonConvert
                .DeserializeObject<List<ForecastSeriesRequestBody>>(payload);

            if (seriesRequested is null)
            {
                return new BadRequestObjectResult("Invalid request body");
            }

            // Then we send request to handler to get forecast data
            return await _mediator
                .Send(new GetDayAheadForecastQuery(
                    StartDate: DateOnly.Parse(req.Query["startDate"]!),
                    EndDate: DateOnly.Parse(req.Query["endDate"]!),
                    Series: seriesRequested.ConvertAll(s => new ForecastSeries
                    (
                        Id: s.Id,
                        Name: s.Name,
                        Tag: s.Tag,
                        Calculation: new ComputedExpression(s.Calculation),
                        Filter: new FilterExpression(s.Filter)
                    )),
                    Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!)
                )
                {
                    BlockId = blockId,
                    // NOTE - internal request requires no authorization
                    UserGroupsId = [],
                    IsAuthorizationEnabled = false
                }, ct)
                .Match(
                    res => new OkObjectResult(res),
                    errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get day ahead forecast. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

