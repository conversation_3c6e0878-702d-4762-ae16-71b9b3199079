using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Forecast;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Forecasting;

public class GetForecastVariableController(
    ILogger<GetForecastVariableController> logger, IMediator mediator)
{
    private readonly ILogger<GetForecastVariableController> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetForecastVariable))]
    public async Task<IActionResult> GetForecastVariable(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "block/{blockId}/forecast/variables")] HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetForecastVariableQuery(
                            UserGroupsId: req.GetUserGroups(),
                            BlockId: blockId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get forecast variables. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
