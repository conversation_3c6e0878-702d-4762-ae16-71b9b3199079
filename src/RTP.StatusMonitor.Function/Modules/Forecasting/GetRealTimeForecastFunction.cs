using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Forecast;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Forecasting;

public class GetRealTimeForecastFunction(
    ILogger<GetRealTimeForecastFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetRealTimeForecastFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetRealTimeForecast))]
    public async Task<IActionResult> GetRealTimeForecast(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "block/{blockId}/forecast/realtime")]HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body)
                        .ReadToEndAsync();
                        List<ForecastSeriesRequestBody>? seriesRequested = JsonConvert.DeserializeObject<List<ForecastSeriesRequestBody>>(requestBody);
                        if (seriesRequested is null)
                        {
                            return new BadRequestObjectResult("Invalid request body");
                        }

                        return await _mediator
                            .Send(new GetRealTimeForecastQuery(
                                Series: seriesRequested
                                    .Select(s => new ForecastSeries
                                    (
                                        Id: s.Id,
                                        Name: s.Name,
                                        Tag: s.Tag,
                                        Calculation: new ComputedExpression(s.Calculation),
                                        Filter: new FilterExpression(s.Filter)
                                    )).ToList(),
                                Interval: Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!))
                            {
                                BlockId = blockId,
                                UserGroupsId = req.GetUserGroups()
                            }, ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve real time forecast data. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
