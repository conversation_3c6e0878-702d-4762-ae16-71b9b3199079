using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;
public class DeleteReportFunction(ILogger<DeleteReportFunction> logger, IMediator mediator)
{
    private readonly ILogger<DeleteReportFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteReport))]
    public async Task<IActionResult> DeleteReport(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "delete",
            Route = "reports/{reportId}")] HttpRequest req,
        Guid reportId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin, AppRole.Production])
                .MatchAsync(
                    async success => await _mediator
                        .Send(new DeleteReportCommand(
                            reportId, req.GetUserAppRoles(), req.GetUserEmailAddress()), ct)
                        .Match(
                            res => new NoContentResult(),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete report. Error: {ErrorMessage}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
