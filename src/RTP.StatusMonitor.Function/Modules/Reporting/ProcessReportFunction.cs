using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting.ProcessReport;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class ProcessReportFunction(ILogger<ProcessReportFunction> logger, IMediator mediator)
{
    private readonly ILogger<ProcessReportFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(ProcessReport))]
    public async Task<IActionResult> ProcessReport(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "reports/{reportId}/process")] HttpRequest req,
        ILogger log,
        Guid reportId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new ProcessReportCommand(reportId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            log.LogError(ex, "Failed to process report. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

