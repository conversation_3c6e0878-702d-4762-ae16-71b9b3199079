using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Modules.Reporting.Request;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class CreateReportFunction(
    IMediator mediator,
    ILogger<CreateReportFunction> logger)
{
    private readonly IMediator _mediator = mediator;
    private readonly ILogger<CreateReportFunction> _logger = logger;

    [Function(nameof(CreateReport))]
    public async Task<IActionResult> CreateReport(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "reports")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin, AppRole.Production])
                .MatchAsync(async success =>
                {
                    string requestBody = await new StreamReader(req.Body)
                        .ReadToEndAsync();
                    ReportRequest? request = JsonConvert.DeserializeObject<ReportRequest>(requestBody);
                    if (request is null)
                    {
                        return new BadRequestResult();
                    }

                    string email = req.GetUserEmailAddress();

                    // Send the request to create appropriate report type
                    ErrorOr<Unit> result = request.ReportType switch
                    {
                        ReportType.Forecast => await _mediator
                            .Send(new CreateForecastReportCommand(
                                    ForecastReport: request.ToForecastReportCommand(Guid.NewGuid()),
                                    RequestedBy: email), ct),
                        // NOTE - Accuracy is a special case of historical report
                        ReportType.Historical or ReportType.Accuracy => await _mediator
                            .Send(new CreateHistoricalReportCommand(
                                HistoricalReport: request.ToHistoricalReportCommand(Guid.NewGuid()),
                                RequestedBy: email), ct),
                        ReportType.Lookup => await _mediator
                            .Send(new CreateLookupReportCommand(
                                LookupReport: request.ToLookupReportCommand(Guid.NewGuid()),
                                RequestedBy: email), ct),
                        _ => throw new ArgumentException("Invalid report type")
                    };

                    return result.Match(
                        res => new NoContentResult(),
                        errors => errors.ToProblemDetails());
                },
                error => Task.FromResult(error.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex, "Failed to create report. Error: {ErrorMessage}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

