using RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.Function.Modules.Reporting.Request;
public record ReportRequest
(
    Guid ReportId,
    string Name,
    string Description,
    ReportType ReportType,
    List<ReportFilterCommand> MainFilters,
    string GroupingInterval,
    ReportVariableGroupingCommand VariableGrouping,
    List<string> GroupingAggregates,
    List<ReportColumnCommand> ReportColumns,
    ReportDateRangeType DateRangeType,
    int? StartOffset,
    int? EndOffset,
    DateTime? StartDate,
    DateTime? EndDate,
    bool IsEnabled,
    string Container,
    string ProcessInterval,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    string DateCreated,
    string CreatedBy
);

public static class ReportRequestMapping
{
    /// <summary>
    /// Map the add report request to a forecast report command
    /// </summary>
    public static ForecastReportCommand ToForecastReportCommand(
        this ReportRequest request, Guid reportId) => new(
            ReportId: reportId,
            Name: request.Name,
            Description: request.Description,
            ReportColumns: request.ReportColumns,
            Container: request.Container,
            BlocksInReport: request.BlocksInReport,
            RelativeDateRange: new ReportRelativeDateRangeCommand(
                StartOffset: request.StartOffset ?? 0,
                EndOffset: request.EndOffset ?? 1),
            IsEnabled: request.IsEnabled,
            ProcessInterval: request.ProcessInterval,
            ReportLayout: request.ReportLayout,
            FileFormat: request.FileFormat,
            UserGroupsAllowed: request.UserGroupsAllowed,
            DateCreated: request.DateCreated,
            CreatedBy: request.CreatedBy);

    /// <summary>
    /// Map the add report request to a historical report command
    /// </summary>
    public static HistoricalOrAccuracyReportCommand ToHistoricalReportCommand(
        this ReportRequest request, Guid reportId)
    => new(
        ReportId: reportId,
        Name: request.Name,
        Description: request.Description,
        ReportType: request.ReportType,
        MainFilters: request.MainFilters,
        GroupingInterval: request.GroupingInterval,
        GroupingAggregates: request.GroupingAggregates,
        ReportColumns: request.ReportColumns,
        Container: request.Container,
        ReportLayout: request.ReportLayout,
        FileFormat: request.FileFormat,
        BlocksInReport: request.BlocksInReport,
        DateRangeInfo: request.DateRangeType switch
        {
            ReportDateRangeType.Fixed => new ReportFixedDateRangeCommand(
                StartDate: request.StartDate ?? DateTime.Now,
                EndDate: request.EndDate ?? DateTime.Now),
            ReportDateRangeType.Relative => new ReportRelativeDateRangeCommand(
                StartOffset: request.StartOffset ?? 0,
                EndOffset: request.EndOffset ?? 1),
            _ => throw new InvalidOperationException("Invalid date range info")
        },
        IsEnabled: request.IsEnabled,
        ProcessInterval:
        request.ProcessInterval,
        UserGroupsAllowed: request.UserGroupsAllowed,
        DateCreated: request.DateCreated,
        CreatedBy: request.CreatedBy);

    /// <summary>
    /// Map the add report request to lookup report command
    /// </summary>
    public static LookupReportCommand ToLookupReportCommand(
        this ReportRequest request, Guid reportId)
    => new(
        ReportId: reportId,
        Name: request.Name,
        Description: request.Description,
        MainFilters: request.MainFilters,
        GroupingInterval: request.GroupingInterval,
        VariableGrouping: request.VariableGrouping,
        ReportColumns: request.ReportColumns,
        BlocksInReport: request.BlocksInReport,
        DateRangeInfo: request.DateRangeType switch
        {
            ReportDateRangeType.Fixed => new ReportFixedDateRangeCommand(
                StartDate: request.StartDate ?? DateTime.Now,
                EndDate: request.EndDate ?? DateTime.Now),
            ReportDateRangeType.Relative => new ReportRelativeDateRangeCommand(
                StartOffset: request.StartOffset ?? 0,
                EndOffset: request.EndOffset ?? 1),
            _ => throw new InvalidOperationException("Invalid date range info")
        },
        IsEnabled: request.IsEnabled,
        Container: request.Container,
        ProcessInterval:
        request.ProcessInterval,
        ReportLayout: request.ReportLayout,
        FileFormat: request.FileFormat,
        UserGroupsAllowed: request.UserGroupsAllowed,
        DateCreated: request.DateCreated,
        CreatedBy: request.CreatedBy);
}
