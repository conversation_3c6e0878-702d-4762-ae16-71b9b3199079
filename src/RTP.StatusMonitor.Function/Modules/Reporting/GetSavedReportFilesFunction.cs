
using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting.GetSavedReports;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class GetSavedReportFilesFunction(ILogger<GetSavedReportFilesFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetSavedReportFilesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetSavedReports))]
    public async Task<IActionResult> GetSavedReports(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "sites/{siteId}/reports/files")] HttpRequest req,
        Guid siteId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetSavedReportFilesQuery(siteId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in querying saved report files");
            return ex.ToErrorResult();
        }
    }
}
