using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Modules.Reporting.Request;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class UpdateReportFunction(ILogger<UpdateReportFunction> logger, IMediator mediator)
{
    private readonly ILogger<UpdateReportFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateReport))]
    public async Task<IActionResult> UpdateReport(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "put",
            Route = "reports/{reportId}")] HttpRequest req,
        Guid reportId,
        CancellationToken ct)
    {
        try
        {
            return await req.AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin, AppRole.Production])
                .MatchAsync(
                    async success =>
                    {
                        string requestBody = await new StreamReader(req.Body)
                            .ReadToEndAsync();
                        ReportRequest? request = JsonConvert.DeserializeObject<ReportRequest>(requestBody);
                        if (request is null)
                        {
                            return new BadRequestResult();
                        }

                        string email = req.GetUserEmailAddress();

                        // Send the request to update the report
                        ErrorOr<Unit> result = request.ReportType switch
                        {
                            ReportType.Forecast => await _mediator.Send
                            (
                                new UpdateReportCommand
                                (
                                    ReportId: reportId,
                                    UpdatedReport: request.ToForecastReportCommand(reportId),
                                    RequestedBy: email
                                ), ct
                            ),
                            ReportType.Historical or ReportType.Accuracy => await _mediator.Send
                            (
                                new UpdateReportCommand
                                (
                                    ReportId: reportId,
                                    UpdatedReport: request.ToHistoricalReportCommand(reportId),
                                    RequestedBy: email
                                ), ct
                            ),
                            ReportType.Lookup => await _mediator.Send
                            (
                                new UpdateReportCommand
                                (
                                    ReportId: reportId,
                                    UpdatedReport: request.ToLookupReportCommand(reportId),
                                    RequestedBy: email
                                ), ct
                            ),
                            _ => throw new ArgumentException("Invalid report type")
                        };

                        return result.Match(
                            res => new NoContentResult(),
                            errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to edit report. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
