using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class ProcessScheduledReportFunction(
    ILogger<ProcessScheduledReportFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<ProcessScheduledReportFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(ProcessScheduledReport))]
    public async Task<IActionResult> ProcessScheduledReport(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "reports/{reportId}/process/scheduled"
        )]
            HttpRequest req,
        Guid reportId,
        CancellationToken ct
    )
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                        await _mediator
                            .Send(new ProcessScheduledReportCommand(reportId), ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()
                            ),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to process scheduled report. Error: {Message}",
                ex.Message
            );
            return ex.ToErrorResult();
        }
    }
}
