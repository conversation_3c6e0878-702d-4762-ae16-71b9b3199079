using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;
public class GetReportsFunction(
    ILogger<GetReportsFunction> logger, IMediator mediator)
{
    private readonly ILogger _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetReports))]
    public async Task<IActionResult> GetReports(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "reports")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async success =>
                        await _mediator
                            .Send(new GetReportsQuery(req.GetUserGroups()), ct)
                            .Match(
                                res => new OkObjectResult(res),
                                errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex, "Failed to get reports. Error: {ex.Message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
