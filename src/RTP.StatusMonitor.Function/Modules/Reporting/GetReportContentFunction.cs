using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class GetReportContentFunction(ILogger<GetReportContentFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetReportContentFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetReportContent))]
    public async Task<IActionResult> GetReportContent(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "reports/{reportId}/content")] HttpRequest req,
        Guid reportId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetReportContentQuery(reportId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed get report content. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

