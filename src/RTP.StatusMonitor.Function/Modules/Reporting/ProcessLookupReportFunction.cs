using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class ProcessLookupReportFunction(ILogger<ProcessLookupReportFunction> logger, IMediator mediator)
{
    private readonly ILogger<ProcessLookupReportFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(ProcessLookupReport))]
    public async Task<IActionResult> ProcessLookupReport(
    [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "post",
            Route = "v2/reports/{reportId}/process")] HttpRequest req,
    Guid reportId,
    CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new ProcessLookupReportCommand(reportId), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process lookup report. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
