using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.App.Shared.Constants;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.Function.Modules.Reporting;

public class ProcessScheduledReportTimerFunction(
    IDataContext dataContext,
    IMediator mediator,
    ILogger<ProcessScheduledReportTimerFunction> logger
)
{
    private readonly IMediator _mediator = mediator;
    private readonly IDataContext _dataContext = dataContext;
    private readonly ILogger<ProcessScheduledReportTimerFunction> _logger = logger;

    [Function("ProcessScheduledReportTimer")]
    public async Task Run([TimerTrigger("* * * * *")] TimerInfo myTimer, CancellationToken ct)
    {
        try
        {
            // Skip in development environment
            if (Environment.GetEnvironmentVariable("AppEnvironment") == AppEnvironment.Development)
                return;

            // TODO - need to tackle lookup report
            // Get all scheduled reports
            List<Report> scheduledReports = await _dataContext
                .Reports.Where(x => x.ReportType != ReportType.Lookup && x.Schedule.IsEnabled)
                .ToListAsync(ct);

            // Send the request to process the scheduled report
            foreach (Report report in scheduledReports)
            {
                if (ShouldProcessReport(report))
                {
                    await _mediator.Send(new ProcessScheduledReportCommand(report.Id), ct);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled reports");
        }
    }

    /// <summary>
    /// Checks if the report should be processed now based on the schedule and the interval
    /// </summary>
    /// <param name="report">The report to process</param>
    /// <returns>True if the this report should be processed now</returns>
    private static bool ShouldProcessReport(Report report)
    {
        if (
            (
                report.Schedule.ProcessInterval == ReportProcessInterval.ThirtyMinutes
                && DateTime.Now.Minute == 0
            )
            || DateTime.Now.Minute == 30
        )
        {
            return true;
        }

        if (
            report.Schedule.ProcessInterval == ReportProcessInterval.Hourly
            && DateTime.Now.Minute == 0
        )
        {
            return true;
        }

        if (
            report.Schedule.ProcessInterval == ReportProcessInterval.Daily
            && DateTime.Now.Hour == 0
            && DateTime.Now.Minute == 0
        )
        {
            return true;
        }

        // First day of the week
        if (
            report.Schedule.ProcessInterval == ReportProcessInterval.Weekly
            && DateTime.Now.DayOfWeek == DayOfWeek.Monday
            && DateTime.Now.Hour == 0
            && DateTime.Now.Minute == 0
        )
        {
            return true;
        }

        // First day of the month at 00:00
        if (
            report.Schedule.ProcessInterval == ReportProcessInterval.Monthly
            && DateTime.Now.Day == 1
            && DateTime.Now.Hour == 0
            && DateTime.Now.Minute == 0
        )
        {
            return true;
        }

        return false;
    }
}
