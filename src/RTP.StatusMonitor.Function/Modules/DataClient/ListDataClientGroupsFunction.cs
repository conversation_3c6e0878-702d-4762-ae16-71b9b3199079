using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using ErrorOr;
using RTP.StatusMonitor.Function.Shared;
using RTP.StatusMonitor.App.Modules.DataClient;

namespace RTP.StatusMonitor.Function.Modules.DataClient;
public class ListDataClientGroupFunction(
    ILogger<ListDataClientGroupFunction> logger,
    IMediator mediator)
{
    private readonly IMediator _mediator = mediator;
    private readonly ILogger<ListDataClientGroupFunction> _logger = logger;

    [Function(nameof(ListDataClientGroups))]
    public async Task<IActionResult> ListDataClientGroups(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "dataclient/block/{Id}/groups")] HttpRequest req,
        Guid Id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new ListDataClientGroupsQuery(
                            UserGroups: req.GetUserGroups(),
                            BlockId: Id,
                            AppRoles: req.GetUserAppRoles()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data client groups. Error: {message}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
