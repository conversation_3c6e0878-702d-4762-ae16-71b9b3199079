using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DataClient;

public sealed class GetDataClientVariablesFunction(ILogger<GetDataClientVariablesFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetDataClientVariablesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetDataClientVariables))]
    public async Task<IActionResult> GetDataClientVariables(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "dataclient/block/{blockId}/variables")] HttpRequest req,
        Guid blockId,
        CancellationToken cancellationToken)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetDataClientVariablesQuery(
                            BlockId: blockId,
                            UserGroupsId: req.GetUserGroups()), cancellationToken)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                        errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data client variables. Error: {Error}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
