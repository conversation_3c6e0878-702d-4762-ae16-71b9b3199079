using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DataClient;

public class MigrateDataClientStorageFunction(
    ILogger<MigrateDataClientStorageFunction> logger,
    IMediator mediator
)
{
    private readonly ILogger<MigrateDataClientStorageFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(MigrateDataClientStorage))]
    public async Task<IActionResult> MigrateDataClientStorage(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Function,
            methods: "post",
            Route = "block/{blockId}/dataclient/migrate"
        )]
            HttpRequest req,
        Guid blockId,
        CancellationToken ct
    )
    {
        try
        {
            // Get the request body
            string payload = await new StreamReader(req.Body).ReadToEndAsync();

            // Deserialize the request body
            List<string>? tags = JsonConvert.DeserializeObject<List<string>>(payload);

            if (tags is null)
            {
                return new BadRequestObjectResult("Invalid request");
            }

            await _mediator.Send(
                new MigrateDataClientStorageCommand(
                    BlockId: blockId,
                    Tags: tags,
                    StartDate: DateTime.Parse(req.Query["start"]!),
                    EndDate: DateTime.Parse(req.Query["end"]!)
                ),
                ct
            );

            return new OkResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to migrate data client storage. Error: {ErrorMessage}",
                ex.Message
            );

            return ex.ToErrorResult();
        }
    }
}
