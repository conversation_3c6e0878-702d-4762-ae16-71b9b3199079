using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using ErrorOr;
using RTP.StatusMonitor.App.Modules.DataClient;

namespace RTP.StatusMonitor.Function.Modules.DataClient;

public class EvaluateDataClientStatsFunction(ILogger<EvaluateDataClientStatsFunction> logger, IMediator mediator)
{
    private readonly ILogger<EvaluateDataClientStatsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(EvaluateDataClientStats))]
    public async Task<IActionResult> EvaluateDataClientStats(
            [HttpTrigger(
                authLevel: AuthorizationLevel.Anonymous,
                methods: "get",
                Route = "block/{blockId}/dataclient/evaluate")] HttpRequest req,
            Guid blockId,
            CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new EvaluateDataClientStatsQuery(
                            StartDate: DateTime.Parse(req.Query["from"]!),
                            EndDate: DateTime.Parse(req.Query["to"]!),
                            Filter: req.Query["filter"]!)
                        {
                            BlockId = blockId,
                            UserGroupsId = req.GetUserGroups()
                        }, ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to evaluate data client stats. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
