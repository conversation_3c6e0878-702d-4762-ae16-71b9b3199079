using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using RTP.StatusMonitor.App.Modules.DataClient;
using ErrorOr;

namespace RTP.StatusMonitor.Function.Modules.DataClient;
public class GetDataClientDetailsFunction(
    ILogger<GetDataClientDetailsFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetDataClientDetailsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetDataClientDetails))]
    public async Task<IActionResult> GetDataClientDetails(
        [HttpTrigger(AuthorizationLevel.Anonymous,
        "get",
        Route = "dataclient/block/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetDataClientDetailsQuery(
                            BlockId: id,
                            GroupNames: [.. req.Query.ToString()!.Split(",")]), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data client details for {blockId}. Error: {message}", id, ex.Message);

            return ex.ToErrorResult();
        }
    }
}
