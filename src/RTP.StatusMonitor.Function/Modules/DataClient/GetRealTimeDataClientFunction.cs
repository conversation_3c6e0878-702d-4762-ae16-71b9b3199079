using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.Function.Modules.DataClient.Requests;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DataClient;

public class GetRealTimeDataClientFunction(
    ILogger<GetRealTimeDataClientFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetRealTimeDataClientFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    // NOTE - Can not deprecate yet (still use on old version of data client)
    [Function(nameof(GetLatestDataClient))]
    public async Task<IActionResult> GetLatestDataClient(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods: "get",
            Route = "block/{blockId}/output/latest")] HttpRequest req,
            Guid blockId,
            CancellationToken cancellationToken)
    {
        try
        {
            return await req.AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetLatestDataClientQuery(
                            BlockId: blockId,
                            Tags: [.. req.Query["tags"].ToString().Split(',')]),
                        cancellationToken)
                        .Match(
                            data => new OkObjectResult(data),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest data client. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    [Function(nameof(GetRealTimeDataClient))]
    public async Task<IActionResult> GetRealTimeDataClient(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Anonymous,
            methods:"post",
            Route = "block/{blockId}/dataclient/latest")] HttpRequest req,
            Guid blockId,
            CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body)
                            .ReadToEndAsync();
                        List<DataClientSeriesRequest>? request = JsonConvert
                            .DeserializeObject<List<DataClientSeriesRequest>>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new GetRealTimeDataClientQuery(
                                Series: request.ConvertAll(x => new Series
                                (
                                    Id: x.Id,
                                    Name: x.Name,
                                    Tag: x.Tag,
                                    Calculation: new(x.Calculation),
                                    Filter: new(x.Filter)
                                )))
                            {
                                BlockId = blockId,
                                UserGroupsId = req.GetUserGroups()
                            }, ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get real time data client. Error: {Error}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}

