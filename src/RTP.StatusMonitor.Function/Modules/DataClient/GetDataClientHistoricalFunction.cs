using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.TimeSeries;
using Microsoft.Azure.Functions.Worker;
using RTP.StatusMonitor.Function.Shared;
using RTP.StatusMonitor.Function.Modules.DataClient.Requests;
using RTP.StatusMonitor.App.Modules.DataClient.Shared.Dtos;
using RTP.StatusMonitor.App.Modules.DataClient;

namespace RTP.StatusMonitor.Function.Modules.DataClient;
public class GetDataClientHistoricalFunction(
    ILogger<GetDataClientHistoricalFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetDataClientHistoricalFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    // NOTE - Still needed for data client on dashboard
    [Function(nameof(GetOutputHistorical))]
    public async Task<IActionResult> GetOutputHistorical(
    [HttpTrigger(
        authLevel: AuthorizationLevel.Anonymous,
        methods: "get",
        Route = "block/{blockId}/output/historical")] HttpRequest req,
        Guid blockId,
        CancellationToken ct)
    {
        try
        {

            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetOutputHistoricalQuery(
                            BlockId: blockId,
                            UserGroupsId: req.GetUserGroups(),
                            Tags: [.. req.Query["tags"].ToString().Split(",")],
                            SkipInterval: int.Parse(req.Query["skipInterval"]!),
                            Filter: req.Query["filters"].ToString(),
                            From: DateTime.Parse(req.Query["from"]!),
                            To: DateTime.Parse(req.Query["to"]!)), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get historical data client. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }

    /// <summary>
    /// Retrieves historical data (data client) for a specific block
    /// </summary>
    [Function(nameof(GetDataClientHistorical))]
    public async Task<IActionResult> GetDataClientHistorical(
        [HttpTrigger(
        authLevel: AuthorizationLevel.Anonymous,
        methods: "post",
        Route = "block/{blockId}/dataclient/historical/v2")] HttpRequest req,
        Guid blockId,
        ILogger log,
        CancellationToken ct)
        => await HandleDataClientHistoricalRequest(
            req, blockId, isInternalRequest: false, ct);


    /// <summary>
    /// Retrieves historical data (data client) for a specific block
    /// This is used internally (no authentication required but requires a valid API key)
    /// </summary>
    /// <param name="req">The HTTP request.</param>
    /// <param name="blockId">The ID of the block.</param>
    /// <param name="log">The logger.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>An <see cref="IActionResult"/> representing the result of the asynchronous operation.</returns>
    [Function(nameof(GetInternalDataClientHistorical))]
    public async Task<IActionResult> GetInternalDataClientHistorical(
            [HttpTrigger(
                authLevel: AuthorizationLevel.Function,
                methods: "post",
                Route = "internal/block/{blockId}/historical/dataclient")] HttpRequest req,
            Guid blockId,
            CancellationToken ct)
            => await HandleDataClientHistoricalRequest(
                req, blockId, isInternalRequest: true, ct);

    /// <summary>
    /// Handles the request for retrieving historical data from the data client.
    /// </summary>
    /// <param name="req">The HTTP request.</param>
    /// <param name="blockId">The ID of the block.</param>
    /// <param name="isInternalRequest">A flag indicating whether the request is internal.</param>
    /// <param name="log">The logger instance.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>An <see cref="IActionResult"/> representing the result of the request.</returns>
    private async Task<IActionResult> HandleDataClientHistoricalRequest(
        HttpRequest req,
        Guid blockId,
        bool isInternalRequest,
        CancellationToken ct)
    {
        try
        {
            // Will need to authenticate the request if this is not an internal request
            if (!isInternalRequest)
            {
                ErrorOr<Success> authResult = await req.AuthenticateRequestAsync();
                if (authResult.IsError)
                {
                    return authResult.Errors.ToProblemDetails();
                }
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            List<DataClientSeriesRequest> seriesRequested = JsonConvert
                .DeserializeObject<List<DataClientSeriesRequest>>(requestBody)!;

            TimeSeriesResamplingInterval interval = req.Query.ContainsKey("interval")
                ? Enum.Parse<TimeSeriesResamplingInterval>(req.Query["interval"]!)
                : TimeSeriesResamplingInterval.Hour;

            // Query the data client historical data
            var data = await _mediator.Send(new GetDataClientHistoricalQuery
            (
                Series: seriesRequested
                    .Select(e => new ActualSeries(
                        Id: e.Id,
                        Name: e.Name,
                        Tag: e.Tag,
                        Calculation: new(e.Calculation),
                        Filter: new(e.Filter)))
                    .ToList(),
                StartTime: DateTime.SpecifyKind(
                    DateTime.Parse(req.Query["startTime"]!),
                    DateTimeKind.Unspecified),
                EndTime: DateTime.SpecifyKind(
                    DateTime.Parse(req.Query["endTime"]!),
                    DateTimeKind.Unspecified),
                Interval: interval
            )
            {
                BlockId = blockId,
                UserGroupsId = isInternalRequest ? [] : req.GetUserGroups(),
                IsAuthorizationEnabled = !isInternalRequest
            }, ct);

            // Match the result with the appropriate response
            return data.Match(
                value => new OkObjectResult(value),
                errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get historical data client. Error: {message}", ex.Message);
            return ex.ToErrorResult();
        }
    }
}
