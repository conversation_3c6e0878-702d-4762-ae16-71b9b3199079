using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Functions.Worker;
using ErrorOr;
using RTP.StatusMonitor.Function.Shared;
using RTP.StatusMonitor.App.Modules.DataClient;

namespace RTP.StatusMonitor.Function.Modules.DataClient;

public class DeleteDataClientCleanupFunction(ILogger<DeleteDataClientCleanupFunction> logger, IMediator mediator)
{
    private readonly ILogger<DeleteDataClientCleanupFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteDataClientCleanup))]
    public async Task<IActionResult> DeleteDataClientCleanup(
        [HttpTrigger(
            authLevel: AuthorizationLevel.Function,
            methods: "delete",
            Route = "block/{blockId}/dataclient")] HttpRequest req,
            Guid blockId,
            CancellationToken ct)
    {
        try
        {
            return await _mediator
                .Send(new DataClientCleanupCommand(
                    BlockId: blockId,
                    StartDate: DateTime.Parse(req.Query["start"]!),
                    EndDate: DateTime.Parse(req.Query["end"]!)), ct)
                .Match(
                    _ => new OkResult(),
                    errors => errors.ToProblemDetails());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clean up data client for {blockId}. Error: {message}", blockId, ex.Message);
            return ex.ToErrorResult();
        }
    }
}
