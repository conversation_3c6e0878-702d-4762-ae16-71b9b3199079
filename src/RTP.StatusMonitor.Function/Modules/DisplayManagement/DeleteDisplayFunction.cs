using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.DisplayManagement.DeleteDisplay;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DisplayManagement;

public class DeleteDisplayFunction(
    ILogger<DeleteDisplayFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteDisplayFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteDisplay))]
    public async Task<IActionResult> DeleteDisplay(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "display/{displayId}")] HttpRequest req,
        Guid displayId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new DeleteDisplayCommand
                        (
                            DisplayId: displayId,
                            UserGroups: req.GetUserGroups()
                        ), ct)
                        .Match(
                            _ => new NoContentResult(),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting display");
            return ex.ToErrorResult();
        }
    }
}

