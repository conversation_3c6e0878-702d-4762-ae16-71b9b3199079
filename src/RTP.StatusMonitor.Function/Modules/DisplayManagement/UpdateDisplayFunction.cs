using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.DisplayManagement.UpdateDisplay;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DisplayManagement;

public class UpdateDisplayFunction(
    ILogger<UpdateDisplayFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateDisplayFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateDisplay))]
    public async Task<IActionResult> UpdateDisplay(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "display/{displayId}")] HttpRequest req,
        Guid displayId,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateDisplayCommand? updateDisplayCommand = JsonConvert.DeserializeObject<UpdateDisplayCommand>(requestBody);

                        if (updateDisplayCommand is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new UpdateDisplayCommand
                            {
                                DisplayId = displayId,
                                UserGroups = req.GetUserGroups(),
                                AppRoles = req.GetUserAppRoles(),
                                GroupsToShare = updateDisplayCommand.GroupsToShare,
                                GroupToSetDefaultDisplay = updateDisplayCommand.GroupToSetDefaultDisplay,
                                Name = updateDisplayCommand.Name,
                                Alias = updateDisplayCommand.Alias,
                                Description = updateDisplayCommand.Description,
                                Layout = updateDisplayCommand.Layout,
                                DisplayComponent = updateDisplayCommand.DisplayComponent,
                            }, ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating display");
            return ex.ToErrorResult();
        }
    }
}
