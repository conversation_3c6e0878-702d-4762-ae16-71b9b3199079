using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.DisplayManagement.CreateDisplay;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DisplayManagement;

public class CreateDisplayFunction(
    ILogger<CreateDisplayFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<CreateDisplayFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateDisplay))]
    public async Task<IActionResult> CreateDisplay(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "post",
            Route = "display/create")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        CreateDisplayCommand? createDisplayCommand = JsonConvert.DeserializeObject<CreateDisplayCommand>(requestBody);

                        if (createDisplayCommand is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new CreateDisplayCommand
                            {
                                UserGroups = req.GetUserGroups(),
                                AppRoles = req.GetUserAppRoles(),
                                GroupsToShare = createDisplayCommand.GroupsToShare,
                                GroupToSetDefaultDisplay = createDisplayCommand.GroupToSetDefaultDisplay,
                                Name = createDisplayCommand.Name,
                                Alias = createDisplayCommand.Alias,
                                Description = createDisplayCommand.Description,
                                Layout = createDisplayCommand.Layout,
                                DisplayComponent = createDisplayCommand.DisplayComponent,
                            }, ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while creating display");
            return ex.ToErrorResult();
        }
    }
}
