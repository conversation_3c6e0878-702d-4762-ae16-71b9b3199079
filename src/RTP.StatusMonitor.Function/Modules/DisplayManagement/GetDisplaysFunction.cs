using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.DisplayManagement.GetDisplays;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.DisplayManagement;

public class GetDisplaysFunction(
    ILogger<GetDisplaysFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetDisplaysFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetDisplays))]
    public async Task<IActionResult> GetDisplays(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "display")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                            .Send(new GetDisplaysQuery(req.GetUserGroups()), ct)
                            .Match(
                                value => new OkObjectResult(value),
                                errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while getting displays");
            return ex.ToErrorResult();
        }
    }
}

