using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Documentation;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement;

public class GetDocumentationFileFunction(
    ILogger<GetDocumentationFileFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetDocumentationFileFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetDocumentationFiles))]
    public async Task<IActionResult> GetDocumentationFiles(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "docs")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetDocumentationFilesQuery(req.GetUserGroups()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while retrieving documentation files");
            return ex.ToErrorResult();
        }
    }
}
