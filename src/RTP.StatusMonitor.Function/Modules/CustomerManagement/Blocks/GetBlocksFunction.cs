using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Queries;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Blocks;

public class GetBlocksFunction(
    ILogger<GetBlocksFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetBlocksFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetBlocks))]
    public async Task<IActionResult> GetBlocks(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "block")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new ListBlocksQuery(req.GetUserGroups()), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while getting blocks");
            return ex.ToErrorResult();
        }
    }
}
