using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Blocks;

public class DeleteBlockFunction(
    ILogger<DeleteBlockFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteBlockFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteBlock))]
    public async Task<IActionResult> DeleteBlock(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "block/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new DeleteBlockCommand(BlockId: id), ct)
                        .Match(
                            _ => new OkResult(),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting block");
            return ex.ToErrorResult();
        }
    }
}
