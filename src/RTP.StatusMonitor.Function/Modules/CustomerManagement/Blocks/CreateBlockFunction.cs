using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Blocks;

public record CreateBlockRequest(
    string Name,
    string? IpAddress,
    int? Port,
    Guid SiteId);

public class CreateBlockFunction(
    ILogger<CreateBlockFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<CreateBlockFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateBlock))]
    public async Task<IActionResult> CreateBlock(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "post",
            Route = "block")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        CreateBlockRequest? request = JsonSerializer.Deserialize<CreateBlockRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(
                                new CreateBlockCommand
                                {
                                    Name = request.Name,
                                    IpAddress = request.IpAddress ?? string.Empty,
                                    Port = request.Port ?? 0,
                                    SiteId = request.SiteId
                                }, ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while creating block");
            return ex.ToErrorResult();
        }
    }
}
