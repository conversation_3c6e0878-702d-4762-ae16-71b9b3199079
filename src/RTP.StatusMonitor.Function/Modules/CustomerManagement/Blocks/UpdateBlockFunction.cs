using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Blocks;

public record UpdateBlockRequest(
    string? Name,
    string? IpAddress,
    int? Port);

public class UpdateBlockFunction(
    ILogger<UpdateBlockFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateBlockFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateBlock))]
    public async Task<IActionResult> UpdateBlock(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "block/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateBlockRequest? request = JsonSerializer.Deserialize<UpdateBlockRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(
                                new UpdateBlockCommand()
                                {
                                    Id = id,
                                    Name = request.Name,
                                    IpAddress = request.IpAddress,
                                    Port = request.Port
                                }, ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating block");
            return ex.ToErrorResult();
        }
    }
}
