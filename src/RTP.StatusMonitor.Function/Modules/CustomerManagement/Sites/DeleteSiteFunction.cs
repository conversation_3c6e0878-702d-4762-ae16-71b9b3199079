using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Sites;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Sites;

public class DeleteSiteFunction(
    ILogger<DeleteSiteFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteSiteFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteSite))]
    public async Task<IActionResult> DeleteSite(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "site/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ => await _mediator
                            .Send(new DeleteSiteCommand(SiteId: id), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting site");
            return ex.ToErrorResult();
        }
    }
}
