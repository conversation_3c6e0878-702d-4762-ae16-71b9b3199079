using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Sites;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Sites;
public class GetSitesFunction(
    ILogger<GetSitesFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<GetSitesFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetSites))]
    public async Task<IActionResult> GetSites(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "site")]
        HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                async success
                    => await _mediator
                        .Send(new GetSitesQuery(req.GetUserGroups()), ct)
                        .Match(
                            res => new OkObjectResult(res),
                            errors => errors.ToProblemDetails()),
                errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get sites. Error: {ErrorMessage}", ex.Message);

            return ex.ToErrorResult();
        }
    }
}
