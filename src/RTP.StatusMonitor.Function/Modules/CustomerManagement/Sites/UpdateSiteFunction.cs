using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Sites.UpdateSite;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Sites;

public record UpdateSiteRequest(
    string Name,
    string Alias,
    string Description,
    string Location,
    double Altitude,
    bool IsMetric,
    double Latitude,
    double Longitude,
    string TimeZone,
    List<string> UserGroupsAllowedAccess,
    string CustomerId);

public class UpdateSiteFunction(
    ILogger<UpdateSiteFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateSiteFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateSite))]
    public async Task<IActionResult> UpdateSite(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "site/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateSiteRequest? request = JsonSerializer.Deserialize<UpdateSiteRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(
                                new UpdateSiteCommand(
                                    Id: id,
                                    Name: request.Name,
                                    Alias: request.Alias,
                                    Description: request.Description,
                                    Location: request.Location,
                                    Altitude: request.Altitude,
                                    IsMetric: request.IsMetric,
                                    Latitude: request.Latitude,
                                    Longitude: request.Longitude,
                                    TimeZone: request.TimeZone,
                                    UserGroupsAllowedAccess: request.UserGroupsAllowedAccess
                                        .Select(Guid.Parse)
                                        .ToList(),
                                    CustomerId: Guid.Parse(request.CustomerId)), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating site");
            return ex.ToErrorResult();
        }
    }
}

