using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Units;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Units;

public record CreateUnitRequest(string Name, Guid BlockId);

public class CreateUnitFunction(
    ILogger<CreateUnitFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<CreateUnitFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateUnit))]
    public async Task<IActionResult> CreateUnit(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "post",
            Route = "unit")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        CreateUnitRequest? request = JsonSerializer
                            .Deserialize<CreateUnitRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(new CreateUnitCommand(request.Name, request.BlockId), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while creating unit");
            return ex.ToErrorResult();
        }
    }
}
