using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Units;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Units;

public record UpdateUnitRequest(
    string Name,
    Guid? BlockId);

public class UpdateUnitFunction(
    ILogger<UpdateUnitFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateUnitFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateUnit))]
    public async Task<IActionResult> UpdateUnit(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "unit/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateUnitRequest? request = JsonSerializer.Deserialize<UpdateUnitRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(new UpdateUnitCommand(id, request.Name, request.BlockId), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating unit");
            return ex.ToErrorResult();
        }
    }
}
