using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Units.Commands;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Units;

public class DeleteUnitFunction(
    ILogger<DeleteUnitFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteUnitFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteUnit))]
    public async Task<IActionResult> DeleteUnit(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "unit/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new DeleteUnitCommand(id), ct)
                        .Match(
                            _ => new OkResult(),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting unit");
            return ex.ToErrorResult();
        }
    }
}
