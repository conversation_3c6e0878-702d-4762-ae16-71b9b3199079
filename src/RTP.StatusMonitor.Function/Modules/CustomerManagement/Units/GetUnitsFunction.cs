using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Units.GetUnits;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Units;

public class GetUnitsFunction(
    ILogger<GetUnitsFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetUnitsFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetUnits))]
    public async Task<IActionResult> GetUnits(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "units")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetUnitsQuery(req.GetUserGroups()), ct)
                        .Match(
                            units => new OkObjectResult(units),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while getting units");
            return ex.ToErrorResult();
        }
    }
}
