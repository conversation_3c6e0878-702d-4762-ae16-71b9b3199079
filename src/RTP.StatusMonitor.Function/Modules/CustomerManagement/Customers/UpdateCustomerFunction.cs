using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Customers.Commands;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;
using System.Text.Json;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Customers;

public record UpdateCustomerRequest(
    string Name,
    string Title,
    string CompanyLogoUrl,
    string BlobContainer);

public class UpdateCustomerFunction(
    ILogger<UpdateCustomerFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<UpdateCustomerFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(UpdateCustomer))]
    public async Task<IActionResult> UpdateCustomer(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "put",
            Route = "customer/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        UpdateCustomerRequest? request = JsonSerializer.Deserialize<UpdateCustomerRequest>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request body!");
                        }

                        return await _mediator
                            .Send(
                                new UpdateCustomerCommand
                                {
                                    Id = id,
                                    Name = request.Name,
                                    Title = request.Title,
                                    CompanyLogoUrl = request.CompanyLogoUrl,
                                    BlobContainer = request.BlobContainer
                                }, ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while updating customer");
            return ex.ToErrorResult();
        }
    }
}
