using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Customers.Commands;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Customers;

public class DeleteCustomerFunction(
    ILogger<DeleteCustomerFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<DeleteCustomerFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(DeleteCustomer))]
    public async Task<IActionResult> DeleteCustomer(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "delete",
            Route = "customer/{id}")] HttpRequest req,
        Guid id,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync([AppRole.Admin])
                .MatchAsync(
                    async _ => await _mediator
                            .Send(new DeleteCustomerCommand(id), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails())
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while deleting customer");
            return ex.ToErrorResult();
        }
    }
}
