using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Modules.Management.Customers.GetCustomers;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Customers;

public class GetCustomersFunction(ILogger<GetCustomersFunction> logger, IMediator mediator)
{
    private readonly ILogger<GetCustomersFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(GetCustomers))]
    public async Task<IActionResult> GetCustomers(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "get",
            Route = "customer")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync()
                .MatchAsync(
                    async _ => await _mediator
                        .Send(new GetCustomersQuery(), ct)
                        .Match(
                            value => new OkObjectResult(value),
                            errors => errors.ToProblemDetails()),
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while getting customers");
            return ex.ToErrorResult();
        }
    }
}
