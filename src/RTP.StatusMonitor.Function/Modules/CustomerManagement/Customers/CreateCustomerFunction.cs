using ErrorOr;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Management.Customers.CreateCustomer;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Function.Shared;

namespace RTP.StatusMonitor.Function.Modules.CustomerManagement.Customers;

public class CreateCustomerFunction(
    ILogger<CreateCustomerFunction> logger,
    IMediator mediator)
{
    private readonly ILogger<CreateCustomerFunction> _logger = logger;
    private readonly IMediator _mediator = mediator;

    [Function(nameof(CreateCustomer))]
    public async Task<IActionResult> CreateCustomer(
        [HttpTrigger(
            AuthorizationLevel.Anonymous,
            "post",
            Route = "customer")] HttpRequest req,
        CancellationToken ct)
    {
        try
        {
            return await req
                .AuthenticateRequestAsync(appRolesAllowed: [AppRole.Admin])
                .MatchAsync(
                    async _ =>
                    {
                        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                        CreateCustomerCommand? request = JsonConvert.DeserializeObject<CreateCustomerCommand>(requestBody);

                        if (request is null)
                        {
                            return new BadRequestObjectResult("Invalid request");
                        }

                        return await _mediator
                            .Send(new CreateCustomerCommand(
                                Name: request.Name,
                                Title: request.Title,
                                CompanyLogoUrl: request.CompanyLogoUrl,
                                BlobContainer: request.BlobContainer), ct)
                            .Match(
                                _ => new OkResult(),
                                errors => errors.ToProblemDetails());
                    },
                    errors => Task.FromResult(errors.ToProblemDetails()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while creating customer");
            return ex.ToErrorResult();
        }
    }
}
