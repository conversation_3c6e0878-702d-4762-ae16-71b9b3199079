{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "AzureAd:Instance": "https://login.microsoftonline.com/", "AzureAd:Domain": "<DOMAIN>", "AzureAd:TenantId": "<TENANT_ID>kk", "AzureAd:ClientId": "<CLIENT_ID>", "AzureAd:ClientSecret": "<CLIENT_SECRET>", "AzureAd:Scope": "api://4cd63797-6163-4da3-ba65-ac7fbb3af455", "BlobStorageConfig:ConnectionString": "<BLOB_STORAGE_CONNECTION_STRING>", "TableStorageConfig:Uri": "<TABLE_STORAGE_URI>", "TableStorageConfig:AccountName": "<TABLE_STORAGE_ACCOUNT_NAME>", "TableStorageConfig:AccountKey": "<TABLE_STORAGE_ACCOUNT_KEY>", "TableStorageConfig:WeatherReadTable": "currentWeatherV2", "TableStorageConfig:WeatherWriteTable": "currentWeatherTest", "ServiceBusConfig:ConnectionString": "<SERVICE_BUS_CONNECTION_STRING>", "ServiceBusConfig:ForecastUpdatesTopic": "<FORECAST_UPDATES_TOPIC>", "ServiceBusConfig:AlertProcessingQueue": "alert-processing-queue", "ServiceBusConfig:AlertProcessingQueueBeta": "alert-processing-queue-beta", "EmailConfig:Host": "<EMAIL_HOST>", "EmailConfig:Port": "<EMAIL_PORT>", "EmailConfig:Address": "<EMAIL_ADDRESS>", "EmailConfig:UserName": "<EMAIL_USERNAME>", "EmailConfig:Password": "<EMAIL_PASSWORD>", "EmailConfig:DefaultCredentials": false, "InternalApiConfig:BaseUrl": "http://localhost:7071/api", "InternalApiConfig:FunctionKey": "<function-key>", "AccuWeatherApiConfig:ApiKey": "<ACCU_WEATHER_API_KEY>", "IBMWeatherApiConfig:ApiKey": "<IBM_WEATHER_API_KEY>", "TomorrowWeatherApiConfig:ApiKey": "<TOMORROW_WEATHER_API_KEY>", "GoogleMapApiConfig:ApiKey": "<GOOGLE_MAP_API_KEY>", "AppEnvironment": "Development", "SqlConnectionString": "<SQL_CONNECTION_STRING>"}, "Host": {"CORS": "*"}}