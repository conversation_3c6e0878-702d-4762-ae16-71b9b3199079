using System.Security.Claims;
using ErrorOr;
using Microsoft.AspNetCore.Http;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.Resource;

namespace RTP.StatusMonitor.Function.Shared;

public static class AzureEntraExtensions
{
    private static readonly string[] _scopeRequiredByApi = ["API.Read"];

    /// <summary>
    /// Authenticate request and validate app roles if provided
    /// </summary>
    /// <param name="req">The HTTP request</param>
    /// <param name="appRolesAllowed">The app roles allowed</param>
    /// <returns>Success if authentication is successful</returns>
    public static async Task<ErrorOr<Success>> AuthenticateRequestAsync(
        this HttpRequest req,
        string[]? appRolesAllowed = null
    )
    {
        // Authenticate request
        (bool authenticationStatus, _) = await req.HttpContext.AuthenticateAzureFunctionAsync();

        // Fail to authenticate
        if (!authenticationStatus)
        {
            return Error.Unauthorized(
                "AzureEntraId.AuthenticationFailed",
                "Fail to authenticate request. Please check your Azure AD credentials and try again."
            );
        }

        // Check if request contains accepted Auzre AD scope
        req.HttpContext.VerifyUserHasAnyAcceptedScope(_scopeRequiredByApi);

        // Check if request contains accepted app role
        if (appRolesAllowed is not null && appRolesAllowed.Length > 0)
        {
            req.HttpContext.ValidateAppRole(appRolesAllowed);
        }

        // Return success if authentication is successful
        return Result.Success;
    }

    /// <summary>
    /// Get user groups id from request
    /// </summary>
    /// <param name="req">The HTTP request</param>
    /// <returns>A list of user groups id</returns>
    public static List<Guid> GetUserGroups(this HttpRequest req) =>
        req
            .HttpContext.User.Claims.Where(c => c.Type == "groups")
            .Select(c => Guid.Parse(c.Value))
            .ToList();

    /// <summary>
    /// Get user email from request
    /// </summary>
    /// <param name="req">The HTTP request</param>
    /// <returns>The user email</returns>
    public static string GetUserEmailAddress(this HttpRequest req) =>
        req.HttpContext.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value
        ?? string.Empty;

    /// <summary>
    /// Get user's app roles from request
    /// </summary>
    /// <param name="request">The Http request</param>
    /// <returns>The user's app roles</returns>
    public static List<string> GetUserAppRoles(this HttpRequest request) =>
        request
            .HttpContext.User.Claims.Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();
}
