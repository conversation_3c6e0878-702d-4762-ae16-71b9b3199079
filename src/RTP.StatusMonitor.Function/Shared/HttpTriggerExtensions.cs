using ErrorOr;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RTP.StatusMonitor.App.Shared.Exceptions;

namespace RTP.StatusMonitor.Function.Shared;

public static class HttpTriggerExtensions
{

    /// <summary>
    /// Convert exception to IActionResult
    /// </summary>
    /// <param name="exception"></param>
    /// <returns></returns>
    public static IActionResult ToErrorResult(this Exception exception)
    {
        (int statusCode, string message) = exception switch
        {
            IServiceException ex => ((int)ex.StatusCode, ex.ErrorMessage),
            _ => (StatusCodes.Status500InternalServerError,
                "An unexpected error occurred.")
        };

        return new ObjectResult(new
        {
            statusCode,
            message
        })
        {
            StatusCode = statusCode
        };
    }

    /// <summary>
    /// Converts a list of errors to a problem details object.
    /// </summary>
    /// <param name="errors">A list of errors</param>
    /// <returns>An object with status code and error message</returns>
    public static IActionResult ToProblemDetails(this List<Error> errors)
    {
        // Get the first error from the list
        Error error = errors[0];

        // Return the appropriate status code and error message
        return error.Type switch
        {
            ErrorType.Unauthorized => new UnauthorizedObjectResult(new ProblemDetails
            {
                Title = error.Code,
                Detail = error.Description,
                Status = StatusCodes.Status401Unauthorized
            }),
            ErrorType.NotFound => new NotFoundObjectResult(new ProblemDetails
            {
                Title = error.Code,
                Detail = error.Description,
                Status = StatusCodes.Status404NotFound
            }),
            ErrorType.Conflict => new ConflictObjectResult(new ProblemDetails
            {
                Title = error.Code,
                Detail = error.Description,
                Status = StatusCodes.Status409Conflict
            }),
            ErrorType.Validation => new BadRequestObjectResult(new ProblemDetails
            {
                Title = error.Code,
                Detail = error.Description,
                Status = StatusCodes.Status400BadRequest
            }),
            _ => new ObjectResult(new ProblemDetails
            {
                Title = error.Code,
                Detail = error.Description,
                Status = StatusCodes.Status500InternalServerError
            })
        };
    }
}
