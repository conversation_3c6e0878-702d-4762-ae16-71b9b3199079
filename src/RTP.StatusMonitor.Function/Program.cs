using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Identity.Web;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using RTP.StatusMonitor.App;
using RTP.StatusMonitor.Persistence;
using Microsoft.AspNetCore.Builder;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Microsoft.Graph;
using Azure.Identity;

IHost host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;
        var azureAd = configuration.GetSection("AzureAd");

        // Add Azure Entra ID Authentication for the API itself
        services
            .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddMicrosoftIdentityWebApi(azureAd);

        // Configure Microsoft Graph client with client credentials
        services.AddSingleton(sp =>
        {
            // Create the credential using client credentials flow
            ClientSecretCredential credential = new(
                configuration["AzureAd:TenantId"],
                configuration["AzureAd:ClientId"],
                configuration["AzureAd:ClientSecret"]);

            // Create Graph client with the credential
            GraphServiceClient graphClient = new(credential, ["https://graph.microsoft.com/.default"]);
            return graphClient;
        });

        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService()
                .ConfigureFunctionsApplicationInsights();

        services
            .AddMvc()
            .AddNewtonsoftJson(opts => JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                Formatting = Formatting.Indented,
            });

        // Add your other DI registrations
        services.AddApplication()
                .AddPersistence();
    })
    .Build();

host.Run();
