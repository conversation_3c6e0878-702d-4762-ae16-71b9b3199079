using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.EquipmentGroup;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.EquipmentSection;
using Microsoft.EntityFrameworkCore.Infrastructure;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Chart;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.IntegrationEventLogEntry;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.Persistence;
public interface IDataContext
{
    DbSet<Customer> Customers { get; set; }
    DbSet<Site> Sites { get; set; }
    DbSet<Block> Blocks { get; set; }
    DbSet<Unit> Units { get; set; }
    DbSet<DataClient> DataClients { get; set; }
    DbSet<DataClientGroup> DataClientGroups { get; set; }
    DbSet<DataClientGroupVariable> DataClientGroupVariables { get; set; }
    DbSet<DataClientVariable> DataClientVariables { get; set; }
    DbSet<DataClientStatusVariable> DataClientStatusVariables { get; set; }
    DbSet<UserGroup> UserGroups { get; set; }
    DbSet<GroupPermission> GroupPermissions { get; set; }
    DbSet<Display> Displays { get; set; }
    DbSet<DisplayViewPermission> DisplayViewPermissions { get; set; }
    DbSet<SiteWeatherSettings> SiteWeatherSettings { get; set; }
    DbSet<WeatherBiasSettings> WeatherBiasSettings { get; set; }
    DbSet<BiasTagSettings> BiasTagSettings { get; set; }
    DbSet<StationDetails> StationDetails { get; set; }
    DbSet<EquipmentGroup> EquipmentGroups { get; set; }
    DbSet<EquipmentSection> EquipmentSections { get; set; }
    DbSet<EquipmentContent> EquipmentContents { get; set; }
    DbSet<Chart> Charts { get; set; }
    DbSet<IntegrationEventLogEntry> IntegrationEventLogEntries { get; set; }
    DbSet<Report> Reports { get; set; }
    DbSet<ReportColumn> ReportColumns { get; set; }
    DbSet<Alert> Alerts { get; set; }
    DbSet<AlertModel> AlertModels { get; set; }
    DbSet<AlertContentModel> AlertContentModels { get; set; }
    DbSet<AlertCriteriaModel> AlertCriteriaModels { get; set; }
    DatabaseFacade Database { get; }
    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    Task<DatabaseTransaction> BeginTransactionAsync(CancellationToken cancellationToken);
}
