using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class ReportDateRangeInfoConfiguration
    : IEntityTypeConfiguration<ReportDateRangeInfo>
{
    public void Configure(EntityTypeBuilder<ReportDateRangeInfo> builder)
    {
        // Configure report id as primary key
        builder.HasKey(dateRangeInfo => dateRangeInfo.Id);

        builder.Property(dateRangeInfo => dateRangeInfo.Id)
            .ValueGeneratedNever();

        // Configure report date range info using TPH (Table-Per-Hierarchy)
        builder.HasDiscriminator<string>("DateRangeInfoType")
            .HasValue<RelativeReportDateRange>("Relative")
            .HasValue<FixedReportDateRange>("Fixed");
    }
}