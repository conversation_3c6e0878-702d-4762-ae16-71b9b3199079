using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class WeatherBiasSettingsConfiguration
    : IEntityTypeConfiguration<WeatherBiasSettings>
{
    public void Configure(EntityTypeBuilder<WeatherBiasSettings> builder)
    {
        builder.HasKey(wbs => wbs.Id);

        builder.Property(wbs => wbs.Id)
            .ValueGeneratedNever();

        builder.Property(wbs => wbs.BiasType)
            .HasConversion(
                v => v.ToString(), // Convert enum to string
                v => Enum.Parse<WeatherBiasType>(v));

        builder.Property(wbs => wbs.TagSource)
            .HasConversion(
                v => v.ToString(), // Convert enum to string
                v => Enum.Parse<WeatherTagSource>(v));

        builder.Property(wbs => wbs.BiasServiceSource)
            .HasConversion(
                v => v.ToString(), // Convert enum to string
                v => Enum.Parse<WeatherService>(v));

        builder.OwnsOne(
            wbs => wbs.BiasLimit, biasLimit =>
            {
                biasLimit
                    .Property(bl => bl.Min)
                    .HasColumnName("Min");
                biasLimit
                    .Property(bl => bl.Max)
                    .HasColumnName("Max");
            });

        builder.OwnsOne(
            wbs => wbs.WindEffect, windEffect =>
            {
                windEffect
                    .Property(we => we.IsWindEffectsEnabled)
                    .HasColumnName("IsWindEffectEnabled");
                windEffect
                    .Property(we => we.Expression)
                    .HasColumnName("Expression");
            });
    }
}