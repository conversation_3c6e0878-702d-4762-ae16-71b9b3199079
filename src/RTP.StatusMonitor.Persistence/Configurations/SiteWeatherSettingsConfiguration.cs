using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class SiteWeatherSettingsConfiguration
    : IEntityTypeConfiguration<SiteWeatherSettings>
{
    public void Configure(EntityTypeBuilder<SiteWeatherSettings> builder)
    {
        builder.HasKey(sws => sws.Id);

        builder.Property(sws => sws.Id)
            .ValueGeneratedNever();

        // Configure forecast settings
        builder.OwnsOne(
            sws => sws.ForecastSettings, forecastSettings =>
            {
                forecastSettings
                    .Property(fs => fs.IsForecastEnabled)
                    .HasColumnName("IsForecastEnabled");
                forecastSettings
                    .Property(fs => fs.IsMetric)
                    .HasColumnName("IsMetric");
                forecastSettings
                    .Property(fs => fs.ForecastDays)
                    .HasColumnName("ForecastDays");
                forecastSettings
                    .Property(fs => fs.ForecastServiceSource)
                    .HasColumnName("ForecastServiceSource")
                    .HasConversion(
                        v => v.ToString(),
                        v => Enum.Parse<WeatherService>(v));
            });

        // Configure lightning strike settings
        builder.OwnsOne(
            sws => sws.LightningStrikeSettings, lightningStrikeSettings =>
            {
                lightningStrikeSettings
                    .Property(lss => lss.IsLightningStrikesEnabled)
                    .HasColumnName("IsLightningStrikesEnabled");
                lightningStrikeSettings
                    .Property(lss => lss.LightningRadius)
                    .HasColumnName("LightningRadius");
            });

        // Configure relationship with site (1-to-1)
        // When the weather settings is deleted, the site will set the weather settings to null
        builder.HasOne(sws => sws.Site)
            .WithOne(s => s.SiteWeatherSettings)
            .HasForeignKey<SiteWeatherSettings>(sws => sws.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure relationship with bias settings (1-to-many)
        // When the weather settings is deleted, the bias settings will be deleted as well
        builder.HasMany(sws => sws.WeatherBiasSettings)
            .WithOne()
            .HasForeignKey(wbs => wbs.SiteWeatherSettingsId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure relationship with bias tags settings (1-to-many)
        // When the weather settings is deleted, all bias tags settings will be deleted as well
        builder.HasMany(sws => sws.BiasTagSettings)
            .WithOne()
            .HasForeignKey(wbs => wbs.SiteWeatherSettingsId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}