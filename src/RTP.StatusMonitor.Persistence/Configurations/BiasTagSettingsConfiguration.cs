using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Persistence.Configurations;
internal sealed class BiasTagSettingsConfiguration
    : IEntityTypeConfiguration<BiasTagSettings>
{
    public void Configure(EntityTypeBuilder<BiasTagSettings> builder)
    {
        builder.HasKey(sws => sws.Id);

        builder.Property(sws => sws.Id)
            .ValueGeneratedNever();

        // Configure the computed expression property
        builder.OwnsOne(
            sws => sws.ComputedExpression, computedExpression =>
            {
                computedExpression
                    .Property(ce => ce.Value)
                    .HasColumnName("Expression");
            });

        builder.OwnsOne(
            sws => sws.UnitId, unitId =>
            {
                unitId
                    .Property(ce => ce.Value)
                    .HasColumnName("UnitId");
            });

        builder.Property(sws => sws.BiasType)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<WeatherBiasType>(v));

        builder.Property(sws => sws.TagSource)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<WeatherTagSource>(v));
    }
}