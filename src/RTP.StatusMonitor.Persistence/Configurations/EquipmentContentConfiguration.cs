using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.EquipmentContent;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class EquipmentContentConfiguration
  : IEntityTypeConfiguration<EquipmentContent>
{
    public void Configure(EntityTypeBuilder<EquipmentContent> builder)
    {
        builder.Ignore(e => e.Value);

        builder.OwnsOne(content => content.Position, position =>
        {
            position.Property(p => p.Row).HasColumnName("Row");
            position.Property(p => p.Column).HasColumnName("Column");
        });

        builder.OwnsOne(content => content.Range, range =>
        {
            range.Property(r => r.Min).HasColumnName("Min");
            range.Property(r => r.Max).HasColumnName("Max");
            range.Property(r => r.Interval).HasColumnName("Interval");
        });

        builder.OwnsOne(
            content => content.AuditInfo, auditInfo =>
            {
                auditInfo
                    .Property(a => a.DateCreated)
                    .HasColumnName("DateCreated");
                auditInfo
                    .Property(a => a.CreatedBy)
                    .HasColumnName("CreatedBy");
                auditInfo
                    .Property(a => a.DateModified)
                    .HasColumnName("DateModified");
                auditInfo
                    .Property(a => a.ModifiedBy)
                    .HasColumnName("ModifiedBy");
            });

        // When an equipment section is deleted
        // Then all associated contents will be deleted
        builder.HasOne(g => g.EquipmentSection)
            .WithMany(e => e.EquipmentContents)
            .OnDelete(DeleteBehavior.Cascade);

        // When a block is deleted
        // Then no action will be taken on the associated group contents
        // NOTE - avoid multiple cascade paths with EF CORE
        builder.HasOne(g => g.Block)
            .WithMany(b => b.EquipmentContents)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
