using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class ReportColumnConfiguration
    : IEntityTypeConfiguration<ReportColumn>
{
    public void Configure(EntityTypeBuilder<ReportColumn> builder)
    {
        // Configure report column id as primary key
        builder.HasKey(reportColumn => reportColumn.Id);

        builder.Property(reportColumn => reportColumn.Id)
            .ValueGeneratedNever();

        // Configure report column expression (filter/calculation)
        builder.OwnsOne(
            reportColumn => reportColumn.Calculation,
            calculation => calculation
                .Property(e => e.Value)
                .HasColumnName("Calculation"));

        builder.OwnsOne(
            reportColumn => reportColumn.Filter,
            filter => filter
                .Property(e => e.Value)
                .HasColumnName("Filter"));

        // Configure report column sources
        builder
            .Property(reportColumn => reportColumn.Sources)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v.Select(x => x.ToString()).ToList()), // Convert list of enum values to json
                v => JsonConvert.DeserializeObject<List<DataSource>>(v) ?? new List<DataSource>())
            .Metadata
            .SetValueComparer(
                new ValueComparer<IReadOnlyList<DataSource>>(
                (c1, c2) => c1!.SequenceEqual(c2!),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList())); // Use value comparer to compare lists 
    }
}
