using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.EquipmentGroup;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class EquipmentGroupConfiguration : IEntityTypeConfiguration<EquipmentGroup>
{
  public void Configure(EntityTypeBuilder<EquipmentGroup> builder)
  {
    // When a site is delete
    // Then all associated equipment groups will be deleted
    builder.HasOne(e => e.Site)
      .WithMany(s => s.EquipmentGroups)
      .OnDelete(DeleteBehavior.Cascade);
  }
}