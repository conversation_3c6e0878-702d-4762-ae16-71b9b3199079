using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class GroupPermissionConfiguration : IEntityTypeConfiguration<GroupPermission>
{
  public void Configure(EntityTypeBuilder<GroupPermission> builder)
  {
    // Configure the composite key
    builder.HasKey(g => new { g.GroupId, g.SiteId });

    // Configure the relationships
    builder.HasOne(g => g.Group)
           .WithMany(ug => ug.GroupPermissions)
           .HasForeignKey(g => g.GroupId)
           .OnDelete(DeleteBehavior.Cascade);

    builder.HasOne(g => g.Site)
           .WithMany(s => s.GroupPermissions)
           .HasForeignKey(g => g.SiteId)
           .OnDelete(DeleteBehavior.Cascade);

    // Configure the table name
    builder.ToTable("GroupPermissions");
  }
}