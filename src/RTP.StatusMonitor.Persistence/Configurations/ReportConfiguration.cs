using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class ReportConfiguration
    : IEntityTypeConfiguration<Report>
{
    public void Configure(EntityTypeBuilder<Report> builder)
    {
        // Configure report id as primary key
        builder.HasKey(report => report.Id);

        builder.Property(report => report.Id)
            .ValueGeneratedNever();

        // Configure audit info
        builder.OwnsOne(
            report => report.AuditInfo, auditInfo =>
            {
                auditInfo
                    .Property(a => a.DateCreated)
                    .HasColumnName("DateCreated");
                auditInfo
                    .Property(a => a.CreatedBy)
                    .HasColumnName("CreatedBy");
                auditInfo
                    .Property(a => a.DateModified)
                    .HasColumnName("DateModified");
                auditInfo
                    .Property(a => a.ModifiedBy)
                    .HasColumnName("ModifiedBy");
            });

        // Configure report type
        builder.Property(report => report.ReportType)
            .HasConversion(
                v => v.ToString(), // Convert enum to string
                v => Enum.Parse<ReportType>(v));

        // Configure report filtering
        builder.Property(report => report.Filters)
            .HasColumnName("Filters")
            .HasConversion(
                v => JsonConvert.SerializeObject(v, new JsonSerializerSettings
                {
                    // NOTE - keep enum as string in json
                    Converters = new List<JsonConverter> { new StringEnumConverter() }
                }),
                v => JsonConvert.DeserializeObject<List<ReportFilter>>(v, new JsonSerializerSettings
                {
                    Converters = new List<JsonConverter> { new StringEnumConverter() }
                }) ?? new List<ReportFilter>())
            .Metadata.SetValueComparer(
                new ValueComparer<IReadOnlyList<ReportFilter>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        // Configure report grouping
        // Project each properties to a column
        builder.OwnsOne(report => report.TimeGrouping, groupingBuilder =>
        {
            groupingBuilder.Property(g => g.GroupingInterval)
                .HasColumnName("GroupingInterval")
                .HasConversion(
                    v => v.ToString(),
                    v => Enum.Parse<ReportGroupingInterval>(v));

            groupingBuilder
                .Property(g => g.GroupingAggregates)
                .HasColumnName("GroupingAggregates")
                .HasConversion(
                    v => JsonConvert
                        .SerializeObject(v.Select(e => e.ToString()).ToList()),
                    v => JsonConvert.DeserializeObject<List<string>>(v)!
                            .Select(Enum.Parse<AggregateType>)
                            .ToList() ?? new List<AggregateType>())
                .Metadata.SetValueComparer(new ValueComparer<IReadOnlyList<AggregateType>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));
        });

        // Configure report grouping
        // Project each properties to a column
        builder.Property(report => report.VariableGrouping)
            .HasColumnName("VariableGrouping")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ReportVariableGrouping>(v));

        // Configure report schedule
        builder.OwnsOne(
            report => report.Schedule, schedule =>
            {
                schedule
                    .Property(s => s.IsEnabled)
                    .HasColumnName("IsEnabled");
                schedule
                    .Property(s => s.ProcessInterval)
                    .HasColumnName("ProcessInterval")
                    .HasConversion(
                        v => v.ToString(), // Convert enum to string
                        v => Enum.Parse<ReportProcessInterval>(v));
            });

        // Configure report layout
        builder.OwnsOne(
            report => report.ReportLayout,
            layout => layout.Property(l => l.Value)
                .HasColumnName("Layout"));

        // Configure report file format as string
        builder.Property(report => report.FileFormat)
            .HasColumnName("FileFormat")
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<ReportFileFormat>(v));

        // Configure group ids as a list
        builder
            .Property(report => report.UserGroupsAllowed)
            .HasConversion(
                groupIds => JsonConvert.SerializeObject(groupIds),
                groupIds => JsonConvert.DeserializeObject<List<Guid>>(groupIds)!)
            .Metadata
            .SetValueComparer(
                new ValueComparer<IReadOnlyList<Guid>>(
                (c1, c2) => c1!.SequenceEqual(c2!),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        // Configure blocks in report
        builder
            .Property(report => report.BlocksInReport)
            .HasConversion(
                blocksInReport => JsonConvert.SerializeObject(blocksInReport),
                blocksInReport => JsonConvert.DeserializeObject<List<Guid>>(blocksInReport)!)
            .Metadata
            .SetValueComparer(
                new ValueComparer<IReadOnlyList<Guid>>(
                (c1, c2) => c1!.SequenceEqual(c2!),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        // Cascade delete report columns when report is deleted
        builder.HasMany(
            report => report.ReportColumns)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);

        // Configure report date range info with cascade delete
        builder.HasOne(report => report.DateRangeInfo)
            .WithOne()
            .HasForeignKey<ReportDateRangeInfo>(
                dateRangeInfo => dateRangeInfo.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}