using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class DataClientStatusVariableConfiguration 
: IEntityTypeConfiguration<DataClientStatusVariable>
{
  public void Configure(EntityTypeBuilder<DataClientStatusVariable> builder)
  {
      // restrict on variable deletion -> delete status variable first
    builder.HasOne(d => d.DataClientVariable)
        .WithOne(d => d.DataClientStatusVariable)
        .HasForeignKey<DataClientStatusVariable>(d => d.DataClientVariableId)
        .OnDelete(DeleteBehavior.Restrict);
  }
}