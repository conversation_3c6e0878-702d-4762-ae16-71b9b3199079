using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class DataClientGroupVariableConfiguration 
: IEntityTypeConfiguration<DataClientGroupVariable>
{
  public void Configure(EntityTypeBuilder<DataClientGroupVariable> builder)
  {
      // many-to-many rel DataClientGroup-DataClientVariable
    builder.HasKey(d => new
        {
          d.DataClientGroupId,
          d.DataClientVariableId
        });

    // restrict on group deletion -> delete the join first
    builder.HasOne(d => d.DataClientGroup)
        .WithMany(d => d.DataClientGroupVariables)
        .HasForeignKey(d => d.DataClientGroupId)
        .OnDelete(DeleteBehavior.Restrict);

    // restrict on variable deletion -> delete the join first
    builder.HasOne(d => d.DataClientVariable)
        .WithMany(d => d.DataClientGroupVariables)
        .HasForeignKey(d => d.DataClientVariableId)
        .OnDelete(DeleteBehavior.Restrict);
  }
}