using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.Persistence.Configurations;

internal sealed class DisplayViewPermissionConfiguration : IEntityTypeConfiguration<DisplayViewPermission>
{
  public void Configure(EntityTypeBuilder<DisplayViewPermission> builder)
  {
    // many-to-many rel UserGroup-Display
    builder.HasKey(d => new
    {
      d.DisplayId,
      d.UserGroupId
    });
  }
}