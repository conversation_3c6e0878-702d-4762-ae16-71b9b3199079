using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.Persistence.Configurations.Alerts;

internal sealed class AlertConfiguration
    : IEntityTypeConfiguration<Alert>
{
    public void Configure(EntityTypeBuilder<Alert> builder)
    {
        builder.ToTable("Alerts");

        // Configure primary key
        builder.HasKey(alert => alert.Id);

        // Configure audit info
        builder.OwnsOne(alert => alert.AuditInfo, auditInfo =>
        {
            auditInfo.Property(a => a.DateCreated)
                .HasColumnName("DateCreated");
            auditInfo.Property(a => a.CreatedBy)
                .HasColumnName("CreatedBy");
            auditInfo.Property(a => a.DateModified)
                .HasColumnName("DateModified");
            auditInfo.Property(a => a.ModifiedBy)
                .HasColumnName("ModifiedBy");
        });

        // Store the alert type as a string
        builder.Property(alert => alert.AlertType)
            .HasConversion(v => v.ToString(), v => Enum.Parse<AlertType>(v));

        // Store the notification type as a string
        builder.Property(alert => alert.NotificationType)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<NotificationType>(v));

        // Configure alert criteria
        builder.OwnsOne(
            alert => alert.Criteria,
            criteria => criteria
                .Property(c => c.Value)
                .HasColumnName("Criteria"));

        // Store list of run times as JSON string
        builder.Property(alert => alert.RunTimes)
            .HasColumnName("RunTimes")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<TimeOnly>>(v) ?? new List<TimeOnly>())
            .Metadata.SetValueComparer(
                new ValueComparer<List<TimeOnly>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        // Store the frequency as a string
        builder.Property(alert => alert.Frequency)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<AlertFrequency>(v));

        // Store alert details as JSON string
        builder.Property(alert => alert.AlertDetails)
            .HasColumnName("AlertDetails")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<AlertDetails>(v) ??
                    new AlertDetails(
                        string.Empty,
                        new List<string>(),
                        new AlertBody(
                            new Dictionary<string, object>(),
                            new List<ReportRenderConfig>()),
                        new List<AlertAttachment>()));

        // Configure the relationship between alert and site
        builder.HasOne(alert => alert.Site)
            .WithMany()
            .HasForeignKey(alert => alert.SiteId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

