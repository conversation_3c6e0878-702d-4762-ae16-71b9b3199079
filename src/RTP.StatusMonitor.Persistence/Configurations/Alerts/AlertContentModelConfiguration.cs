using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.Persistence.Configurations.Alerts;

internal sealed class AlertContentModelConfiguration
    : IEntityTypeConfiguration<AlertContentModel>
{
    public void Configure(EntityTypeBuilder<AlertContentModel> builder)
    {
        builder.ToTable("AlertContents");

        // Configure primary key
        builder.HasKey(content => content.AlertContentId);

        builder.Property(content => content.Type)
            .HasConversion(v => v.ToString(), v => Enum.Parse<AlertContentType>(v));
    }
}
