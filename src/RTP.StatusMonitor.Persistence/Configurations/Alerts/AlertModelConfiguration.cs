using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.Persistence.Configurations.Alerts;

internal sealed class AlertModelConfiguration
    : IEntityTypeConfiguration<AlertModel>
{
    public void Configure(EntityTypeBuilder<AlertModel> builder)
    {
        builder.ToTable("AlertsBeta");

        // Configure primary key
        builder.HasKey(alert => alert.AlertId);

        // Configure one-to-many relationship with AlertContentModel
        builder.HasMany(alert => alert.Contents)
            .WithOne(content => content.Alert)
            .HasForeignKey(content => content.AlertId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(alert => alert.Criterias)
            .WithOne(criteria => criteria.Alert)
            .HasForeignKey(criteria => criteria.AlertId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(alert => alert.TriggerType)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<AlertTriggerType>(v));

        // Store list of run times as JSON string
        builder.Property(alert => alert.RunTimes)
            .HasColumnName("RunTimes")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<TimeOnly>>(v) ?? new List<TimeOnly>())
            .Metadata.SetValueComparer(
                new ValueComparer<List<TimeOnly>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        builder.Property(alert => alert.AlertTypes)
            .HasColumnName("AlertTypes")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v) ?? new List<string>())
            .Metadata.SetValueComparer(
                new ValueComparer<List<string>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        // Store list of days of week as string
        builder.Property(alert => alert.DaysOfWeek)
            .HasColumnName("DaysOfWeek")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v) ?? new List<string>())
            .Metadata.SetValueComparer(
                new ValueComparer<List<string>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        builder.Property(alert => alert.DaysOfMonth)
            .HasColumnName("DaysOfMonth")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v) ?? new List<int>())
            .Metadata.SetValueComparer(
                new ValueComparer<List<int>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        builder.Property(alert => alert.DaysOfYear)
            .HasColumnName("DaysOfYear")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v) ?? new List<int>())
                .Metadata.SetValueComparer(
                new ValueComparer<List<int>>(
                    (c1, c2) => c1!.SequenceEqual(c2!),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

        // Configure the relationship with Site without navigation property
        builder.Property(alert => alert.SiteId)
            .IsRequired();

        builder.HasOne<Site>()  // Type specified but no navigation property
            .WithMany()         // No navigation property on Site side either
            .HasForeignKey(alert => alert.SiteId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

