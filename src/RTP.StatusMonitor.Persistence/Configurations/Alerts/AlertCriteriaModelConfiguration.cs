using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.Persistence.Configurations.Alerts;
internal sealed class AlertCriteriaModelConfiguration
    : IEntityTypeConfiguration<AlertCriteriaModel>
{
    public void Configure(EntityTypeBuilder<AlertCriteriaModel> builder)
    {
        builder.ToTable("AlertCriteriaTriggers");

        // Configure primary key
        builder.HasKey(alert => alert.AlertCriteriaId);
    }
}

