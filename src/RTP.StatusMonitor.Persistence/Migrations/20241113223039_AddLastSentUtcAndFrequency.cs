using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddLastSentUtcAndFrequency : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "FrequencyInMinutes",
                table: "Alerts",
                newName: "MinimumMinutesBetweenAlerts");

            migrationBuilder.AddColumn<string>(
                name: "Frequency",
                table: "Alerts",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastSentUtc",
                table: "Alerts",
                type: "datetime2",
                nullable: true);

            // Default all alerts to None
            migrationBuilder.Sql("UPDATE Alerts SET Frequency = 'None'");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Frequency",
                table: "Alerts");

            migrationBuilder.DropColumn(
                name: "LastSentUtc",
                table: "Alerts");

            migrationBuilder.RenameColumn(
                name: "MinimumMinutesBetweenAlerts",
                table: "Alerts",
                newName: "FrequencyInMinutes");
        }
    }
}
