using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddSiteWeatherStations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SiteWeatherStation_Sites_SiteId",
                table: "SiteWeatherStation");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SiteWeatherStation",
                table: "SiteWeatherStation");

            migrationBuilder.RenameTable(
                name: "SiteWeatherStation",
                newName: "SiteWeatherStations");

            migrationBuilder.RenameIndex(
                name: "IX_SiteWeatherStation_SiteId",
                table: "SiteWeatherStations",
                newName: "IX_SiteWeatherStations_SiteId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SiteWeatherStations",
                table: "SiteWeatherStations",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SiteWeatherStations_Sites_SiteId",
                table: "SiteWeatherStations",
                column: "SiteId",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SiteWeatherStations_Sites_SiteId",
                table: "SiteWeatherStations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SiteWeatherStations",
                table: "SiteWeatherStations");

            migrationBuilder.RenameTable(
                name: "SiteWeatherStations",
                newName: "SiteWeatherStation");

            migrationBuilder.RenameIndex(
                name: "IX_SiteWeatherStations_SiteId",
                table: "SiteWeatherStation",
                newName: "IX_SiteWeatherStation_SiteId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SiteWeatherStation",
                table: "SiteWeatherStation",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SiteWeatherStation_Sites_SiteId",
                table: "SiteWeatherStation",
                column: "SiteId",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
