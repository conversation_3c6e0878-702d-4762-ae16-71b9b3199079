using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddStatusVariable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataClientStatusVariables",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Tag = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Abbreviation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Expression = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Color = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false),
                    DataClientId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DataClientVariableId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataClientStatusVariables", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataClientStatusVariables_DataClients_DataClientId",
                        column: x => x.DataClientId,
                        principalTable: "DataClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataClientStatusVariables_DataClientVariables_DataClientVariableId",
                        column: x => x.DataClientVariableId,
                        principalTable: "DataClientVariables",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataClientStatusVariables_DataClientId",
                table: "DataClientStatusVariables",
                column: "DataClientId");

            migrationBuilder.CreateIndex(
                name: "IX_DataClientStatusVariables_DataClientVariableId",
                table: "DataClientStatusVariables",
                column: "DataClientVariableId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataClientStatusVariables");
        }
    }
}
