using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddFixedDateRangeToReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Reports");

            migrationBuilder.AddColumn<DateTime>(
                name: "StartDate",
                table: "Reports",
                type: "datetime2",
                nullable: true);

            migrationBuilder.DropColumn(
                name: "EndDate",
                table: "Reports");

            migrationBuilder.AddColumn<DateTime>(
                name: "EndDate",
                table: "Reports",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DateRangeType",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "EndOffset",
                table: "Reports",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StartOffset",
                table: "Reports",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateRangeType",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "EndOffset",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "StartOffset",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Reports");

            migrationBuilder.AddColumn<int>(
                name: "StartDate",
                table: "Reports",
                type: "int",
                nullable: false);

            migrationBuilder.DropColumn(
                name: "EndDate",
                table: "Reports");

            migrationBuilder.AddColumn<int>(
                name: "EndDate",
                table: "Reports",
                type: "int",
                nullable: false);

        }
    }
}
