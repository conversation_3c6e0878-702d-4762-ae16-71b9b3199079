using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddBlocksToReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "MainFilters",
                table: "Reports",
                newName: "Filters");

            migrationBuilder.AddColumn<string>(
                name: "BlocksInReport",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BlocksInReport",
                table: "Reports");

            migrationBuilder.RenameColumn(
                name: "Filters",
                table: "Reports",
                newName: "MainFilters");
        }
    }
}
