using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddSiteWeatherSettings : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SiteWeatherSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsForecastEnabled = table.Column<bool>(type: "bit", nullable: false),
                    ForecastDays = table.Column<int>(type: "int", nullable: false),
                    IsMetric = table.Column<bool>(type: "bit", nullable: false),
                    ForecastServiceSource = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsLightningStrikesEnabled = table.Column<bool>(type: "bit", nullable: false),
                    LightningRadius = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SiteWeatherSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SiteWeatherSettings_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WeatherBiasSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsBiasEnabled = table.Column<bool>(type: "bit", nullable: false),
                    BiasType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TagSource = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BiasServiceSource = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Min = table.Column<double>(type: "float", nullable: true),
                    Max = table.Column<double>(type: "float", nullable: true),
                    IsWindEffectEnabled = table.Column<bool>(type: "bit", nullable: false),
                    Expression = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SiteWeatherSettingsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WeatherBiasSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WeatherBiasSettings_SiteWeatherSettings_SiteWeatherSettingsId",
                        column: x => x.SiteWeatherSettingsId,
                        principalTable: "SiteWeatherSettings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SiteWeatherSettings_SiteId",
                table: "SiteWeatherSettings",
                column: "SiteId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WeatherBiasSettings_SiteWeatherSettingsId",
                table: "WeatherBiasSettings",
                column: "SiteWeatherSettingsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WeatherBiasSettings");

            migrationBuilder.DropTable(
                name: "SiteWeatherSettings");
        }
    }
}
