// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RTP.StatusMonitor.Persistence;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20240216200300_AddBlocksToReport")]
    partial class AddBlocksToReport
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Port")
                        .HasColumnType("int");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Blocks");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Chart.Chart", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ChartAreas")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChartStatuses")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Charts");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Customer.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlobContainer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyLogoUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId")
                        .IsUnique();

                    b.ToTable("DataClients");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCustomerAccess")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.Property<Guid>("DataClientGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Max")
                        .HasColumnType("float");

                    b.Property<double>("Min")
                        .HasColumnType("float");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<int>("Thickness")
                        .HasColumnType("int");

                    b.HasKey("DataClientGroupId", "DataClientVariableId");

                    b.HasIndex("DataClientVariableId");

                    b.ToTable("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Abbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Expression")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.HasIndex("DataClientVariableId")
                        .IsUnique();

                    b.ToTable("DataClientStatusVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("DefaultValue")
                        .HasColumnType("float");

                    b.Property<string>("EngUnits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayComponent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Layout")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Displays");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.Property<Guid>("DisplayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.HasKey("DisplayId", "UserGroupId");

                    b.HasIndex("UserGroupId");

                    b.ToTable("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("GroupId", "SiteId");

                    b.HasIndex("SiteId");

                    b.ToTable("GroupPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.StationDetails", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Api")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DirectionFrom")
                        .HasColumnType("float");

                    b.Property<string>("DirectionFromTag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DistanceFrom")
                        .HasColumnType("float");

                    b.Property<double>("Elevation")
                        .HasColumnType("float");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("StationDetails");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Unit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId");

                    b.ToTable("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.UserGroup", b =>
                {
                    b.Property<Guid>("UserGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserGroupId");

                    b.ToTable("UserGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentContent.EquipmentContent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EquipmentSectionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Format")
                        .HasColumnType("int");

                    b.Property<string>("Suffix")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlockId");

                    b.HasIndex("EquipmentSectionId");

                    b.ToTable("EquipmentContents");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Width")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("EquipmentGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EquipmentGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<int>("SectionType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentGroupId");

                    b.ToTable("EquipmentSections");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.IntegrationEventLogEntry.IntegrationEventLogEntry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TriggeredBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("IntegrationEventLogEntries");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlocksInReport")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filters")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Filters");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReportType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserGroupsAllowed")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Reports");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportColumn", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Calculation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngUnits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filter")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoOfDecimals")
                        .HasColumnType("int");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<Guid?>("ReportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Sources")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UnitId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ReportId");

                    b.ToTable("ReportColumns");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Altitude")
                        .HasColumnType("float");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsMetric")
                        .HasColumnType("bit");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LocationKey")
                        .HasColumnType("int");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("MapLocation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("Blocks")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Chart.Chart", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("Charts")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithOne("DataClient")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClient", "BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientGroups")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientGroup", "DataClientGroup")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClientGroup");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientStatusVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithOne("DataClientStatusVariable")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", "DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClient");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Display", "Display")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("DisplayId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.UserGroup", "UserGroup")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Display");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.UserGroup", "Group")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.StationDetails", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("StationDetails")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Unit", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithMany("Units")
                        .HasForeignKey("BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentContent.EquipmentContent", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithMany("EquipmentContents")
                        .HasForeignKey("BlockId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", "EquipmentSection")
                        .WithMany("EquipmentContents")
                        .HasForeignKey("EquipmentSectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.EquipmentContent.ContentPosition", "Position", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Column")
                                .HasColumnType("int")
                                .HasColumnName("Column");

                            b1.Property<int>("Row")
                                .HasColumnType("int")
                                .HasColumnName("Row");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.EquipmentContent.ContentRange", "Range", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<double?>("Interval")
                                .HasColumnType("float")
                                .HasColumnName("Interval");

                            b1.Property<double?>("Max")
                                .HasColumnType("float")
                                .HasColumnName("Max");

                            b1.Property<double?>("Min")
                                .HasColumnType("float")
                                .HasColumnName("Min");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Shared.AuditInfo", "AuditInfo", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("CreatedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("CreatedBy");

                            b1.Property<DateTime?>("DateCreated")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateCreated");

                            b1.Property<DateTime?>("DateModified")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateModified");

                            b1.Property<string>("ModifiedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ModifiedBy");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.Navigation("AuditInfo")
                        .IsRequired();

                    b.Navigation("Block");

                    b.Navigation("EquipmentSection");

                    b.Navigation("Position")
                        .IsRequired();

                    b.Navigation("Range")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("EquipmentGroups")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", "EquipmentGroup")
                        .WithMany("EquipmentSections")
                        .HasForeignKey("EquipmentGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentGroup");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.OwnsOne("RTP.StatusMonitor.Domain.Shared.AuditInfo", "AuditInfo", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("CreatedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("CreatedBy");

                            b1.Property<DateTime?>("DateCreated")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateCreated");

                            b1.Property<DateTime?>("DateModified")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateModified");

                            b1.Property<string>("ModifiedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ModifiedBy");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportDateRange", "DateRange", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("EndDate")
                                .HasColumnType("int")
                                .HasColumnName("EndDate");

                            b1.Property<int>("StartDate")
                                .HasColumnType("int")
                                .HasColumnName("StartDate");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportGrouping", "Grouping", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("GroupingAggregates")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("GroupingAggregates");

                            b1.Property<string>("GroupingInterval")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("GroupingInterval");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportSchedule", "Schedule", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsEnabled")
                                .HasColumnType("bit")
                                .HasColumnName("IsEnabled");

                            b1.Property<string>("ProcessInterval")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ProcessInterval");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.Navigation("AuditInfo")
                        .IsRequired();

                    b.Navigation("DateRange")
                        .IsRequired();

                    b.Navigation("Grouping")
                        .IsRequired();

                    b.Navigation("Schedule")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportColumn", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Reports.Report", null)
                        .WithMany("ReportColumns")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Customer.Customer", "Customer")
                        .WithMany("Sites")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.Navigation("DataClient")
                        .IsRequired();

                    b.Navigation("EquipmentContents");

                    b.Navigation("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Customer.Customer", b =>
                {
                    b.Navigation("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Navigation("DataClientGroups");

                    b.Navigation("DataClientStatusVariables");

                    b.Navigation("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Navigation("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Navigation("DataClientGroupVariables");

                    b.Navigation("DataClientStatusVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Navigation("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.UserGroup", b =>
                {
                    b.Navigation("DisplayViewPermissions");

                    b.Navigation("GroupPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.Navigation("EquipmentSections");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.Navigation("EquipmentContents");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.Navigation("ReportColumns");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.Navigation("Blocks");

                    b.Navigation("Charts");

                    b.Navigation("EquipmentGroups");

                    b.Navigation("GroupPermissions");

                    b.Navigation("StationDetails");
                });
#pragma warning restore 612, 618
        }
    }
}
