using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddBiasTagSettings : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BiasTagSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Tag = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Expression = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BiasType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TagSource = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SiteWeatherSettingsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BiasTagSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BiasTagSettings_SiteWeatherSettings_SiteWeatherSettingsId",
                        column: x => x.SiteWeatherSettingsId,
                        principalTable: "SiteWeatherSettings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BiasTagSettings_SiteWeatherSettingsId",
                table: "BiasTagSettings",
                column: "SiteWeatherSettingsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BiasTagSettings");
        }
    }
}
