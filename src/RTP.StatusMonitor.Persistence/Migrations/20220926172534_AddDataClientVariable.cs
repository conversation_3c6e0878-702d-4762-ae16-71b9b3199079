using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddDataClientVariable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataClientVariables",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Tag = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Alias = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    EngUnits = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DefaultValue = table.Column<double>(type: "float", nullable: false),
                    DataClientId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataClientVariables", x => x.Id);
                    table.<PERSON><PERSON>ey(
                        name: "FK_DataClientVariables_DataClients_DataClientId",
                        column: x => x.DataClientId,
                        principalTable: "DataClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataClientVariables_DataClientId",
                table: "DataClientVariables",
                column: "DataClientId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataClientVariables");
        }
    }
}
