// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RTP.StatusMonitor.Persistence;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20241113223039_AddLastSentUtcAndFrequency")]
    partial class AddLastSentUtcAndFrequency
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Alerts.Alert", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AlertDetails")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AlertDetails");

                    b.Property<string>("AlertType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Frequency")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastSentUtc")
                        .HasColumnType("datetime2");

                    b.Property<int>("MinimumMinutesBetweenAlerts")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NotificationType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RunTimes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RunTimes");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("UploadToBlob")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Alerts", (string)null);
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Port")
                        .HasColumnType("int");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Blocks");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Chart.Chart", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ChartAreas")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChartStatuses")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Charts");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Customer.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlobContainer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyLogoUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId")
                        .IsUnique();

                    b.ToTable("DataClients");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCustomerAccess")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.Property<Guid>("DataClientGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Max")
                        .HasColumnType("float");

                    b.Property<double>("Min")
                        .HasColumnType("float");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<int>("Thickness")
                        .HasColumnType("int");

                    b.HasKey("DataClientGroupId", "DataClientVariableId");

                    b.HasIndex("DataClientVariableId");

                    b.ToTable("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Abbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Expression")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.HasIndex("DataClientVariableId")
                        .IsUnique();

                    b.ToTable("DataClientStatusVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("DefaultValue")
                        .HasColumnType("float");

                    b.Property<string>("EngUnits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayComponent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Layout")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Displays");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.Property<Guid>("DisplayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.HasKey("DisplayId", "UserGroupId");

                    b.HasIndex("UserGroupId");

                    b.ToTable("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("GroupId", "SiteId");

                    b.HasIndex("SiteId");

                    b.ToTable("GroupPermissions", (string)null);
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.StationDetails", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Api")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DirectionFrom")
                        .HasColumnType("float");

                    b.Property<string>("DirectionFromTag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DistanceFrom")
                        .HasColumnType("float");

                    b.Property<double>("Elevation")
                        .HasColumnType("float");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("StationDetails");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentContent.EquipmentContent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EquipmentSectionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Format")
                        .HasColumnType("int");

                    b.Property<string>("Suffix")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlockId");

                    b.HasIndex("EquipmentSectionId");

                    b.ToTable("EquipmentContents");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Width")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("EquipmentGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EquipmentGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<int>("SectionType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentGroupId");

                    b.ToTable("EquipmentSections");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.IntegrationEventLogEntry.IntegrationEventLogEntry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TriggeredBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("IntegrationEventLogEntries");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlocksInReport")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Container")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileFormat")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileFormat");

                    b.Property<string>("Filters")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Filters");

                    b.Property<DateTime?>("LastProcessedUtc")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReportType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserGroupsAllowed")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VariableGrouping")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("VariableGrouping");

                    b.HasKey("Id");

                    b.ToTable("Reports");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportColumn", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngUnits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoOfDecimals")
                        .HasColumnType("int");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<Guid?>("ReportId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Sources")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UnitId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ReportId");

                    b.ToTable("ReportColumns");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportDateRangeInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DateRangeInfoType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ReportId")
                        .IsUnique();

                    b.ToTable("ReportDateRangeInfo");

                    b.HasDiscriminator<string>("DateRangeInfoType").HasValue("ReportDateRangeInfo");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Altitude")
                        .HasColumnType("float");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsMetric")
                        .HasColumnType("bit");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LocationKey")
                        .HasColumnType("int");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.BiasTagSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BiasType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SiteWeatherSettingsId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TagSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SiteWeatherSettingsId");

                    b.ToTable("BiasTagSettings");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId")
                        .IsUnique();

                    b.ToTable("SiteWeatherSettings");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.WeatherBiasSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BiasServiceSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BiasType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsBiasEnabled")
                        .HasColumnType("bit");

                    b.Property<Guid>("SiteWeatherSettingsId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TagSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UnitId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteWeatherSettingsId");

                    b.ToTable("WeatherBiasSettings");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Units.Unit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId");

                    b.ToTable("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.UserGroups.UserGroup", b =>
                {
                    b.Property<Guid>("UserGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserGroupId");

                    b.ToTable("UserGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.FixedReportDateRange", b =>
                {
                    b.HasBaseType("RTP.StatusMonitor.Domain.Reports.ReportDateRangeInfo");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasDiscriminator().HasValue("Fixed");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.RelativeReportDateRange", b =>
                {
                    b.HasBaseType("RTP.StatusMonitor.Domain.Reports.ReportDateRangeInfo");

                    b.Property<int>("EndOffset")
                        .HasColumnType("int");

                    b.Property<int>("StartOffset")
                        .HasColumnType("int");

                    b.HasDiscriminator().HasValue("Relative");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Alerts.Alert", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.Shared.AuditInfo", "AuditInfo", b1 =>
                        {
                            b1.Property<Guid>("AlertId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("CreatedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("CreatedBy");

                            b1.Property<DateTime?>("DateCreated")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateCreated");

                            b1.Property<DateTime?>("DateModified")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateModified");

                            b1.Property<string>("ModifiedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ModifiedBy");

                            b1.HasKey("AlertId");

                            b1.ToTable("Alerts");

                            b1.WithOwner()
                                .HasForeignKey("AlertId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.TimeSeries.Types.FilterExpression", "Criteria", b1 =>
                        {
                            b1.Property<Guid>("AlertId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Criteria");

                            b1.HasKey("AlertId");

                            b1.ToTable("Alerts");

                            b1.WithOwner()
                                .HasForeignKey("AlertId");
                        });

                    b.Navigation("AuditInfo")
                        .IsRequired();

                    b.Navigation("Criteria")
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("Blocks")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Chart.Chart", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("Charts")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithOne("DataClient")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClient", "BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientGroups")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientGroup", "DataClientGroup")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClientGroup");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientStatusVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithOne("DataClientStatusVariable")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", "DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClient");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Display", "Display")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("DisplayId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.UserGroups.UserGroup", "UserGroup")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Display");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.UserGroups.UserGroup", "Group")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.StationDetails", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("StationDetails")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentContent.EquipmentContent", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithMany("EquipmentContents")
                        .HasForeignKey("BlockId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", "EquipmentSection")
                        .WithMany("EquipmentContents")
                        .HasForeignKey("EquipmentSectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.EquipmentContent.ContentPosition", "Position", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Column")
                                .HasColumnType("int")
                                .HasColumnName("Column");

                            b1.Property<int>("Row")
                                .HasColumnType("int")
                                .HasColumnName("Row");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.EquipmentContent.ContentRange", "Range", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<double?>("Interval")
                                .HasColumnType("float")
                                .HasColumnName("Interval");

                            b1.Property<double?>("Max")
                                .HasColumnType("float")
                                .HasColumnName("Max");

                            b1.Property<double?>("Min")
                                .HasColumnType("float")
                                .HasColumnName("Min");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Shared.AuditInfo", "AuditInfo", b1 =>
                        {
                            b1.Property<Guid>("EquipmentContentId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("CreatedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("CreatedBy");

                            b1.Property<DateTime?>("DateCreated")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateCreated");

                            b1.Property<DateTime?>("DateModified")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateModified");

                            b1.Property<string>("ModifiedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ModifiedBy");

                            b1.HasKey("EquipmentContentId");

                            b1.ToTable("EquipmentContents");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentContentId");
                        });

                    b.Navigation("AuditInfo")
                        .IsRequired();

                    b.Navigation("Block");

                    b.Navigation("EquipmentSection");

                    b.Navigation("Position")
                        .IsRequired();

                    b.Navigation("Range")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithMany("EquipmentGroups")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", "EquipmentGroup")
                        .WithMany("EquipmentSections")
                        .HasForeignKey("EquipmentGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentGroup");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.OwnsOne("RTP.StatusMonitor.Domain.Shared.AuditInfo", "AuditInfo", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("CreatedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("CreatedBy");

                            b1.Property<DateTime?>("DateCreated")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateCreated");

                            b1.Property<DateTime?>("DateModified")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateModified");

                            b1.Property<string>("ModifiedBy")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ModifiedBy");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportLayout", "ReportLayout", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Layout");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportSchedule", "Schedule", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsEnabled")
                                .HasColumnType("bit")
                                .HasColumnName("IsEnabled");

                            b1.Property<string>("ProcessInterval")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ProcessInterval");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Reports.ReportTimeGrouping", "TimeGrouping", b1 =>
                        {
                            b1.Property<Guid>("ReportId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("GroupingAggregates")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("GroupingAggregates");

                            b1.Property<string>("GroupingInterval")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("GroupingInterval");

                            b1.HasKey("ReportId");

                            b1.ToTable("Reports");

                            b1.WithOwner()
                                .HasForeignKey("ReportId");
                        });

                    b.Navigation("AuditInfo")
                        .IsRequired();

                    b.Navigation("ReportLayout")
                        .IsRequired();

                    b.Navigation("Schedule")
                        .IsRequired();

                    b.Navigation("TimeGrouping")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportColumn", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Reports.Report", null)
                        .WithMany("ReportColumns")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.OwnsOne("RTP.StatusMonitor.Domain.TimeSeries.Types.ComputedExpression", "Calculation", b1 =>
                        {
                            b1.Property<Guid>("ReportColumnId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Calculation");

                            b1.HasKey("ReportColumnId");

                            b1.ToTable("ReportColumns");

                            b1.WithOwner()
                                .HasForeignKey("ReportColumnId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.TimeSeries.Types.FilterExpression", "Filter", b1 =>
                        {
                            b1.Property<Guid>("ReportColumnId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Filter");

                            b1.HasKey("ReportColumnId");

                            b1.ToTable("ReportColumns");

                            b1.WithOwner()
                                .HasForeignKey("ReportColumnId");
                        });

                    b.Navigation("Calculation")
                        .IsRequired();

                    b.Navigation("Filter")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.ReportDateRangeInfo", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Reports.Report", null)
                        .WithOne("DateRangeInfo")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Reports.ReportDateRangeInfo", "ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Customer.Customer", "Customer")
                        .WithMany("Sites")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.BiasTagSettings", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", null)
                        .WithMany("BiasTagSettings")
                        .HasForeignKey("SiteWeatherSettingsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.TimeSeries.Types.ComputedExpression", "ComputedExpression", b1 =>
                        {
                            b1.Property<Guid>("BiasTagSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Expression");

                            b1.HasKey("BiasTagSettingsId");

                            b1.ToTable("BiasTagSettings");

                            b1.WithOwner()
                                .HasForeignKey("BiasTagSettingsId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.Units.UnitId", "UnitId", b1 =>
                        {
                            b1.Property<Guid>("BiasTagSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<Guid>("Value")
                                .HasColumnType("uniqueidentifier")
                                .HasColumnName("UnitId");

                            b1.HasKey("BiasTagSettingsId");

                            b1.ToTable("BiasTagSettings");

                            b1.WithOwner()
                                .HasForeignKey("BiasTagSettingsId");
                        });

                    b.Navigation("ComputedExpression")
                        .IsRequired();

                    b.Navigation("UnitId")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Site.Site", "Site")
                        .WithOne("SiteWeatherSettings")
                        .HasForeignKey("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", "SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.ForecastSettings", "ForecastSettings", b1 =>
                        {
                            b1.Property<Guid>("SiteWeatherSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("ForecastDays")
                                .HasColumnType("int")
                                .HasColumnName("ForecastDays");

                            b1.Property<string>("ForecastServiceSource")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("ForecastServiceSource");

                            b1.Property<bool>("IsForecastEnabled")
                                .HasColumnType("bit")
                                .HasColumnName("IsForecastEnabled");

                            b1.Property<bool>("IsMetric")
                                .HasColumnType("bit")
                                .HasColumnName("IsMetric");

                            b1.HasKey("SiteWeatherSettingsId");

                            b1.ToTable("SiteWeatherSettings");

                            b1.WithOwner()
                                .HasForeignKey("SiteWeatherSettingsId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.LightningStrikeSettings", "LightningStrikeSettings", b1 =>
                        {
                            b1.Property<Guid>("SiteWeatherSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsLightningStrikesEnabled")
                                .HasColumnType("bit")
                                .HasColumnName("IsLightningStrikesEnabled");

                            b1.Property<int>("LightningRadius")
                                .HasColumnType("int")
                                .HasColumnName("LightningRadius");

                            b1.HasKey("SiteWeatherSettingsId");

                            b1.ToTable("SiteWeatherSettings");

                            b1.WithOwner()
                                .HasForeignKey("SiteWeatherSettingsId");
                        });

                    b.Navigation("ForecastSettings")
                        .IsRequired();

                    b.Navigation("LightningStrikeSettings")
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.WeatherBiasSettings", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", null)
                        .WithMany("WeatherBiasSettings")
                        .HasForeignKey("SiteWeatherSettingsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.BiasLimit", "BiasLimit", b1 =>
                        {
                            b1.Property<Guid>("WeatherBiasSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<double?>("Max")
                                .HasColumnType("float")
                                .HasColumnName("Max");

                            b1.Property<double?>("Min")
                                .HasColumnType("float")
                                .HasColumnName("Min");

                            b1.HasKey("WeatherBiasSettingsId");

                            b1.ToTable("WeatherBiasSettings");

                            b1.WithOwner()
                                .HasForeignKey("WeatherBiasSettingsId");
                        });

                    b.OwnsOne("RTP.StatusMonitor.Domain.SiteWeatherSettings.WindEffect", "WindEffect", b1 =>
                        {
                            b1.Property<Guid>("WeatherBiasSettingsId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<string>("Expression")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("Expression");

                            b1.Property<bool>("IsWindEffectsEnabled")
                                .HasColumnType("bit")
                                .HasColumnName("IsWindEffectEnabled");

                            b1.HasKey("WeatherBiasSettingsId");

                            b1.ToTable("WeatherBiasSettings");

                            b1.WithOwner()
                                .HasForeignKey("WeatherBiasSettingsId");
                        });

                    b.Navigation("BiasLimit")
                        .IsRequired();

                    b.Navigation("WindEffect")
                        .IsRequired();
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Units.Unit", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Blocks.Block", "Block")
                        .WithMany("Units")
                        .HasForeignKey("BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Blocks.Block", b =>
                {
                    b.Navigation("DataClient")
                        .IsRequired();

                    b.Navigation("EquipmentContents");

                    b.Navigation("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Customer.Customer", b =>
                {
                    b.Navigation("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Navigation("DataClientGroups");

                    b.Navigation("DataClientStatusVariables");

                    b.Navigation("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Navigation("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Navigation("DataClientGroupVariables");

                    b.Navigation("DataClientStatusVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Navigation("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentGroup.EquipmentGroup", b =>
                {
                    b.Navigation("EquipmentSections");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.EquipmentSection.EquipmentSection", b =>
                {
                    b.Navigation("EquipmentContents");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Reports.Report", b =>
                {
                    b.Navigation("DateRangeInfo")
                        .IsRequired();

                    b.Navigation("ReportColumns");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Site.Site", b =>
                {
                    b.Navigation("Blocks");

                    b.Navigation("Charts");

                    b.Navigation("EquipmentGroups");

                    b.Navigation("GroupPermissions");

                    b.Navigation("SiteWeatherSettings")
                        .IsRequired();

                    b.Navigation("StationDetails");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.SiteWeatherSettings.SiteWeatherSettings", b =>
                {
                    b.Navigation("BiasTagSettings");

                    b.Navigation("WeatherBiasSettings");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.UserGroups.UserGroup", b =>
                {
                    b.Navigation("DisplayViewPermissions");

                    b.Navigation("GroupPermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
