using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddDateRangeInfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateRangeType",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "EndDate",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "EndOffset",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "StartOffset",
                table: "Reports");

            migrationBuilder.AddColumn<string>(
                name: "VariableGrouping",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ReportDateRangeInfo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DateRangeInfoType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    StartOffset = table.Column<int>(type: "int", nullable: true),
                    EndOffset = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportDateRangeInfo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportDateRangeInfo_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReportDateRangeInfo_ReportId",
                table: "ReportDateRangeInfo",
                column: "ReportId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReportDateRangeInfo");

            migrationBuilder.DropColumn(
                name: "VariableGrouping",
                table: "Reports");

            migrationBuilder.AddColumn<string>(
                name: "DateRangeType",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "EndDate",
                table: "Reports",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EndOffset",
                table: "Reports",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "StartDate",
                table: "Reports",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StartOffset",
                table: "Reports",
                type: "int",
                nullable: true);
        }
    }
}
