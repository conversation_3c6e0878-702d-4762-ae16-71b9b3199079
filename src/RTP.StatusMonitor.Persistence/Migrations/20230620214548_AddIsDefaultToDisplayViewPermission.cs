using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddIsDefaultToDisplayViewPermission : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsDefault",
                table: "Displays");

            migrationBuilder.AddColumn<bool>(
                name: "IsDefault",
                table: "DisplayViewPermissions",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsDefault",
                table: "DisplayViewPermissions");

            migrationBuilder.AddColumn<bool>(
                name: "IsDefault",
                table: "Displays",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
