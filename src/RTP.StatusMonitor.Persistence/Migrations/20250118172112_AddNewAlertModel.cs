using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddNewAlertModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "DateRangeInfoType",
                table: "ReportDateRangeInfo",
                type: "nvarchar(21)",
                maxLength: 21,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.CreateTable(
                name: "AlertsBeta",
                columns: table => new
                {
                    AlertId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Recipients = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TriggerType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ExpirationTimeInMinutes = table.Column<int>(type: "int", nullable: false),
                    CriteriaCombinationRule = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RunTimes = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DaysOfWeek = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DaysOfMonth = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DaysOfYear = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Attachments = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LastSentUtc = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SiteId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AlertsBeta", x => x.AlertId);
                    table.ForeignKey(
                        name: "FK_AlertsBeta_Sites_SiteId",
                        column: x => x.SiteId,
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AlertContents",
                columns: table => new
                {
                    AlertContentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Position = table.Column<int>(type: "int", nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AlertId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AlertContents", x => x.AlertContentId);
                    table.ForeignKey(
                        name: "FK_AlertContents_AlertsBeta_AlertId",
                        column: x => x.AlertId,
                        principalTable: "AlertsBeta",
                        principalColumn: "AlertId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AlertCriteriaTriggers",
                columns: table => new
                {
                    AlertCriteriaId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BlockId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Filter = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AlertId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AlertCriteriaTriggers", x => x.AlertCriteriaId);
                    table.ForeignKey(
                        name: "FK_AlertCriteriaTriggers_AlertsBeta_AlertId",
                        column: x => x.AlertId,
                        principalTable: "AlertsBeta",
                        principalColumn: "AlertId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AlertContents_AlertId",
                table: "AlertContents",
                column: "AlertId");

            migrationBuilder.CreateIndex(
                name: "IX_AlertCriteriaTriggers_AlertId",
                table: "AlertCriteriaTriggers",
                column: "AlertId");

            migrationBuilder.CreateIndex(
                name: "IX_AlertsBeta_SiteId",
                table: "AlertsBeta",
                column: "SiteId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AlertContents");

            migrationBuilder.DropTable(
                name: "AlertCriteriaTriggers");

            migrationBuilder.DropTable(
                name: "AlertsBeta");

            migrationBuilder.AlterColumn<string>(
                name: "DateRangeInfoType",
                table: "ReportDateRangeInfo",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(21)",
                oldMaxLength: 21);
        }
    }
}
