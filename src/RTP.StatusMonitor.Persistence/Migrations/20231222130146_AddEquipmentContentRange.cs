using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddEquipmentContentRange : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Value",
                table: "EquipmentContents");

            migrationBuilder.AddColumn<double>(
                name: "Interval",
                table: "EquipmentContents",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Max",
                table: "EquipmentContents",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Min",
                table: "EquipmentContents",
                type: "float",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Interval",
                table: "EquipmentContents");

            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "EquipmentContents");

            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "EquipmentContents");

            migrationBuilder.AddColumn<double>(
                name: "Value",
                table: "EquipmentContents",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }
    }
}
