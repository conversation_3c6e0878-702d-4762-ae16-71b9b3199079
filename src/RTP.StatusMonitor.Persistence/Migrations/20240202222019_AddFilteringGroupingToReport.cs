using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddFilteringGroupingToReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Filter",
                table: "Reports",
                newName: "MainFilters");

            migrationBuilder.AddColumn<string>(
                name: "GroupingAggregates",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "GroupingInterval",
                table: "Reports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GroupingAggregates",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "GroupingInterval",
                table: "Reports");

            migrationBuilder.RenameColumn(
                name: "MainFilters",
                table: "Reports",
                newName: "Filter");
        }
    }
}
