using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    public partial class AddGroupVariable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataClientGroupVariables",
                columns: table => new
                {
                    DataClientGroupId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DataClientVariableId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    NoDecimals = table.Column<int>(type: "int", nullable: false),
                    Min = table.Column<double>(type: "float", nullable: false),
                    Max = table.Column<double>(type: "float", nullable: false),
                    Color = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LineType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Thickness = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataClientGroupVariables", x => new { x.DataClientGroupId, x.DataClientVariableId });
                    table.ForeignKey(
                        name: "FK_DataClientGroupVariables_DataClientGroups_DataClientGroupId",
                        column: x => x.DataClientGroupId,
                        principalTable: "DataClientGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DataClientGroupVariables_DataClientVariables_DataClientVariableId",
                        column: x => x.DataClientVariableId,
                        principalTable: "DataClientVariables",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataClientGroupVariables_DataClientVariableId",
                table: "DataClientGroupVariables",
                column: "DataClientVariableId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataClientGroupVariables");
        }
    }
}
