// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RTP.StatusMonitor.Persistence;

#nullable disable

namespace RTP.StatusMonitor.Persistence.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20230620214548_AddIsDefaultToDisplayViewPermission")]
    partial class AddIsDefaultToDisplayViewPermission
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Block", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Port")
                        .HasColumnType("int");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.ToTable("Blocks");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlobContainer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyLogoUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId")
                        .IsUnique();

                    b.ToTable("DataClients");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCustomerAccess")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.Property<Guid>("DataClientGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Max")
                        .HasColumnType("float");

                    b.Property<double>("Min")
                        .HasColumnType("float");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<int>("Thickness")
                        .HasColumnType("int");

                    b.HasKey("DataClientGroupId", "DataClientVariableId");

                    b.HasIndex("DataClientVariableId");

                    b.ToTable("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Abbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataClientVariableId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Expression")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoDecimals")
                        .HasColumnType("int");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.HasIndex("DataClientVariableId")
                        .IsUnique();

                    b.ToTable("DataClientStatusVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DataClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("DefaultValue")
                        .HasColumnType("float");

                    b.Property<string>("EngUnits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DataClientId");

                    b.ToTable("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayComponent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Layout")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Displays");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.Property<Guid>("DisplayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.HasKey("DisplayId", "UserGroupId");

                    b.HasIndex("UserGroupId");

                    b.ToTable("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("GroupId", "SiteId");

                    b.HasIndex("SiteId");

                    b.ToTable("GroupPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Altitude")
                        .HasColumnType("float");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsMetric")
                        .HasColumnType("bit");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LocationKey")
                        .HasColumnType("int");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("MapLocation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Unit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Alias")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("BlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlockId");

                    b.ToTable("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.UserGroup", b =>
                {
                    b.Property<Guid>("UserGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserGroupId");

                    b.ToTable("UserGroups");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Block", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Site", "Site")
                        .WithMany("Blocks")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Block", "Block")
                        .WithOne("DataClient")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClient", "BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientGroups")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroupVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientGroup", "DataClientGroup")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithMany("DataClientGroupVariables")
                        .HasForeignKey("DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClientGroup");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientStatusVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClientVariable", "DataClientVariable")
                        .WithOne("DataClientStatusVariable")
                        .HasForeignKey("RTP.StatusMonitor.Domain.Entities.DataClientStatusVariable", "DataClientVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataClient");

                    b.Navigation("DataClientVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.DataClient", "DataClient")
                        .WithMany("DataClientVariables")
                        .HasForeignKey("DataClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClient");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DisplayViewPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Display", "Display")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("DisplayId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.UserGroup", "UserGroup")
                        .WithMany("DisplayViewPermissions")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Display");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.GroupPermission", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.UserGroup", "Group")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Site", "Site")
                        .WithMany("GroupPermissions")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Site", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Customer", "Customer")
                        .WithMany("Sites")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Unit", b =>
                {
                    b.HasOne("RTP.StatusMonitor.Domain.Entities.Block", "Block")
                        .WithMany("Units")
                        .HasForeignKey("BlockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Block");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Block", b =>
                {
                    b.Navigation("DataClient")
                        .IsRequired();

                    b.Navigation("Units");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Customer", b =>
                {
                    b.Navigation("Sites");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClient", b =>
                {
                    b.Navigation("DataClientGroups");

                    b.Navigation("DataClientStatusVariables");

                    b.Navigation("DataClientVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientGroup", b =>
                {
                    b.Navigation("DataClientGroupVariables");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.DataClientVariable", b =>
                {
                    b.Navigation("DataClientGroupVariables");

                    b.Navigation("DataClientStatusVariable");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Display", b =>
                {
                    b.Navigation("DisplayViewPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.Site", b =>
                {
                    b.Navigation("Blocks");

                    b.Navigation("GroupPermissions");
                });

            modelBuilder.Entity("RTP.StatusMonitor.Domain.Entities.UserGroup", b =>
                {
                    b.Navigation("DisplayViewPermissions");

                    b.Navigation("GroupPermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
