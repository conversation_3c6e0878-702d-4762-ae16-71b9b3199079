using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.Persistence.Models;

public enum AlertTriggerType { Criteria, Daily, Weekly, Monthly, Yearly }

public class AlertModel
{
    public Guid AlertId { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public bool IsEnabled { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public List<string> AlertTypes { get; private set; } = [];
    public List<string> Recipients { get; private set; } = [];
    public AlertTriggerType TriggerType { get; private set; }
    public int ExpirationTimeInMinutes { get; private set; }
    public List<AlertCriteriaModel> Criterias = [];
    // AND/OR (used for combining different criterias)
    public string CriteriaCombinationRule { get; private set; } = string.Empty;
    public List<TimeOnly> RunTimes { get; private set; } = [];
    public List<string> DaysOfWeek { get; private set; } = []; // Monday, Tues, etc...
    public List<int> DaysOfMonth { get; private set; } = []; // 1st, 15th, 22nd, etc...
    public List<int> DaysOfYear { get; private set; } = []; // from 1-365
    public List<AlertContentModel> Contents { get; private set; } = [];
    public List<Guid> Attachments { get; private set; } = [];
    public DateTime? LastSentUtc { get; private set; }
    public Guid SiteId { get; private set; }

    private AlertModel() { }
    public AlertModel(
        Guid alertId,
        string name,
        string description,
        bool isEnabled,
        string title,
        List<AlertType> alertTypes,
        List<string> recipients,
        AlertTrigger trigger,
        List<AlertContent> contents,
        List<Guid> attachments,
        Guid siteId,
        DateTime? lastSentUtc)
    {
        AlertId = alertId;
        Name = name;
        Description = description;
        IsEnabled = isEnabled;
        Title = title;
        AlertTypes = [.. alertTypes.Select(a => a.ToString())];
        Recipients = recipients;
        TriggerType = trigger switch
        {
            CriteriaTrigger => AlertTriggerType.Criteria,
            DailyFrequency => AlertTriggerType.Daily,
            WeeklyFrequency => AlertTriggerType.Weekly,
            MonthlyFrequency => AlertTriggerType.Monthly,
            YearlyFrequency => AlertTriggerType.Yearly,
            _ => throw new InvalidOperationException($"Invalid trigger type {trigger.GetType().Name} is not supported")
        };
        ExpirationTimeInMinutes = trigger.ExpirationTimeInMinutes;
        Criterias = trigger switch
        {
            CriteriaTrigger criteria => [.. criteria.Criterias.Select(c => new AlertCriteriaModel(c.AlertCriteriaId, c.BlockId, c.Filter.Value, this))],
            _ => []
        };
        CriteriaCombinationRule = trigger switch
        {
            CriteriaTrigger criteria => criteria.CombinationRule.ToString(),
            _ => string.Empty
        };
        ;
        RunTimes = trigger switch
        {
            CriteriaTrigger _ => [],
            ScheduleTrigger scheduled => scheduled.RunTimes,
            _ => throw new InvalidOperationException($"Trigger type {trigger.GetType().Name} is not supported")
        };
        DaysOfWeek = trigger switch
        {
            WeeklyFrequency weekly => [.. weekly.DaysOfWeeks.Select(d => d.ToString())],
            _ => []
        };
        DaysOfMonth = trigger switch
        {
            MonthlyFrequency monthly => monthly.DaysOfMonth,
            _ => []
        };
        DaysOfYear = trigger switch
        {
            YearlyFrequency yearly => yearly.DaysOfYear,
            _ => []
        };
        Contents = [.. contents
            .Select((content, index) => content switch
            {
                AlertNoteContent note => new AlertContentModel(
                    content.AlertContentId, AlertContentType.Note, index, JsonConvert.SerializeObject(note), this),
                AlertContactContent contact => new AlertContentModel(
                    content.AlertContentId, AlertContentType.Contact, index, JsonConvert.SerializeObject(contact), this),
                AlertReportContent report => new AlertContentModel(
                    content.AlertContentId, AlertContentType.Report, index, JsonConvert.SerializeObject(report), this),
                AlertEquipmentContent equipment => new AlertContentModel(
                    content.AlertContentId, AlertContentType.Equipment, index, JsonConvert.SerializeObject(equipment), this),
                AlertWeatherContent weather => new AlertContentModel(
                    content.AlertContentId, AlertContentType.Weather, index, JsonConvert.SerializeObject(weather), this),
                AlertSqlContent sql => new AlertContentModel(
                    content.AlertContentId, AlertContentType.SqlQuery, index, JsonConvert.SerializeObject(sql), this),
                _ => throw new InvalidOperationException($"Invalid content type {content.GetType().Name}")
            })];
        Attachments = attachments;
        LastSentUtc = lastSentUtc;
        SiteId = siteId;
    }
}

public static class AlertModelExtensions
{
    /// <summary>
    /// Map from domain model to persistence model for EF core 
    /// </summary>
    public static AlertModel ToPersistence(this AlertBeta alert) => new(
        alertId: alert.Id,
        name: alert.Name,
        description: alert.Description,
        isEnabled: alert.IsEnabled,
        title: alert.Title,
        alertTypes: [.. alert.AlertTypes],
        recipients: alert.Recipients.ConvertAll(e => e.Value),
        trigger: alert.Trigger,
        contents: [.. alert.AlertContents],
        attachments: [.. alert.Attachments.Select(a => a.ReportId)],
        siteId: alert.SiteId,
        lastSentUtc: alert.LastSentUtc);

    /// <summary>
    /// Map from persistence model (ef core) to domain model
    /// </summary>
    public static AlertBeta ToDomain(this AlertModel alertModel) => new(
          id: alertModel.AlertId,
          name: alertModel.Name,
          description: alertModel.Description,
          isEnabled: alertModel.IsEnabled,
          trigger: alertModel.TriggerType switch
          {
              AlertTriggerType.Criteria => new CriteriaTrigger(
                alertModel.ExpirationTimeInMinutes,
                [.. alertModel.Criterias.Select(c => new AlertCriteria(c.AlertCriteriaId, c.BlockId, new(c.Filter)))],
                Enum.Parse<CombinationRule>(alertModel.CriteriaCombinationRule)),
              AlertTriggerType.Daily => new DailyFrequency(alertModel.ExpirationTimeInMinutes, alertModel.RunTimes),
              AlertTriggerType.Weekly => new WeeklyFrequency(
                alertModel.ExpirationTimeInMinutes,
                alertModel.RunTimes,
                [.. alertModel.DaysOfWeek.Select(Enum.Parse<DayOfWeek>)]),
              AlertTriggerType.Monthly => new MonthlyFrequency(
                alertModel.ExpirationTimeInMinutes, alertModel.RunTimes, alertModel.DaysOfMonth),
              AlertTriggerType.Yearly => new YearlyFrequency(
                alertModel.ExpirationTimeInMinutes, alertModel.RunTimes, alertModel.DaysOfYear),
              _ => throw new ArgumentException($"Invalid alert trigger type: {alertModel.TriggerType}")
          },
          title: alertModel.Title,
          alertTypes: [.. alertModel.AlertTypes.Select(Enum.Parse<AlertType>)],
          recipients: alertModel.Recipients
              .Select(EmailAddress.From)
              .Where(e => e is not null)
              .ToList()!,
          contents: [.. alertModel.Contents
            .OrderBy(c => c.Position)
            .Select(c => c.ToDomain())],
          attachments: [.. alertModel.Attachments.Select(a => new AlertAttachment(a))],
          siteId: alertModel.SiteId,
          lastSentUtc: alertModel.LastSentUtc);
}
