namespace RTP.StatusMonitor.Persistence.Models;

public class AlertCriteriaModel
{
    public Guid AlertCriteriaId { get; init; }
    public Guid BlockId { get; init; }
    public string Filter { get; init; } = string.Empty;
    public AlertModel Alert { get; init; } = null!;
    public Guid AlertId { get; init; }

    private AlertCriteriaModel() { }
    public AlertCriteriaModel(Guid alertCriteriaId, Guid blockId, string filter, AlertModel alertModel)
    {
        AlertCriteriaId = alertCriteriaId;
        BlockId = blockId;
        Filter = filter;
        Alert = alertModel;
        AlertId = alertModel.AlertId;
    }
}
