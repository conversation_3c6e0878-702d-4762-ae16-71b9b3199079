using Newtonsoft.Json;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.Persistence.Models;
public enum AlertContentType { Note, Contact, Report, Equipment, Weather, SqlQuery }

public class AlertContentModel
{
    public Guid AlertContentId { get; init; }
    public AlertContentType Type { get; init; }
    public int Position { get; init; }
    public string Content { get; init; } = string.Empty;
    public Guid AlertId { get; init; }
    public AlertModel Alert { get; init; } = null!;
    private AlertContentModel() { }
    internal AlertContentModel(
        Guid alertContentId,
        AlertContentType type, int position, string content, AlertModel alert)
    {
        AlertContentId = alertContentId;
        Type = type;
        Position = position;
        Content = content;
        AlertId = alert.AlertId;
        Alert = alert;
    }
}

public static class AlertContentModelExtensions
{
    public static AlertContent ToDomain(this AlertContentModel alertContentModel)
        => alertContentModel.Type switch
        {
            AlertContentType.Note => JsonConvert.DeserializeObject<AlertNoteContent>(alertContentModel.Content)!,
            AlertContentType.Contact => JsonConvert.DeserializeObject<AlertContactContent>(alertContentModel.Content)!,
            AlertContentType.Report => JsonConvert.DeserializeObject<AlertReportContent>(alertContentModel.Content)!,
            AlertContentType.Equipment => JsonConvert.DeserializeObject<AlertEquipmentContent>(alertContentModel.Content)!,
            AlertContentType.Weather => JsonConvert.DeserializeObject<AlertWeatherContent>(alertContentModel.Content)!,
            AlertContentType.SqlQuery => JsonConvert.DeserializeObject<AlertSqlContent>(alertContentModel.Content)!,
            _ => throw new ArgumentException($"Invalid alert content type: {alertContentModel.Type}")
        };
}
