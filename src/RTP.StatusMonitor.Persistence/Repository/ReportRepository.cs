using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.Persistence.Repository;

internal sealed class ReportRepository(DataContext dbContext) : IReportRepository
{
    private readonly DataContext _dbContext = dbContext;

    /// <inheritdoc />
    public Task<List<Report>> GetEnabledReportsAsync(CancellationToken ct)
    => _dbContext.Reports
        .AsNoTracking()
        .Include(r => r.DateRangeInfo)
        .Include(r => r.ReportColumns)
        .Where(r => r.Schedule.IsEnabled)
        .ToListAsync(ct);

    /// <inheritdoc />
    public async Task<Maybe<Report>> GetReportByIdAsync(
        Guid reportId,
        CancellationToken ct)
    {
        // Get the first report that matches the id
        Report? report = await _dbContext.Reports
            .AsNoTracking()
            .Include(r => r.DateRangeInfo)
            .Include(r => r.ReportColumns)
            .FirstOrDefaultAsync(r => r.Id == reportId, ct);

        // If the report is null, return a new Nothing<Report> instance
        return report is null
            ? new Nothing<Report>()
            : new Something<Report>(report);
    }

    /// <inheritdoc />
    public async Task<List<Report>> GetReportsAsync(
        CancellationToken ct)
        => await _dbContext.Reports
            .AsNoTracking()
            .Include(r => r.DateRangeInfo)
            .Include(r => r.ReportColumns)
            .ToListAsync(ct);

    /// <inheritdoc />
    public async Task AddReportAsync(
        Report report,
        CancellationToken ct)
        => await _dbContext.Reports.AddAsync(report, ct);

    /// <inheritdoc />
    public void DeleteReport(Report report)
        => _dbContext.Reports.Remove(report);
}
