using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.Persistence.Repository;

public class BlockRepository : IBlockRepository
{
    private readonly IDataContext _dbContext;

    public BlockRepository(IDataContext dbContext)
        => _dbContext = dbContext;

    public async Task<List<Block>> FilterBlocksById(List<Guid> blockIds, CancellationToken ct)
    {
        return await _dbContext.Blocks
            .AsNoTracking()
            .Where(b => blockIds.Contains(b.Id))
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .ToListAsync(ct);
    }
}