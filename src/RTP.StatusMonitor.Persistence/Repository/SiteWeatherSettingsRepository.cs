using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.Persistence.Repository;

public class SiteWeatherSettingsRepository : ISiteWeatherSettingsRepository
{
    private readonly DataContext _dataContext;

    public SiteWeatherSettingsRepository(DataContext dataContext)
    => _dataContext = dataContext;

    public async Task<SiteWeatherSettings?> GetWeatherSettingsBySiteId(
        Guid siteId, CancellationToken ct)
    => await _dataContext
        .SiteWeatherSettings
        .Where(sws => sws.SiteId == siteId)
            .Include(sws => sws.WeatherBiasSettings)
            .Include(sws => sws.BiasTagSettings)
        .FirstOrDefaultAsync(ct);

    public async Task SaveSiteWeatherSetttings(
        SiteWeatherSettings updatedWeatherSettings,
        CancellationToken ct)
    {
        SiteWeatherSettings? existingWeatherSettings = await GetWeatherSettingsBySiteId(
            updatedWeatherSettings.SiteId, ct);

        if (existingWeatherSettings == null)
        {
            await _dataContext.SiteWeatherSettings.AddAsync(updatedWeatherSettings, ct);
        }
        else
        {
            existingWeatherSettings.UpdateSiteWeatherSettings(
                updatedForecastSettings: updatedWeatherSettings.ForecastSettings,
                updatedLightningStrikeSettings: updatedWeatherSettings.LightningStrikeSettings,
                updatedBiasSettings: updatedWeatherSettings.WeatherBiasSettings,
                updatedBiasTagSettings: updatedWeatherSettings.BiasTagSettings);
        }
    }
}
