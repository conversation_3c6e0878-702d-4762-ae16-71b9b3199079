using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.Site;

namespace RTP.StatusMonitor.Persistence.Repository;

internal sealed class SiteRepository
    : ISiteRepository
{
    private readonly IDataContext _dbContext;

    public SiteRepository(IDataContext dbContext)
        => _dbContext = dbContext;

    /// <summary>
    /// Get all sites in the database
    /// </summary>
    public async Task<List<Site>> GetSitesAsync(
        CancellationToken ct)
        => await _dbContext.Sites
            .Include(s => s.Blocks.SelectMany(b => b.Units))
            .ToListAsync(ct);

    public Task<Site?> GetSiteByIdAsync(Guid siteId, CancellationToken ct)
    => _dbContext.Sites
        .AsNoTracking()
        .Include(s => s.Blocks)
            .ThenInclude(b => b.Units)
        .FirstOrDefaultAsync(s => s.Id == siteId, ct);
}