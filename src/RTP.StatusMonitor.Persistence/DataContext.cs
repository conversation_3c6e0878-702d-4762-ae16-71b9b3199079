using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.EquipmentGroup;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.EquipmentSection;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Chart;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.IntegrationEventLogEntry;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.Persistence;

public sealed class DataContext : DbContext, IDataContext, IUnitOfWork
{
    public DataContext(DbContextOptions options) : base(options)
    { }

    public DbSet<Customer> Customers { get; set; } = null!;
    public DbSet<Site> Sites { get; set; } = null!;
    public DbSet<Block> Blocks { get; set; } = null!;
    public DbSet<Unit> Units { get; set; } = null!;


    // SECTION - Data client
    public DbSet<DataClient> DataClients { get; set; } = null!;
    public DbSet<DataClientGroup> DataClientGroups { get; set; } = null!;
    public DbSet<DataClientGroupVariable> DataClientGroupVariables { get; set; } = null!;
    public DbSet<DataClientVariable> DataClientVariables { get; set; } = null!;
    public DbSet<DataClientStatusVariable> DataClientStatusVariables { get; set; } = null!;

    // SECTION - User permission
    public DbSet<UserGroup> UserGroups { get; set; } = null!;
    public DbSet<GroupPermission> GroupPermissions { get; set; } = null!;

    // SECTION - Display permission
    public DbSet<Display> Displays { get; set; } = null!;
    public DbSet<DisplayViewPermission> DisplayViewPermissions { get; set; } = null!;

    // SECTION - Weather 
    public DbSet<SiteWeatherSettings> SiteWeatherSettings { get; set; } = null!;
    public DbSet<WeatherBiasSettings> WeatherBiasSettings { get; set; } = null!;
    public DbSet<BiasTagSettings> BiasTagSettings { get; set; } = null!;
    public DbSet<StationDetails> StationDetails { get; set; } = null!;

    // SECTION - Equipments
    public DbSet<EquipmentGroup> EquipmentGroups { get; set; } = null!;
    public DbSet<EquipmentSection> EquipmentSections { get; set; } = null!;
    public DbSet<EquipmentContent> EquipmentContents { get; set; } = null!;

    // SECTION - Chart
    public DbSet<Chart> Charts { get; set; } = null!;

    // SECTION - Integration events log
    public DbSet<IntegrationEventLogEntry> IntegrationEventLogEntries { get; set; } = null!;

    // SECTION - Reporting
    public DbSet<Report> Reports { get; set; } = null!;
    public DbSet<ReportColumn> ReportColumns { get; set; } = null!;

    // SECTION - Alerts
    public DbSet<Alert> Alerts { get; set; } = null!;
    public DbSet<AlertModel> AlertModels { get; set; }
    public DbSet<AlertContentModel> AlertContentModels { get; set; }
    public DbSet<AlertCriteriaModel> AlertCriteriaModels { get; set; }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        // NOTE 
        // This will scan all the configurations (inherit IEntityTypeConfiguration<T>) in the assembly and apply them automatically
        builder.ApplyConfigurationsFromAssembly(typeof(DataContext).Assembly);

        base.OnModelCreating(builder);
    }

    public async Task<DatabaseTransaction> BeginTransactionAsync(
        CancellationToken ct)
    => new DatabaseTransaction(await Database.BeginTransactionAsync(ct));

    Task<int> IUnitOfWork.SaveChangesAsync(CancellationToken ct)
    => SaveChangesAsync(ct);
}

