// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.Design;
// using Microsoft.Extensions.Configuration;

// namespace RTP.StatusMonitor.Persistence;

// public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<DataContext>
// {
//     public DataContext CreateDbContext(string[] args)
//     {
//         var config = new ConfigurationBuilder()
//             .SetBasePath(Path.Combine(Directory.GetCurrentDirectory()))
//             .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
//             .AddEnvironmentVariables()
//             .Build();

//         // Get connection string from config
//         var connectionString = config.GetValue<string>("Values:SqlConnectionString");

//         var optionsBuilder = new DbContextOptionsBuilder<DataContext>();
//         optionsBuilder.UseSqlServer(connectionString);

//         return new DataContext(optionsBuilder.Options);
//     }
// }
