using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Persistence.Data;
using RTP.StatusMonitor.Persistence.Repository;

namespace RTP.StatusMonitor.Persistence;

public static class DependencyInjection
{
    public static IServiceCollection AddPersistence(
      this IServiceCollection services)
    {
        string connectionString = Environment.GetEnvironmentVariable("SqlConnectionString")!;

        services.AddDbContext<DataContext>(opts => opts.UseSqlServer(connectionString));

        services.AddRepositories();

        // Register the unit of work 
        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<DataContext>());

        services.AddSingleton<ISqlConnectionFactory>(_ => new SqlConnectionFactory(connectionString));

        return services;
    }

    public static IServiceCollection AddRepositories(
      this IServiceCollection services)
    {
        services.AddScoped<ISiteRepository, SiteRepository>();
        services.AddScoped<IBlockRepository, BlockRepository>();
        services.AddScoped<ISiteWeatherSettingsRepository, SiteWeatherSettingsRepository>();
        services.AddScoped<IReportRepository, ReportRepository>();

        return services;
    }
}
