using System.Data;
using Microsoft.Data.SqlClient;

namespace RTP.StatusMonitor.Persistence.Data;

internal sealed class SqlConnectionFactory(string connectionString) : ISqlConnectionFactory
{
    private readonly string _connectionString = connectionString;
    public IDbConnection CreateConnection()
    {
        SqlConnection connection = new(_connectionString);
        connection.Open();

        return connection;
    }
}
