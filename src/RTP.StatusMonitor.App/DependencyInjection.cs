using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.App.Shared.Util;
using RTP.StatusMonitor.App.Shared.Behaviors;
using RTP.StatusMonitor.App.Shared.Repository.InputData;
using RTP.StatusMonitor.App.Shared.Api;
using RTP.StatusMonitor.App.Shared.Api.InternalData;
using RTP.StatusMonitor.App.Shared.Emails;
using RTP.StatusMonitor.App.Modules.Reporting;
using RTP.StatusMonitor.App.Modules.Alerts;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.App.Modules.Airsonic;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.GoogleMap;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.OpenWeather;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.TomorrowWeather;
using RTP.StatusMonitor.App.Modules.Forecast;
using RTP.StatusMonitor.App.Shared.TableStorage;
using RTP.StatusMonitor.App.Shared.BlobStorage;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather;

namespace RTP.StatusMonitor.App;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(
        this IServiceCollection services)
    {

        // Inject libraries
        services.AddLogging();
        services.AddMemoryCache();
        services.AddAutoMapper(typeof(DependencyInjection).Assembly);

        // Pipeline
        services.AddMediatRWithPipelineBehaviors();

        // Add settings from local.settings.json (Azure Functions)
        services.AddOptions();

        services.AddAzureServices();

        services.AddTransient<EmailService>();

        services.AddScoped<IAuthorizationService, AuthorizationService>();

        // Repository
        services.AddTransient<InputDataRepository>();

        // Inject services
        services.AddTransient<IDataContext, DataContext>();
        services.AddTransient<IDateTimeProvider, DateTimeProvider>();
        services.AddTransient<ExpressionParser>();
        services.AddTransient<InternalDataApi>();

        services.AddAirsonic();
        services.AddForecasting();
        services.AddDataClient();
        services.AddReportingServices();
        services.AddAlertServices();

        // Weather
        services.AddScoped<IMapService, GoogleMapService>();
        services.AddWeather();

        return services;
    }

    private static IServiceCollection AddMediatRWithPipelineBehaviors(this IServiceCollection services)
    {
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly));

        // services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
        services.AddTransient(
            typeof(IPipelineBehavior<,>),
            typeof(QueryCachingPipelineBehavior<,>));
        services.AddTransient(
            typeof(IPipelineBehavior<,>),
            typeof(QueryAuthorizationPipelineBehavior<,>));
        services.AddTransient(
            typeof(IPipelineBehavior<,>),
            typeof(QueryGroupAuthorizationPipelineBehavior<,>));
        services.AddTransient(
            typeof(IPipelineBehavior<,>),
            typeof(CommandGroupAuthorizationPipelineBehavior<,>));

        return services;
    }

    private static IServiceCollection AddOptions(
        this IServiceCollection services)
    {
        // Azure blob storage
        services
            .AddOptions<BlobStorageOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(BlobStorageOptions.SectionName).Bind(settings));

        // Azure table storage
        services
            .AddOptions<TableStorageOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(TableStorageOptions.SectionName).Bind(settings));

        // Azure service bus
        services
            .AddOptions<AzureServiceBusOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(AzureServiceBusOptions.SectionName).Bind(settings));

        // Email config
        services
            .AddOptions<EmailOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(EmailOptions.SectionName).Bind(settings));

        // Data client API
        services
            .AddOptions<InternalApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(InternalApiOptions.SectionName).Bind(settings));

        // GoogleMap API 
        services
            .AddOptions<GoogleMapApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(GoogleMapApiOptions.SectionName).Bind(settings));

        return services;
    }

    private static IServiceCollection AddWeather(this IServiceCollection services)
    {
        // Read API keys from settings
        services
            .AddOptions<AccuWeatherApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(AccuWeatherApiOptions.SectionName).Bind(settings));
        services
            .AddOptions<IBMWeatherApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(IBMWeatherApiOptions.SectionName).Bind(settings));
        services
            .AddOptions<TomorrowWeatherApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(TomorrowWeatherApiOptions.SectionName).Bind(settings));
        services
            .AddOptions<OpenWeatherApiOptions>()
            .Configure<IConfiguration>((settings, config) =>
                config.GetSection(OpenWeatherApiOptions.SectionName).Bind(settings));

        // Register services
        services.AddScoped<IWeatherApi, TomorrowWeatherApi>();
        services.AddScoped<IWeatherApi, IBMWeatherApi>();
        services.AddScoped<IWeatherApi, AccuWeatherApi>();
        services.AddScoped<IWeatherApi, OpenWeatherApi>();

        // Register as standalone to get location key when needed
        services.AddTransient<AccuWeatherApi>();

        services.AddScoped<IWeatherServiceFactory, WeatherServiceFactory>();

        services.AddScoped<IDistanceService, DistanceService>();
        services.AddTransient<WeatherWriteRepository>();
        services.AddTransient<WeatherReadRepository>();

        return services;
    }

    private static IServiceCollection AddAzureServices(this IServiceCollection services)
    {
        services.AddScoped<IBlobStorageService, BlobStorageService>();
        services.AddScoped<ITableStorageService, TableStorageService>();

        services.AddScoped<ITableStorage, TableStorage>();
        services.AddSingleton<IEventBus, EventBus>();

        return services;
    }
}
