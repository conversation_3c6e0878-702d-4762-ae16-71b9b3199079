using ErrorOr;

namespace RTP.StatusMonitor.App.Shared.Errors;

public static class SiteErrors
{
    // Error for site not found
    public static Error NotFound => Error.NotFound(
        code: "Site.NotFound",
        description: "Site not found"
    );

    public static Error AwLocationKeyNotFound => Error.NotFound(
        code: "Site.AwLocationKeyNotFound",
        description: "AccuWeather location key not found"
    );

    // Error for site already exists for customer
    public static Error AlreadyExists => Error.Conflict(
        code: "Site.AlreadyExists",
        description: "Site already exists for this customer"
    );

    // Unexpected error when adding site to database
    public static Error UnexpectedError => Error.Unexpected(
        code: "Site.UnexpectedError",
        description: "Unexpected error when persisting site to database"
    );

    // Unauthorized error 
    public static Error Unauthorized => Error.Failure(
        code: "Site.Unauthorized",
        description: "Unauthorized to access site"
    );
}
