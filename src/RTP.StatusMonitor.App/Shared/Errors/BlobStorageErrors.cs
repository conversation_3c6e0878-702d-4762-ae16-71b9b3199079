using ErrorOr;

namespace RTP.StatusMonitor.App.Shared.Errors;

public static class BlobStorageErrors
{
    public static Error ContainerNotFound => Error.Failure("BlobStorage.ContainerNotFound", "The container was not found.");
    public static Error BlobNotFound => Error.Failure("BlobStorage.BlobNotFound", "The blob was not found.");
    public static Error UploadFailed => Error.Failure("BlobStorage.UploadFailed", "Failed to upload to blob storage.");
    public static Error DownloadFailed => Error.Failure("BlobStorage.DownloadFailed", "Failed to download from blob storage.");
}