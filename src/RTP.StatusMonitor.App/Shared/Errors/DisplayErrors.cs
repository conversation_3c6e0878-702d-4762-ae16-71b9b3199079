using ErrorOr;

namespace RTP.StatusMonitor.App.Shared.Errors;

public static class DisplayErrors
{
    public static Error UnexpectedError => Error.Failure(
        code: "Display.UnexpectedError",
        description: "An unexpected error occurred while creating the display"
    );
    public static Error Unauthorized => Error.Failure(
        code: "Display.Unauthorized",
        description: "Permission to display not granted"
    );
    public static Error ShareForbidden => Error.Failure(
        code: "Display.ShareForbidden",
        description: "Not allowed to share display with the requested groups"
    );
    public static Error NotFound => Error.NotFound(
        code: "Display.NotFound",
        description: "Display not found"
    );
    public static Error SetDefaultForbidden => Error.Failure(
        code: "Display.SetDefaultForbidden",
        description: "Not allowed to set default display for a group with no view permission"
    );
    public static Error DefaultDisplayForMultipleGroups => Error.Conflict(
        code: "Display.DefaultDisplayForMultipleGroups",
        description: "Can't not set default display for more than one group"
    );
    public static Error NameAlreadyExists => Error.Conflict(
        code: "Display.NameAlreadyExists",
        description: "Display name already exists"
    );
    public static Error DefaultDisplayAlreadyExists => Error.Conflict(
        code: "Display.DefaultDisplayAlreadyExists",
        description: "Default display already exists for the group"
    );
}