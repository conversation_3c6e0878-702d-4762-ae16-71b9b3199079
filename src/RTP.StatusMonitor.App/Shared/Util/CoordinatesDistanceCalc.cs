namespace RTP.StatusMonitor.App.Shared.Util;
public sealed class UnitOfLength
{
  private readonly double _fromMilesFactor;
  private UnitOfLength(double fromMilesFactor)
  {
    _fromMilesFactor = fromMilesFactor;
  }
  public static UnitOfLength Miles { get; set; } = new(1);
  public static UnitOfLength Kilometers { get; set; } = new(1.609344);
  public static UnitOfLength NauticalMiles { get; set; } = new(0.8684);

  public double ConvertFromMiles(double input)
  {
    return input * _fromMilesFactor;
  }
}
