using System.Text.RegularExpressions;

namespace RTP.StatusMonitor.App.Shared.Util;

// FIXME - DEPRECATED => NEED TO REMOVE AFTER REFACTORING
public class ExpressionParser
{
	/// <summary>
	/// This method use regex to extract tags from filter string
	/// </summary>
	/// 
	/// <param name="expression">the filter string contains the tags to extract</param>
	/// 
	/// <returns>a list of distinct tags from the filter</returns>
	/// 
	/// <example>
	/// filter = "[tag1] > 10 AND [tag2] < 20"
	/// 
	/// return ["tag1", "tag2"]
	/// </example>
	public List<string> ExtractItemFromExpression(
		string expression, string pattern)
	{
		if (string.IsNullOrEmpty(expression))
		{
			return new List<string>();
		}

		// Find matching item from the expression using regex 
		MatchCollection? matches = Regex.Matches(expression, pattern);

		return new HashSet<string>(matches.Select(m => m.Groups[1].Value)).ToList();
	}
}