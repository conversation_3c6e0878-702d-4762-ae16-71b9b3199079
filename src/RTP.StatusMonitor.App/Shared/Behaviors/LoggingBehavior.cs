using MediatR;
using Microsoft.Extensions.Logging;

namespace RTP.StatusMonitor.App.Shared.Behaviors;

// NOTE Logging behavior for MediatR
// This is typically used for command handlers
// to have a record of what commands were executed
public class LoggingBehavior<TRequest, TResponse>
  : <PERSON>ipelineBehavior<TRequest, TResponse>
  where TRequest : IRequest<TResponse>
{
  private readonly ILogger<TRequest> _logger;

  public LoggingBehavior(ILogger<TRequest> logger)
    => _logger = logger;

  // Log the request (command) and whether it succeeded or failed
  public async Task<TResponse> Handle(
  TRequest request,
  RequestHandlerDelegate<TResponse> next,
  CancellationToken ct)
  {
    // Get the name of the request
    string name = request.GetType().Name;

    try
    {
      // Log the request
      _logger.LogInformation($"Executing request {name}", name);

      // Execute the request
      var result = await next();

      _logger.LogInformation($"Request {name} executed successfully", name);

      return result;
    }
    catch (Exception exception)
    {
      _logger.LogError(
        exception,
        $"Request {name} failed",
        name);

      throw;
    }
  }
}