using ErrorOr;
using MediatR;
using RTP.StatusMonitor.App.Shared.Authorization;

namespace RTP.StatusMonitor.App.Shared.Behaviors;

internal sealed class QueryGroupAuthorizationPipelineBehavior<TRequest, TResponse>(
    IAuthorizationService authorizationService)
    : IPipelineBehavior<TRequest, ErrorOr<TResponse>>
    where TRequest : IRequest<ErrorOr<TResponse>>, IAuthorizedGroupQuery
{
    private readonly IAuthorizationService _authorizationService = authorizationService;
    public async Task<ErrorOr<TResponse>> Handle(
        TRequest request,
        RequestHandlerDelegate<ErrorOr<TResponse>> next,
        CancellationToken ct)
    {
        // Check if the user is authorized to access the resource they are trying to access
        bool isUserAuthorized = await _authorizationService
            .CheckUserAccessByGroup(
                userGroupsId: request.UserGroupsId,
                resourceId: request.ResourceId,
                ct: ct);

        return isUserAuthorized
            ? await next()
            : Error.Failure(
                code: "User.Unauthorized",
                description: "User are not authorized to access this resource");
    }
}
