using MediatR;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.App.Shared.Caching;

namespace RTP.StatusMonitor.App.Shared.Behaviors;

/// <summary>
/// This pipeline behavior will be used to cache queries that implement ICachedQuery
/// </summary>
/// 
/// <typeparam name="TRequest"></typeparam>
/// <typeparam name="TResponse"></typeparam>
internal sealed class QueryCachingPipelineBehavior<TRequest, TResponse>(IMemoryCache cache)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>, ICachedQuery
{
    private readonly IMemoryCache _cache = cache;


    // Use cache aside pattern
    // Attempt to read from cache first. 
    // If not found, execute the query and cache the  result
    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken ct)
    {
        bool isCacheEnabled = request.IsCacheEnabled;
        string? cacheKey = request.CacheKey;
        TimeSpan? cacheDuration = request.CacheDuration;

        if (isCacheEnabled is true)
        {
            return await _cache.GetOrCreateAsync(
            cacheKey,
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = cacheDuration;
                return await next();
            });
        }

        return await next();
    }
}