using ErrorOr;
using MediatR;
using RTP.StatusMonitor.App.Shared.Authorization;

namespace RTP.StatusMonitor.App.Shared.Behaviors;

internal sealed class QueryAuthorizationPipelineBehavior<TRequest, TResponse>(IAuthorizationService authorizationService)
    : IPipelineBehavior<TRequest, ErrorOr<TResponse>>
    where TRequest : IRequest<ErrorOr<TResponse>>, IAuthorizedQuery
{
    private readonly IAuthorizationService _authorizationService = authorizationService;

    // TODO - optimize this logic to avoid multiple calls to the database in different scenarios
    public async Task<ErrorOr<TResponse>> Handle(
        TRequest request,
        RequestHandlerDelegate<ErrorOr<TResponse>> next,
        CancellationToken ct)
    {
        // By pass authorization if it is not enabled
        if (!request.IsAuthorizationEnabled) return await next();

        // Check if the user is authorized to access the resource they requested (either site, block or unit)
        bool isUserAuthorized = await _authorizationService
            .CheckUserAccessByGroup(
                userGroupsId: request.UserGroupsId,
                siteId: request.SiteId,
                blockId: request.BlockId,
                unitId: request.UnitId,
                ct: ct);

        return isUserAuthorized
            ? await next()
            : Error.Failure(
                code: "User.Unauthorized",
                description: "User are not authorized to access this resource");
    }
}