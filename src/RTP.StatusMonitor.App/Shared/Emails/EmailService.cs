using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Shared.Emails;

public class EmailService
{
    const int TIME_OUT = 15000; // timeout in 15 seconds
    private readonly EmailOptions _emailOptions;
    const string SenderDisplayName = "Real Time Power Analytics";
    public EmailService(IOptions<EmailOptions> emailOptions)
    => _emailOptions = emailOptions.Value;

    public void SendEmail(
        List<string> recipients,
        string subject,
        string body,
        bool isBodyHtml,
        List<Attachment> attachments)
    {
        try
        {
            // Prepare the message
            MailMessage mailMessage = new()
            {
                From = new MailAddress(
                    address: _emailOptions.Address,
                    displayName: SenderDisplayName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isBodyHtml,
            };

            // Add the recipients
            recipients
                .ForEach(recipient => mailMessage.To.Add(recipient));

            // Attach the file attachments
            attachments
                .ForEach(attachment => mailMessage.Attachments.Add(attachment));

            // Configure the SMTP client
            SmtpClient smtpClient = new(_emailOptions.Host)
            {
                Port = _emailOptions.Port,
                Credentials = new NetworkCredential(
                    _emailOptions.Username, _emailOptions.Password),
                EnableSsl = true,
                Timeout = TIME_OUT,
                DeliveryMethod = SmtpDeliveryMethod.Network
            };

            // Send the email
            smtpClient.Send(mailMessage);
        }
        catch (Exception ex)
        {
            throw new Exception("EmailMessageError: " + ex.Message);
        }
    }
}
