using System.Net;
using System.Runtime.Serialization;

namespace RTP.StatusMonitor.App.Shared.Exceptions;

public class NotFoundException : Exception, IServiceException
{
    public NotFoundException() : base() { }
    protected NotFoundException(
        SerializationInfo info,
        StreamingContext context) : base(info, context) { }
    public NotFoundException(string? message) : base(message) { }
    public NotFoundException(string? message, Exception? innerException) : base(message, innerException) { }

    public HttpStatusCode StatusCode => HttpStatusCode.NotFound;
    public string ErrorMessage => Message;
}