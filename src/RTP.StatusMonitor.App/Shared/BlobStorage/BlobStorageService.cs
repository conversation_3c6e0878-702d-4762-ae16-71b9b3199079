using Azure.Storage.Files.DataLake;
using Azure.Storage.Files.DataLake.Models;
using Azure.Storage.Sas;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Shared.BlobStorage;
public class BlobStorageService(
    IOptions<BlobStorageOptions> blobStorageOptions,
    IMemoryCache memoryCache,
    ILogger<BlobStorageService> log) : IBlobStorageService
{
    private readonly BlobStorageOptions _blobStorageOptions = blobStorageOptions.Value;
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly ILogger<BlobStorageService> _log = log;

    /// <summary>
    /// If the directory client can generate a SAS URI, create a SAS token that's valid for one hour, set
    /// the permissions to read and list, and return the SAS URI
    /// </summary>
    /// <param name="storedPolicyName">The name of the stored access policy to use when creating the SAS. If
    /// null, a new access policy is created.</param>
    /// <returns>
    /// A SAS URI for the directory.
    /// </returns>
    // TODO - refactor this method
    public static Uri? GetServiceSasUriForDirectory(
        DataLakeDirectoryClient directoryClient,
        string? storedPolicyName = null)
    {
        if (directoryClient.CanGenerateSasUri)
        {
            // Create a SAS token that's valid for one hour.
            DataLakeSasBuilder sasBuilder = new()
            {
                // Specify the file system name, the path, and indicate that
                // the client object points to a directory.
                FileSystemName = directoryClient.FileSystemName,
                Resource = "d",
                IsDirectory = true,
                Path = directoryClient.Path,
            };

            // If no stored access policy is specified, create the policy
            // by specifying expiry and permissions.
            if (storedPolicyName == null)
            {
                sasBuilder.ExpiresOn = DateTimeOffset.UtcNow.AddHours(1);
                sasBuilder.SetPermissions(
                    DataLakeSasPermissions.Read |
                    DataLakeSasPermissions.List);
            }
            else
            {
                sasBuilder.Identifier = storedPolicyName;
            }

            // Get the SAS URI for the specified directory.
            return directoryClient.GenerateSasUri(sasBuilder);
        }
        else
        {
            throw new InvalidOperationException("DataLakeDirectoryClient must be authorized with Shared Key credentials to create a service SAS.");
        }
    }

    /// <summary>
    /// Get all directories in the container under the specifed path in the blob
    /// </summary>
    /// 
    /// <param name="container"></param>
    /// <param name="directoryPath"></param>
    /// <param name="ct"></param>
    /// 
    /// <returns>a list of directories (if a file) under the specified path</returns>
    public async Task<List<string>> GetFilesAsync(
        string container,
        string directoryPath,
        CancellationToken ct)
    {
        DataLakeFileSystemClient fileSystemClient = new(
            _blobStorageOptions.ConnectionString,
            container);

        // Check if the file system exists
        if (fileSystemClient.Exists(ct) == false)
        {
            return Enumerable.Empty<string>().ToList();
        }

        DataLakeDirectoryClient directoryClient = fileSystemClient.GetDirectoryClient(directoryPath);


        // Check if the directory exists
        if (directoryClient.Exists(ct).Value == false)
        {
            return Enumerable.Empty<string>().ToList();
        }

        List<string> files = [];

        // Iterate through the paths in the directory recursively and add them to the list
        await foreach (PathItem pathItem in directoryClient.GetPathsAsync(
            recursive: true, cancellationToken: ct))
        {
            // If the path item is a file, add it to the list
            if (pathItem.IsDirectory == false)
            {
                files.Add(pathItem.Name);
            }
        }

        return files;
    }

    public Uri? GetServiceSasUri(
        string container,
        string directoryPath,
        string? storedPolicyName = null,
        CancellationToken ct = default)
    {
        // create client to interact with the data lake
        DataLakeDirectoryClient directoryClient = new(
            _blobStorageOptions.ConnectionString,
            container,
            directoryPath);

        if (!directoryClient.Exists(ct))
        {
            _log.LogWarning("Directory {prefix} does not exist", directoryPath);
            return null;
        }

        if (directoryClient.CanGenerateSasUri)
        {
            // Create a SAS token that's valid for one hour.
            DataLakeSasBuilder sasBuilder = new()
            {
                // Specify the file system name, the path, 
                // and indicate that
                // the client object points to a directory.
                FileSystemName = directoryClient.FileSystemName,
                Resource = "d",
                IsDirectory = true,
                Path = directoryClient.Path,
            };

            // If no stored access policy is specified, create the policy
            // by specifying expiry and permissions.
            if (storedPolicyName == null)
            {
                sasBuilder.ExpiresOn = DateTimeOffset.UtcNow.AddHours(1);
                sasBuilder.SetPermissions(DataLakeSasPermissions.Read);
            }
            else
            {
                sasBuilder.Identifier = storedPolicyName;
            }

            // Get the SAS URI for the specified directory.
            return directoryClient.GenerateSasUri(sasBuilder);
        }
        else
        {
            _log.LogError("DataLakeDirectoryClient must be authorized with Shared Key credentials to create a service SAS.");
            return null;
        }
    }
}
