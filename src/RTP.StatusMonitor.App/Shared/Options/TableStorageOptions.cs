namespace RTP.StatusMonitor.App.Shared.Options;

public record TableStorageOptions
{
    public const string SectionName = "TableStorageConfig";
    public string Uri { get; init; } = null!;
    public string AccountName { get; init; } = null!;
    public string AccountKey { get; init; } = null!;

    // NOTE - separate write table in development to avoid conflicts with production data
    // NOTE - use same read table for both development and production
    public string WeatherWriteTable { get; init; } = null!;
    public string WeatherReadTable { get; init; } = null!;
}
