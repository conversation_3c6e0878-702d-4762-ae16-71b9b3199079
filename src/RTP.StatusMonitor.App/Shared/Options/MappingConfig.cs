using AutoMapper;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Common;
using RTP.StatusMonitor.App.Modules.Management.Customers.Commands;
using RTP.StatusMonitor.App.Modules.Management.Customers.Common;
using RTP.StatusMonitor.App.Modules.Management.Sites.Common;
using RTP.StatusMonitor.App.Modules.Management.Sites.UpdateSite;
using RTP.StatusMonitor.App.Modules.Management.Units.Common;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Shared.Options;

public class MappingConfig : Profile
{
    public MappingConfig()
    {
        // auto mapper config for partial update
        CreateMap<int?, int>().ConvertUsing((src, dest) => src ?? dest);
        CreateMap<double?, double>().ConvertUsing((src, dest) => src ?? dest);
        CreateMap<bool?, bool>().ConvertUsing((src, dest) => src ?? dest);
        CreateMap<DateTime?, DateTime>().ConvertUsing((src, dest) => src ?? dest);
        CreateMap<Guid?, Guid>().ConvertUsing((src, dest) => src ?? dest);

        // customer mapping
        CreateMap<Customer, CustomerResponse>();
        CreateMap<UpdateCustomerCommand, Customer>()
          .ForMember(
            dest => dest.DateModified, opt => opt.MapFrom(_ => DateTime.Now))
          .ForAllMembers(opt => opt.Condition((_, __, srcMember) => srcMember != null)); // only map non-null values

        // site mapping
        CreateMap<Site, SiteResponse>()
          .ForMember(
            dest => dest.Customer,
            opt => opt.MapFrom(s => s.Customer.Name));
        CreateMap<UpdateSiteCommand, Site>()
          .ForMember(
            dest => dest.DateModified,
            opt => opt.MapFrom(_ => DateTime.Now))
          .ForAllMembers(opts => opts.Condition((_, __, srcMember) => srcMember != null));

        // block mapping
        CreateMap<Block, BlockResponse>();
        CreateMap<CreateBlockCommand, Block>();
        CreateMap<UpdateBlockCommand, Block>()
          .ForMember(
            dest => dest.DateModified,
            opt => opt.MapFrom(_ => DateTime.Now))
          .ForAllMembers(opts => opts.Condition((_, __, srcMember) => srcMember != null));

        // unit mapping
        CreateMap<Unit, UnitResponse>();
    }
}
