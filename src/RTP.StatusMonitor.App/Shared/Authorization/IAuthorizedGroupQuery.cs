using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Shared.Authorization;


/// <summary>
/// Represents a query for user groups authorization based on the resource id.
/// This query is intended to be used with the pipeline behavior of the MediatR library.
/// </summary>
public interface IAuthorizedGroupQuery
{
    List<Guid> UserGroupsId { get; init; }
    ResourceId ResourceId { get; init; }
}