using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Shared.Authorization;

public sealed class AuthorizationService(
    IDataContext dataContext,
    IMemoryCache memoryCache)
        : IAuthorizationService
{
    private const int CacheExpirationMinutes = 30;
    private readonly IDataContext _dataContext = dataContext;
    private readonly IMemoryCache _memoryCache = memoryCache;


    /// <summary>
    /// Determines whether the user is authorized to access the specified site based on their user groups.
    /// </summary>
    /// 
    /// <param name="userGroupsId">The list of user group IDs that the user belongs to.</param>
    /// <param name="siteId">The ID of the site to check authorization for.</param>
    /// 
    /// <returns>A boolean value indicating whether the user is authorized to access the site.</returns>
    public async Task<bool> CheckUserAccessByGroup(
        List<Guid> userGroupsId,
        Guid? siteId = null,
        Guid? blockId = null,
        Guid? unitId = null,
        CancellationToken ct = default)
    {
        // TODO - need to evolve this caching strategy (adopt for block and unit as well)
        // TODO - need to find a way to invalidate the cache when the user group is updated
        // Create a cache key for the authorization result
        string cacheKey = $"Auth_{siteId}-{string.Join('-', userGroupsId)}";

        // Find the authorization cache result if available
        // If not, create a new cache entry
        bool isAuthorized = await _memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            // Set the cache expiration to n minutes
            entry.AbsoluteExpirationRelativeToNow = TimeSpan
                .FromMinutes(CacheExpirationMinutes);

            if (siteId is not null)
            {
                // Load the site data and the user groups that have access to site data
                var site = await _dataContext.Sites
                    .Select(s => new
                    {
                        SiteId = s.Id,
                        UserGroupsWithAccess = s.GroupPermissions.Select(g => g.GroupId),
                    })
                    .FirstOrDefaultAsync(s => s.SiteId == siteId, ct);

                return
                    site is not null &&
                    site.UserGroupsWithAccess.Any(g => userGroupsId.Contains(g));
            }
            else if (blockId is not null)
            {
                // Load the block data and the user groups that have access to this block
                var block = await _dataContext.Blocks
                    .Include(b => b.Site)
                        .ThenInclude(s => s.GroupPermissions)
                    .Select(b => new
                    {
                        BlockId = b.Id,
                        UserGroupsWithAccess = b.Site.GroupPermissions.Select(g => g.GroupId),
                    })
                    .FirstOrDefaultAsync(b => b.BlockId == blockId, ct);

                return block is not null
                    ? block.UserGroupsWithAccess.Any(g => userGroupsId.Contains(g))
                    : false;

            }
            else if (unitId is not null)
            {
                // Load the unit data and the user groups that have access to this unit
                var unit = await _dataContext.Units
                    .Include(u => u.Block)
                        .ThenInclude(b => b.Site)
                            .ThenInclude(s => s.GroupPermissions)
                    .Select(u => new
                    {
                        UnitId = u.Id,
                        UserGroupsWithAccess = u.Block.Site.GroupPermissions.Select(g => g.GroupId),
                    })
                    .FirstOrDefaultAsync(u => u.UnitId == unitId, ct);

                return
                    unit is not null &&
                    unit.UserGroupsWithAccess.Any(g => userGroupsId.Contains(g));
            }
            else
            { return false; }
        });

        return isAuthorized;
    }


    /// <summary>
    /// Checks if a user has access to a resource based on their group membership.
    /// </summary>
    /// <param name="userGroupsId">The list of user group IDs.</param>
    /// <param name="resourceId">The ID of the resource.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean value indicating whether the user has access to the resource.</returns>
    public async Task<bool> CheckUserAccessByGroup(
        List<Guid> userGroupsId,
        ResourceId resourceId,
        CancellationToken ct = default)
    {
        // Create a cache key for the authorization result
        string cacheKey = $"Auth_{resourceId.Value}-{string.Join('-', userGroupsId)}";

        // Find the authorization cache result if available
        // If not, create a new cache entry
        return await _memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            // Set the cache expiration to n minutes
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes);

            // Use pattern matching to determine the type of resource
            return resourceId switch
            {
                SiteId siteId => await IsUserAllowedAccessToSite(userGroupsId, siteId, ct),
                BlockId blockId => await IsUserAllowedAccessToBlock(userGroupsId, blockId, ct),
                UnitId unitId => await IsUserAllowedAccessToUnit(userGroupsId, unitId, ct),
                _ => false
            };
        });
    }


    /// <summary>
    /// Checks if the groups the user belongs to are allowed access to a specific site.
    /// </summary>
    /// <param name="userGroupsId">The list of user group IDs.</param>
    /// <param name="siteId">The ID of the site.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>True if one of the groups have access to the site, false otherwise.</returns>
    private async Task<bool> IsUserAllowedAccessToSite(
        List<Guid> userGroupsId,
        SiteId siteId,
        CancellationToken ct)
    {
        // Load the site data and the user groups that have access to site data
        var site = await _dataContext.Sites
            .Select(s => new
            {
                SiteId = s.Id,
                UserGroupsWithAccess = s.GroupPermissions.Select(g => g.GroupId),
            })
            .FirstOrDefaultAsync(s => s.SiteId == siteId.Value, ct);

        // Check if the user groups have access to the site
        return site is not null &&
            site.UserGroupsWithAccess
                .Any(userGroupsId.Contains);
    }

    /// <summary>
    /// Checks if one of the groups the user belongs to are allowed access to a specific block. 
    /// If there is at least one group that has access to the block, the user is authorized.
    /// </summary>
    /// <param name="userGroupsId">The list of user group IDs.</param>
    /// <param name="siteId">The ID of the site.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>True if one of the groups have access to the site, false otherwise.</returns>
    private async Task<bool> IsUserAllowedAccessToBlock(
        List<Guid> userGroupsId,
        BlockId blockId,
        CancellationToken ct)
    {
        // Load the block data and the user groups that have access to this block
        var block = await _dataContext.Blocks
            .Include(b => b.Site)
                .ThenInclude(s => s.GroupPermissions)
            .Select(b => new
            {
                BlockId = b.Id,
                UserGroupsWithAccess = b.Site.GroupPermissions.Select(g => g.GroupId),
            })
            .FirstOrDefaultAsync(b => b.BlockId == blockId.Value, ct);

        return block is not null &&
            block.UserGroupsWithAccess
            .Any(userGroupsId.Contains);
    }

    /// <summary>
    /// Checks if one of the groups the user belongs to are allowed access to a specific unit. 
    /// If there is at least one group that has access to the unit, the user is authorized.
    /// </summary>
    /// <param name="userGroupsId">The list of user group IDs.</param>
    /// <param name="siteId">The ID of the site.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>True if one of the groups have access to the unit, false otherwise.</returns>
    private async Task<bool> IsUserAllowedAccessToUnit(
        List<Guid> userGroupsId,
        UnitId unitId,
        CancellationToken ct)
    {
        // Load the unit data and the user groups that have access to this unit
        var unit = await _dataContext.Units
            .Include(u => u.Block)
                .ThenInclude(b => b.Site)
                    .ThenInclude(s => s.GroupPermissions)
            .Select(u => new
            {
                UnitId = u.Id,
                UserGroupsWithAccess = u.Block.Site.GroupPermissions.Select(g => g.GroupId),
            })
            .FirstOrDefaultAsync(u => u.UnitId == unitId.Value, ct);

        return unit is not null &&
            unit.UserGroupsWithAccess
            .Any(userGroupsId.Contains);
    }
}
