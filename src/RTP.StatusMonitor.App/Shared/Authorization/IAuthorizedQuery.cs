namespace RTP.StatusMonitor.App.Shared.Authorization;

public interface IAuthorizedQuery
{
    bool IsAuthorizationEnabled { get; set; } // Use to enable/disable authorization
    List<Guid> UserGroupsId { get; set; } // Use to authorize access based on group membership
    Guid? SiteId { get; set; } // Use to authorize access based on group membership
    Guid? BlockId { get; set; } // Use to authorize access based on group membership
    Guid? UnitId { get; set; } // Use to authorize access based on group membership
}
