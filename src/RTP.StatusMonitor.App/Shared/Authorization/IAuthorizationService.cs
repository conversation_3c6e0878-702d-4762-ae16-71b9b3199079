using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Shared.Authorization;

public interface IAuthorizationService
{
    Task<bool> CheckUserAccessByGroup(
        List<Guid> userGroupsId,
        Guid? siteId = null,
        Guid? blockId = null,
        Guid? unitId = null,
        CancellationToken ct = default);

    Task<bool> CheckUserAccessByGroup(
        List<Guid> userGroupsId,
        ResourceId resourceId,
        CancellationToken ct = default);
}