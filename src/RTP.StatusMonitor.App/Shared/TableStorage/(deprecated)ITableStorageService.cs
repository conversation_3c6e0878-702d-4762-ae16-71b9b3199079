using System.Collections.Concurrent;
using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Entities;

namespace RTP.StatusMonitor.App.Shared.TableStorage;
public interface ITableStorageService
{
    Task<List<OutputEntity>> GetHistoricalOutputAsync(
      string table, List<string> queries, CancellationToken ct);
    OutputEntity? GetOutput(
      string table, string filter, CancellationToken ct);
}

public class TableStorageService(IOptions<TableStorageOptions> tableStorageOptions) : ITableStorageService
{
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;

    /// <summary>
    /// Query Table Storage for the latest value of a tag
    /// </summary>
    /// <param name="table">the name of the table</param>
    /// <param name="filter">the filter to apply to the query. In this case, we're filtering by the Tag
    /// name.</param>
    /// <returns>
    /// The latest output record for the tag (OutputEntity object).
    /// </returns>
    public OutputEntity? GetOutput(
        string table,
        string filter,
        CancellationToken ct)
    {
        try
        {
            // create table client to interact with the Azure table storage
            TableClient tableClient = new(
                new Uri(_tableStorageOptions.Uri),
                table,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            return tableClient.Query<OutputEntity>(
                filter: filter,
                select: new[] {
                    "Tag",
                    "Value",
                    "Quality",
                    "BatchId" },
                cancellationToken: ct).ToList().LastOrDefault();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }

    /// <summary>
    /// Get historical data from Table Storage
    /// </summary>
    /// <param name="table">the name of the table</param>
    /// <param name="queries">filter to apply for the query</param>
    public async Task<List<OutputEntity>> GetHistoricalOutputAsync(
        string table,
        List<string> queries,
        CancellationToken ct)
    {
        try
        {
            // create table client to interact with the Azure table storage
            TableClient tableClient = new(
                new Uri(_tableStorageOptions.Uri),
                table,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));
            ConcurrentBag<OutputEntity> historicals = [];

            // retrieve records in time ranges in parallel
            await Parallel.ForEachAsync(queries, async (query, _) =>
            {
                // get output from table storage
                Azure.AsyncPageable<OutputEntity> res = tableClient.QueryAsync<OutputEntity>(
                    filter: query,
                    maxPerPage: 1000,
                    select: ["Tag", "Value", "Quality", "BatchId"],
                    cancellationToken: ct);

                await foreach (var page in res.AsPages())
                {
                    foreach (var record in page.Values)
                    {
                        historicals.Add(record);
                    }
                }
            });

            return [.. historicals];
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }
}
