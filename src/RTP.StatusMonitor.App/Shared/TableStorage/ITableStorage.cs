using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Shared.Storage;

public interface ITableStorage
{
	/// <summary>
	/// Given the table name and the query 
	/// Query the table storage and return the result
	/// </summary>
	/// 
	/// <param name="tableName">the table to query</param>
	/// <param name="query">the query to get data from table</param>
	/// <param name="ct">cancellation token</param>
	/// 
	/// <returns>a list of TableEntity which is key-value pairs</returns>
	Task<List<TableEntity>> QueryEntitiesAsync(
		string tableName,
		string query,
		CancellationToken ct);


	/// <summary>
	/// Given the table name and a list of queries
	/// Send the queries to table storage in parallel and return the result
	/// </summary>
	/// 
	/// <param name="tableName">the table to query</param>
	/// <param name="queries">the list of queries to send in parallel</param>
	/// <param name="columns">the list of columns to get from table</param>
	/// <param name="ct">cancellation token</param>
	/// 
	/// <returns>a list of TableEntity which is key-value pairs</returns>
	Task<List<TableEntity>> QueryEntitiesAsync(
		string tableName,
		List<string> queries,
		List<string> columns,
		CancellationToken ct);
}