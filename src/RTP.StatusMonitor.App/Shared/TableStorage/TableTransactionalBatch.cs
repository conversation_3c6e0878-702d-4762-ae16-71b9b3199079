using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Shared.Storage;

public static class TableTransactionalBatch
{
    /// <summary>
    /// Given a list of entities, group the entities by partition key and create batches of 100 entities.
    /// </summary>
    /// 
    /// <param name="entities">The list of entities to create batches from</param>
    /// <param name="actionType">The action type for the batch</param>
    /// <typeparam name="T">The type of the entities</typeparam>
    /// 
    /// <returns>A list of batches of entities</returns>
    public static List<List<TableTransactionAction>> CreateBatches<T>(
        this List<T> entities,
        TableTransactionActionType actionType)
        where T : ITableEntity
    {
        // Group entities by partition key
        Dictionary<string, List<T>> groupedEntities = entities
            .GroupBy(e => e.PartitionKey)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Create batches
        List<List<TableTransactionAction>> batches = new();
        foreach (var group in groupedEntities)
        {
            var batchEntities = group.Value
                .Select(entity => new TableTransactionAction(actionType, entity))
                .ToList();

            // Split into batches of 100
            for (int i = 0; i < batchEntities.Count; i += 100)
            {
                batches.Add(batchEntities.Skip(i).Take(100).ToList());
            }
        }

        return batches;
    }
}
