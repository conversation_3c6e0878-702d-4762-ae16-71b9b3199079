using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Shared.Storage;

/// <summary>
/// This class is the base class for all table entities
/// </summary>
public class BaseTableEntity : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public DateTimeOffset? Timestamp { get; set; } = null;
    public ETag ETag { get; set; } = ETag.All;
}
