using System.Collections.Concurrent;
using Azure;
using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Storage;

namespace RTP.StatusMonitor.App.Shared.TableStorage;

public class TableStorage(IOptions<TableStorageOptions> tableStorageOptions) : ITableStorage
{
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;

    /// <inheritdoc />
    public async Task<List<TableEntity>> QueryEntitiesAsync(
        string tableName,
        string query,
        CancellationToken ct)
    {
        try
        {
            // Create a new table client
            TableClient tableClient = new(
                new Uri(_tableStorageOptions.Uri),
                tableName,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            // Query entities from table
            AsyncPageable<TableEntity> result = tableClient.QueryAsync<TableEntity>(
                filter: query,
                cancellationToken: ct);

            // Get all entities from the result
            List<TableEntity> entities = [];
            await foreach (Page<TableEntity> item in result.AsPages())
            {
                entities.AddRange(item.Values);
            }

            return entities;
        }
        catch
        {
            return [];
        }
    }

    /// <inheritdoc />
    public async Task<List<TableEntity>> QueryEntitiesAsync(
        string tableName,
        List<string> queries,
        List<string> columns,
        CancellationToken ct)
    {
        try
        {
            // Create a new table client
            TableClient tableClient = new(
                new Uri(_tableStorageOptions.Uri),
                tableName,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            // Query entities from table in parallel for each query
            ConcurrentBag<TableEntity> entities = [];

            await Parallel.ForEachAsync(queries, async (query, _) =>
            {
                AsyncPageable<TableEntity> res = tableClient.QueryAsync<TableEntity>(
                    filter: query,
                    select: columns,
                    cancellationToken: ct);

                await foreach (Page<TableEntity> page in res.AsPages())
                {
                    for (int i = 0; i < page.Values.Count; i++)
                    {
                        entities.Add(page.Values[i]);
                    }
                }
            });

            return [.. entities];
        }
        catch
        {
            return [];
        }
    }
}
