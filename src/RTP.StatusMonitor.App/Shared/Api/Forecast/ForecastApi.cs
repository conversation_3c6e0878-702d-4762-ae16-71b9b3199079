using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Api.Forecast;

public class ForecastApi
{
    private readonly IForecastApi _forecastApi;
    private readonly InternalApiOptions _options;
    public ForecastApi(IOptions<InternalApiOptions> options)
    {
        _options = options.Value;
        _forecastApi = RestService
            .For<IForecastApi>(_options.BaseUrl);
    }

    public async Task<List<ForecastDataResponse>> GetDayAheadForecastAsync(
        Guid blockId,
        string startDate,
        string endDate,
        TimeSeriesResamplingInterval interval,
        [Body] List<ForecastSeriesRequestBody> request)
        => await _forecastApi.GetDayAheadForecastAsync(
            apiKey: _options.FunctionKey,
            blockId: blockId,
            startDate: startDate,
            endDate: endDate,
            interval: interval,
            request: request);
}