using Refit;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Api.Forecast;

public record ForecastSeriesRequestBody(
    Guid Id,
    string Name,
    string Tag,
    string Calculation,
    string Filter);

public interface IForecastApi
{
    [Post("/internal/block/{blockId}/forecast/dayahead?code={apiKey}&startDate={startDate}&endDate={endDate}&interval={interval}")]
    Task<List<ForecastDataResponse>> GetDayAheadForecastAsync(
        string apiKey,
        Guid blockId,
        string startDate,
        string endDate,
        TimeSeriesResamplingInterval interval,
        [Body] List<ForecastSeriesRequestBody> request);
}