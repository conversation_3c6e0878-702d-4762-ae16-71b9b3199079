using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Modules.Analytics;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Api.Airsonic;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.App.Shared.Api.InternalData.Requests;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Api.InternalData;

public class InternalDataApi(IOptions<InternalApiOptions> options)
{
    private readonly InternalApiOptions _options = options.Value;
    private readonly IInternalDataApi _internalDataApi = RestService
            .For<IInternalDataApi>(options.Value.BaseUrl);

    public async Task<List<AnalyticsResponse>> GetHistoricalMultiSourceDataAsync(
        Guid blockId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AnalyticsSeriesRequestBody> request)
        => await _internalDataApi.GetHistoricalMultiSourceDataAsync(
            apiKey: _options.FunctionKey,
            blockId: blockId,
            startTime: startTime,
            endTime: endTime,
            interval: interval,
            request: request);

    public async Task<List<DataClientHistoricalResponse>> GetActualHistoricalDataAsync(
        Guid blockId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<DataClientSeriesRequestBody> request,
        CancellationToken ct)
        => await _internalDataApi.GetActualHistoricalDataAsync(
            apiKey: _options.FunctionKey,
            blockId: blockId,
            startTime: startTime,
            endTime: endTime,
            interval: interval,
            request: request,
            ct: ct);

    public async Task<List<AirsonicRawDataResponse>> GetAirsonicHistoricalDataAsync(
        Guid unitId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AirsonicSeriesRequestBody> request)
        => await _internalDataApi.GetAirsonicHistoricalDataAsync(
            apiKey: _options.FunctionKey,
            unitId: unitId,
            startTime: startTime,
            endTime: endTime,
            interval: interval,
            request: request);

    public async Task<List<ForecastDataResponse>> GetDayAheadForecastAsync(
        Guid blockId,
        string startDate,
        string endDate,
        TimeSeriesResamplingInterval interval,
        [Body] List<ForecastSeriesRequestBody> request)
        => await _internalDataApi.GetDayAheadForecastAsync(
            apiKey: _options.FunctionKey,
            blockId: blockId,
            startDate: startDate,
            endDate: endDate,
            interval: interval,
            request: request);
}
