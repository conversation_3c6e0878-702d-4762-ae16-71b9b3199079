using Refit;
using RTP.StatusMonitor.App.Modules.Analytics;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Api.Airsonic;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.App.Shared.Api.InternalData.Requests;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Api.InternalData;

public record AnalyticsSeriesRequestBody(
    Guid Id,
    string Name,
    string Filter,
    string Calculation);

public interface IInternalDataApi
{
    [Post("/internal/block/{blockId}/historical/multisource?code={apiKey}&startTime={startTime}&endTime={endTime}&interval={interval}")]
    Task<List<AnalyticsResponse>> GetHistoricalMultiSourceDataAsync(
        string apiKey,
        Guid blockId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AnalyticsSeriesRequestBody> request);

    [Post("/internal/block/{blockId}/historical/dataclient?code={apiKey}&startTime={startTime}&endTime={endTime}&interval={interval}")]
    Task<List<DataClientHistoricalResponse>> GetActualHistoricalDataAsync(
        string apiKey,
        Guid blockId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<DataClientSeriesRequestBody> request,
        CancellationToken ct);

    [Post("/internal/unit/{unitId}/historical/airsonic?code={apiKey}&startTime={startTime}&endTime={endTime}&interval={interval}")]
    Task<List<AirsonicRawDataResponse>> GetAirsonicHistoricalDataAsync(
        string apiKey,
        Guid unitId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AirsonicSeriesRequestBody> request);

    [Post("/internal/block/{blockId}/forecast/dayahead?code={apiKey}&startDate={startDate}&endDate={endDate}&interval={interval}")]
    Task<List<ForecastDataResponse>> GetDayAheadForecastAsync(
        string apiKey,
        Guid blockId,
        string startDate,
        string endDate,
        TimeSeriesResamplingInterval interval,
        [Body] List<ForecastSeriesRequestBody> request);
}
