using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.Domain.TimeSeries;
namespace RTP.StatusMonitor.App.Shared.Api.Airsonic;

public class AirsonicApi
{
    private readonly IAirsonicApi _airsonicApi;
    private readonly InternalApiOptions _options;
    public AirsonicApi(IOptions<InternalApiOptions> options)
    {
        _options = options.Value;
        _airsonicApi = RestService
            .For<IAirsonicApi>(_options.BaseUrl);
    }

    /// <summary>
    /// Retrieves historical data from the Airsonic API.
    /// </summary>
    /// <param name="unitId">The ID of the unit.</param>
    /// <param name="startTime">The start time of the data range.</param>
    /// <param name="endTime">The end time of the data range.</param>
    /// <param name="interval">The resampling interval for the time series data.</param>
    /// <param name="request">The list of Airsonic series request bodies.</param>
    /// <returns>A list of Airsonic historical data responses.</returns>
    public async Task<List<AirsonicRawDataResponse>> GetHistoricalDataAsync(
        Guid unitId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AirsonicSeriesRequestBody> request
        )
        => await _airsonicApi.GetHistoricalDataAsync(
            apiKey: _options.FunctionKey,
            unitId: unitId,
            startTime: startTime,
            endTime: endTime,
            interval: interval,
            request: request);
}

public record AirsonicRawDataResponse(
    Guid Id,
    string Name,
    string Alias,
    List<object> Values,
    List<string> Timestamps);
