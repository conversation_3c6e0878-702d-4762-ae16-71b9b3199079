using Refit;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Api.Airsonic;

public record AirsonicSeriesRequestBody(
    Guid Id,
    string Name,
    string Alias,
    string Calculation,
    string Filter);

public interface IAirsonicApi
{
    [Post("/internal/unit/{unitId}/historical/airsonic?code={apiKey}&startTime={startTime}&endTime={endTime}&interval={interval}")]
    Task<List<AirsonicRawDataResponse>> GetHistoricalDataAsync(
        string apiKey,
        Guid unitId,
        string startTime,
        string endTime,
        TimeSeriesResamplingInterval interval,
        [Body] List<AirsonicSeriesRequestBody> request);
}