using System.Globalization;
using Azure.Storage.Blobs;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Kiota.Abstractions;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.App.Shared.Repository.Forecast;

public class ForecastRepository(IOptions<BlobStorageOptions> options)
{
    private readonly BlobStorageOptions _options = options.Value;

    /// <summary>
    /// This method will get the day ahead forecast file for a given block
    /// </summary>
    /// <param name="customerName">The name of the customer</param>
    /// <param name="siteName">The name of the site</param>
    /// <param name="blockName">The name of the block</param>
    /// <param name="forecastDate">The date of the day ahead forecast. Will get the 6AM forecast file for THIS requested date</param>
    /// <param name="ct"></param>
    /// <returns>The list of all forecast data from the day ahead file</returns>
    public async Task<List<ForecastDataDto>> GetDayAheadForecastAsync(
        Block block,
        List<string> tags,
        DateOnly forecastDate,
        CancellationToken ct)
    {
        // Get the container and directory of the day ahead file
        string container = GetForecastContainer(
            customerName: block.Site.Customer.Name,
            siteName: block.Site.Name);
        string directoryPath = GetForecastBlobPath(
            block: block,
            forecastType: new DayAheadForecast(forecastDate));

        List<ForecastDataDto> dayAheadForecast = await ReadForecastBlob(
            container, directoryPath, ct);

        // Filter any tags not requested
        List<ForecastDataDto> forecastFilteredByTag = dayAheadForecast
            .Where(x => tags.Contains(x.Tag))
            .ToList();

        // Filter any dates other than the current date (the day ahead)
        DateTime filterStartTime = new(
            forecastDate.Year,
            forecastDate.Month,
            forecastDate.Day, 0, 0, 0);
        DateTime filterEndTime = filterStartTime.AddDays(1);

        List<ForecastDataDto> forecastData = [];
        foreach (ForecastDataDto tagForecast in forecastFilteredByTag)
        {
            List<DateTime> timestamps = [];
            List<object> values = [];

            for (int i = 0; i < tagForecast.Timestamps.Count; i++)
            {
                if (tagForecast.Timestamps[i] >= filterStartTime
                    && tagForecast.Timestamps[i] <= filterEndTime)
                {
                    timestamps.Add(tagForecast.Timestamps[i]);
                    values.Add(tagForecast.Values[i]);
                }
            }

            forecastData.Add(new ForecastDataDto(
                Tag: tagForecast.Tag,
                Timestamps: timestamps,
                Values: values));
        }

        return forecastData;
    }

    /// <summary>
    /// Get the day ahead forecast (made on previous date) for a specified date and time 
    /// </summary>
    public async Task<List<ForecastDataDto>> GetDayAheadForecastForDateTimeAsync(
        Block block,
        List<string> tags,
        DateOnly date,
        int hourEnding,
        CancellationToken ct)
    {
        // Get the container and directory of the day ahead file
        string container = GetForecastContainer(
            customerName: block.Site.Customer.Name,
            siteName: block.Site.Name);
        string directoryPath = GetForecastBlobPath(
            block: block,
            forecastType: new DayAheadForecast(date));

        List<ForecastDataDto> dayAheadForecast = await ReadForecastBlob(
            container, directoryPath, ct);

        // Filter any tags not requested
        List<ForecastDataDto> forecastFilteredByTag = [.. dayAheadForecast
            .Where(x => tags.Contains(x.Tag))];

        // Filter any dates other than the current date (the day ahead)
        DateTime filterStartTime = new(
            date.Year,
            date.Month,
            date.Day, 0, 0, 0);
        DateTime filterEndTime = filterStartTime.AddDays(1);

        List<ForecastDataDto> forecastData = [];
        foreach (ForecastDataDto tagForecast in forecastFilteredByTag)
        {
            List<DateTime> timestamps = [];
            List<object> values = [];

            for (int i = 0; i < tagForecast.Timestamps.Count; i++)
            {
                if (tagForecast.Timestamps[i] >= filterStartTime
                    && tagForecast.Timestamps[i] <= filterEndTime)
                {
                    // Get only the forecasted hour ending needed
                    if (tagForecast.Timestamps[i].Hour == hourEnding)
                    {
                        timestamps.Add(tagForecast.Timestamps[i]);
                        values.Add(tagForecast.Values[i]);
                    }
                }
            }

            forecastData.Add(new ForecastDataDto(
                Tag: tagForecast.Tag,
                Timestamps: timestamps,
                Values: values));
        }

        return forecastData;
    }



    /// <summary>
    /// Get day ahead forecast for a range of dates
    /// </summary>
    /// <param name="block">The block to get the forecast</param>
    /// <param name="startDate">The start date of the forecast</param>
    /// <param name="endDate">The end date of the forecast</param>
    /// <param name="ct"></param>
    /// <returns>The list of the day ahead forecast for each day</returns>
    public async Task<List<ForecastDataDto>> GetDayAheadForecastAsync(
        Block block,
        List<string> tags,
        DateOnly startDate,
        DateOnly endDate,
        CancellationToken ct)
    {
        // If no tags is requested, no need to read blob storage
        if (tags.Count == 0)
            return [];

        // Create lookup table (dictionary) to store forecast data by tag
        Dictionary<string, ForecastDataDto> forecastDataLookupByTag = [];

        // Query the day ahead forecast for each day
        DateOnly currentDate = startDate;
        while (currentDate < endDate)
        {
            // Get the day ahead forecast of all requested tag for current day
            List<ForecastDataDto> currentDayDayAheadForecast = await GetDayAheadForecastAsync(
                block: block,
                tags: tags,
                forecastDate: currentDate,
                ct: ct);

            // Find forecast with the same tag and merge the data together
            foreach (var item in currentDayDayAheadForecast)
            {
                if (forecastDataLookupByTag.ContainsKey(item.Tag))
                {
                    forecastDataLookupByTag[item.Tag].Timestamps.AddRange(item.Timestamps);
                    forecastDataLookupByTag[item.Tag].Values.AddRange(item.Values);
                }
                else
                {
                    forecastDataLookupByTag.Add(item.Tag, item);
                }
            }

            // Otherwise, increment the date to get the next day forecast
            currentDate = currentDate.AddDays(1);
        }

        return [.. forecastDataLookupByTag.Values];
    }

    /// <summary>
    /// This method will get the latest forecast file for a given block
    /// </summary>
    /// <param name="customerName">The name of the customer</param>
    /// <param name="siteName">The name of the site</param>
    /// <param name="blockName">The name of the block</param>
    /// <param name="ct"></param>
    /// <returns>The list of all forecast data from the latest forecast</returns>
    public async Task<List<ForecastDataDto>> GetLatestForecastAsync(
        Block block,
        List<string> tags,
        CancellationToken ct)
    {
        // No tags requested, then dont need to read blob storage
        if (tags.Count == 0)
            return [];

        // Otherwise, read the blob to get real time forecast
        List<ForecastDataDto> realtimeForecast = await ReadForecastBlob(
            container: GetForecastContainer(
                customerName: block.Site.Customer.Name,
                siteName: block.Site.Name),
            directoryPath: GetForecastBlobPath(
                block: block,
                forecastType: new RealTimeForecast()),
            ct: ct);

        // Filter the forecast data by tags
        return [.. realtimeForecast.Where(x => tags.Contains(x.Tag))];
    }


    /// <summary>
    /// This method will get the latest forecast file for a given block
    /// </summary>
    /// <param name="customerName">The name of the customer</param>
    /// <param name="siteName">The name of the site</param>
    /// <param name="blockName">The name of the block</param>
    /// <param name="ct"></param>
    /// <returns>The list of all forecast data from the latest forecast</returns>
    public async Task<List<ForecastDataDto>> GetLatestForecastForDateTimeAsync(
        Block block,
        List<string> tags,
        DateTime dateTime,
        CancellationToken ct)
    {
        // No tags requested, then dont need to read blob storage
        if (tags.Count == 0)
            return [];

        // Otherwise, read the blob to get real time forecast
        List<ForecastDataDto> realtimeForecast = await ReadForecastBlob(
            container: GetForecastContainer(
                customerName: block.Site.Customer.Name,
                siteName: block.Site.Name),
            directoryPath: GetForecastBlobPath(
                block: block,
                forecastType: new RealTimeForecast()),
            ct: ct);

        // Get the forecast for only the date time and tags requested
        List<ForecastDataDto> result = [];
        foreach (ForecastDataDto? forecast in realtimeForecast.Where(x => tags.Contains(x.Tag)))
        {
            List<DateTime> timestamps = [];
            List<object> values = [];
            for (int i = 0; i < forecast.Timestamps.Count; i++)
            {
                DateTime currentForecastTime = forecast.Timestamps[i];
                if (currentForecastTime.Date == dateTime.Date && currentForecastTime.Hour == dateTime.Hour)
                {
                    timestamps.Add(currentForecastTime);
                    values.Add(forecast.Values[i]);
                }
            }

            result.Add(new(forecast.Tag, timestamps, values));
        }

        return result;
    }

    /// <summary>
    /// Get the list of forecast tags for a specific block
    /// </summary>
    /// <param name="block">The block to get the forecast</param>
    /// <param name="ct"></param>
    /// <returns>The list of forecast tags</returns>
    public async Task<List<string>> GetForecastTagsAsync(
        Block block,
        CancellationToken ct)
    {
        List<ForecastDataDto> realtimeForecast = await ReadForecastBlob(
            container: GetForecastContainer(
                customerName: block.Site.Customer.Name,
                siteName: block.Site.Name),
            directoryPath: GetForecastBlobPath(
                block: block,
                forecastType: new RealTimeForecast()),
            ct: ct);

        // Filter the forecast data by tags
        return realtimeForecast.Select(x => x.Tag).ToList();
    }


    /// <summary>
    /// Remap the forecast container in case of demo site
    /// </summary> 
    private static string GetForecastContainer(
        string customerName,
        string siteName) => siteName switch
        {
            "GT24" => "vistra",
            "7FA" => "vandolah",
            "W501G" => "vistra",
            _ => customerName.ToLower(),
        };

    /// <summary>
    /// This method will return the path to the forecast file for the block
    /// If forecast data is not specified, then the latest forecast will be returned by default
    /// Example: bellingham/block1/forecast/Bellingham_1_Forecast.txt
    /// </summary>
    private static string GetForecastBlobPath(
        Block block,
        ForecastDataType forecastType)
    {
        // Remap site/block name in case of demo site
        (string formattedSiteName, string formattedBlockName) = RemapDemoSite(
            block.Site.Name, block.Name);

        return forecastType switch
        {
            RealTimeForecast => $"{formattedSiteName}/block{formattedBlockName}/forecast/current/{CaptialiseName(block.Site.Name)}_{CaptialiseName(block.Name)}_Forecast.txt",
            DayAheadForecast dayAheadForecast => $"{formattedSiteName}/block{formattedBlockName}/forecast/archive/{dayAheadForecast.DayAheadDate:yyyyMMdd}0600_{CaptialiseName(block.Site.Name)}_{CaptialiseName(block.Name)}_Forecast.txt",
            _ => throw new NotImplementedException()
        };
    }


    /// <summary>
    /// This method will read the forecast data csv file from blob storage and parse the data.
    /// It will read the entire data file and return all tags
    /// </summary>
    /// 
    /// <param name="container">The container of the forecast file</param>
    /// <param name="directoryPath">The path to the forecast file</param>
    /// <param name="ct"></param>
    /// 
    /// <returns>The forecast data as a list of time series data</returns>
    private async Task<List<ForecastDataDto>> ReadForecastBlob(
        string container,
        string directoryPath,
        CancellationToken ct)
    {
        BlobClient blobClient = new(
            connectionString: _options.ConnectionString,
            blobContainerName: container,
            blobName: directoryPath);

        // If the blob doesn't exist, return an empty response
        if (blobClient.Exists(ct) == false)
        {
            return Enumerable.Empty<ForecastDataDto>().ToList();
        }

        // Download data from blob
        var res = await blobClient.DownloadAsync(ct);

        if (res.Value.Content is null)
        {
            return Enumerable.Empty<ForecastDataDto>().ToList();
        }

        // And parse it as csv file
        using var reader = new StreamReader(res.Value.Content);

        using CsvReader csv = new(
            reader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            { Delimiter = "\t", HasHeaderRecord = true, });

        // Read the header
        csv.Read();
        csv.ReadHeader();

        // Skip the eng/unit row
        csv.Read();

        // If there is no header (no data), then return empty response
        string[]? headerColumns = csv.Context.Reader?.HeaderRecord;
        if (headerColumns is null)
        {
            return Enumerable.Empty<ForecastDataDto>().ToList();
        }

        // The data starts at index 4
        int columnStartIdx = 4;

        Dictionary<string, List<Tuple<DateTime, object>>> forecastGroupedByTag = new();

        while (csv.Read())
        {
            string? timestamp = csv.GetField(0);
            for (int i = columnStartIdx; i < headerColumns.Length; i++)
            {
                object? value = csv.GetField(i);

                // If the value can be parse to double, then it is a number
                // Otherwise, keep it as tring
                if (double.TryParse(value?.ToString(), out double recordValue))
                {
                    value = recordValue;
                }

                // If data row is null then ignore it
                if (value is null || timestamp is null)
                    continue;

                // For each metric, add the timestamp and value
                string capitalisedTag = headerColumns[i].ToUpper();

                // Either add or update the forecast data to the dictionary
                forecastGroupedByTag.AddOrUpdate(
                    key: capitalisedTag,
                    value: new Tuple<DateTime, object>(DateTime.Parse(timestamp), value));
            }
        }

        // Map the dictionary of forecast data to a list of time series data
        return forecastGroupedByTag.Select(x => new ForecastDataDto(
            Tag: x.Key,
            Timestamps: x.Value.Select(y => y.Item1).ToList(),
            Values: x.Value.Select(y => y.Item2).ToList())).ToList();
    }

    /// <summary>
    /// Convert to title case
    /// </summary>
    private static string CaptialiseName(string s)
        => new CultureInfo("en-US").TextInfo.ToTitleCase(s);

    /// <summary>
    /// Remove empty string and convert to lower case
    /// </summary>
    private static string FormatName(string s)
        => s.ToLower().Replace(" ", string.Empty);

    /// <summary>
    /// Remap site/block name in case of demo site
    /// </summary>
    private static (string, string) RemapDemoSite(string siteName, string blockName) => FormatName(siteName) switch
    {
        "7fa" => ("vandolah", "4"),
        "gt24" => ("lakeroad", "3"),
        "w501g" => ("ennis", "1"),
        _ => (FormatName(siteName), FormatName(blockName))
    };
}
