using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public static class ForecastDataDtoMapping
{
    /// <summary>
    /// Converts a <see cref="ForecastDataDto"/> to a <see cref="TimeSeriesData"/>.
    /// </summary>
    /// 
    /// <param name="data">The <see cref="ForecastDataDto"/> to convert.</param>
    /// 
    /// <returns>The converted <see cref="TimeSeriesData"/>.</returns>
    public static TimeSeriesData ToTimeSeriesData(
        this ForecastDataDto data)
        => new(
            Tag: data.Tag,
            Values: data.Values
                .ToArray(),
            Timestamps: data.Timestamps.ToArray());
}