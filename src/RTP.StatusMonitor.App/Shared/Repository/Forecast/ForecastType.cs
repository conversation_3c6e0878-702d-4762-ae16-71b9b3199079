namespace RTP.StatusMonitor.App.Shared.Repository.Forecast;

public abstract record ForecastDataType;

public record RealTimeForecast() : ForecastDataType;

/// <summary>
/// Represents a day-ahead forecast.
/// </summary>
public record DayAheadForecast : ForecastDataType
{
    public DateOnly DayAheadDate { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DayAheadForecast"/> class with the specified forecast date.
    /// </summary>
    /// <param name="forecastDate">The forecast date.</param>
    public DayAheadForecast(DateOnly forecastDate) => DayAheadDate = forecastDate.AddDays(-1);
}
