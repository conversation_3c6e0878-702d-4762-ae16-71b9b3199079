using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.App.Shared.Repository.InputData;

public class InputDataRepository
{
    private readonly TableStorageOptions _tableStorageOptions;

    public InputDataRepository(
        IOptions<TableStorageOptions> tableStorageOptions)
    => _tableStorageOptions = tableStorageOptions.Value;

    public List<InputDataDto> GetInputData(Block block)
    {
        try
        {
            return new TableClient(
                endpoint: new Uri(_tableStorageOptions.Uri),
                tableName: $"{block.Site.Customer.Name.ToLower()}Input",
                credential: new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName, _tableStorageOptions.AccountKey))
            .Query<InputDataDto>(
                $"PartitionKey eq '{block.Site.Name}-{block.Name}-1'")
            .ToList();
        }
        catch
        {
            return new List<InputDataDto>();
        }
    }
}