using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public static class StatisticsDataClientDtoMapping
{
    /// <summary>
    /// Map from the time series data to the stats data client DTO.
    /// depending on the interval, the stats are calculated for the interval.
    /// </summary>
    /// <param name="data">The time series data</param>
    /// <param name="block">The block of the data</param>
    /// <returns>A list of stats data client DTO</returns>
    public static List<StatisticsDataClientDto> ToStatisticsDataClientDto(
        this List<Domain.TimeSeries.Types.TimeSeriesData> data,
        Block block,
        TimeSeriesResamplingInterval interval
    )
    {
        // Get the timezome info of the block
        TimeZoneInfo tzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);

        List<StatisticsDataClientDto> entities = [];
        foreach (Domain.TimeSeries.Types.TimeSeriesData timeSeriesData in data)
        {
            List<(DateTime, double[])> stats = timeSeriesData.Downsample(
                interval,
                new Func<IEnumerable<double>, double>[]
                {
                    TimeSeriesDownsampling.Min,
                    TimeSeriesDownsampling.Max,
                    TimeSeriesDownsampling.Average,
                    TimeSeriesDownsampling.StdDev,
                }
            );

            entities.AddRange(
                stats.Select(s => new StatisticsDataClientDto()
                {
                    PartitionKey = $"{block.Alias}-{timeSeriesData.Tag}-{interval}",
                    RowKey = new DateTimeWithZone(s.Item1, tzInfo).UnixTimeInSeconds.ToString(),
                    Tag = timeSeriesData.Tag,
                    LocalTimestamp = s.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
                    Min = s.Item2[0],
                    Max = s.Item2[1],
                    Avg = s.Item2[2],
                    StdDev = s.Item2[3],
                    // Depending on the interval => get the number of points for the interval (day/hour/etc...)
                    Points = interval switch
                    {
                        TimeSeriesResamplingInterval.Day => timeSeriesData
                            .Timestamps.Count(t => t.Date == s.Item1.Date),
                        TimeSeriesResamplingInterval.Hour => timeSeriesData
                            .Timestamps.Count(t => t.Date == s.Item1.Date && t.Hour == s.Item1.Hour),
                        _ => throw new NotImplementedException(),
                    },
                })
            );
        }

        return entities;
    }

    /// <summary>
    /// Converts a list of <see cref="StatisticsDataClientDto"/> objects to a list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.
    /// </summary>
    /// <param name="data">The list of <see cref="StatisticsDataClientDto"/> objects to convert.</param>
    /// <returns>A list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.</returns>
    public static List<Domain.TimeSeries.Types.TimeSeriesData> ToTimeSeriesData(
        this List<StatisticsDataClientDto> data
    )
    {
        // Group the statistics data by tag first
        Dictionary<string, List<StatisticsDataClientDto>> groupedData = data.GroupBy(x => x.Tag)
            .ToDictionary(x => x.Key, x => x.ToList());

        // Get the hourly time series data for each tag
        // NOTE - for now only get the average (will refactor for more flexibility later etc min/max/etc...)
        List<Domain.TimeSeries.Types.TimeSeriesData> timeSeriesData = new();
        foreach (var item in groupedData)
       {
            timeSeriesData.Add(
                new Domain.TimeSeries.Types.TimeSeriesData(
                    Tag: item.Key,
                    Values: item.Value.Select(v => (object)v.Avg).ToArray(),
                    Timestamps: item.Value.Select(v => DateTime.Parse(v.LocalTimestamp)).ToArray()
                )
            );
        }

        return timeSeriesData;
    }
}
