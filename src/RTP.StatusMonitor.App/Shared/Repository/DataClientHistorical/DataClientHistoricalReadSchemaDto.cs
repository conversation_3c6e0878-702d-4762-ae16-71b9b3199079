using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public class DataClientHistoricalReadSchemaDto : BaseTableEntity
{
    public string Tag { get; init; } = string.Empty;
    public string LocalTimestamp { get; init; } = string.Empty;
    public double Value { get; init; }
    public bool Quality { get; init; }
}

public static class DataClientReadSchemaMapping
{
    /// <summary>
    /// Map from the data client write schema to the data client read schema.
    /// </summary>
    ///
    /// <param name="data">a list of data client write schema</param>
    /// <param name="block">the block of the data => use to compute local time</param>
    ///
    /// <returns>a list of data client read schema</returns>
    public static List<DataClientHistoricalReadSchemaDto> ToDataClientReadSchemaDto(
        this List<DataClientHistoricalWriteSchemaDto> data,
        Block block
    )
    {
        List<DataClientHistoricalReadSchemaDto> results = [];
        for (int i = 0; i < data.Count; i++)
        {
            SiteLocalTime siteLocalTime = SiteLocalTime.Create(
                block.Site,
                DateTimeOffset.FromUnixTimeSeconds(data[i].BatchId).DateTime
            );

            results.Add(
                new DataClientHistoricalReadSchemaDto
                {
                    PartitionKey =
                        $"{block.Alias}-{data[i].RowKey}-{siteLocalTime.Value:yyyy-MM-dd}",
                    RowKey = data[i].BatchId.ToString(),
                    Tag = data[i].RowKey,
                    LocalTimestamp = siteLocalTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    Value = data[i].Value,
                    Quality = data[i].Quality == "Good",
                }
            );
        }

        return results.DistinctBy(r => new { r.PartitionKey, r.RowKey }).ToList();
    }

    /// <summary>
    /// Map from the new data client table DTO to the time series data.
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static List<TimeSeriesData> ToTimeSeriesData(
        this IEnumerable<DataClientHistoricalReadSchemaDto> data
    ) =>
        data.GroupBy(d => d.Tag)
            .Select(g => new TimeSeriesData(
                Tag: g.Key,
                Values: g.Select(v => (object)v.Value).ToArray(),
                Timestamps: g.Select(v => DateTime.Parse(v.LocalTimestamp)).ToArray()
            ))
            .ToList();
}
