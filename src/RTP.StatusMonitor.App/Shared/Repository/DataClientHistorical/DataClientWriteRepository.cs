using System.Collections.Concurrent;
using Azure.Data.Tables;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.Blocks;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public class DataClientWriteRepository : TableRepository
{
    private readonly ILogger<DataClientWriteRepository> _logger;

    public DataClientWriteRepository(
        IOptions<TableStorageOptions> tableStorageOptions,
        ILogger<DataClientWriteRepository> logger
    )
        : base(tableStorageOptions) => _logger = logger;

    /// <summary>
    /// Migrate historical data from the existing schema to the new schema
    /// </summary>
    ///
    /// <param name="block">The block to migrate the data</param>
    /// <param name="entities">The list of historical entities from old schema to migrate</param>
    /// <param name="ct"></param>
    ///
    /// <returns></returns>
    public async Task<List<TableEntity>> MigrateHistoricalDataAsync(
        Block block,
        List<DataClientHistoricalWriteSchemaDto> entities,
        CancellationToken ct
    )
    {
        // Create table client instance
        string tableName = $"data{block.Site.Name.Replace(" ", string.Empty)}";
        TableClient tableClient = CreateTableClient(tableName);

        // Map from existing data client schema to the new schema
        List<DataClientHistoricalReadSchemaDto> newEntities = entities.ToDataClientReadSchemaDto(
            block
        );

        // Create batches of upsert replace actions (100 entities per batch)
        List<List<TableTransactionAction>> batches = newEntities.CreateBatches(
            TableTransactionActionType.UpsertReplace
        );

        // Execute the batches in parallel
        ConcurrentBag<DataClientHistoricalReadSchemaDto> migratedEntities = [];
        await Parallel.ForEachAsync(
            batches,
            async (batch, _) =>
            {
                // Submit the batch transaction
                Azure.Response<IReadOnlyList<Azure.Response>> results = await tableClient.SubmitTransactionAsync(batch, ct);

                // If the batch succeed, capture the entities for later deletion
                if (results.GetRawResponse().Status == 202)
                {
                    for (int i = 0; i < results.Value.Count; i++)
                    {
                        migratedEntities.Add((DataClientHistoricalReadSchemaDto)batch[i].Entity);
                    }
                }
            }
        );

        // Create the entities to delete
        List<TableEntity> entitiesToDelete = migratedEntities
            .Select(entity => new TableEntity()
            {
                PartitionKey = $"{block.Alias}-{entity.RowKey}",
                RowKey = entity.Tag,
            })
            .ToList();

        return entitiesToDelete;
    }

    /// <summary>
    /// Delete the write schema data from the table storage
    /// </summary>
    ///
    /// <param name="block">The block for the data</param>
    /// <param name="entities">The entities to delete
    /// <param name="ct"></param>
    ///
    /// <returns></returns>
    public async Task DeleteWriteSchemaDataAsync(
        Block block,
        List<TableEntity> entities,
        CancellationToken ct
    )
    {
        // Create table client instance
        string tableName = $"dataclient{block.Site.Customer.Name}";
        TableClient tableClient = CreateTableClient(tableName);

        // Create batches of delete actions (100 entities per batch)
        List<List<TableTransactionAction>> batches = entities.CreateBatches(
            TableTransactionActionType.Delete
        );

        // Capture the status of each transaction in the batch
        ConcurrentDictionary<TableTransactionAction, int> test = new();
        await Parallel.ForEachAsync(
            batches,
            async (batch, _) =>
            {
                try
                {
                    var results = await tableClient.SubmitTransactionAsync(batch, ct);

                    // Log the status of the transaction
                    if (results.GetRawResponse().Status != 202)
                    {
                        _logger.LogWarning(
                            $"Batch {batch[0].Entity.PartitionKey} failed with status {results.GetRawResponse().Status}"
                        );
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception
                    _logger.LogError(ex.Message);
                }
            }
        );
    }

    /// <summary>
    /// Given the time series data, compute the statistics then transform to schema for statistics and upload to the table storage
    /// </summary>
    public async Task UploadStatisticsDataAsync(
        Block block,
        List<StatisticsDataClientDto> data,
        CancellationToken ct
    )
    {
        // Create table client instance
        string tableName = $"data{block.Site.Name.Replace(" ", string.Empty)}";
        TableClient tableClient = CreateTableClient(tableName);

        // Create batches of the statistics entities (100 items in each batch)
        List<List<TableTransactionAction>> batches = data.CreateBatches(
            TableTransactionActionType.UpsertReplace
        );

        // Submit to the table storage
        try
        {
            foreach (var batch in batches)
            {
                var results = await tableClient.SubmitTransactionAsync(batch, ct);

                if (results.GetRawResponse().Status != 202)
                {
                    // Log the status of the transaction
                    _logger.LogWarning(
                        $"Batch {batch[0].Entity.PartitionKey} failed with status {results.GetRawResponse().Status}"
                    );
                }
            }
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError(ex.Message);
        }
    }
}
