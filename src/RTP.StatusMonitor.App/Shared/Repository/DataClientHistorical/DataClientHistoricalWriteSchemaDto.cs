using RTP.StatusMonitor.App.Shared.Storage;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public class DataClientHistoricalWriteSchemaDto : BaseTableEntity
{
    /// <summary>
    /// The batch id of the data (unix time in seconds)
    /// </summary>
    public int BatchId { get; init; }
    public string Tag { get; init; } = string.Empty;
    public double Value { get; init; }
    public string Quality { get; init; } = string.Empty;
}

public static class DataClientWriteSchemaMapping
{
    /// <summary>
    /// Converts a collection of <see cref="DataClientHistoricalWriteSchemaDto"/> objects to a list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.
    /// </summary>
    /// <param name="data">The collection of <see cref="DataClientHistoricalWriteSchemaDto"/> objects to convert.</param>
    /// <param name="siteTimeZone">The time zone information for the site.</param>
    /// <returns>A list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.</returns>
    public static List<Domain.TimeSeries.Types.TimeSeriesData> ToTimeSeriesData(
        this IEnumerable<DataClientHistoricalWriteSchemaDto> data,
        TimeZoneInfo siteTimeZone
    )
    {
        List<Domain.TimeSeries.Types.TimeSeriesData> results = [];

        // NOTE - need to group by RowKey => formatted to remove special character which will cause issues for table storage PartitionKey and RowKey
        foreach (
            IGrouping<string, DataClientHistoricalWriteSchemaDto> item in data.GroupBy(x =>
                x.RowKey
            )
        )
        {
            results.Add(
                new Domain.TimeSeries.Types.TimeSeriesData(
                    Tag: item.Key,
                    Values: [.. item.Select(x => (object)x.Value)],
                    Timestamps:
                    [
                        .. item.Select(x =>
                        {
                            // Get the date time utc from batch id
                            DateTime utcTime = DateTimeOffset
                                .FromUnixTimeSeconds(x.BatchId)
                                .DateTime;

                            // Convert the utc time to site time zone
                            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, siteTimeZone);
                        }),
                    ]
                )
            );
        }

        return results;
    }
}
