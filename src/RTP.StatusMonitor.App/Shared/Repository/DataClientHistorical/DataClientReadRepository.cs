using System.Collections.Concurrent;
using Azure;
using Azure.Data.Tables;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.DataClient.Shared;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public class DataClientReadRepository(
    IDataClientTableQueryBuilder queryBuilder,
    IOptions<TableStorageOptions> tableStorageOptions,
    IMemoryCache cache
) : TableRepository(tableStorageOptions)
{
    const int QUERY_GROUP_SIZE = 5; // each group will have 5 queries
    const int QUERY_TIME_INTERVAL_IN_SECOND = 60 * 30;
    private readonly IDataClientTableQueryBuilder _queryBuilder = queryBuilder;
    private readonly IMemoryCache _cache = cache;
    public readonly TimeSpan HISTORICAL_CACHE_DURATION_IN_MINS = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Get the latest data for data client of a block from table storage
    /// (filter out anything bad or not updated within 5 minutes)
    /// </summary>
    /// <param name="block">The block to get the latest data</param>
    /// <param name="uniqueTags">The list of tags to needed</param>
    /// <returns>The list of data client entity</returns>
    public List<DataClientHistoricalWriteSchemaDto> GetLatestData(
        Block block,
        List<string> uniqueTags
    )
    {
        Pageable<DataClientHistoricalWriteSchemaDto> snapshots = CreateTableClient(
                _queryBuilder.GetTableName(
                    siteName: block.Site.Name,
                    customerName: block.Site.Customer.Name
                )
            )
            .Query<DataClientHistoricalWriteSchemaDto>(
                filter: _queryBuilder.GetSnapshotQuery(
                    siteName: block.Site.Name,
                    blockName: block.Name,
                    tags: uniqueTags
                ),
                cancellationToken: default
            );

        // Filter out stale data (anything not updated for 5 minutes or bad quality)
        List<DataClientHistoricalWriteSchemaDto> filteredSnapshots = [];
        DateTimeOffset currentOffset = DateTimeOffset.UtcNow;
        foreach (DataClientHistoricalWriteSchemaDto snapshot in snapshots)
        {
            TimeSpan? difference = snapshot.Timestamp.HasValue
                ? currentOffset - snapshot.Timestamp
                : null;

            if (
                snapshot.Quality == "Good"
                && difference.HasValue
                && difference.Value.TotalMinutes < 5
            )
            {
                filteredSnapshots.Add(snapshot);
            }
        }

        return filteredSnapshots;
    }

    /// <summary>
    /// Query the data client table for the tags of a block
    /// </summary>
    /// <param name="block">The block to get the tags</param>
    /// <returns>The list of tags that are peristed to data client table for the block</returns>
    public List<string> GetDataClientTags(Block block, CancellationToken ct) =>
        [
            .. CreateTableClient(
                    _queryBuilder.GetTableName(
                        siteName: block.Site.Name,
                        customerName: block.Site.Customer.Name
                    )
                )
                .Query<DataClientHistoricalWriteSchemaDto>(
                    filter: $"PartitionKey eq '{block.Alias}-Snapshot'",
                    select: ["RowKey"],
                    cancellationToken: ct
                )
                .Select(x => x.RowKey),
        ];

    /// <summary>
    /// Query data client for today using old schema
    /// </summary>
    public async Task<List<DataClientHistoricalWriteSchemaDto>> GetCurrentDayHistoricalAsync(
        Block block,
        List<string> uniqueTags,
        long unixStartTimeInSeconds,
        long unixEndTimeInSeconds,
        string[] columns,
        CancellationToken ct
    )
    {
        // Build the query to get the data from table storage
        List<List<string>> queryBatches = _queryBuilder.GetHistoricalQuery(
            siteName: block.Site.Name,
            blockName: block.Name,
            aliases: uniqueTags,
            filter: string.Empty, // no filter for table storage query
            startTime: unixStartTimeInSeconds,
            endTime: unixEndTimeInSeconds,
            rangeLength: QUERY_TIME_INTERVAL_IN_SECOND,
            queryGroupSize: QUERY_GROUP_SIZE
        );

        // Get the table name based on site and customer name
        string tableName = _queryBuilder.GetTableName(
            siteName: block.Site.Name,
            customerName: block.Site.Customer.Name
        );

        // FIXME - a hack for now
        if (tableName == "dataclientNsgen")
            tableName = "dataclientNSGen";

        // Create table client instance
        TableClient tableClient = CreateTableClient(tableName);

        // Execute the queries in parallel
        ConcurrentBag<DataClientHistoricalWriteSchemaDto> entities = [];
        await Parallel.ForEachAsync(
            queryBatches,
            async (queriesInBatch, _) =>
                await Parallel.ForEachAsync(
                    queriesInBatch,
                    async (query, _) =>
                    {
                        AsyncPageable<DataClientHistoricalWriteSchemaDto> data =
                            tableClient.QueryAsync<DataClientHistoricalWriteSchemaDto>(
                                filter: query,
                                select: columns,
                                cancellationToken: ct
                            );

                        await foreach (DataClientHistoricalWriteSchemaDto record in data)
                        {
                            if (record.Quality == "Good")
                            {
                                entities.Add(record);
                            }
                        }
                    }
                )
        );

        return [.. entities];
    }

    /// <summary>
    /// Query historical data client for yesterday and beyond using new schema (current day historical data has not been migrated over yet)
    /// </summary>
    /// <param name="block">The block to get the data</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    ///  <param name="ct"></param>
    /// <returns>The list of data client entity</returns>
    private async Task<List<DataClientHistoricalReadSchemaDto>> GetPreviousDayHistoricalAsync(
        Block block,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct
    ) =>
        await _cache.GetOrCreateAsync(
            key: $"dataclient-{block.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            async (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                // Create table client instance
                string tableName = $"data{block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                ConcurrentBag<DataClientHistoricalReadSchemaDto> entities = new();
                int days = (int)(endDate - startDate).TotalDays;

                // Divide the unique tags into groups of 5
                List<List<string>> uniqueTagsGroups = uniqueTags
                    .Select((value, index) => new { Index = index, Value = value })
                    .GroupBy(x => x.Index / 5)
                    .Select(x => x.Select(v => v.Value).ToList())
                    .ToList();

                // Query the table in parallel for each group of tags and
                // each tag in the group
                await Parallel.ForEachAsync(
                    uniqueTagsGroups,
                    async (tagGroup, _) =>
                    {
                        await Parallel.ForEachAsync(
                            tagGroup,
                            async (tag, _) =>
                            {
                                for (int i = 0; i <= days; i++)
                                {
                                    string query =
                                        $"PartitionKey eq '{block.Alias}-{tag}-{startDate.AddDays(i):yyyy-MM-dd}' and Quality eq true";

                                    AsyncPageable<DataClientHistoricalReadSchemaDto> data =
                                        tableClient.QueryAsync<DataClientHistoricalReadSchemaDto>(
                                            filter: query,
                                            select: new[]
                                            {
                                                "Tag",
                                                "LocalTimestamp",
                                                "Value",
                                                "Quality",
                                            },
                                            cancellationToken: ct
                                        );

                                    await foreach (DataClientHistoricalReadSchemaDto record in data)
                                    {
                                        if (record.Quality == true)
                                        {
                                            entities.Add(record);
                                        }
                                    }
                                }
                            }
                        );
                    }
                );

                return entities.ToList();
            }
        ) ?? [];

    /// <summary>
    /// Split the date range and query accordingly using either the old schema
    /// for current day and new schema for any other day.
    /// </summary>
    /// <param name="block">The block to query</param>
    /// <param name="uniqueVariables">The tags to query</param>
    /// <param name="startTime">The start time of the query</param>
    /// <param name="endTime">The end time of the query</param>
    /// <param name="ct"></param>
    /// <returns>The time series data of all tags</returns>
    public async Task<List<TimeSeriesData>> GetHistoricalDataAsync(
        Block block,
        List<string> uniqueVariables,
        DateTime startTime,
        DateTime endTime,
        CancellationToken ct
    )
    {
        // If there is no variables requested, dont bother querying the table
        if (uniqueVariables.Count == 0)
            return [];

        // Replace all special characters in tags (these are used to query)
        List<string> uniqueTags =
        [
            .. uniqueVariables.Select(v => v.Replace("#", "+").Replace("/", "_")),
        ];

        TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);

        // Convert UTC time to local site time using timezone
        SiteLocalTime currentLocalTime = SiteLocalTime.Create(block.Site, DateTime.UtcNow);

        // NOTE - The split time is site's local time current day start time
        DateTime splitTime = new(
            currentLocalTime.Value.Year,
            currentLocalTime.Value.Month,
            currentLocalTime.Value.Day,
            0,
            0,
            0
        );

        // Not current day => query using new schema
        if (endTime < splitTime)
        {
            List<DataClientHistoricalReadSchemaDto> previousDayEntities =
                await GetPreviousDayHistoricalAsync(
                    block: block,
                    uniqueTags: uniqueTags,
                    startDate: startTime,
                    endDate: endTime,
                    ct: ct
                );

            return [.. previousDayEntities.ToTimeSeriesData()];
        }
        // Current day => query using old schema
        else if (startTime >= splitTime)
        {
            List<DataClientHistoricalWriteSchemaDto> currentDayEntities =
                await GetCurrentDayHistoricalAsync(
                    block,
                    uniqueTags: uniqueTags,
                    unixStartTimeInSeconds: new DateTimeWithZone(
                        startTime,
                        siteTzInfo
                    ).UnixTimeInSeconds,
                    unixEndTimeInSeconds: new DateTimeWithZone(
                        endTime,
                        siteTzInfo
                    ).UnixTimeInSeconds,
                    columns: ["RowKey", "BatchId", "Tag", "Value", "Quality"],
                    ct
                );

            return currentDayEntities.ToTimeSeriesData(siteTzInfo);
        }
        else
        {
            // Both current day and not current day => query using both schema
            List<DataClientHistoricalReadSchemaDto> previousDayEntities =
                await GetPreviousDayHistoricalAsync(
                    block: block,
                    uniqueTags: uniqueTags,
                    startDate: startTime,
                    endDate: splitTime.AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59),
                    ct: ct
                );

            List<DataClientHistoricalWriteSchemaDto> currentDayEntities =
                await GetCurrentDayHistoricalAsync(
                    block: block,
                    uniqueTags: uniqueTags,
                    unixStartTimeInSeconds: new DateTimeWithZone(
                        splitTime,
                        siteTzInfo
                    ).UnixTimeInSeconds,
                    unixEndTimeInSeconds: new DateTimeWithZone(
                        endTime,
                        siteTzInfo
                    ).UnixTimeInSeconds,
                    columns: ["RowKey", "BatchId", "Tag", "Value", "Quality"],
                    ct: ct
                );

            return previousDayEntities
                .ToTimeSeriesData()
                .Concat(currentDayEntities.ToTimeSeriesData(siteTzInfo))
                .ToList();
        }
    }

    /// <summary>
    /// Query the hourly statistical data for a block for the given tags
    /// </summary>
    /// <param name="block">The block to query</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    /// <param name="ct"></param>
    /// <returns>The statistics data for all the tags</returns>
    public async Task<List<StatisticsDataClientDto>> GetHistoricalHourlyDataAsync(
        Block block,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct
    ) =>
        await _cache.GetOrCreateAsync(
            key: $"dataclient-hourly-{block.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            async (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                if (uniqueTags.Count == 0)
                    return [];

                // Create table client instance
                string tableName = $"data{block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                // Divide the unique tags into groups of 3
                List<List<string>> uniqueTagsGroups =
                [
                    .. uniqueTags
                        .Select((value, index) => new { Index = index, Value = value })
                        .GroupBy(x => x.Index / 3)
                        .Select(x => x.Select(v => v.Value).ToList()),
                ];

                // Get the start and end time of the local site timezone
                TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);
                DateTimeWithZone startTime = new(startDate, siteTzInfo);
                DateTimeWithZone endTime = new(endDate, siteTzInfo);

                // Query the table in parallel for each group of tags and
                // each tag in the group
                ConcurrentBag<StatisticsDataClientDto> entities = [];

                int days = (int)(endDate - startDate).TotalDays;

                // Query the table in parallel for each group of tags and
                // each tag in the group
                await Parallel.ForEachAsync(
                    uniqueTagsGroups,
                    async (tagGroup, _) =>
                        await Parallel.ForEachAsync(
                            tagGroup,
                            async (tag, _) =>
                            {
                                string query =
                                    $"PartitionKey eq '{block.Alias}-{tag}-Hour' and RowKey ge '{startTime.UnixTimeInSeconds}' and RowKey le '{endTime.UnixTimeInSeconds + 1}'";

                                AsyncPageable<StatisticsDataClientDto> data =
                                    tableClient.QueryAsync<StatisticsDataClientDto>(
                                        filter: query,
                                        select:
                                        [
                                            "Tag",
                                            "LocalTimestamp",
                                            "Avg",
                                            "Max",
                                            "Min",
                                            "StdDev",
                                            "Points",
                                        ],
                                        cancellationToken: ct
                                    );

                                await foreach (StatisticsDataClientDto record in data)
                                {
                                    entities.Add(record);
                                }
                            }
                        )
                );

                return entities.ToList();
            }
        ) ?? [];

    /// <summary>
    /// Query the daily statistical data for a block for the given tags
    /// </summary>
    ///
    /// <param name="block">The block to query</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    /// <param name="ct"></param>
    ///
    /// <returns>The statistics data for all the tags</returns>
    public async Task<List<StatisticsDataClientDto>> GetHistoricalDailyDataAsync(
        Block block,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct
    ) =>
        await _cache.GetOrCreateAsync(
            key: $"dataclient-daily-{block.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            async (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                if (uniqueTags.Count == 0)
                    return new List<StatisticsDataClientDto>();

                // Create table client instance
                string tableName = $"data{block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                // Divide the unique tags into groups of 3
                List<List<string>> uniqueTagsGroups = uniqueTags
                    .Select((value, index) => new { Index = index, Value = value })
                    .GroupBy(x => x.Index / 3)
                    .Select(x => x.Select(v => v.Value).ToList())
                    .ToList();

                // Get the start and end time of the local site timezone
                TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);
                DateTimeWithZone startTime = new(startDate, siteTzInfo);
                DateTimeWithZone endTime = new(endDate, siteTzInfo);

                // Query the table in parallel for each group of tags and
                // each tag in the group
                ConcurrentBag<StatisticsDataClientDto> entities = new();
                int days = (int)(endDate - startDate).TotalDays;
                await Parallel.ForEachAsync(
                    uniqueTagsGroups,
                    async (tagGroup, _) =>
                    {
                        await Parallel.ForEachAsync(
                            tagGroup,
                            async (tag, _) =>
                            {
                                string query =
                                    $"PartitionKey eq '{block.Alias}-{tag}-Day' and RowKey ge '{startTime.UnixTimeInSeconds}' and RowKey le '{endTime.UnixTimeInSeconds + 1}'";

                                AsyncPageable<StatisticsDataClientDto> data =
                                    tableClient.QueryAsync<StatisticsDataClientDto>(
                                        filter: query,
                                        select: new[]
                                        {
                                            "Tag",
                                            "LocalTimestamp",
                                            "Avg",
                                            "Max",
                                            "Min",
                                            "StdDev",
                                            "Points",
                                        },
                                        cancellationToken: ct
                                    );

                                await foreach (StatisticsDataClientDto record in data)
                                {
                                    entities.Add(record);
                                }
                            }
                        );
                    }
                );

                return entities.ToList();
            }
        ) ?? [];
}
