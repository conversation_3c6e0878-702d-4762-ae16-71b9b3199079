using RTP.StatusMonitor.App.Shared.Storage;

namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public class StatisticsDataClientDto : BaseTableEntity
{
    /// <summary>
    /// LocalTimestamp for statiscal data is the start of the hour 
    /// For example: 2021-09-01 00:00:00 means the statistical data is for the hour of 2021-09-01 00:00:00 to 2021-09-01 00:59:59 (hour is floored and same for date and minute)
    /// </summary>
    public string LocalTimestamp { get; init; } = string.Empty;
    public string Tag { get; init; } = string.Empty;
    public double Avg { get; init; }
    public double StdDev { get; init; }
    public double Max { get; init; }
    public double Min { get; init; }
    public double Points { get; init; }
}
