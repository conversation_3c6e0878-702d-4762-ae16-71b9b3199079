namespace RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

public static class DataClientStatisticsFilter
{
    public static Func<DateTime, DateTime, bool> FilterByHour =
        (date, filterDate)
        => date.Date == filterDate.Date &&
        date.Hour == filterDate.Hour;

    public static Func<DateTime, DateTime, bool> FilterByDate =
        (date, filterDate)
        => date.Date == filterDate.Date;

    /// <summary>
    /// Given a list of statistics data, this method will filter the statistics data by the specified dates using the filter function
    /// </summary>
    /// <param name="statisticsDataClientDtos">The statistics data to filter</param>
    /// <param name="datesToFilter">The dates to filter by</param>
    /// <param name="filterFunc">The filter function to use</param>
    /// <returns>The statistics data that falls into the satisfied dates</returns>
    public static List<StatisticsDataClientDto> FilterByDateCondition(
        this List<StatisticsDataClientDto> statisticsDataClientDtos,
        List<DateTime> datesToFilter,
        Func<DateTime, DateTime, bool> filterFunc)
    {
        List<StatisticsDataClientDto> filteredStats = new();

        // Use the sastified dates to filter the statistics data based on the filter function
        for (int i = 0; i < datesToFilter.Count; i++)
        {
            for (int ii = 0; ii < statisticsDataClientDtos.Count; ii++)
            {
                // If the condition is satisfied, add the statistics data to the filtered list
                if (filterFunc(
                    DateTime.Parse(statisticsDataClientDtos[ii].LocalTimestamp),
                    datesToFilter[i]))
                {
                    filteredStats.Add(statisticsDataClientDtos[ii]);
                }
            }
        }

        return filteredStats;
    }
}
