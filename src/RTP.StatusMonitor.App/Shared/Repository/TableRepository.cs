using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Shared.Repository;

public abstract class TableRepository(IOptions<TableStorageOptions> tableStorageOptions)
{
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;

    /// <summary>
    /// This method will create a new instance of the TableClient class.
    /// </summary>
    /// 
    /// <param name="tableName">The name of the table.</param>
    /// 
    /// <returns>A new instance of the TableClient class.</returns>
    protected TableClient CreateTableClient(string tableName) => new(
            endpoint: new Uri(_tableStorageOptions.Uri),
            tableName: tableName,
            credential: new TableSharedKeyCredential(
                _tableStorageOptions.AccountName,
                _tableStorageOptions.AccountKey));

    /// <summary>
    /// This method will create a new instance of the TableServiceClient class.
    /// </summary>
    /// 
    /// <returns>A new instance of the TableServiceClient class.</returns>
    protected TableServiceClient CreateTableServiceClient() => new(
            new Uri(_tableStorageOptions.Uri),
            new TableSharedKeyCredential(
                _tableStorageOptions.AccountName,
                _tableStorageOptions.AccountKey));
}
