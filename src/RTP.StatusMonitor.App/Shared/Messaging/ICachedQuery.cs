using RTP.StatusMonitor.App.Shared.Caching;

namespace RTP.StatusMonitor.App.Shared.Messaging;

/// <summary>
/// This query implements IQuery is a request from MediatR that returns an ErrorOr<TResponse>
/// It also implements ICachedQuery which includes properties for caching
/// </summary>
/// 
/// <typeparam name="TResponse"></typeparam>
public interface ICachedQuery<TResponse> : IQuery<TResponse>, ICachedQuery
{ }

