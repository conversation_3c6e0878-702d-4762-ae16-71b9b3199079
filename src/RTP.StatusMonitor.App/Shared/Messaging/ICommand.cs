using ErrorOr;
using MediatR;

namespace RTP.StatusMonitor.App.Shared.Messaging;

// This is used to identify a command (used for cross-cutting concerns ex LoggingBehavior, etc...)
public interface IBaseCommand { }

// This command will be an request from MediatR that returns an ErrorOr<MediatR.Unit> (no response)
public interface ICommand
  : IRequest<ErrorOr<Unit>>, IBaseCommand
{ }

// This command will be an request from MediatR that returns an ErrorOr<TResponse>
public interface ICommand<TResponse>
  : IRequest<ErrorOr<TResponse>>, IBaseCommand
{ }