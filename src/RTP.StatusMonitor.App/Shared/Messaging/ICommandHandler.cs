using ErrorOr;
using MediatR;

namespace RTP.StatusMonitor.App.Shared.Messaging;

// This command handler will process a request and return an ErrorOr<MediatR.Unit> (no response)
public interface ICommandHandler<TCommand>
  : IRequestHandler<TCommand, ErrorOr<Unit>>
  where TCommand : ICommand
{ }

// This command handler will process a request and return an ErrorOr<TResponse>
public interface ICommandHandler<TCommand, TResponse>
  : IRequestHandler<TCommand, ErrorOr<TResponse>>
  where TCommand : ICommand<TResponse>
{ }

