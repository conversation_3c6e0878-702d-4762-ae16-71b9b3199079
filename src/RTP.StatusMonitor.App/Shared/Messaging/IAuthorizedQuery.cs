using RTP.StatusMonitor.App.Shared.Authorization;

namespace RTP.StatusMonitor.App.Shared.Messaging;

/// <summary>
/// This query implements IQuery is a request from MediatR that returns an ErrorOr<TResponse>
/// It also implements IAuthorizedQuery which includes properties for authorization
/// </summary>
/// 
/// <typeparam name="TResponse"></typeparam>
public interface IAuthorizedQuery<TResponse> : IQuery<TResponse>, IAuthorizedQuery
{ }
