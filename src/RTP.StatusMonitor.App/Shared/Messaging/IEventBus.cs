using Azure.Messaging.ServiceBus;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Shared.Messaging;

public interface IEventBus
{
    /// <summary>
    /// Asynchronously peeks messages from a queue or topic.
    /// </summary>
    /// <param name="queueOrTopicName">The name of the queue or topic.</param>
    /// <param name="maxMessages">The maximum number of messages to peek. Default is 5.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task<List<ServiceBusReceivedMessage>> PeekMessagesAsync(
        string queueOrTopicName,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously cancels a scheduled message from a queue or topic.
    /// </summary>
    /// <param name="queueOrTopicName">The name of the queue or topic.</param>
    /// <param name="sequenceNumber">The sequence number of the message to cancel.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task CancelScheduledMessages(
        string queueOrTopicName,
        long sequenceNumber,
        CancellationToken ct = default);

    /// <summary>
    /// Publishes a message to a specified topic asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the message to be published.</typeparam>
    /// <param name="message">The message to be published.</param>
    /// <param name="queueOrTopicName">The name of the topic to publish the message to.</param>
    /// <param name="ct">The cancellation token.</param>
    Task PublishAsync<T>(
        T message,
        string queueOrTopicName,
        CancellationToken ct) where T : IntegrationEvent;


    /// <summary>
    /// Schedule a message to be published to a specified topic asynchronously.
    /// </summary>
    /// <param name="message">The message to be published.</param>
    /// <param name="queueOrTopicName">The name of the topic or queue to publish the message to.</param>
    /// <param name="scheduledEnqueueTimeUtc">The time at which the message should be published.</param>
    /// <param name="ct"></param>
    /// <typeparam name="T">The message must be of type IntegrationEvent.</typeparam>
    Task ScheduleMessageAsync<T>(
        T message,
        string queueOrTopicName,
        DateTimeOffset scheduledEnqueueTimeUtc,
        CancellationToken ct) where T : IntegrationEvent;

    /// <summary>
    /// Batch publishes a collection of messages to a specified topic asynchronously.
    /// </summary>
    /// <param name="messages">The list of messages to be published.</param>
    /// <param name="queueOrTopicName">The name of the topic to publish the messages to.</param>
    /// <param name="ct"></param>
    /// <typeparam name="T"></typeparam>
    Task PublishMessageBatchAsync<T>(
        IEnumerable<T> messages,
        string queueOrTopicName,
        CancellationToken ct) where T : IntegrationEvent;
}