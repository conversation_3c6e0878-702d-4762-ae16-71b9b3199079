using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Shared.Messaging;

public sealed class EventBus : IEventBus
{
    private readonly AzureServiceBusOptions _serviceBusOptions;
    private readonly ServiceBusClient _serviceBusClient;

    public EventBus(IOptions<AzureServiceBusOptions> serviceBusOptions)
    {
        _serviceBusOptions = serviceBusOptions.Value;
        _serviceBusClient = new ServiceBusClient(_serviceBusOptions.ConnectionString);
    }

    /// <inheritdoc />
    public async Task PublishAsync<T>(
        T message,
        string queueOrTopicName,
        CancellationToken ct = default) where T : IntegrationEvent
    {
        // Create a sender for the topic.
        ServiceBusSender sender = _serviceBusClient.CreateSender(queueOrTopicName);

        // Create a message that we can send.
        ServiceBusMessage serviceBusMessage = new(JsonConvert.SerializeObject(message));

        // Send the message to the topic.
        await sender.SendMessageAsync(serviceBusMessage, ct);
    }

    /// <inheritdoc />
    public async Task ScheduleMessageAsync<T>(
        T message,
        string queueOrTopicName,
        DateTimeOffset scheduledEnqueueTimeUtc,
        CancellationToken ct = default) where T : IntegrationEvent
    {
        // Create a sender for the topic.
        ServiceBusSender sender = _serviceBusClient.CreateSender(queueOrTopicName);

        // Create a message that we can send.
        ServiceBusMessage serviceBusMessage = new(JsonConvert.SerializeObject(message));

        // Send the message to the topic.
        await sender.ScheduleMessageAsync(
            message: serviceBusMessage,
            scheduledEnqueueTime: scheduledEnqueueTimeUtc,
            ct);
    }

    /// <inheritdoc />
    public async Task PublishMessageBatchAsync<T>(
        IEnumerable<T> messages,
        string queueOrTopicName,
        CancellationToken ct = default) where T : IntegrationEvent
    {
        // Create a sender for the topic.
        ServiceBusSender sender = _serviceBusClient.CreateSender(queueOrTopicName);

        // Create the queue to batch all messages
        List<ServiceBusMessage> serviceBusMessages =
        [
            .. messages.Select(message => new ServiceBusMessage(JsonConvert.SerializeObject(message))),
        ];

        // Send the message to the topic or queue
        await sender.SendMessagesAsync(serviceBusMessages, ct);
    }

    /// <inheritdoc />
    public async Task<List<ServiceBusReceivedMessage>> PeekMessagesAsync(
        string queueOrTopicName,
        CancellationToken ct = default)
    {
        ServiceBusReceiver receiver = _serviceBusClient
            .CreateReceiver(queueOrTopicName);

        var messagesOfQueue = new List<ServiceBusReceivedMessage>();
        var previousSequenceNumber = -1L;
        var sequenceNumber = 0L;
        do
        {
            var messageBatch = await receiver
                .PeekMessagesAsync(int.MaxValue, sequenceNumber, ct);

            if (messageBatch.Count > 0)
            {
                sequenceNumber = messageBatch[^1].SequenceNumber;

                if (sequenceNumber == previousSequenceNumber)
                    break;

                messagesOfQueue.AddRange(messageBatch);

                previousSequenceNumber = sequenceNumber;
            }
            else
            { break; }
        } while (true);

        return messagesOfQueue;
    }

    /// <inheritdoc />
    public async Task CancelScheduledMessages(
        string queueOrTopicName,
        long sequenceNumber,
        CancellationToken ct = default)
        => await _serviceBusClient
            .CreateSender(queueOrTopicName)
            .CancelScheduledMessageAsync(sequenceNumber, ct);
}
