using ErrorOr;
using MediatR;

namespace RTP.StatusMonitor.App.Shared.Messaging;

/// <summary>
/// This query handler will process a query of type IQuery
/// which is a request from MediatR that returns an ErrorOr<TResponse>
/// </summary>
/// 
/// <typeparam name="IQuery"></typeparam>
/// <typeparam name="TResponse"></typeparam>
public interface IQueryHandler<IQuery, TResponse>
	: IRequestHandler<IQuery, ErrorOr<TResponse>> 
	where IQuery : IQuery<TResponse>
{ }