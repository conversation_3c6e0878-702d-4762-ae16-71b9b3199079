<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="Azure.Storage.Files.DataLake" Version="12.21.0" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="DuckDB.NET.Data.Full" Version="1.1.3" />
    <PackageReference Include="Handlebars.Net" Version="2.1.6" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Identity.Web.GraphServiceClient" Version="2.21.1" />
    <PackageReference Include="Parquet.Net" Version="5.0.2" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RTP.StatusMonitor.Domain\RTP.StatusMonitor.Domain.csproj" />
    <ProjectReference Include="..\RTP.StatusMonitor.Persistence\RTP.StatusMonitor.Persistence.csproj" />
  </ItemGroup>

</Project>
