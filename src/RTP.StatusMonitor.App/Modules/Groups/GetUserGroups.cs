using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Groups;
public record GetUserGroupsQuery : IQuery<List<UserGroupResponse>>;
public record UserGroupResponse(
    string GroupName,
    Guid GroupId,
    string Description,
    DateTime DateCreated,
    DateTime DateModified,
    List<Guid> HasAccessToSites,
    List<Guid> HasAccessToBlocks);

public class GetUserGroupsQueryHandler(DataContext context)
        : IQueryHandler<GetUserGroupsQuery, List<UserGroupResponse>>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<List<UserGroupResponse>>> Handle(
        GetUserGroupsQuery request,
        CancellationToken ct = default)
    {
        // Get the list of all user groups
        List<UserGroup> userGroups = await _context.UserGroups
            .Include(g => g.GroupPermissions)
                .ThenInclude(gp => gp.Site)
                    .ThenInclude(s => s.Blocks)
            .AsNoTracking()
            .ToListAsync(ct);

        return userGroups
            .Select(g => new UserGroupResponse(
                GroupId: g.UserGroupId,
                GroupName: g.Name,
                Description: g.Description,
                DateCreated: g.DateCreated,
                DateModified: g.DateModified,
                HasAccessToSites: [.. g.GroupPermissions
                    .Select(gp => gp.SiteId)
                    .OrderBy(s => s)],
                HasAccessToBlocks: [.. g.GroupPermissions
                    .SelectMany(gp => gp.Site.Blocks.Select(b => b.Id))
                    .OrderBy(b => b)])
            )
            .OrderBy(g => g.GroupName)
            .ToList();
    }
}

// using ErrorOr;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Graph;
// using Microsoft.Graph.Models;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.Domain.UserGroups;
// using RTP.StatusMonitor.Persistence;
// namespace RTP.StatusMonitor.App.Modules.Groups; public record GetUserGroupsQuery : IQuery<List<UserGroupResponse>>; public record UserGroupResponse(string GroupName, string GroupId, string Description, DateTime? DateCreated,
//     DateTime? DateModified);

// public class GetUserGroupsQueryHandler(
//     GraphServiceClient graphServiceClient,
//     DataContext context)
//     : IQueryHandler<GetUserGroupsQuery, List<UserGroupResponse>>
// {
//     private readonly GraphServiceClient _graphServiceClient = graphServiceClient;
//     private readonly DataContext _context = context;
//     public async Task<ErrorOr<List<UserGroupResponse>>> Handle(
//         GetUserGroupsQuery request,
//         CancellationToken ct = default)
//     {
//         try
//         {
//             // Get all groups from Azure AD
//             var groups = await _graphServiceClient.Groups
//                 .GetAsync(requestConfiguration =>
//                 {
//                     requestConfiguration.QueryParameters.Select = [
//                         "id",
//                         "displayName",
//                         "description",
//                         "createdDateTime",
//                         "modifiedDateTime"];
//                 }, ct);

//             if (groups?.Value == null)
//             {
//                 return new List<UserGroupResponse>();
//             }

//             return groups.Value
//                 .Select(g => new UserGroupResponse(
//                     GroupName: g.DisplayName ?? "",
//                     GroupId: g.Id ?? "",
//                     Description: g.Description ?? "",
//                     DateCreated: g.CreatedDateTime?.DateTime,
//                     DateModified: g.CreatedDateTime?.DateTime))
//                 .OrderBy(g => g.GroupName)
//                 .ToList();
//         }
//         catch (Exception ex)
//         {
//             // Log the exception details for debugging
//             Console.WriteLine($"Error fetching groups: {ex.Message}");
//             throw;
//         }
//     }
// }
