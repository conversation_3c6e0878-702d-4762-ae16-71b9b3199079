using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Groups.AddUserGroup;

public class AddUserGroupCommandHandler(DataContext context)
        : ICommandHandler<AddUserGroupCommand, Unit>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<Unit>> Handle(
        AddUserGroupCommand request,
        CancellationToken cancellationToken)
    {
        // Create a new user group
        UserGroup userGroup = new(
            request.UserGroupId, request.Name, request.Description);

        // Query all sites user group has access to
        List<Site> sites = await _context.Sites
            .Where(s => request.SitesAllowedAccess.Contains(s.Id))
            .ToListAsync(cancellationToken);

        // Add permissions to each site
        sites.ForEach(s => userGroup.AddPermissionToSite(s));

        // Add the user group to the database
        _context.UserGroups.Add(userGroup);

        // Save the changes
        var success = await _context.SaveChangesAsync(cancellationToken) > 0;

        if (success)
            return Unit.Value;

        return Error.Failure(
            "AddUserGroup.Failed",
            "Failed to add user group and assign permissions.");
    }
}
