using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Groups.UpdateUserGroup;

public class UpdateUserGroupCommandHandler : ICommandHandler<UpdateUserGroupCommand, Unit>
{
    private readonly IDataContext _context;

    public UpdateUserGroupCommandHandler(IDataContext context)
    => _context = context;


    public async Task<ErrorOr<Unit>> Handle(
        UpdateUserGroupCommand request,
        CancellationToken ct = default)
    {
        // Find the user group
        UserGroup? userGroup = await _context.UserGroups
            .Include(ug => ug.GroupPermissions)
            .FirstOrDefaultAsync(ug => ug.UserGroupId == request.UserGroupId, ct);

        if (userGroup is null)
            return UserGroupErrors.NotFound;

        // Query all sites user group has access to
        List<Site> sitesAllowedAccess = await _context.Sites
            .Where(s => request.SitesAllowedAccess.Contains(s.Id))
            .ToListAsync(ct);

        // Update the user group
        userGroup.UpdateUserGroup(
            name: request.Name,
            description: request.Description,
            sitesAllowedAccess: sitesAllowedAccess);

        // Save changes
        bool success = await _context.SaveChangesAsync(ct) > 0;

        if (success)
            return Unit.Value;

        return Error.Failure(
            "UpdateUserGroup.Failed",
            "Failed to update the user group and its associated permissions.");
    }
}

