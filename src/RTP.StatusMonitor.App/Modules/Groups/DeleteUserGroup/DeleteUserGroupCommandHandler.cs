using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Groups.DeleteUserGroup;

public class DeleteUserGroupCommandHandler(DataContext context)
    : ICommandHandler<DeleteUserGroupCommand, Unit>
{
    private readonly DataContext _context = context;
    public async Task<ErrorOr<Unit>> Handle(DeleteUserGroupCommand request, CancellationToken cancellationToken)
    {
        // Find the user group
        UserGroup? userGroup = await _context.UserGroups
            .FirstOrDefaultAsync(ug => ug.UserGroupId == request.UserGroupId, cancellationToken);

        if (userGroup is null)
        {
            return Error.NotFound(
                "UserGroup.NotFound",
                "The specified user group was not found.");
        }

        // Remove the user group
        _context.UserGroups.Remove(userGroup);

        // Save changes
        var result = await _context.SaveChangesAsync(cancellationToken);

        if (result > 0)
        {
            return Unit.Value;
        }

        return Error.Failure(
            "DeleteUserGroup.Failed",
            "Failed to delete the user group and its associated permissions.");
    }
}
