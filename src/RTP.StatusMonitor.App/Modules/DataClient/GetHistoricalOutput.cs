using System.Collections.Concurrent;
using AutoMapper;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DataClient.Shared;
using RTP.StatusMonitor.App.Shared.TableStorage;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record GetOutputHistoricalQuery(
    Guid BlockId,
    List<Guid> UserGroupsId,
    List<string> Tags,
    int SkipInterval,
    string Filter,
    DateTime From,
    DateTime To) : IRequest<ErrorOr<List<OutputHistoricalResponse>>>;
public record OutputHistoricalResponse(
    string Tag,
    List<OutputData> Data
);
public record OutputData
(
    object? Value,
    string Quality,
    string Timestamp
);

public class GetHistoricalOutputHandler(
    ITableStorageService storageClient,
    DataContext context,
    IDataClientTableQueryBuilder queryBuilder,
    IMapper mapper) : IRequestHandler<GetOutputHistoricalQuery, ErrorOr<List<OutputHistoricalResponse>>>
{
    private readonly ITableStorageService _storageClient = storageClient;
    private readonly DataContext _context = context;
    private readonly IDataClientTableQueryBuilder _queryBuilder = queryBuilder;
    private readonly IMapper _mapper = mapper;

    public async Task<ErrorOr<List<OutputHistoricalResponse>>> Handle(
      GetOutputHistoricalQuery request,
      CancellationToken cancellationToken)
    {
        // Load block data
        var blockDto = await _context.Blocks
            .AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
              .ThenInclude(s => s.GroupPermissions)
            .Select(b => new
            {
                BlockName = b.Name,
                b.Site,
                TimeZone = TimeZoneInfo.FindSystemTimeZoneById(b.Site.TimeZone),
                TableName = $"dataclient{b.Site.Customer.Name}",
                b.Site.GroupPermissions
            })
            .FirstOrDefaultAsync(cancellationToken: cancellationToken);

        // Check if block exists
        if (blockDto is null)
        {
            return BlockDomainErrors.NotFound;
        }

        // Check user permission to block data
        bool hasAccess = blockDto.Site.HasAccessToSite(request.UserGroupsId);

        if (hasAccess is false)
            return SiteDomainErrors.Unauthorized;

        // Lookup for historical data using tag as key
        ConcurrentDictionary<string, List<OutputEntity>> historicalDataLookupByTag = new();

        // Then we build the query groups based on the time range
        // NOTE - Each group of query will be send to a thread to execute
        // and each group will span 5 minutes of data
        const int QUERY_GROUP_SIZE = 8;
        const int QUERY_TIME_INTERVAL = 300;

        // convert local time to utc time
        long startTimeInUnixTimeSeconds = new DateTimeOffset(
          request.From.ToUniversalTime()).ToUnixTimeSeconds();

        long endTimeInUnixTimeSeconds = new DateTimeOffset(
          request.To.ToUniversalTime()).ToUnixTimeSeconds();

        List<List<string>> queryGroups = _queryBuilder.GetHistoricalQuery(
            blockDto.Site.Name,
            blockDto.BlockName,
            request.Tags,
            request.Filter,
            startTimeInUnixTimeSeconds,
            endTimeInUnixTimeSeconds,
            QUERY_TIME_INTERVAL,
            QUERY_GROUP_SIZE);

        // execute the queries in parallel
        await Parallel.ForEachAsync(queryGroups, async (queryGroup, _) =>
        {
            // get the historical data for a single batch of queries
            var historicals = await _storageClient.GetHistoricalOutputAsync(
          blockDto.TableName,
          queryGroup,
          cancellationToken);

            // categorize the historical data by tag and add to dictionary
            foreach (var i in historicals)
            {
                if (!historicalDataLookupByTag.ContainsKey(i.Tag))
                {
                    historicalDataLookupByTag.TryAdd(i.Tag, []);
                }

                // convert unix timestamp to date time of site
                i.BatchIdLocal = TimeZoneInfo
                    .ConvertTimeFromUtc(
                        DateTimeOffset.FromUnixTimeSeconds(i.BatchId).DateTime,
                        blockDto.TimeZone)
                    .ToString("yyyy-MM-dd HH:mm:ss");

                historicalDataLookupByTag[i.Tag].Add(i);
            }
        });

        // map each tag in dictionary to OutputResult
        ConcurrentBag<OutputHistoricalResponse> outputResults = [];
        Parallel.ForEach(historicalDataLookupByTag.Keys, tag =>
        {
            // if no data for the tag, ignore
            if (historicalDataLookupByTag[tag].Count == 0)
                return;

            List<OutputEntity> skipIntervalList = [];
            for (int i = 0; i < historicalDataLookupByTag[tag].Count; i += request.SkipInterval)
            {
                skipIntervalList.Add(historicalDataLookupByTag[tag][i]);
            }

            // map to output result
            OutputHistoricalResponse output = new(
                Tag: skipIntervalList[0].Tag,
                Data: skipIntervalList
                    .Select(i => new OutputData(
                        i.Value,
                        i.Quality,
                        i.BatchIdLocal))
                    .ToList());

            outputResults.Add(output);
        });

        return outputResults.ToList();
    }
}
