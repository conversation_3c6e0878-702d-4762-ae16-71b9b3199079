using System.Collections.Concurrent;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.TableStorage;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record GetLatestDataClientQuery(
    Guid BlockId,
    List<string> Tags) : IQuery<List<LatestDataClientResponse>>;

public record LatestDataClientResponse(
    string Tag,
    double Value,
    string Quality,
    string Timestamp);

internal sealed class GetLatestDataClientQueryHandler(
  ITableStorageService storageClient,
  IDataContext context)
    : IQueryHandler<GetLatestDataClientQuery, List<LatestDataClientResponse>>
{
    private readonly ITableStorageService _storageClient = storageClient;

    private readonly IDataContext _context = context;

    public async Task<ErrorOr<List<LatestDataClientResponse>>> Handle(
      GetLatestDataClientQuery request,
      CancellationToken ct)
    {
        var blockDto = await _context.Blocks
            .AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Select(b => new
            {
                BlockName = b.Name,
                SiteName = b.Site.Name,
                TimeZone = TimeZoneInfo.FindSystemTimeZoneById(b.Site.TimeZone),
                TableName = $"dataclient{b.Site.Customer.Name}",
            })
            .FirstOrDefaultAsync(cancellationToken: ct);

        if (blockDto is null)
        {
            return BlockDomainErrors.NotFound;
        };

        // get the partition range for the last minute to get the latest data
        long to = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        long from = to - 60;
        string paritionKeyFrom = $"{blockDto.SiteName}-{blockDto.BlockName}-{from}";
        string paritionKeyTo = $"{blockDto.SiteName}-{blockDto.BlockName}-{to}";

        // dictionary to hold the results with key as tag and value as OutputEntity
        ConcurrentDictionary<string, OutputEntity> latestOutputDict = new();

        // get the latest data for each tag
        Parallel.ForEach(request.Tags, (tag, _) =>
        {
            var query = $"PartitionKey ge '{paritionKeyFrom}' and PartitionKey le '{paritionKeyTo}' and RowKey eq '{tag.Replace("#", "+").Replace("/", "_")}'";

            // get the latest data for the tag
            var latestOutput = _storageClient.GetOutput(blockDto.TableName, query, ct);

            // add to dictionary if not null
            if (latestOutput is not null)
            {
                latestOutput.BatchIdLocal = TimeZoneInfo.ConvertTimeFromUtc(
              DateTimeOffset.FromUnixTimeSeconds(latestOutput.BatchId).DateTime, blockDto.TimeZone)
              .ToString("yyyy-MM-dd HH:mm:ss");

                latestOutputDict.TryAdd(tag, latestOutput);
            }
        });

        // Map the results from dict to response
        return latestOutputDict.Select(x => new LatestDataClientResponse(
            Tag: x.Key,
            Value: x.Value.Value,
            Quality: x.Value.Quality,
            Timestamp: x.Value.BatchIdLocal)).ToList();
    }
}
