using System.Data;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Util;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;
public record EvaluateDataClientStatsQuery(
  DateTime StartDate,
  DateTime EndDate,
  string Filter)
    : IAuthorizedQuery<DataClientEvaluationResponse>, ICachedQuery<DataClientEvaluationResponse>
{
    public List<Guid> UserGroupsId { get; set; } = Enumerable.Empty<Guid>().ToList();
    public Guid? SiteId { get; set; } = null; // irrelevant for this query
    public Guid? UnitId { get; set; } = null; // irrelevant for this query
    public Guid? BlockId { get; set; }
    public bool IsAuthorizationEnabled { get; set; } = true; // default to true

    // Cache properties
    public bool IsCacheEnabled { get; set; } = true; // default to true
    public string CacheKey => $"data-client-evaluation-{BlockId}-{StartDate}-{EndDate}-{Filter}";
    public TimeSpan CacheDuration { get; set; } = TimeSpan.FromMinutes(30);
}
public record DataClientEvaluationResponse(List<string> Dates);
public record PrefixedTagDto(string Tag, string Timestamp, double? Value);

internal sealed class EvaluateDataClientStatsQueryHandler(
    IDataContext context,
    ExpressionParser parser,
    DataClientReadRepository dataClientReadRepository)
        : IQueryHandler<EvaluateDataClientStatsQuery, DataClientEvaluationResponse>
{
    const string FILTER_PATTERN = @"\[(.*?)\]";
    private readonly IDataContext _context = context;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;
    private readonly ExpressionParser _parser = parser;

    public async Task<ErrorOr<DataClientEvaluationResponse>> Handle(
        EvaluateDataClientStatsQuery request,
        CancellationToken ct)
    {
        // Get the block data
        Block? block = await _context.Blocks
            .Include(b => b.Site)
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .FirstOrDefaultAsync(b => b.Id == request.BlockId, ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Parse the filter to find all variables to query ([Min-Tag1] or [Max-Tag2])
        List<string> tagsFromFilter = _parser.ExtractItemFromExpression(
            request.Filter,
            FILTER_PATTERN);

        // Then remove the prefix from the tag to get only the tag name
        // ([Min-Tag1] -> Tag1)
        List<string> tagToQuery = [];
        foreach (string tag in tagsFromFilter)
        {
            string tagName = tag.Split('-')[1];

            tagToQuery.Add(tagName);
        }

        // Query the data
        List<StatisticsDataClientDto> entities = await _dataClientReadRepository
            .GetHistoricalDailyDataAsync(
                block: block,
                uniqueTags: tagToQuery,
                startDate: request.StartDate,
                endDate: request.EndDate,
                ct);

        // Then map from entities to dictionary of tag and list of stats

        // This dictionary will appropriately map the prefixed tag to the stats
        // { [Min-Tag1, list of min stats for tag 1], etc... }
        Dictionary<string, List<PrefixedTagDto>> prefixedTagValue = [];
        for (int i = 0; i < entities.Count; i++)
        {
            // Get the columns from table entity
            string localTimestamp = entities[i].LocalTimestamp;
            string tag = entities[i].Tag;
            double avg = entities[i].Avg;
            double stdDev = entities[i].StdDev;
            double max = entities[i].Max;
            double min = entities[i].Min;

            // Find all the original variables with prefix
            // [Min-Tag1, Max-Tag2, etc...]
            List<string> originalTagsWithPrefix = [];
            foreach (string tagToFind in tagsFromFilter)
            {
                if (tagToFind.Contains(tag) is true)
                {
                    originalTagsWithPrefix.Add(tagToFind);
                }
            }

            // Populate the dictionary with the prefixed tag and the associated stats
            // { [Min-Tag1, list of min stats for tag 1], etc... }
            foreach (var tagWithPrefifx in originalTagsWithPrefix)
            {
                // If the tag does not exist in the dictionary, create it first
                if (prefixedTagValue.ContainsKey(tagWithPrefifx) is false)
                {
                    prefixedTagValue.Add(tagWithPrefifx, []);
                }

                double? value = null;
                if (tagWithPrefifx.Contains("Min") is true)
                { value = min; }
                else if (tagWithPrefifx.Contains("Max") is true)
                { value = max; }
                else if (tagWithPrefifx.Contains("Avg") is true)
                { value = avg; }
                else if (tagWithPrefifx.Contains("StdDev") is true)
                { value = stdDev; }

                // Update the stats with new record
                prefixedTagValue[tagWithPrefifx].Add(new PrefixedTagDto(
                    Tag: tag,
                    Timestamp: localTimestamp,
                    Value: value));
            }
        }

        // Create a table to evaluate the expression to get the list of timestamps
        List<string> resultTimestamps = EvaluateExpression(
            prefixedTagValue,
            tagsFromFilter,
            request.Filter);

        // Then combine the two list of timestamps to get the final result
        return new DataClientEvaluationResponse(resultTimestamps);
    }

    private static List<string> EvaluateExpression(
            Dictionary<string, List<PrefixedTagDto>> stats,
            List<string> tagsFromFilter,
            string filter)
    {
        DataTable dataTable = new();

        // Add timestamp column and make it as primary key
        dataTable.Columns.Add("Timestamp", typeof(string));
        dataTable.PrimaryKey = [dataTable.Columns["Timestamp"]!];

        // Add each tag to the table as a column with type double
        dataTable.Columns.AddRange(tagsFromFilter
            .Select(tag => new DataColumn(tag, typeof(double)))
            .ToArray());

        // Add min or max stats of each tag to the data table
        foreach (string key in stats.Keys)
        {
            for (int i = 0; i < stats[key].Count; i++)
            {
                PrefixedTagDto record = stats[key][i];

                DataRow? foundRow = dataTable.Rows.Find(record.Timestamp);

                if (foundRow == null)
                {
                    DataRow row = dataTable.NewRow();

                    row["Timestamp"] = record.Timestamp;
                    row[key] = record.Value is not null
                        ? record.Value
                        : DBNull.Value;

                    dataTable.Rows.Add(row);
                }
                else
                {
                    foundRow[key] = record.Value is not null
                        ? record.Value
                        : DBNull.Value;
                }
            }
        }

        // Filter the table
        dataTable.DefaultView.RowFilter = filter;
        DataTable filteredTable = dataTable.DefaultView.ToTable();

        // Get the list of timestamps from the filtered table
        List<string> timestamps = [];
        for (int i = 0; i < filteredTable.Rows.Count; i++)
        {
            if (filteredTable.Rows[i]["Timestamp"] is DBNull)
            {
                continue;
            }

            DataRow row = filteredTable.Rows[i];
            timestamps.Add(row["Timestamp"].ToString()!);
        }

        return timestamps;
    }
}
