using System.Data;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;
public record GetRealTimeDataClientQuery(List<Series> Series)
    : IAuthorizedQuery<List<RealTimeDataClientResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? BlockId { get; set; }

    // NOTE - only block id is relevant for this query
    public Guid? SiteId { get; set; } = null;
    public Guid? UnitId { get; set; } = null;
}
public record Series(
    Guid Id,
    string Name,
    string Tag,
    ComputedExpression Calculation,
    FilterExpression Filter);
public record RealTimeDataClientResponse(Guid Id, string Name, double Value, string Timestamp);

internal sealed class GetDataClientSnapshotQueryHandler(
    IDataContext context,
    DataClientReadRepository dataClientReadRepository)
        : IQueryHandler<GetRealTimeDataClientQuery, List<RealTimeDataClientResponse>>
{
    private readonly IDataContext _context = context;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;

    public async Task<ErrorOr<List<RealTimeDataClientResponse>>> Handle(
        GetRealTimeDataClientQuery request,
        CancellationToken ct = default)
    {
        // Get block info
        Block? block = await _context.Blocks
            .AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .FirstOrDefaultAsync(cancellationToken: ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Extract all unique variables from the series
        List<string> uniqueVariables = [.. request.Series
            .Select(s => s.Tag)
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Filter)))
            .Distinct()];

        // Query table storage for the latest data
        List<DataClientHistoricalWriteSchemaDto> entities = _dataClientReadRepository
            .GetLatestData(block: block, uniqueTags: uniqueVariables);

        // Convert the entities to time series data for analytics
        List<TimeSeriesData> timeSeriesData = entities
            .ToTimeSeriesData(TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone));

        // Create the data table and load time series data for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(timeSeriesData);

        // Process data for each series
        List<RealTimeDataClientResponse> responses = [];
        foreach (Series series in request.Series)
        {
            TimeSeriesData data = dataTable.TryEvaluateExpression(
                tag: series.Tag,
                calculation: series.Calculation,
                filter: series.Filter);

            // Make sure there is data after analytics
            if (data.Timestamps.Length == 0)
            {
                continue;
            }

            // If there is real time data => should only be 1 value and timestamp
            responses.Add(new RealTimeDataClientResponse(
                Id: series.Id,
                Name: series.Name,
                Value: data.Values
                    .Select(x =>
                        double.TryParse(x.ToString(), out double value)
                        ? value
                        : double.NaN)
                    .First(),
                Timestamp: data
                    .Timestamps.First().ToString("yyyy-MM-dd HH:mm:ss")));
        }

        return responses;
    }
}
