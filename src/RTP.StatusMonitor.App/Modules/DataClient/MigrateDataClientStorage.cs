using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record MigrateDataClientStorageCommand(
    Guid BlockId,
    List<string> Tags,
    DateTime StartDate,
    DateTime EndDate
) : ICommand;

internal sealed class MigrateDataClientStorageCommandHandler(
    IDataContext dataContext,
    DataClientReadRepository dataClientReadRepository,
    DataClientWriteRepository dataClientWriteRepository
) : ICommandHandler<MigrateDataClientStorageCommand>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;
    private readonly DataClientWriteRepository _dataClientWriteRepository =
        dataClientWriteRepository;

    public async Task<ErrorOr<Unit>> Handle(
        MigrateDataClientStorageCommand request,
        CancellationToken cancellationToken = default
    )
    {
        // Get the block info
        Block block = await _dataContext
            .Blocks.Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .SingleAsync(b => b.Id == request.BlockId, cancellationToken);

        // Get the local start and end time
        TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);
        DateTimeWithZone localStartTime = new(request.StartDate, siteTzInfo);
        DateTimeWithZone localEndTime = new(request.EndDate, siteTzInfo);

        // Get the historical data from existing data client
        List<DataClientHistoricalWriteSchemaDto> historicalEntities =
            await _dataClientReadRepository.GetCurrentDayHistoricalAsync(
                block,
                request.Tags,
                localStartTime.UnixTimeInSeconds,
                localEndTime.UnixTimeInSeconds,
                ["PartitionKey", "RowKey", "BatchId", "Tag", "Value", "Quality"],
                cancellationToken
            );

        // Migrate historical data to new schema (only if requested)
        _ = await _dataClientWriteRepository.MigrateHistoricalDataAsync(
            block,
            historicalEntities,
            cancellationToken
        );

        // Filter bad data before calculating the statistics
        List<Domain.TimeSeries.Types.TimeSeriesData> timeSeriesData = historicalEntities
            .Where(e => e.Quality == "Good")
            .ToTimeSeriesData(siteTzInfo);

        // Upload hourly statistics
        await _dataClientWriteRepository.UploadStatisticsDataAsync(
            block: block,
            data: timeSeriesData.ToStatisticsDataClientDto(
                block: block,
                interval: TimeSeriesResamplingInterval.Hour
            ),
            cancellationToken
        );

        // Upload daily statistics
        await _dataClientWriteRepository.UploadStatisticsDataAsync(
            block: block,
            data: timeSeriesData.ToStatisticsDataClientDto(
                block: block,
                interval: TimeSeriesResamplingInterval.Day
            ),
            cancellationToken
        );

        return Unit.Value;
    }
}
