using System.Text;

namespace RTP.StatusMonitor.App.Modules.DataClient.Shared;

public class DataClientTableQueryBuilder : IDataClientTableQueryBuilder
{
    /// <summary>
    /// Given a list of tags and a date range for a site and block
    /// Build the query to get stats data from data client table
    /// for the site and block of the tags during those dates
    /// </summary>
    public string GetStatsQuery(
        DateTime start,
        DateTime end,
        string siteName,
        string blockName,
        List<string> tags
    )
    {
        // Example: Start Bellingham-1-20231001, End Bellingham-1-20241001
        string startPartition = $"{siteName}-{blockName}-{start:yyyyMMdd}";
        string endPartition = $"{siteName}-{blockName}-{end:yyyyMMdd}";
        string partitionKeyQuery =
            @$"PartitionKey ge '{startPartition}' and PartitionKey le '{endPartition}'";

        // Combine all tags into one query for row key
        // Example: (RowKey eq 'Amb Temp' or <PERSON><PERSON><PERSON> eq 'Amb RH')
        string rowKeyQuery = string.Join(
            " or ",
            tags.Select(t => $"RowKey eq '{EscapeODataString(t)}'")
        );

        return $"{partitionKeyQuery} and ({rowKeyQuery})";
    }

    /// <summary>
    /// Get the query for snapshot partition of data client
    /// </summary>
    public string GetSnapshotQuery(string siteName, string blockName, List<string> tags)
    {
        Tuple<string, string> siteBlockTuple = DetermineSiteBlock(siteName, blockName);

        string site = siteBlockTuple.Item1;
        string block = siteBlockTuple.Item2;
        string query = $"PartitionKey eq '{site}-{block}-Snapshot'";
        if (tags.Count != 0)
        {
            List<string> rowKeyQueries = [];
            foreach (string tag in tags)
            {
                rowKeyQueries.Add($"RowKey eq '{EscapeODataString(tag)}'");
            }

            query += $" and ({string.Join(" or ", rowKeyQueries)})";
        }

        return query;
    }

    /// <summary>
    /// Given the site and customer
    /// Get the table storing data client data for that site and customer
    /// </summary>
    public string GetTableName(string siteName, string customerName) =>
        siteName switch
        {
            "GT24" => "dataclientVistra",
            "7FA" => "dataclientVandolah",
            "W501g" => "dataclientVistra",
            _ => $"dataclient{customerName}",
        };

    /// <summary>
    /// Split the time ranges in groups of smaller ranges
    /// Then build the query for each group
    /// <returns> a list of queries each is for specific time range
    /// Example:
    /// [
    ///   [ // NOTE - this is an example of a group of queries
    ///     "PartitionKey ge 'Bellingham-1-1625097600' and PartitionKey le   Bellingham-1-1625097600' and (RowKey eq 'Amb Temp' or RowKey eq 'Amb RH')", etc...
    ///   ],
    ///   [
    ///     "PartitionKey ge 'Bellingham-1-1625097600' and PartitionKey le 'Bellingham-1-1625097600' and (RowKey eq 'Amb Temp' or RowKey eq 'Amb RH')", etc...
    ///   ]
    /// ]
    public List<List<string>> GetHistoricalQuery(
        string siteName,
        string blockName,
        List<string> aliases,
        string filter,
        long startTime,
        long endTime,
        long rangeLength,
        int queryGroupSize
    )
    {
        Tuple<string, string> siteBlockTuple = DetermineSiteBlock(siteName, blockName);

        string site = siteBlockTuple.Item1;
        string block = siteBlockTuple.Item2;

        // Split the time range into smaller ranges
        List<Tuple<long, long>> timeRanges = SplitTimeRanges(startTime, endTime, rangeLength);

        List<string> queries = [];
        foreach (Tuple<long, long> i in timeRanges)
        {
            // Get the partition key range to filter
            StringBuilder query = new();
            string startPartition = $"{site}-{block}-{i.Item1}";
            string endPartition = $"{site}-{block}-{i.Item2}";

            // Build the query
            // Example: (PartitionKey ge 'Bellingham-1-1625097600' and PartitionKey le 'Bellingham-1-1625097600' and (RowKey eq 'Amb Temp' or RowKey eq 'Amb RH'))
            query
                .Append("(PartitionKey gt '")
                .Append(startPartition)
                .Append("' and PartitionKey lt '")
                .Append(endPartition)
                .Append("' and (RowKey eq '")
                .Append(EscapeODataString(aliases[0].Replace("#", string.Empty)))
                .Append('\'');

            // Add query conditional to filter by each tag
            foreach (string? tag in aliases.Skip(1))
            {
                query
                    .Append(" or RowKey eq '")
                    .Append(EscapeODataString(tag.Replace("#", string.Empty)))
                    .Append('\'');
            }
            query.Append("))");

            // Add additional filter if any
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query.Append(" and ").Append(filter);
            }

            // Add the time range query to the list
            queries.Add(query.ToString());

            query.Clear();
        }

        // Then further group the queries into smaller groups each with a size of {queryGroupSize}
        List<List<string>> queryGroups = [];
        for (int i = 0; i < queries.Count; i += queryGroupSize)
        {
            queryGroups.Add(queries.Skip(i).Take(queryGroupSize).ToList());
        }

        return queryGroups;
    }

    /// <summary>
    /// Given a start time and end time and the desired length of each range
    /// Then we split the time range into smaller ranges
    /// </summary>
    /// <param name="startTime">the start time of the range</param>
    /// <param name="endTime">the end time of the range</param>
    /// <param name="rangeLength">the desired length of each time range</param>
    /// <returns>The list of time ranges with each equal the desired length</returns>
    /// Example: [(1625097600, 1625097700), (1625097800, 1625097900)]
    private static List<Tuple<long, long>> SplitTimeRanges(
        long startTime,
        long endTime,
        long rangeLength
    )
    {
        // Split the start till end time to smaller time ranges
        List<Tuple<long, long>> timeRanges = [];

        while (startTime < endTime)
        {
            long unixNextTime = startTime + rangeLength;
            if (unixNextTime > endTime)
            {
                unixNextTime = endTime;
            }

            timeRanges.Add(Tuple.Create(startTime, unixNextTime));

            startTime = unixNextTime;
        }

        // Then we return the list of time ranges (start, end)
        return timeRanges;
    }

    private static Tuple<string, string> DetermineSiteBlock(string siteName, string blockName)
    {
        if (siteName == "GT24")
        {
            siteName = "Lake Road";
            blockName = "3";
        }
        else if (siteName == "W501g")
        {
            siteName = "Ennis";
            blockName = "1";
        }
        else if (siteName == "7FA")
        {
            siteName = "Vandolah";
            blockName = "4";
        }

        return new Tuple<string, string>(siteName, blockName);
    }

    /// <summary>
    /// Escapes single quotes in strings for OData queries used by Azure Table Storage.
    /// In OData, single quotes within string literals must be escaped by doubling them.
    /// </summary>
    /// <param name="value">The string value to escape</param>
    /// <returns>The escaped string safe for use in OData queries</returns>
    private static string EscapeODataString(string value) => value.Replace("'", "''");
}
