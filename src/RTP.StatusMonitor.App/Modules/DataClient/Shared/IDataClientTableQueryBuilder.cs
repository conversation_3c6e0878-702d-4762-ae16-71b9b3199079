namespace RTP.StatusMonitor.App.Modules.DataClient.Shared;

public interface IDataClientTableQueryBuilder
{
    public string GetStatsQuery(
      DateTime start,
      DateTime end,
      string siteName,
      string blockName,
      List<string> tags);

    public List<List<string>> GetHistoricalQuery(
      string siteName,
      string blockName,
      List<string> aliases,
      string filter,
      long startTime,
      long endTime,
      long rangeLength,
      int queryGroupSize);

    /// <summary>
    /// Given the site name, block name, and tags
    /// Build the query to get the snapshot data for the given tags 
    /// If no tags is given, return all tags
    /// </summary>
    public string GetSnapshotQuery(
      string siteName,
      string blockName,
      List<string> tags);

    public string GetTableName(string siteName, string customerName);
}
