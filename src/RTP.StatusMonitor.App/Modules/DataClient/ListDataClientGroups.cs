using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record ListDataClientGroupsQuery(
    List<Guid> UserGroups,
    Guid BlockId,
    List<string> AppRoles) : IQuery<List<string>>;

public class ListDataClientGroupsQueryHandler(DataContext context) : IQueryHandler<ListDataClientGroupsQuery, List<string>>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<List<string>>> <PERSON>le(
        ListDataClientGroupsQuery request,
        CancellationToken cancellationToken)
    {
        if (request.AppRoles.Contains("Admin.Write"))
        {
            return await _context.GroupPermissions
                .AsNoTracking()
                .Where(x => request.UserGroups.Contains(x.GroupId))
                .Distinct()
                .SelectMany(
                    x => x.Site.Blocks.Where(b => b.Id == request.BlockId).SelectMany(
                        b => b.DataClient.DataClientGroups.Select(
                            dc => dc.Name)))
                .OrderBy(x => x)
                .ToListAsync(cancellationToken);
        }

        return await _context.GroupPermissions
            .AsNoTracking()
            .Where(x => request.UserGroups.Contains(x.GroupId))
            .Distinct()
            .SelectMany(
                x => x.Site.Blocks.Where(b => b.Id == request.BlockId).SelectMany(
                    b => b.DataClient.DataClientGroups.Where(dg => dg.IsCustomerAccess).Select(
                        dc => dc.Name)))
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);
    }
}
