using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Exceptions;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record GetDataClientDetailsQuery(
    Guid BlockId,
    List<string> GroupNames) : IQuery<DataClientResult>;

public record DataClientResult(
    List<DataClientGroupResult>? VariableGroups,
    List<StatusVariableResult>? AmbientVariables,
    List<StatusVariableResult>? StatusVariables,
    List<StatusVariableResult>? InfoVariables);

public record DataClientGroupResult(
    Guid Id,
    string Name,
    string? Description,
    string? Alias,
    List<GroupVariableResult> Variables);

public record GroupVariableResult(
    Guid Id,
    string Tag,
    string Alias,
    string EngUnits,
    int NoDecimals,
    double Min,
    double Max,
    string? Color,
    int Thickness,
    string LineType = "line");

public record StatusVariableResult(
    Guid Id,
    string Tag,
    string Alias,
    string Abbreviation,
    string Expression,
    string EngUnits,
    int NoDecimals,
    string? Color);

public class GetDataClientDetailsQueryHandler(DataContext context) : IQueryHandler<GetDataClientDetailsQuery, DataClientResult>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<DataClientResult>> Handle(GetDataClientDetailsQuery request, CancellationToken cancellationToken)
    {
        Block? block = await _context.Blocks
            .AsNoTracking()
            .Include(b => b.DataClient)
            .ThenInclude(dc => dc.DataClientStatusVariables)
            .ThenInclude(dcsv => dcsv.DataClientVariable)
            .SingleOrDefaultAsync(b => b.Id == request.BlockId, cancellationToken);

        Block? selectedGroup = await _context.Blocks
            .AsNoTracking()
            .Include(b => b.DataClient)
            .ThenInclude(dc => dc.DataClientGroups.Where(dcg => request.GroupNames.Contains(dcg.Name)))
            .ThenInclude(dcg => dcg.DataClientGroupVariables)
            .ThenInclude(dcgv => dcgv.DataClientVariable)
            .SingleOrDefaultAsync(b => b.Id == request.BlockId, cancellationToken);

        if (block is null)
        {
            throw new NotFoundException("Block with given Id not found");
        }

        // Manual mapping for groups and their variables
        List<DataClientGroupResult>? groupResults = selectedGroup?.DataClient.DataClientGroups
            .Select(group => new DataClientGroupResult(
                Id: group.Id,
                Name: group.Name,
                Description: group.Description,
                Alias: group.Alias,
                Variables: group.DataClientGroupVariables
                    .Select(variable => new GroupVariableResult(
                        Id: variable.DataClientVariable.Id,
                        Tag: variable.DataClientVariable.Tag,
                        Alias: variable.DataClientVariable.Alias,
                        EngUnits: variable.DataClientVariable.EngUnits,
                        NoDecimals: variable.NoDecimals,
                        Min: variable.Min,
                        Max: variable.Max,
                        Color: variable.Color,
                        Thickness: variable.Thickness,
                        LineType: variable.LineType))
                    .ToList()))
            .ToList();

        List<StatusVariableResult> ambientVariables = [.. block.DataClient.DataClientStatusVariables
            .Where(dcsv => dcsv.Type == DataClientStatusType.Ambient)
            .Select(variable => new StatusVariableResult(
                Id: variable.DataClientVariable.Id,
                Tag: variable.Tag,
                Alias: variable.DataClientVariable.Alias,
                Abbreviation: variable.Abbreviation!,
                Expression: variable.Expression!,
                EngUnits: variable.DataClientVariable.EngUnits,
                NoDecimals: variable.NoDecimals,
                Color: variable.Color))
            .OrderBy(av => av.Abbreviation switch
                {
                    "T" => 0,
                    "RH" => 1,
                    _ => av.Abbreviation == "P" ? 2 : 3,
                })];

        List<StatusVariableResult> statusVariables = block.DataClient.DataClientStatusVariables
            .Where(dcsv => dcsv.Type == DataClientStatusType.Status)
            .Select(variable => new StatusVariableResult(
                Id: variable.DataClientVariable.Id,
                Tag: variable.Tag,
                Alias: variable.DataClientVariable.Alias,
                Abbreviation: variable.Abbreviation!,
                Expression: variable.Expression!,
                EngUnits: variable.DataClientVariable.EngUnits,
                NoDecimals: variable.NoDecimals,
                Color: variable.Color))
            .ToList();

        List<StatusVariableResult> infoVariables = block.DataClient.DataClientStatusVariables
            .Where(dcsv => dcsv.Type == DataClientStatusType.Info)
            .Select(variable => new StatusVariableResult(
                Id: variable.DataClientVariable.Id,
                Tag: variable.Tag,
                Alias: variable.DataClientVariable.Alias,
                Abbreviation: variable.Abbreviation!,
                Expression: variable.Expression!,
                EngUnits: variable.DataClientVariable.EngUnits,
                NoDecimals: variable.NoDecimals,
                Color: variable.Color))
            .ToList();

        return new DataClientResult(
            VariableGroups: groupResults,
            AmbientVariables: ambientVariables,
            StatusVariables: statusVariables,
            InfoVariables: infoVariables);
    }
}
