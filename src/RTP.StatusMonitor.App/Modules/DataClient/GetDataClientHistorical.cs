using System.Diagnostics;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DataClient.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record GetDataClientHistoricalQuery(
    List<ActualSeries> Series,
    DateTime StartTime,
    DateTime EndTime,
    TimeSeriesResamplingInterval Interval)
    : IAuthorizedQuery<List<DataClientHistoricalResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    // NOTE - data client is at block level
    public Guid? BlockId { get; set; }

    public Guid? SiteId { get; set; }
    public Guid? UnitId { get; set; } = null;
}
public record DataClientHistoricalResponse(
    Guid Id,
    string Name,
    List<double> Values,
    List<string> Timestamps);
internal sealed class GetDataClientHistoricalQueryHandler(
    DataClientReadRepository dataClientReadRepository,
    IDataContext dataContext)
        : IQueryHandler<GetDataClientHistoricalQuery, List<DataClientHistoricalResponse>>
{
    private readonly IDataContext _context = dataContext;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;

    public async Task<ErrorOr<List<DataClientHistoricalResponse>>> Handle(
        GetDataClientHistoricalQuery request,
        CancellationToken ct)
    {
        // Get the block data
        Block? block = await _context.Blocks
            .AsNoTracking()
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .SingleOrDefaultAsync(b => b.Id == request.BlockId, ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Extract and combine all tags from series (tag/filter/calculation)
        List<string> uniqueVariables = [.. request.Series
            .Select(s => s.Tag)
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Filter)))
            .Distinct()];

        Stopwatch timer = new();
        timer.Start();
        List<TimeSeriesData> timeSeriesData = [];
        if (request.Interval == TimeSeriesResamplingInterval.Hour)
        {
            // Get the statistical data from data client
            List<StatisticsDataClientDto> results = await _dataClientReadRepository
                .GetHistoricalHourlyDataAsync(
                    block: block,
                    uniqueTags: uniqueVariables,
                    startDate: request.StartTime,
                    endDate: request.EndTime,
                    ct: ct);

            // Transform to time series data for analytics
            timeSeriesData = results.ToTimeSeriesData();
        }
        else
        {
            // Split the date range and query accordingly
            timeSeriesData = await _dataClientReadRepository
                .GetHistoricalDataAsync(
                    block: block,
                    uniqueVariables: uniqueVariables,
                    startTime: request.StartTime,
                    endTime: request.EndTime,
                    ct: ct);
        }

        // Downsample the data by requested interval and create a data table for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable
            .Create([.. timeSeriesData
                .Select(ts => ts.Downsample(
                request.Interval,
                TimeSeriesDownsampling.Average))]);

        List<DataClientHistoricalResponse> responses = [.. request.Series
            .Select(x =>
            {
                // Get the data for the series
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: x.Tag,
                    calculation: x.Calculation,
                    filter: x.Filter);

                return new DataClientHistoricalResponse(
                    Id: x.Id,
                    Name: x.Name,
                    Values: [.. data.Values.Select(x => x.ToDoubleOrDefault())],
                    Timestamps: [.. data.Timestamps.Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))]);
            })];

        // Evaluate the expression for each chart series
        return responses;
    }
}

// NOTE - experiment with DuckDb
// Working version with parallel processing for each series (very slow)
// TODO - Need to group the queries by filter and query 
// using System.Collections.Concurrent;
// using System.Data;
// using System.Diagnostics;
// using Dapper;
// using DuckDB.NET.Data;
// using ErrorOr;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Options;
// using RTP.StatusMonitor.App.DataClient.Shared.Dtos;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.App.Shared.Options;
// using RTP.StatusMonitor.Domain.Abstractions.Extensions;
// using RTP.StatusMonitor.Domain.Blocks;
// using RTP.StatusMonitor.Domain.TimeSeries;
// using RTP.StatusMonitor.Domain.TimeSeries.Types;
// using RTP.StatusMonitor.Persistence;

// namespace RTP.StatusMonitor.App.DataClient.GetDataClientHistorical;

// internal sealed class GetDataClientHistoricalQueryHandler
//     : IQueryHandler<GetDataClientHistoricalQuery, List<DataClientHistoricalResponse>>
// {
//     private readonly IDataContext _context;
//     private readonly BlobStorageOptions _blobStorageOptions;

//     public GetDataClientHistoricalQueryHandler(
//         IDataContext dataContext,
//         IOptions<BlobStorageOptions> options)
//     {
//         _context = dataContext;
//         _blobStorageOptions = options.Value;
//     }
//     public async Task<ErrorOr<List<DataClientHistoricalResponse>>> Handle(
//         GetDataClientHistoricalQuery request,
//         CancellationToken ct)
//     {
//         // Get the block data
//         Block? block = await _context.Blocks
//             .AsNoTracking()
//             .Include(b => b.Site)
//                 .ThenInclude(s => s.Customer)
//             .SingleOrDefaultAsync(b => b.Id == request.BlockId, ct);

//         if (block is null) return BlockDomainErrors.NotFound;

//         // Extract and combine all tags from series (tag/filter/calculation)
//         List<string> uniqueVariables = request.Series
//             .Select(s => s.Tag)
//             .Concat(request.Series
//                 .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
//             .Concat(request.Series
//                 .SelectMany(s => ExpressionParser.Parse(s.Filter)))
//             .Distinct()
//             .ToList();

//         Stopwatch timer = new();
//         timer.Start();

//         List<DataClientHistoricalResponse> responses = await GetHistoricalDataAsync(
//             request.Series,
//             request.StartTime,
//             request.EndTime);
//         List<TimeSeriesData> timeSeriesData = new();


//         timer.Stop();
//         Console.WriteLine($"Time taken to get historical data: {timer.ElapsedMilliseconds}ms");

//         return responses;
//     }

//     // TODO - Need to optimize by grouping the query by filter instead of querying each series separately
//     public async Task<List<DataClientHistoricalResponse>> GetHistoricalDataAsync(
//            List<ActualSeries> series,
//            DateTime startTime,
//            DateTime endTime)
//     {
//         // Get the range to query
//         string start = startTime.ToString("yyyy-MM-dd HH:mm:ss");
//         string end = endTime.ToString("yyyy-MM-dd HH:mm:ss");

//         using DuckDBConnection conn = new("DataSource=:memory:");
//         conn.Open();

//         // Setup Azure credentials and connection
//         await conn.ExecuteAsync("INSTALL azure; LOAD azure;");
//         await conn.ExecuteAsync(@$"
//             CREATE SECRET azure_secret (
//                 TYPE AZURE,
//                 CONNECTION_STRING '{_blobStorageOptions.ConnectionString}');");

//         // Process each series in parallel 
//         ConcurrentBag<DataClientHistoricalResponse> responses = new();
//         Parallel.ForEach(series, (s) =>
//         {
//             try
//             {
//                 // Get the filter expression
//                 string filter = s.Filter.Value;

//                 // Replace the [ and ] with ""
//                 filter = !string.IsNullOrEmpty(filter)
//                     ? filter.Replace("[", "\"")
//                         .Replace("]", "\"")
//                     : filter;

//                 // NOTE -The filter currently has format of "Unit at Base Load = 1"
//                 // Need to convert to DuckDb format of ""Unit at Base Load"" = 1
//                 // Build the query if filter is not null
//                 string filterStatement = !string.IsNullOrEmpty(filter)
//                     ? $@"WHERE year = {endTime.Year} AND month = {endTime.Month} AND {filter} AND ""Timestamp"" >= '{start}' AND ""Timestamp"" <= '{end}'"
//                     : $@"WHERE year = {endTime.Year} AND month = {endTime.Month} AND ""Timestamp"" >= '{start}' AND ""Timestamp"" <= '{end}'";

//                 // Build the query for each series in the group
//                 string selectStatement = $"\"{s.Tag}\"";

//                 string query = $@"
//                 SELECT 
//                     ""Timestamp"",
//                     {selectStatement}
//                 FROM read_parquet(
//                     'abfss://rtpdata.blob.core.windows.net/vistra/hays/block1/performance/*/*/{endTime:yyyy-MM-dd}*.parquet', hive_partitioning = true)
//                 {filterStatement};";

//                 // Create a DuckDBCommand to execute the query
//                 using DuckDBCommand cmd = new(query, conn);

//                 // Execute the query and load the result into a DataTable
//                 using DuckDBDataReader reader = cmd.ExecuteReader();

//                 // Load the result into a DataTable
//                 DataTable table = new();

//                 table.Load(reader);

//                 // Get the time series data from the table
//                 TimeSeriesData data = table.GetColumnData(s.Tag);

//                 responses.Add(new DataClientHistoricalResponse(
//                     Id: s.Id,
//                     Name: s.Name,
//                     Values: data.Values
//                         .Select(x => x.ToDoubleOrDefault())
//                         .ToList(),
//                     Timestamps: data.Timestamps
//                         .Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))
//                         .ToList()));
//             }
//             catch (Exception ex)
//             {
//                 Console.WriteLine(ex);
//             }
//         });

//         return responses.ToList();
//     }
// }


