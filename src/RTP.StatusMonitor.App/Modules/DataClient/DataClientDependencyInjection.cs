using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.App.Modules.DataClient.Shared;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public static class DataClientDependencyInjection
{
    public static IServiceCollection AddDataClient(this IServiceCollection services)
    {
        services.AddScoped<DataClientReadRepository>();
        services.AddScoped<DataClientWriteRepository>();

        services.AddScoped<IDataClientTableQueryBuilder, DataClientTableQueryBuilder>();

        return services;
    }
}
