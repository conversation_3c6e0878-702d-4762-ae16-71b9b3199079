using Azure.Data.Tables;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record DataClientCleanupCommand(
    Guid BlockId,
    DateTime StartDate,
    DateTime EndDate) : ICommand;

internal sealed class DataClientCleanupCommandHandler(
    DataContext context,
    DataClientReadRepository dataClientReadRepository,
    DataClientWriteRepository dataClientWriteRepository)
    : ICommandHandler<DataClientCleanupCommand>
{
    private readonly DataContext _dataContext = context;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;
    private readonly DataClientWriteRepository _dataClientWriteRepository = dataClientWriteRepository;
    public async Task<ErrorOr<Unit>> Handle(
        DataClientCleanupCommand request,
        CancellationToken ct)
    {
        // Get the block info
        Block block = await _dataContext.Blocks
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .SingleAsync(b => b.Id == request.BlockId, ct);

        // Get the local start and end time
        TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);
        DateTimeWithZone localStartTime = new(request.StartDate, siteTzInfo);
        DateTimeWithZone localEndTime = new(request.EndDate, siteTzInfo);

        // Get the historical data from existing data client
        List<string> tags = _dataClientReadRepository
            .GetDataClientTags(block, ct);

        // Partition the tags into groups of 200 
        List<List<string>> tagGroups = [.. tags
            .Select((value, index) => new { value, index })
            .GroupBy(x => x.index / 200)
            .Select(group => group.Select(x => x.value).ToList())];

        await Parallel.ForEachAsync(tagGroups, async (tagGroup, _) =>
        {
            List<DataClientHistoricalWriteSchemaDto> historicalEntities = await _dataClientReadRepository
                .GetCurrentDayHistoricalAsync(
                    block: block,
                    uniqueTags: tagGroup,
                    unixStartTimeInSeconds: localStartTime.UnixTimeInSeconds,
                    unixEndTimeInSeconds: localEndTime.UnixTimeInSeconds,
                    columns: ["PartitionKey", "RowKey"],
                    ct: ct);

            // After all migration is done, delete data from old schema
            await _dataClientWriteRepository.DeleteWriteSchemaDataAsync(
                block: block,
                entities: [.. historicalEntities.Select(e => new TableEntity
                {
                    PartitionKey = e.PartitionKey,
                    RowKey = e.RowKey
                })],
                ct);
        });

        return Unit.Value;
    }
}
