using Azure.Data.Tables;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DataClient.Shared;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DataClient;

public record GetDataClientVariablesQuery(
    Guid BlockId,
    List<Guid> UserGroupsId) : IQuery<List<DataClientVariableResponse>>;

public record DataClientVariableResponse(
    Guid Id,
    string Tag,
    string? Alias,
    string? EngUnits,
    double? DefaultValue);

internal sealed class GetDataClientVariablesQueryHandler(
    IDataContext context,
    ITableStorage storageClient,
    IDataClientTableQueryBuilder queryBuilder)
    : IQueryHandler<GetDataClientVariablesQuery, List<DataClientVariableResponse>>
{
    private readonly IDataContext _context = context;
    private readonly ITableStorage _storageClient = storageClient;
    private readonly IDataClientTableQueryBuilder _queryBuilder = queryBuilder;

    public async Task<ErrorOr<List<DataClientVariableResponse>>> Handle(
        GetDataClientVariablesQuery request,
        CancellationToken ct)
    {
        // Get block information
        var blockDto = await _context.Blocks
            .AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Select(b => new
            {
                BlockName = b.Name,
                SiteName = b.Site.Name,
                CustomerName = b.Site.Customer.Name,
            })
            .FirstOrDefaultAsync(cancellationToken: ct);

        if (blockDto is null)
        {
            return BlockDomainErrors.NotFound;
        };

        // Get the snapshot partition
        string query = _queryBuilder.GetSnapshotQuery(
            siteName: blockDto.SiteName,
            blockName: blockDto.BlockName,
            tags: []);

        string tableName = _queryBuilder.GetTableName(blockDto.SiteName, blockDto.CustomerName);

        List<TableEntity> entities = await _storageClient
            .QueryEntitiesAsync(tableName, query, ct);

        return entities.ConvertAll(row => new DataClientVariableResponse(
            Id: Guid.NewGuid(),
            Tag: row["Tag"].ToString() ?? string.Empty,
            Alias: row["RowKey"].ToString() ?? string.Empty,
            EngUnits: string.Empty,
            DefaultValue: null));
    }
}
