using ErrorOr;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Datasets;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json;

namespace RTP.StatusMonitor.App.Modules.DashboardManagement.CreateNewDataset;

public class CreateNewDatasetCommandHandler
    : ICommandHandler<CreateNewDatasetCommand, Dataset>
{
    private const string DatasetFilePath = @"C:\Users\<USER>\source\repos\RTP_Status_Monitor_API\test-data\datasets.json";
    private static readonly JsonSerializerSettings JsonSettings = new()
    {
        Formatting = Formatting.Indented,
        Converters = { new StringEnumConverter() },
        ContractResolver = new CamelCasePropertyNamesContractResolver()
    };

    public async Task<ErrorOr<Dataset>> Handle(
        CreateNewDatasetCommand request,
        CancellationToken ct = default)
    {
        Dataset dataset = Dataset.Create(
            request.Name,
            request.Description,
            request.Type,
            request.Configuration,
            request.CreatedBy);
        try
        {
            await PersistDatasetToFile(dataset);
            return dataset;
        }
        catch (Exception ex)
        {
            return Error.Failure("SaveDatasetError", ex.Message);
        }
    }

    private static async Task PersistDatasetToFile(Dataset newDataset)
    {
        List<Dataset> datasets;
        string? directory = Path.GetDirectoryName(DatasetFilePath);

        if (!Directory.Exists(directory))
        {
            throw new DirectoryNotFoundException($"Directory {directory} does not exist");
        }


        if (File.Exists(DatasetFilePath))
        {
            string jsonContent = await File.ReadAllTextAsync(DatasetFilePath);
            datasets = JsonConvert.DeserializeObject<List<Dataset>>(jsonContent, JsonSettings) ?? [];
        }
        else
        {
            datasets = [];
        }
        datasets.Add(newDataset);
        string updatedJson = JsonConvert.SerializeObject(datasets, JsonSettings);
        await File.WriteAllTextAsync(DatasetFilePath, updatedJson);
    }
}
