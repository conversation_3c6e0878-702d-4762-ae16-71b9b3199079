using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Forecast;
public record GetRealTimeForecastQuery(
    List<ForecastSeries> Series,
    TimeSeriesResamplingInterval Interval)
    : ICachedQuery<List<ForecastDataResponse>>, IAuthorizedQuery<List<ForecastDataResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? BlockId { get; set; }

    // NOTE - forecast is at block level
    public Guid? SiteId { get; set; } = null;
    public Guid? UnitId { get; set; } = null;
    public bool IsCacheEnabled => true;
    public string CacheKey
    => $"realtime-forecast-{BlockId}-{JsonConvert.SerializeObject(Series)}-{Interval}";
    public TimeSpan CacheDuration => TimeSpan.FromMinutes(30);
}

internal class GetRealTimeForecastQueryHandler(
    IDataContext dataContext,
    ForecastRepository forecastRepository)
        : IQueryHandler<GetRealTimeForecastQuery, List<ForecastDataResponse>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly ForecastRepository _forecastRepository = forecastRepository;

    public async Task<ErrorOr<List<ForecastDataResponse>>> Handle(
        GetRealTimeForecastQuery request,
        CancellationToken ct)
    {
        // Get the block info
        Block? block = await _dataContext.Blocks
            .AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .FirstOrDefaultAsync(ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Extract all unique variables from the series
        List<string> uniqueVariables = request.Series
                .Select(s => s.Tag)
                .Concat(request.Series
                    .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
                .Concat(request.Series
                    .SelectMany(s => ExpressionParser.Parse(s.Filter)))
                .Distinct()
                .ToList();

        // Get the real time forecast
        List<ForecastDataDto> realTimeForecast = await _forecastRepository
            .GetLatestForecastAsync(
                block: block,
                tags: uniqueVariables,
                ct);

        // Convert to time series data and interpolate for analytics
        IEnumerable<TimeSeriesData> timeSeriesData = realTimeForecast
            .Select(x => x.ToTimeSeriesData()
                .LinearInterpolate(request.Interval));

        // Dump data into data table for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(timeSeriesData);

        // Evaluate the expression for each chart series
        return request.Series
            .Select(x =>
            {
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: x.Tag,
                    calculation: x.Calculation,
                    filter: x.Filter);

                return new ForecastDataResponse(
                    Id: x.Id,
                    Name: x.Name,
                    Values: data.Values
                        // .Select(x => x.ToDoubleOrDefault())
                        .ToList(),
                    Timestamps: data.Timestamps
                        .Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))
                        .ToList());
            })
            .ToList();
    }
}
