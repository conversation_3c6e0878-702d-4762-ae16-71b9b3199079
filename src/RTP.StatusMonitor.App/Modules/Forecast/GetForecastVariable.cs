using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;

namespace RTP.StatusMonitor.App.Modules.Forecast;

public record GetForecastVariableQuery(
  List<Guid> UserGroupsId,
  Guid BlockId) : IQuery<List<string>>;

internal sealed class GetForecastVariableQueryHandler(
    IDataContext dataContext,
    IAuthorizationService authorizationService,
    ForecastRepository forecastRepository)
        : IQueryHandler<GetForecastVariableQuery, List<string>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IAuthorizationService _authorizationService = authorizationService;
    private readonly ForecastRepository _forecastRepository = forecastRepository;

    public async Task<ErrorOr<List<string>>> Handle(
        GetForecastVariableQuery request,
        CancellationToken ct = default)
    {
        // Get block information
        Block? block = await _dataContext.Blocks
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .Include(b => b.Site)
            .ThenInclude(s => s.GroupPermissions)
            .FirstOrDefaultAsync(ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Check for user authorization
        bool isAuthorized = await _authorizationService
            .CheckUserAccessByGroup(
                userGroupsId: request.UserGroupsId,
                siteId: block.Site.Id,
                ct: ct);

        if (!isAuthorized)
            return SiteDomainErrors.Unauthorized;

        // Return the list of forecast variables        
        return await _forecastRepository.GetForecastTagsAsync(block, ct);
    }
}
