using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Forecast;

public record GetDayAheadForecastQuery(
    DateOnly StartDate,
    DateOnly EndDate,
    List<ForecastSeries> Series,
    TimeSeriesResamplingInterval Interval
) : IAuthorizedQuery<List<ForecastDataResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? BlockId { get; set; }

    // NOTE - forecast is at block level
    public Guid? SiteId { get; set; } = null;
    public Guid? UnitId { get; set; } = null;
}

internal class GetDayAheadForecastQueryHandler(
    IDataContext dataContext,
    ForecastRepository forecastRepository
) : IQueryHandler<GetDayAheadForecastQuery, List<ForecastDataResponse>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly ForecastRepository _forecastRepository = forecastRepository;

    public async Task<ErrorOr<List<ForecastDataResponse>>> Handle(
        GetDayAheadForecastQuery request,
        CancellationToken ct
    )
    {
        Block? block = await _dataContext
            .Blocks.AsNoTracking()
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .FirstOrDefaultAsync(ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Extract all unique variables from the series
        List<string> uniqueVariables = request
            .Series.Select(s => s.Tag)
            .Concat(request.Series.SelectMany(s => ExpressionParser.Parse(s.Calculation)))
            .Concat(request.Series.SelectMany(s => ExpressionParser.Parse(s.Filter)))
            .Distinct()
            .ToList();

        // Filter the forecast data dict by variables requested
        // Get all forecast data from blob file
        (DateOnly startDate, DateOnly endDate) = (request.StartDate, request.EndDate.AddDays(1));

        // Get the day ahead forecast for the date range
        List<ForecastDataDto> dayAheadForecast = await _forecastRepository.GetDayAheadForecastAsync(
            block: block,
            tags: uniqueVariables,
            startDate: startDate,
            endDate: endDate,
            ct
        );

        // Convert to time series data and interpolate for analytics
        IEnumerable<TimeSeriesData> timeSeriesData = dayAheadForecast.Select(x =>
            x.ToTimeSeriesData().LinearInterpolate(request.Interval)
        );

        // Get unique timestamps from dataset
        HashSet<DateTime> uniqueTimestamps = timeSeriesData
            .SelectMany(x => x.Timestamps)
            .ToHashSet();

        // Dump data into data table for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(timeSeriesData);

        // Evaluate the expression for each chart series
        return request
            .Series.Select(x =>
            {
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: x.Tag,
                    calculation: x.Calculation,
                    filter: x.Filter
                );

                return new ForecastDataResponse(
                    Id: x.Id,
                    Name: x.Name,
                    Values: [.. data.Values],
                    Timestamps: data.Timestamps.Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))
                        .ToList()
                );
            })
            .ToList();
    }
}
