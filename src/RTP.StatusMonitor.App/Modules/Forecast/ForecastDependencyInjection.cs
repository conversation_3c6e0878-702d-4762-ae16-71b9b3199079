using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;

namespace RTP.StatusMonitor.App.Modules.Forecast;

public static class ForecastDependencyInjection
{
    public static IServiceCollection AddForecasting(this IServiceCollection services)
    {
        services.AddTransient<ForecastApi>();
        services.AddTransient<ForecastRepository>();

        return services;
    }
}
