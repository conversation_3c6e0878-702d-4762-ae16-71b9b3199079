using System.Data;
using System.Data.Common;
using Dapper;
using DuckDB.NET.Data;
using ErrorOr;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Modules.Performance.GetPerformanceData;
internal sealed class GetPerformanceDataQueryHandler(
    ILogger<GetPerformanceDataQueryHandler> logger,
    IOptions<BlobStorageOptions> options)
        : IQueryHandler<GetPerformanceDataQuery, List<Dictionary<string, object?>>>
{
    private readonly ILogger<GetPerformanceDataQueryHandler> _logger = logger;
    private readonly BlobStorageOptions _blobStorageOptions = options.Value;
    public async Task<ErrorOr<List<Dictionary<string, object?>>>> Handle(
        GetPerformanceDataQuery request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using DuckDBConnection conn = new("DataSource=:memory:");
            await conn.OpenAsync(cancellationToken);

            // Setup Azure credentials and connection
            await conn.ExecuteAsync($"SET azure_transport_option_type = curl;");
            await conn.ExecuteAsync(@$"
            CREATE SECRET azure_secret (
                TYPE AZURE,
                CONNECTION_STRING '{_blobStorageOptions.ConnectionString}');");

            // Create a DuckDBCommand to execute the query
            using DuckDBCommand cmd = new(request.SqlQuery, conn);

            // Execute the query and load the result into a DataTable
            using DbDataReader reader = await cmd.ExecuteReaderAsync(cancellationToken);

            // Convert to list of dictionaries where each dictionary represents a row
            List<Dictionary<string, object?>> records = [];

            // Get column names from reader
            List<string> columnNames = Enumerable.Range(0, reader.FieldCount)
                .Select(reader.GetName)
                .ToList();

            // Read each row
            while (reader.Read())
            {
                Dictionary<string, object?> record = [];
                foreach (string columnName in columnNames)
                {
                    record[columnName] = reader[columnName];
                }
                records.Add(record);
            }

            return records;
        }
        catch (DuckDBException ex)
        {
            _logger.LogError(ex, "Failed to storage using DuckDb. Error: {message}", ex.Message);
            throw;
        }
    }
}

// REFERENCE
// using System.Data;
// using Dapper;
// using DuckDB.NET.Data;
// using ErrorOr;
// using Microsoft.Extensions.Options;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.App.Shared.Options;

// namespace RTP.StatusMonitor.App.Modules.Performance.GetPerformanceData;

// internal sealed class GetPerformanceDataQueryHandler(
//     IOptions<BlobStorageOptions> options)
//         : IQueryHandler<GetPerformanceDataQuery, List<PerformanceData>>
// {
//     private readonly BlobStorageOptions _blobStorageOptions = options.Value;

//     public async Task<ErrorOr<List<PerformanceData>>> Handle(
//         GetPerformanceDataQuery request,
//         CancellationToken cancellationToken = default)
//     {
//         DataTable table = new();

//         using DuckDBConnection conn = new("DataSource=:memory:");
//         conn.Open();

//         // Setup Azure credentials and connection (similar to your Python example)
//         await conn.ExecuteAsync("INSTALL azure; LOAD azure;");
//         await conn.ExecuteAsync(@$"
//             CREATE SECRET azure_secret (
//                 TYPE AZURE,
//                 CONNECTION_STRING '{_blobStorageOptions.ConnectionString}');");

//         // Create a DuckDBCommand to execute the query
//         using DuckDBCommand cmd = new(request.SqlQuery, conn);

//         // Execute the query and load the result into a DataTable
//         using DuckDBDataReader reader = cmd.ExecuteReader();

//         // Load the result into a DataTable
//         table.Load(reader);

//         List<PerformanceData> performanceData = [];
//         // Extract each column from the DataTable into a PerformanceData object with the timestamp column as the first column
//         List<DateTime> timestamps = [.. table
//             .AsEnumerable()
//             .Select(row => row.Field<DateTime>("Timestamp"))];

//         foreach (DataColumn column in table.Columns)
//         {
//             if (column.ColumnName == "Timestamp")
//                 continue;

//             List<object?> values = table.AsEnumerable()
//                 .Select(row => row.Field<object>(column.ColumnName))
//                 .ToList();

//             performanceData.Add(new PerformanceData(
//                 Guid.NewGuid(),
//                 column.ColumnName,
//                 timestamps.Select(t => t.ToString("yyyy-MM-dd HH:mm:ss")).ToList(),
//                 values));
//         }

//         return performanceData;
//     }
// }

// Define the SQL query to read from the Parquet files
//string query = $@"
//    SELECT ""Timestamp"", ""Unit Net Power Output"", ""Ambient Air Temp""
//    FROM 'abfss://rtpdata.blob.core.windows.net/vistra/hays/test/Performance-Parquet/2024-09-10_Hays_1_Performance.parquet'
//    WHERE ""Unit at Base Load"" = 1";

// REFERENCE
// using System.Data;
// using System.Diagnostics;
// using Dapper;
// using DuckDB.NET.Data;
// using ErrorOr;
// using Microsoft.Extensions.Caching.Memory;
// using Microsoft.Extensions.Options;
// using Parquet;
// using Parquet.Schema;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.App.Shared.Options;

// namespace RTP.StatusMonitor.App.Modules.Performance.GetPerformanceData;

// internal sealed class GetPerformanceDataQueryHandler
//     : IQueryHandler<GetPerformanceDataQuery, byte[]>
// {
//     private readonly BlobStorageOptions _blobStorageOptions;
//     private readonly IMemoryCache _cache;

//     public GetPerformanceDataQueryHandler(
//         IMemoryCache cache,
//         IOptions<BlobStorageOptions> options)
//     {
//         _cache = cache;
//         _blobStorageOptions = options.Value;
//     }

//     public async Task<ErrorOr<byte[]>> Handle(
//         GetPerformanceDataQuery request,
//         CancellationToken cancellationToken = default)
//     {
//         return await _cache.GetOrCreateAsync("TEST", async entry =>
//         {
//             entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60);

//             DataTable table = new();
//             using DuckDBConnection conn = new("DataSource=:memory:");

//             conn.Open();

//             // Setup Azure credentials and connection (similar to your Python example)
//             await conn.ExecuteAsync("INSTALL azure; LOAD azure;");
//             await conn.ExecuteAsync(@$"
//             CREATE SECRET azure_secret (
//                 TYPE AZURE,
//                 CONNECTION_STRING '{_blobStorageOptions.ConnectionString}');");

//             // Create a DuckDBCommand to execute the query
//             //using DuckDBCommand cmd = new(request.SqlQuery, conn);
//             //SELECT "Timestamp", "Unit Net Power Output", "Ambient Air Temp", "GT FLOWTIM Air Speed"
//             //FROM read_parquet('abfss://rtpdata.blob.core.windows.net/vistra/hays/block1/performance/*/*/*.parquet', hive_partitioning = true)
//             //WHERE year = 2024 AND month = 8 AND "Unit at Base Load" = 1;
//             using DuckDBCommand cmd = new(@$"
//             SELECT ""Timestamp"", ""Unit Net Power Output"", ""Ambient Air Temp"", ""GT FLOWTIM Air Speed""
//             FROM read_parquet(
//                 'abfss://rtpdata.blob.core.windows.net/vistra/hays/block1/performance/*/*/*.parquet', hive_partitioning = true)
//             WHERE year = 2024 AND month = 8 AND ""Unit at Base Load"" = 1;", conn);

//             // Execute the query and load the result into a DataTable
//             using DuckDBDataReader reader = cmd.ExecuteReader();

//             // Load the result into a DataTable
//             table.Load(reader);

//             // Convert DataTable to Parquet
//             MemoryStream parquetStream = new();

//             var schema = new ParquetSchema(GetParquetColumns(table));

//             // Create ParquetWriter using CreateAsync
//             try
//             {
//                 using (ParquetWriter parquetWriter = await ParquetWriter
//                             .CreateAsync(schema, parquetStream, cancellationToken: cancellationToken))
//                 {
//                     using ParquetRowGroupWriter rowGroupWriter = parquetWriter.CreateRowGroup();

//                     foreach (DataColumn column in table.Columns)
//                     {
//                         // Extract data from the column as an array of objects
//                         var objectData = table.AsEnumerable().Select(row => row[column]).ToArray();

//                         Array typedData;

//                         if (column.DataType == typeof(DateTime))
//                         {
//                             typedData = objectData.Cast<DateTime>().ToArray();
//                         }
//                         else if (column.DataType == typeof(DateOnly))
//                         {
//                             typedData = objectData.Cast<DateOnly>().ToArray();
//                         }
//                         else if (column.DataType == typeof(double) || column.DataType == typeof(float))
//                         {
//                             typedData = objectData.Cast<double?>()
//                                 .Select(d => d ?? double.NaN)
//                                 .ToArray();
//                         }
//                         else if (column.DataType == typeof(int) || column.DataType == typeof(long))
//                         {
//                             typedData = objectData.Cast<long?>()
//                                 .Select(d => d ?? double.NaN)
//                                 .ToArray();
//                         }
//                         else
//                         {
//                             throw new InvalidOperationException($"Unsupported column data type: {column.DataType}");
//                         }

//                         // Find the correct data field for this column
//                         var dataField = schema.Fields.OfType<DataField>()
//                             .FirstOrDefault(f => f.Name == column.ColumnName);

//                         if (dataField != null)
//                         {
//                             // Write the typed data to the column
//                             await rowGroupWriter.WriteColumnAsync(new Parquet.Data.DataColumn(dataField, typedData));
//                         }
//                     }
//                 }

//                 parquetStream.Seek(0, SeekOrigin.Begin); // Reset the stream position for download

//                 return parquetStream.ToArray();
//             }
//             catch (Exception ex)
//             {
//                 throw new Exception("Error creating ParquetWriter", ex);
//             }
//         });
//     }

//     private static DataField[] GetParquetColumns(DataTable table)
//     {
//         return table.Columns
//             .Cast<DataColumn>()
//             .Select<DataColumn, DataField>(col =>
//             {
//                 if (col.DataType == typeof(DateTime))
//                 {
//                     return new DataField<DateTime>(col.ColumnName);
//                 }
//                 else if (col.DataType == typeof(DateOnly))
//                 {
//                     return new DataField<DateOnly>(col.ColumnName);
//                 }
//                 else if (col.DataType == typeof(double) || col.DataType == typeof(float))
//                 {
//                     return new DataField<double>(col.ColumnName); // Use double for numeric columns
//                 }
//                 else if (col.DataType == typeof(int) || col.DataType == typeof(long))
//                 {
//                     return new DataField<long>(col.ColumnName); // Use long for integers
//                 }
//                 else
//                 {
//                     throw new InvalidOperationException($"Unsupported column data type: {col.DataType}");
//                 }
//             })
//             .ToArray();
//     }
// }

// // Helper method to convert object array to a strongly typed array
// private Array ConvertToTypedArray(object[] objectArray, Type targetType)
// {
//     var typedArray = Array.CreateInstance(targetType, objectArray.Length);
//     for (int i = 0; i < objectArray.Length; i++)
//     {
//         typedArray.SetValue(Convert.ChangeType(objectArray[i], targetType), i);
//     }
//     return typedArray;
// }

