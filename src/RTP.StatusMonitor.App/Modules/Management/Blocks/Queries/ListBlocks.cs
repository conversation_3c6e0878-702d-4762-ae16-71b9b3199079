using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Common;
using RTP.StatusMonitor.App.Modules.Management.Units.Common;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Blocks.Queries;

public record ListBlocksQuery(List<Guid> UserGroups) : IQuery<List<BlockResponse>>;

public class ListBlocksQueryHandler(DataContext context)
    : IQueryHandler<ListBlocksQuery, List<BlockResponse>>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<List<BlockResponse>>> Handle(ListBlocksQuery request, CancellationToken cancellationToken)
    {
        // get all group permissions matching the group ids from request
        List<GroupPermission> groupPermissions = await _context.GroupPermissions
            .AsNoTracking()
            .Where(gp => request.UserGroups.Contains(gp.GroupId))
            .Distinct()
            .Include(gp => gp.Site.Blocks)
            .ThenInclude(b => b.Units)
            .ToListAsync(cancellationToken);

        // get all blocks with units info from group permissions
        List<Domain.Blocks.Block> blocks = [.. groupPermissions.SelectMany(gp => gp.Site.Blocks)];

        // manually map blocks to BlockResponse
        return blocks.Select(block => new BlockResponse(
            Id: block.Id,
            Name: block.Name,
            Alias: block.Alias,
            IpAddress: block.IpAddress ?? string.Empty,
            Port: block.Port,
            SiteId: block.SiteId,
            DateCreated: block.DateCreated,
            DateModified: block.DateModified,
            Units: block.Units.Select(unit => new UnitResponse(
                Id: unit.Id,
                Name: unit.Name,
                Alias: unit.Alias,
                IpAddress: block.IpAddress ?? string.Empty,
                Port: block.Port,
                BlockId: unit.BlockId,
                SiteId: block.SiteId,
                DateTimeOffset: TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone).GetUtcOffset(DateTime.UtcNow),
                DateCreated: unit.DateCreated,
                DateModified: unit.DateModified
            )).ToList()
        )).ToList();
    }
}
