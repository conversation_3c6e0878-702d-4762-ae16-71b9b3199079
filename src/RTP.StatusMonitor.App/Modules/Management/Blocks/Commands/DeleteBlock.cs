using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;

public record DeleteBlockCommand(Guid BlockId) : ICommand;

public class DeleteBlockCommandHandler(DataContext context) : ICommandHandler<DeleteBlockCommand>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<Unit>> Handle(DeleteBlockCommand request, CancellationToken cancellationToken)
    {
        // get block
        Domain.Blocks.Block? block = await _context.Blocks
            .FirstOrDefaultAsync(
                b => b.Id == request.BlockId,
                cancellationToken);

        if (block == null)
            throw new Exception("Block not found");

        // remove block
        _context.Remove(block);

        // save changes to database (return the number of state entries written to the database)
        var success = await _context.SaveChangesAsync(cancellationToken) > 0;

        // if any rows were written to the database -> success
        if (success)
            return Unit.Value;

        throw new Exception("Problem saving changes");
    }
}
