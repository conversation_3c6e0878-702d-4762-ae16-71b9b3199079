using AutoMapper;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;

public class CreateBlockCommand : ICommand
{
    public string Name { get; set; } = null!;
    public string IpAddress { get; set; } = string.Empty;
    public int Port { get; set; }
    public Guid SiteId { get; set; }
}
public class CreateBlockCommandHandler(DataContext context, IMapper mapper) : ICommandHandler<CreateBlockCommand>
{
    private readonly DataContext _context = context;
    private readonly IMapper _mapper = mapper;

    public async Task<ErrorOr<Unit>> Handle(
        CreateBlockCommand request,
        CancellationToken ct)
    {
        // find the site to which the block will be added
        var site = await _context.Sites
            .Include(x => x.Blocks)
            .FirstOrDefaultAsync(
                x => x.Id == request.SiteId, ct);

        if (site is null)
        {
            return SiteDomainErrors.NotFound;
        }

        // make sure no duplicate block names
        bool isDuplicate = site.Blocks.Any(x => x.Name == request.Name);

        if (isDuplicate)
        {
            return BlockDomainErrors.DuplicateBlockName;
        }

        // map the request to block entity
        var newBlock = _mapper.Map<Block>(request);
        newBlock.Alias = $"{site.Name}-{newBlock.Name}"; // Bellingham-1

        // add new block to the database
        await _context.Blocks.AddAsync(newBlock, ct);

        // save changes to the database
        var success = await _context.SaveChangesAsync(ct) > 0;

        // if any changes were saved -> success
        if (success)
            return Unit.Value;

        // otherwise throw exception
        throw new Exception("Problem saving changes");
    }
}
