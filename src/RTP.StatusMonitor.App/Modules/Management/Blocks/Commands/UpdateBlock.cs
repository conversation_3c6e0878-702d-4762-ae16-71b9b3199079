using AutoMapper;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Blocks.Commands;

public class UpdateBlockCommand : ICommand
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? IpAddress { get; set; }
    public int? Port { get; set; }
}
// query handler
public class UpdateBlockCommandHandler(DataContext context, IMapper mapper) : ICommandHandler<UpdateBlockCommand>
{
    private readonly DataContext _context = context;
    private readonly IMapper _mapper = mapper;

    public async Task<ErrorOr<Unit>> Handle(
        UpdateBlockCommand request,
        CancellationToken cancellationToken)
    {
        // find the block to be updated
        Block? block = await _context.Blocks
            .Include(b => b.Site)
            .FirstOrDefaultAsync(
                b => b.Id == request.Id,
                cancellationToken);

        if (block is null)
        {
            return BlockDomainErrors.NotFound;
        }

        // map the request to block and update DateModified
        _mapper.Map(request, block);
        block.Alias = $"{block.Site.Name}-{block.Name}";

        // save changes to database (return the number of state entries written to the database)
        var success = await _context.SaveChangesAsync(cancellationToken) > 0;

        // if any rows were written to the database -> success
        if (success)
            return Unit.Value;

        throw new Exception("Problem saving changes");
    }
}
