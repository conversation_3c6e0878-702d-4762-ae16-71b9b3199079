using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

namespace RTP.StatusMonitor.App.Modules.Management.Sites.CreateSite;

internal sealed class CreateSiteCommandHandler
    : ICommandHandler<CreateSiteCommand, Unit>
{
    private readonly IDataContext _context;
    private readonly AccuWeatherApi _accuWeatherApi;

    public CreateSiteCommandHandler(
        IDataContext context,
        AccuWeatherApi accuWeatherApi)
    {
        _context = context;
        _accuWeatherApi = accuWeatherApi;
    }

    public async Task<ErrorOr<Unit>> Handle(
        CreateSiteCommand request, CancellationToken ct)
    {
        // Verify customer exists in database
        Customer? customer = await _context
            .Customers
            .Include(c => c.Sites)
            .FirstOrDefaultAsync(c => c.Id == Guid.Parse(request.CustomerId), ct);

        // If customer does not exist -> failure
        if (customer is null)
            return CustomerErrors.NotFound;

        // Find the location of the new site from the weather API using lat/long
        string? locationKey = await _accuWeatherApi.GetLocationKeyFromLatLng(
            request.Latitude,
            request.Longitude);

        // If having trouble getting location key -> failure
        if (locationKey is null)
            return SiteErrors.AwLocationKeyNotFound;

        // NOTE - Make sure RTP is allowed access to the site ALWAYS
        if (!request.UserGroupsAllowedAccess.Contains(UserGroupsConstants.RealTimePower))
        {
            request.UserGroupsAllowedAccess.Add(UserGroupsConstants.RealTimePower);
        }

        // Get the list of user groups allowed access to the site
        List<UserGroup> userGroupsAllowedAccess = await _context.UserGroups
            .Where(ug => request.UserGroupsAllowedAccess.Contains(ug.UserGroupId))
            .ToListAsync(ct);

        // Add new site to customer (this will also create the default weather settings)
        ErrorOr<Site> site = customer.AddSite(
            name: request.Name,
            alias: request.Alias,
            description: request.Description,
            location: request.Location,
            altitude: request.Altitude,
            isMetric: request.IsMetric,
            latitude: request.Latitude,
            longitude: request.Longitude,
            timeZone: request.TimeZone,
            locationKey: int.Parse(locationKey),
            userGroups: userGroupsAllowedAccess);

        // If creating the site fails -> failure
        if (site.IsError)
            return site.Errors;

        // Create default weather settings for the site
        SiteWeatherSettings siteWeatherSettings = new(
            id: Guid.NewGuid(),
            forecastSettings: new ForecastSettings(),
            lightningStrikeSettings: new LightningStrikeSettings(false, 0),
            weatherBiasSettings: new List<WeatherBiasSettings>(),
            biasTagSettings: new List<BiasTagSettings>(),
            site: site.Value);

        // Add the site and its weather settings to the context
        _context.Sites.Add(site.Value);
        _context.SiteWeatherSettings.Add(siteWeatherSettings);

        bool success = await _context.SaveChangesAsync(ct) > 0;

        // If saving to the database fails -> failure
        if (!success)
            return SiteErrors.UnexpectedError;

        return Unit.Value;
    }
}