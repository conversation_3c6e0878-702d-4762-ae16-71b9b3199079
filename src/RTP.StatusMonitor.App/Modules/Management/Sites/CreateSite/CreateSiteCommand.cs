using MediatR;
using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Management.Sites.CreateSite;

public record CreateSiteCommand(
  string Name,
  string Alias,
  string Description,
  string Location,
  double Altitude,
  bool IsMetric,
  double Latitude,
  double Longitude,
  string TimeZone,

  /// <summary>
  /// The list of user group ids that are allowed access to the site data
  /// </summary>
  List<Guid> UserGroupsAllowedAccess,
  string CustomerId) : ICommand<Unit>;