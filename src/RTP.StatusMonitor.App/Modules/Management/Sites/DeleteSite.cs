using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Sites;

public record DeleteSiteCommand(Guid SiteId) : ICommand;

public class DeleteSiteCommandHandler(DataContext context)
    : ICommandHandler<DeleteSiteCommand>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<Unit>> Handle(DeleteSiteCommand request, CancellationToken cancellationToken)
    {
        // get site
        Site? site = await _context.Sites
            .SingleOrDefaultAsync(s => s.Id == request.SiteId, cancellationToken);

        if (site is null)
            return SiteErrors.NotFound;

        _context.Sites.Remove(site);
        bool success = await _context.SaveChangesAsync(cancellationToken) > 0;

        // if no changes were made to database -> fail
        if (!success)
        {
            return Error.Failure("DeleteSite.Failed", "Fail to delete site");
        }

        return Unit.Value;
    }
}