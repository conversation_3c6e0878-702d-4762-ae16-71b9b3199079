using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.UserGroups;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Sites.UpdateSite;

internal sealed class UpdateSiteCommandHandler(
    IDataContext context,
    AccuWeatherApi weatherClient)
        : ICommandHandler<UpdateSiteCommand, Unit>
{
    private readonly IDataContext _context = context;
    private readonly AccuWeatherApi _weatherClient = weatherClient;

    public async Task<ErrorOr<Unit>> Handle(
        UpdateSiteCommand request,
        CancellationToken cancellationToken)
    {
        // Find the site to be updated
        Site? site = await _context.Sites
            .Include(s => s.Customer)
            .Include(s => s.GroupPermissions)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        // If site does not exist -> return error
        if (site is null)
            return SiteErrors.NotFound;

        // Check if lat/long changes and get the location key if they do
        if (request.Latitude != site.Latitude || request.Longitude != site.Longitude)
        {
            // Get the new location key
            string? locationKey = await _weatherClient
                .GetLocationKeyFromLatLng(request.Latitude, request.Longitude);

            // If location key is not found -> return error
            if (locationKey is null)
                return SiteErrors.AwLocationKeyNotFound;

            // Update the location key for the site 
            site.LocationKey = int.Parse(locationKey);
        }

        // Check for name conflicts
        bool nameConflict = await _context.Sites.AnyAsync(
            s => s.Id != request.Id && s.Name == request.Name, cancellationToken);
        if (nameConflict)
        {
            return SiteDomainErrors.NameConflict;
        }

        // NOTE - Make sure Real Time Power is always allowed access
        if (!request.UserGroupsAllowedAccess
            .Contains(UserGroupsConstants.RealTimePower))
        {
            request.UserGroupsAllowedAccess
                .Add(UserGroupsConstants.RealTimePower);
        }

        // Get the user groups allowed access
        List<UserGroup> userGroupsAllowedAccess = await _context.UserGroups
            .Where(ug => request.UserGroupsAllowedAccess.Contains(ug.UserGroupId))
            .ToListAsync(cancellationToken);

        // Update site entity
        ErrorOr<Updated> updatedResult = site.Update(
            request.Name,
            request.Alias,
            request.Description,
            request.Location,
            // request.MapLocation,
            request.Altitude,
            request.IsMetric,
            request.Latitude,
            request.Longitude,
            request.TimeZone,
            userGroupsAllowedAccess,
            request.CustomerId);

        if (updatedResult.IsError)
            return updatedResult.Errors;

        // Save updated site to database
        bool success = await _context.SaveChangesAsync(cancellationToken) > 0;

        // If no operations were written to the database -> failure
        if (!success)
            return SiteErrors.UnexpectedError;

        return Unit.Value;
    }
}
