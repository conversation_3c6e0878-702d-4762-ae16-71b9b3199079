using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Common;
using RTP.StatusMonitor.App.Modules.Management.Sites.Common;
using RTP.StatusMonitor.App.Modules.Management.Units.Common;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;
using TimeZoneConverter;

namespace RTP.StatusMonitor.App.Modules.Management.Sites;

public record GetSitesQuery(List<Guid> UserGroupsId) : IQuery<List<SiteResponse>>;

public class GetSitesQueryHandler(IDataContext context)
    : IQueryHandler<GetSitesQuery, List<SiteResponse>>
{
    private readonly IDataContext _context = context;

    public async Task<ErrorOr<List<SiteResponse>>> Handle(
    GetSitesQuery request,
    CancellationToken ct)
    {
        // Get all sites info that the user has access to
        List<Site> sites = await _context.Sites
            .AsNoTracking()
            .AsSplitQuery()
            .Include(s => s.GroupPermissions)
            .Where(s => s.GroupPermissions.Any(
              gp => request.UserGroupsId.Contains(gp.GroupId)))
            .Include(s => s.Blocks.OrderBy(b => b.Name))
              .ThenInclude(b => b.Units.OrderBy(u => u.Name))
            .Include(s => s.Customer)
            .OrderBy(s => s.Name)
            .ToListAsync(ct);

        // Map to SiteResult
        return sites.Select(site => new SiteResponse(
          site.Id,
          site.Name,
          site.Alias,
          site.Description,
          site.Location,
          site.LocationKey,
          site.Altitude,
          site.IsMetric,
          site.Latitude,
          site.Longitude,
          site.TimeZone,
          TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone).GetUtcOffset(DateTime.UtcNow),
          TZConvert.WindowsToIana(site.TimeZone),
          site.DateCreated,
          site.DateModified,
          site.Customer.Name,
          site.GroupPermissions
            .Select(gp => gp.GroupId)
            .Distinct()
            .ToList(),
          site.Blocks.Select(block => new BlockResponse(
            block.Id,
            block.Name,
            block.Alias,
            block.IpAddress ?? string.Empty,
            block.Port,
            block.SiteId,
            block.DateCreated,
            block.DateModified,
            block.Units.Select(unit => new UnitResponse(
              unit.Id,
              unit.Name,
              unit.Alias,
              block.IpAddress ?? string.Empty,
              block.Port,
              unit.Block.SiteId,
              unit.BlockId,
              TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone).GetUtcOffset(DateTime.UtcNow),
              unit.DateCreated,
              unit.DateModified
            )).ToList()
          )).ToList()
        )).ToList();
    }
}
