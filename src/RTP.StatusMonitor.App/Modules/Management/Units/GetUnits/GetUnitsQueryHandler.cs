using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Management.Units.Common;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Units.GetUnits;
internal sealed class GetUnitsQueryHandler(DataContext context)
    : IQueryHandler<GetUnitsQuery, List<UnitResponse>>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<List<UnitResponse>>> Handle(
        GetUnitsQuery request,
        CancellationToken ct = default)
    {
        // Get all groups user has access to
        List<GroupPermission> groupPermissions = await _context.GroupPermissions
          .AsNoTracking()
          .Where(gp => request.UserGroupsId.Contains(gp.GroupId))
          .Distinct()
          .Include(gp => gp.Site.Blocks)
          .ThenInclude(b => b.Units)
          .ToListAsync(ct);

        return groupPermissions
            .SelectMany(gp => gp.Site.Blocks) // get blocks with permissions
            .SelectMany(b => b.Units // get units of those blocks
            .Select(
                u => new
                {
                    Unit = u,
                    Block = b
                }))
            .Select(ub => new UnitResponse
            (
                Id: ub.Unit.Id,
                Name: ub.Unit.Name,
                Alias: ub.Unit.Alias,
                IpAddress: ub.Block.IpAddress ?? string.Empty,
                Port: ub.Block.Port,
                BlockId: ub.Block.Id,
                SiteId: ub.Block.SiteId,
                DateTimeOffset: TimeZoneInfo
                .FindSystemTimeZoneById(ub.Block.Site.TimeZone)
                .GetUtcOffset(DateTime.UtcNow),
                DateCreated: ub.Unit.DateCreated,
                DateModified: ub.Unit.DateModified))
            .ToList();
    }
}

