using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Units;

public record CreateUnitCommand(string Name, Guid BlockId) : ICommand;

public class CreateUnitCommandHandler(DataContext context) : ICommandHandler<CreateUnitCommand>
{
    private readonly DataContext _context = context;
    public async Task<ErrorOr<MediatR.Unit>> Handle(CreateUnitCommand request, CancellationToken cancellationToken)
    {
        // find the block to which the unit will be added
        Block? block = await _context.Blocks
            .Include(b => b.Site)
            .Include(b => b.Units)
            .SingleOrDefaultAsync(b => b.Id == request.BlockId, cancellationToken);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // make sure no duplicate unit names
        bool isDuplicate = block.Units.Any(u => u.Name == request.Name);
        if (isDuplicate)
        {
            return UnitErrors.DuplicateName;
        }

        // Create new unit
        Unit newUnit = new()
        {
            Name = request.Name,
            BlockId = request.BlockId,
            Block = block,
            Alias = $"{block.Site.Name}-{block.Name}-{request.Name}"
        };

        // add the new unit to database
        await _context.Units.AddAsync(newUnit, cancellationToken);

        // save changes
        bool success = await _context.SaveChangesAsync(cancellationToken) > 0;

        if (!success)
        {
            return Error.Failure("CreateUnit.Failed", "Failed to create unit");
        }

        return MediatR.Unit.Value;
    }
}