using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Management.Units.Commands;

public record DeleteUnitCommand(Guid Id) : ICommand;

public class DeleteUnitCommandHandler(DataContext context)
    : ICommandHandler<DeleteUnitCommand>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        DeleteUnitCommand request,
        CancellationToken ct)
    {
        Unit? unit = await _context.Units
            .SingleOrDefaultAsync(u => u.Id == request.Id, ct);

        if (unit is null)
            return UnitErrors.NotFound;

        _context.Units.Remove(unit);
        bool success = await _context.SaveChangesAsync(ct) > 0;

        if (!success)
        {
            return Error.Failure("DeleteUnit.Failed", "Failed to delete unit");
        }

        return MediatR.Unit.Value;
    }
}