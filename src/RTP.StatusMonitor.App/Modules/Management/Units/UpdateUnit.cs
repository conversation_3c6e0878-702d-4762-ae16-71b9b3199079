using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Units;

public record UpdateUnitCommand(Guid UnitId, string Name, Guid? BlockId) : ICommand;

public class UpdateUnitCommandHandler(DataContext context) : ICommandHandler<UpdateUnitCommand>
{
    private readonly DataContext _context = context;
    public async Task<ErrorOr<MediatR.Unit>> Handle(UpdateUnitCommand request, CancellationToken cancellationToken)
    {
        // find the unit to be updated
        Unit? unit = await _context.Units
            .Include(u => u.Block)
            .SingleOrDefaultAsync(u => u.Id == request.UnitId, cancellationToken);

        if (unit is null)
            return UnitErrors.NotFound;

        // find the block to which the unit will be added
        Block? blockToAdd = await _context.Blocks
            .Include(b => b.Site)
            .Include(b => b.Units)
            .SingleOrDefaultAsync(b => b.Id == request.BlockId, cancellationToken);

        if (blockToAdd is null)
            return BlockDomainErrors.NotFound;

        unit.Name = request.Name;
        unit.Block = blockToAdd;
        unit.BlockId = request.BlockId ?? unit.BlockId;

        // update alias with format site-block-unit -> "Bellingham-1-1"
        unit.Alias = $"{blockToAdd.Site.Name}-{blockToAdd.Name}-{unit.Name}";

        // save changes
        bool success = await _context.SaveChangesAsync(cancellationToken) > 0;

        if (!success)
        {
            return Error.Failure("UpdateUnit.Failed", "Failed to update unit");
        }

        return MediatR.Unit.Value;
    }
}
