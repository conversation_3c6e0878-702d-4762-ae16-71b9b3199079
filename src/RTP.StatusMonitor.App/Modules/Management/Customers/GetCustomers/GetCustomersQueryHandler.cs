using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Management.Blocks.Common;
using RTP.StatusMonitor.App.Modules.Management.Customers.Common;
using RTP.StatusMonitor.App.Modules.Management.Sites.Common;
using RTP.StatusMonitor.App.Modules.Management.Units.Common;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Persistence;
using TimeZoneConverter;

namespace RTP.StatusMonitor.App.Modules.Management.Customers.GetCustomers;

internal sealed class GetCustomersQueryHandler
    : IQueryHandler<GetCustomersQuery, List<CustomerResponse>>
{
    private readonly DataContext _context;

    public GetCustomersQueryHandler(DataContext context) => _context = context;

    public async Task<ErrorOr<List<CustomerResponse>>> Handle(
        GetCustomersQuery request,
        CancellationToken ct = default
    )
    {
        List<Customer> customers = await _context
            .Customers.Include(c => c.Sites)
            .ThenInclude(s => s.Blocks)
            .ThenInclude(b => b.Units)
            .Include(c => c.Sites)
            .ThenInclude(s => s.GroupPermissions)
            .ToListAsync(ct);

        return customers
            .OrderBy(c => c.Name)
            .Select(c => new CustomerResponse(
                c.Id,
                c.Name,
                c.Title,
                c.CompanyLogoUrl,
                c.BlobContainer,
                c.DateCreated,
                c.DateModified,
                c.Sites.OrderBy(site => site.Name)
                    .Select(site => new SiteResponse(
                        site.Id,
                        site.Name,
                        site.Alias,
                        site.Description,
                        site.Location,
                        site.LocationKey,
                        // site.MapLocation,
                        site.Altitude,
                        site.IsMetric,
                        site.Latitude,
                        site.Longitude,
                        site.TimeZone,
                        TimeZoneInfo
                            .FindSystemTimeZoneById(site.TimeZone)
                            .GetUtcOffset(DateTime.UtcNow),
                        TZConvert.WindowsToIana(site.TimeZone),
                        site.DateCreated,
                        site.DateModified,
                        site.Customer.Name,
                        [.. site.GroupPermissions.Select(gp => gp.GroupId).Distinct()],
                        site.Blocks.OrderBy(block => block.Name)
                            .Select(block => new BlockResponse(
                                block.Id,
                                block.Name,
                                block.Alias,
                                block.IpAddress ?? string.Empty,
                                block.Port,
                                block.SiteId,
                                block.DateCreated,
                                block.DateModified,
                                block
                                    .Units.OrderBy(unit => unit.Name)
                                    .Select(unit => new UnitResponse(
                                        unit.Id,
                                        unit.Name,
                                        unit.Alias,
                                        block.IpAddress ?? string.Empty,
                                        block.Port,
                                        unit.Block.SiteId,
                                        unit.BlockId,
                                        TimeZoneInfo
                                            .FindSystemTimeZoneById(site.TimeZone)
                                            .GetUtcOffset(DateTime.UtcNow),
                                        unit.DateCreated,
                                        unit.DateModified
                                    ))
                                    .ToList()
                            ))
                            .ToList()
                    ))
                    .ToList()
            ))
            .ToList();
    }
}
