using AutoMapper;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Customers.Commands;

public class UpdateCustomerCommand : ICommand
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string CompanyLogoUrl { get; set; } = string.Empty;
    public string BlobContainer { get; set; } = string.Empty;
}

public class UpdateCustomerCommandHandler(DataContext context, IMapper mapper)
    : ICommandHandler<UpdateCustomerCommand>
{
    private readonly DataContext _context = context;
    private readonly IMapper _mapper = mapper;

    public async Task<ErrorOr<Unit>> Handle(
        UpdateCustomerCommand request,
        CancellationToken ct)
    {
        Customer? customer = await _context.Customers
            .FirstOrDefaultAsync(
                c => c.Id == request.Id,
                ct);
        if (customer == null)
        {
            return CustomerErrors.NotFound;
        }

        // map the request to customer and update DateModified
        _mapper.Map(request, customer);
        customer.DateModified = DateTime.Now;

        // save changes to database (return the number of state entries written to the database)
        bool success = await _context.SaveChangesAsync(ct) > 0;

        // if any rows were written to the database -> success
        if (success)
            return Unit.Value;

        throw new Exception("Problem saving changes");
    }
}
