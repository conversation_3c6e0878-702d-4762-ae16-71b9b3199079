using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Customers.Commands;

public record DeleteCustomerCommand(Guid CustomerId) : ICommand;

public class DeleteCustomerCommandHandler(DataContext context)
    : ICommandHandler<DeleteCustomerCommand>
{
    private readonly DataContext _context = context;

    public async Task<ErrorOr<Unit>> Handle(
        DeleteCustomerCommand request,
        CancellationToken ct)
    {
        Customer? customer = await _context.Customers
            .FirstOrDefaultAsync(c => c.Id == request.CustomerId, ct);

        if (customer == null)
        {
            return CustomerErrors.NotFound;
        }

        // delete customer
        _context.Customers.Remove(customer);

        // save changes to database (return the number of state entries written to the database)
        var success = await _context.SaveChangesAsync(ct) > 0;

        // if any rows were written to the database -> success
        if (success)
            return Unit.Value;

        throw new Exception("Problem saving changes");
    }
}
