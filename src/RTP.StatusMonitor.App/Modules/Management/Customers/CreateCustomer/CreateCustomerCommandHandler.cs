using ErrorOr;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Customer;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Customers.CreateCustomer;

internal sealed class CreateCustomerCommandHandler(IDataContext context)
    : ICommandHandler<CreateCustomerCommand>
{
    private readonly IDataContext _context = context;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
      CreateCustomerCommand request,
      CancellationToken ct)
    {
        // Check if the customer already exist
        if (_context.Customers.Any(c => c.Name == request.Name))
        {
            return CustomerErrors.AlreadyExists;
        }

        // Create the customer
        ErrorOr<Customer> customer = Customer.Create(
          id: Guid.NewGuid(),
          name: request.Name,
          title: request.Title,
          companyLogoUrl: request.CompanyLogoUrl,
          blobContainer: request.BlobContainer);

        if (customer.IsError)
        {
            return customer.Errors;
        }

        // Add the customer to the context
        _context.Customers.Add(customer.Value);

        await _context.SaveChangesAsync(ct);

        return MediatR.Unit.Value;
    }
}
