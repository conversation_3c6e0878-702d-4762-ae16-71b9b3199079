using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.BlobStorage;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Management.Documentation;

public class GetDocumentationFilesQueryHandler
    : IQueryHandler<GetDocumentationFilesQuery, List<DocumentationFileResponse>>
{
    private readonly DataContext _context;
    private readonly IBlobStorageService _blobStorageService;

    public GetDocumentationFilesQueryHandler(
        DataContext context,
        IBlobStorageService storageClient)
    {
        _context = context;
        _blobStorageService = storageClient;
    }

    public async Task<ErrorOr<List<DocumentationFileResponse>>> Handle(
        GetDocumentationFilesQuery request,
        CancellationToken ct)
    {
        // Query customer and site name for all site user has access to
        // customer name = container name
        // site name = folder name
        var sites = await _context.Sites
            .AsNoTracking()
            .Where(s => s.GroupPermissions
                .Any(gp => request.UserGroupsId.Contains(gp.GroupId)))
            .Select(s => new
            {
                CustomerName = s.Customer.Name,
                SiteName = s.Name,
            })
            .ToListAsync(ct);

        // Get all sas uri for the according folders for each site
        List<DocumentationFileResponse> results = new();

        Parallel.ForEach(sites, site =>
        {
            string container = site.CustomerName.ToLower();
            string directoryPath = $"{site.SiteName.ToLower().Replace(" ", string.Empty)}/docs";

            // get all files under the docs folder
            List<string> files = _blobStorageService.GetFilesAsync(
                container,
                directoryPath,
                ct).Result;

            // in case no files found, continue to next site
            if (files is null || files.Count == 0)
                return;

            // Get sas uri for the docs folder for each site
            Uri? sasUri = _blobStorageService.GetServiceSasUri(
                container, directoryPath, null, ct);

            // Failure to generate sas uri, continue to next site
            if (sasUri is null)
                return;

            // add the site data to list
            results.Add(new DocumentationFileResponse
            (
                Customer: site.CustomerName,
                Site: site.SiteName,
                Scheme: sasUri.Scheme,
                Authority: sasUri.Authority,
                SasToken: sasUri.Query,
                Files: files
            ));
        });

        return results;
    }
}
