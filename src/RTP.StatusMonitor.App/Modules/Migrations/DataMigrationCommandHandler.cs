using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Migrations;

internal sealed class DataMigrationCommandHandler
    : ICommandHandler<DataMigrationCommand>
{
    private readonly IDataContext _dataContext;
    private readonly DataClientReadRepository _dataClientReadRepository;
    private readonly IEventBus _eventBus;
    public DataMigrationCommandHandler(
        IDataContext dataContext,
        IEventBus eventBus,
        DataClientReadRepository dataClientReadRepository)
    {
        _dataContext = dataContext;
        _eventBus = eventBus;
        _dataClientReadRepository = dataClientReadRepository;
    }

    public async Task<ErrorOr<Unit>> Handle(
        DataMigrationCommand request,
        CancellationToken ct)
    {
        // Get all blocks data
        List<Block> blocks = await _dataContext.Blocks
            .Include(b => b.Site)
                .Include(b => b.Site.Customer)
            .ToListAsync(ct);

        // 

        throw new NotImplementedException();
    }
}