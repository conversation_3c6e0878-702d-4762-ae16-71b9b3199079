using System.Data;
using System.Net.Mail;
using System.Text;
using ErrorOr;
using HandlebarsDotNet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Emails;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.EquipmentContent.Events;
using RTP.StatusMonitor.Domain.IntegrationEventLogEntry;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.UpdateEquipment;

public sealed class UpdateEquipmentCommandHandler(
    IOptions<AzureServiceBusOptions> serviceBusOptions,
    IDataContext dataContext,
    IDateTimeProvider dateTimeProvider,
    IEventBus eventBus,
    EmailService emailService,
    ILogger<UpdateEquipmentCommandHandler> logger) : ICommandHandler<UpdateEquipmentCommand>
{
    private readonly AzureServiceBusOptions _serviceBusOptions = serviceBusOptions.Value;
    private readonly IDataContext _dataContext = dataContext;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IEventBus _eventBus = eventBus;
    private readonly EmailService _emailService = emailService;
    private readonly ILogger<UpdateEquipmentCommandHandler> _logger = logger;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        UpdateEquipmentCommand request,
        CancellationToken ct = default)
    {
        // Find the site to update
        Site? site = await _dataContext.Sites
            .Where(s => s.Id == request.ResourceId.Value)
            .FirstOrDefaultAsync(ct);

        if (site is null)
            return SiteDomainErrors.NotFound;

        // Then query all the equipment data of each block in the site
        List<Block> blocks = await _dataContext.Blocks
            .Include(b => b.EquipmentContents)
            .Include(b => b.Site)
            .Where(b => b.SiteId == request.ResourceId.Value)
            .ToListAsync(ct);

        // Compose the message containing the data to update
        long unixTimeInSeconds = new DateTimeOffset(
            _dateTimeProvider.UtcNow,
            TimeSpan.Zero).ToUnixTimeSeconds();

        List<EquipmentContent> equipmentContents = blocks
            .SelectMany(b => b.EquipmentContents)
            .ToList();

        // Dictionary of block id to its updated variables
        Dictionary<Guid, List<UpdateVariable>> blockUpdates = [];

        // Find all the equipment contents to update
        // and get its tag to send the new value
        List<EquipmentContent> updatedEquipmentContents = [];
        foreach (EquipmentToUpdate equipmentToUpdate in request.UpdatedEquipment.EquipmentsToUpdate)
        {
            // Find the equipment content to update by using the id
            EquipmentContent? contentToUpdate = equipmentContents
                .Where(e => e.Id == equipmentToUpdate.Id)
                .FirstOrDefault();

            // If the equipment is not found, continue to the next equipment
            if (contentToUpdate is null)
                continue;

            // Otherwise, create a new variable to update
            UpdateVariable variableToUpdate = new(
                Name: contentToUpdate.Tag,
                Value: equipmentToUpdate.Value,
                Timestamp: unixTimeInSeconds);

            // Add the variable to update to the dictionary of block id to its updated variables
            blockUpdates.AddOrUpdate(contentToUpdate.BlockId, variableToUpdate);

            // Also update the value of the equipment content in the database for reference of last updated value
            contentToUpdate.UpdateEquipmentValue(equipmentToUpdate.Value);

            updatedEquipmentContents.Add(contentToUpdate);
        }

        // Create the list of forecast updates
        List<ForecastUpdate> forecastUpdates = new();
        foreach (var blockUpdate in blockUpdates)
        {
            // Use the block id to get the block name
            string? blockName = blocks
                .Where(block => block.Id == blockUpdate.Key)
                .Select(block => block.Name)
                .FirstOrDefault();

            if (blockName is null)
                continue;

            forecastUpdates.Add(
                new ForecastUpdate(Block: blockName, Data: blockUpdate.Value));
        }

        // Check the request requires sending email
        UpdateAction forecastAction = request.UpdatedEquipment.SendEmail
            ? UpdateAction.UpdateAndSend
            : UpdateAction.UpdateOnly;

        // Find the site forecast alert needed to be sent out when forecast is updated
        Guid? siteForecastAlertId = await _dataContext.Alerts
            .Where(alert =>
                alert.IsEnabled == true &&
                alert.AlertType == AlertType.Forecast &&
                alert.NotificationType == NotificationType.Email &&
                alert.SiteId == site.Id)
            .Select(alert => alert.Id)
            .FirstOrDefaultAsync(ct);

        // Create the forecast update event
        ForecastUpdateEvent forecastUpdateEvent = new(
            updates: forecastUpdates,
            action: forecastAction,
            isTest: request.UpdatedEquipment.SendEmail
                && request.UpdatedEquipment.EmailTarget is not null
                && Enum.Parse<EmailTarget>(request.UpdatedEquipment.EmailTarget) == EmailTarget.Internal,
            createdBy: request.CreatedBy,
            site: site.Name,
            note: request.UpdatedEquipment.Note,
            alertId: siteForecastAlertId);

        // Create an event log entry
        IntegrationEventLogEntry eventLogEntry = new(
            eventItem: forecastUpdateEvent, state: "Pending");

        try
        {
            // Create a transaction to save the event log entry to the database and publish the event to Service Bus
            using IDbContextTransaction transaction = await _dataContext
                .Database
                .BeginTransactionAsync(ct);

            // Save the event log entry to the database
            await _dataContext
                .IntegrationEventLogEntries
                .AddAsync(eventLogEntry, ct);

            _dataContext.EquipmentContents.UpdateRange(updatedEquipmentContents);

            // If there is no scheduled update time, publish the event immediately
            if (request.UpdatedEquipment.ScheduledUpdateTime is null)
            {
                await _eventBus.PublishAsync(
                    forecastUpdateEvent, _serviceBusOptions.ForecastUpdatesTopic, ct);
            }
            // Otherwise, schedule the event to be published at the scheduled update time
            else
            {
                // Convert the scheduled update time to the site's time zone
                DateTimeWithZone siteScheduledUpdateTime = new(
                    dateTime: DateTime.Parse(
                        request.UpdatedEquipment.ScheduledUpdateTime),
                    timeZoneInfo: TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone));

                // If the schedule time already past, return an error
                if (siteScheduledUpdateTime.UtcDateTime < _dateTimeProvider.UtcNow)
                {
                    return Error.Conflict(
                        "EquipmentScheduleUpdate.ScheduledUpdateTimePast",
                        "The scheduled time already past. Please schedule a future time or send update immediately.");
                }

                await _eventBus.ScheduleMessageAsync(
                    message: forecastUpdateEvent,
                    queueOrTopicName: _serviceBusOptions.ForecastUpdatesTopic,
                    scheduledEnqueueTimeUtc: siteScheduledUpdateTime.UtcDateTime,
                    ct: ct);
            }

            // Generate the email body for the updated forecast
            string emailBody = GenerateEmailBody(
                forecastUpdateEvent,
                request.UpdatedEquipment.ScheduledUpdateTime is not null
                    ? DateTime.Parse(request.UpdatedEquipment.ScheduledUpdateTime)
                    : _dateTimeProvider.UtcNow,
                request.UpdatedEquipment.ScheduledUpdateTime is not null);

            // Send email to RTP and the actor about any changes to the forecast
            List<string> recipients = Environment
                .GetEnvironmentVariable("AppEnvironment") == "Development" ?
                [request.ActorEmail] :
                [request.ActorEmail, "<EMAIL>"];
            _emailService.SendEmail(
                recipients: recipients,
                subject: request.UpdatedEquipment.ScheduledUpdateTime is null
                    ? $"Forecast Update Alert"
                    : $"Forecast Schedule Alert",
                body: emailBody,
                isBodyHtml: true,
                attachments: []);

            // Wait for 5 seconds to allow message to be published
            await Task.Delay(5000, ct);

            await _dataContext.SaveChangesAsync(ct);

            await transaction.CommitAsync(ct);

            return MediatR.Unit.Value;
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError(ex, "Error while updating forecast");

            return EquipmentContentErrors.UpdateForecastFailed;
        }
    }

    /// <summary>
    /// Generate the email body (using HTML format) for the updated forecast
    /// </summary>
    /// <param name="evt">The forecast update event</param>
    /// <param name="updateTime">The time of the update</param>
    /// <param name="isScheduled">Whether the update is scheduled</param>
    /// <returns>The email body</returns>
    private static string GenerateEmailBody(
        ForecastUpdateEvent evt,
        DateTime updateTime,
        bool isScheduled)
    {
        var sb = new StringBuilder();
        sb.Append(@"
      <html>
      <head>
          <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              h1 { color: #0056b3; }
              h2 { color: #007bff; }
              table { border-collapse: collapse; width: 100%; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              .note { background-color: #ffffd9; border: 1px solid #e6e6b8; padding: 10px; margin-top: 20px; }
          </style>
      </head>
      <body>");

        if (isScheduled)
        {
            sb.Append(@"<h1>Forecast Schedule Notification</h1>");

            sb.Append("A forecast update has been scheduled for the following site:");
        }
        else
        {
            sb.Append(@"<h1>Forecast Update Notification</h1>");

            sb.Append("A forecast update has been made for the following site:");
        }

        sb.Append(@"</p>
          <h2>").Append(evt.Site).Append(@"</h2>

          <p><strong>Updated by:</strong> ").Append(evt.CreatedBy).Append(@"</p>");

        if (isScheduled)
        {
            sb.Append(@"<p><strong>Scheduled update time:</strong> ").Append(updateTime.ToString("yyyy-MM-dd HH:mm:ss UTC")).Append(@"</p>");
        }
        else
        {
            sb.Append(@"<p><strong>Update time:</strong> ").Append(updateTime.ToString("yyyy-MM-dd HH:mm:ss UTC")).Append(@"</p>");
        }

        sb.Append(@"<p><strong>Action:</strong> ").Append(evt.Action).Append(@"</p>

          <h3>").Append(isScheduled ? "Scheduled Forecast Updates:" : "Updated Forecasts:").Append(@"</h3>
          <table>
              <tr><th>Block</th><th>Variable</th><th>New Value</th></tr>");

        foreach (var update in evt.Updates)
        {
            foreach (var data in update.Data)
            {
                sb.Append("<tr>");
                sb.Append("<td>").Append(update.Block).Append("</td>");
                sb.Append("<td>").Append(data.Name).Append("</td>");
                // NOTE - If value is 0 or 1, display a button
                sb.Append("<td>").Append(data.Value == 0 || data.Value == 1
                    ? data.Value == 0 ?
                        "<button style='background-color: red; color: white; border: none; padding: 5px 10px; border-radius: 5px;'><strong>OFF</strong></button>"
                        : "<button style='background-color: green; color: white; border: none; padding: 5px 10px; border-radius: 5px;'><strong>ON</strong></button>"
                    : data.Value).Append("</td>");
                sb.Append("</tr>");
            }
        }

        sb.Append(@"</table>");

        if (!string.IsNullOrEmpty(evt.Note))
        {
            sb.Append(@"<div class='note'><strong>Note:</strong> ").Append(evt.Note).Append(@"</div>");
        }

        sb.Append(@"</p>
      </body>
      </html>");

        return sb.ToString();
    }
}
