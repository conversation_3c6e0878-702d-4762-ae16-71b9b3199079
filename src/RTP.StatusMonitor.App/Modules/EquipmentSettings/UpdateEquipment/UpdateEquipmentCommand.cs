using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.UpdateEquipment;

public enum EmailTarget { All, Internal }

public record UpdateEquipmentCommand(
    UpdatedEquipment UpdatedEquipment,
    string ActorEmail,
    string CreatedBy)
    : ICommand, IAuthorizedGroupCommand
{
    public List<Guid> UserGroupsId { get; init; } = new();
    public ResourceId ResourceId { get; init; } = null!;
}

// NOTE - id of the equipment content
public record EquipmentToUpdate(Guid Id, double Value);

public record UpdatedEquipment(
    string Note,
    List<EquipmentToUpdate> EquipmentsToUpdate,
    bool SendEmail,
    string? EmailTarget,
    string? ScheduledUpdateTime);
