namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.UpdateEquipment;

public static class ImageUtilities
{
    public static string ConvertToBase64(string imagePath)
    {
        byte[] imageBytes = File.ReadAllBytes(imagePath);
        return Convert.ToBase64String(imageBytes);
    }

    public static string GetImageMimeType(string imagePath)
    {
        string extension = Path.GetExtension(imagePath).ToLower();
        return extension switch
        {
            ".png" => "image/png",
            ".jpg" => "image/jpeg",
            ".jpeg" => "image/jpeg",
            ".gif" => "image/gif",
            _ => "application/octet-stream"
        };
    }

    public static string CreateBase64Image(string imagePath)
    {
        string base64 = ConvertToBase64(imagePath);
        string mimeType = GetImageMimeType(imagePath);
        return $"data:{mimeType};base64,{base64}";
    }
}