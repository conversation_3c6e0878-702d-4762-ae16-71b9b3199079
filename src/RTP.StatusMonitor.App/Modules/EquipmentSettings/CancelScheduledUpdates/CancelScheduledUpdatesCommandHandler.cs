using ErrorOr;
using MediatR;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.CancelScheduledUpdates;

internal sealed class CancelScheduledUpdatesCommandHandler
    : ICommandHandler<CancelScheduledUpdatesCommand>
{
    private readonly IEventBus _eventBus;
    private readonly AzureServiceBusOptions _serviceBusOptions;

    public CancelScheduledUpdatesCommandHandler(
        IEventBus eventBus,
        IOptions<AzureServiceBusOptions> serviceBusOptions)
    {
        _eventBus = eventBus;
        _serviceBusOptions = serviceBusOptions.Value;
    }

    public async Task<ErrorOr<Unit>> Handle(
        CancelScheduledUpdatesCommand request,
        CancellationToken ct)
    {
        await _eventBus.CancelScheduledMessages(
            queueOrTopicName: _serviceBusOptions.ForecastUpdatesTopic,
            sequenceNumber: request.SequenceNumber,
            ct);

        return Unit.Value;
    }
}
