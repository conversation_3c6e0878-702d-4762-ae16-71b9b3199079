using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.CancelScheduledUpdates;

public record CancelScheduledUpdatesCommand(long SequenceNumber)
    : ICommand, IAuthorizedGroupCommand
{
    public List<Guid> UserGroupsId { get; init; } = [];
    public ResourceId ResourceId { get; init; } = null!;
}
