using Azure.Messaging.ServiceBus;
using ErrorOr;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Site;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.ViewScheduledUpdates;

internal sealed class ViewScheduledUpdatesQueryHandler
    : IQueryHandler<ViewScheduledUpdatesQuery, List<ScheduledUpdateResponse>>
{
    private readonly ISiteRepository _siteRepository;
    private readonly IEventBus _eventBus;
    private readonly AzureServiceBusOptions _serviceBusOptions;

    public ViewScheduledUpdatesQueryHandler(
        IEventBus eventBus,
        IOptions<AzureServiceBusOptions> serviceBusOptions,
        ISiteRepository siteRepository)
    {
        _eventBus = eventBus;
        _serviceBusOptions = serviceBusOptions.Value;
        _siteRepository = siteRepository;
    }

    public async Task<ErrorOr<List<ScheduledUpdateResponse>>> Handle(
        ViewScheduledUpdatesQuery request,
        CancellationToken ct)
    {
        // Find the site by id
        Site? site = await _siteRepository
            .GetSiteByIdAsync(request.ResourceId.Value, ct);

        // If the site is not found, return NotFound error
        if (site is null)
            return SiteDomainErrors.NotFound;

        // Otherwise get all scheduled updates for the site
        List<ServiceBusReceivedMessage> messages = await _eventBus
            .PeekMessagesAsync(
            queueOrTopicName: _serviceBusOptions.ForecastUpdatesTopic,
            ct: ct);

        return messages
            .Where(m => m.State == ServiceBusMessageState.Scheduled)
            .Select(m => new ScheduledUpdateResponse(
                MessageId: m.MessageId,
                SequenceNumber: m.SequenceNumber,
                Content: JsonConvert
                    .DeserializeObject<ScheduledUpdateEventContent>(m.Body.ToString()) ?? throw new InvalidOperationException(),
                ScheduledTimeUtc: m.ScheduledEnqueueTime))
            .Where(m => m.Content.Site == site.Name)
            .ToList();
    }
}