using RTP.StatusMonitor.Domain.EquipmentContent.Events;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.ViewScheduledUpdates;

public record ScheduledUpdateResponse(
    string MessageId,
    long SequenceNumber,
    ScheduledUpdateEventContent Content,
    DateTimeOffset ScheduledTimeUtc);

public record ScheduledUpdateEventContent(
    string Site,
    List<ForecastUpdate> Updates,
    UpdateAction Action,
    bool IsTest,
    string Note,
    string CreatedBy,
    long Timestamp);
