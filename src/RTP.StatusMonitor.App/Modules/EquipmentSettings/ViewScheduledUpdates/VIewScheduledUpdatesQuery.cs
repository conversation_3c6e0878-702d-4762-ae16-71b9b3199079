using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.ViewScheduledUpdates;

public record ViewScheduledUpdatesQuery
    : IQuery<List<ScheduledUpdateResponse>>, IAuthorizedGroupQuery
{
    public List<Guid> UserGroupsId { get; init; } = new List<Guid>();
    public ResourceId ResourceId { get; init; } = null!;
}
