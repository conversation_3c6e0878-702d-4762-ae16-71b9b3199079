namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentSettings;

public record EquipmentGroupResponse(
  Guid Id,
  string Name,
  double Width,
  int Position,
  string Notes,
  DateTime? DateModified,
  string? ModifiedBy,
  List<EquipmentSectionResponse> EquipmentSections
);

public record EquipmentSectionResponse(
  Guid id,
  Guid GroupId,
  string Name,
  string SectionType,
  int Position,
  List<EquipmentContentResponse> EquipmentContents
);

public record EquipmentContentResponse(
  Guid Id,
  Guid SectionId,
  Guid GroupId,
  Guid BlockId,
  string Alias,
  string Type,
  string Format,
  int Row,
  int Column,
  double? Max,
  double? Min,
  double? Interval,
  string Tag,
  double Value,
  string Suffix,
  DateTime? DateModified,
  string? ModifiedBy);


