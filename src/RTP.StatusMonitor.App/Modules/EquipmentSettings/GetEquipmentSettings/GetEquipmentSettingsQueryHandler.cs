using Azure.Data.Tables;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DataClient.Shared;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentSettings;

internal sealed class GetEquipmentSettingsQueryHandler
    : IQueryHandler<GetEquipmentSettingsQuery, List<EquipmentGroupResponse>>
{
    private readonly IDataContext _dataContext;
    private readonly IAuthorizationService _authorizationService;
    private readonly IDataClientTableQueryBuilder _queryBuilder;
    private readonly ITableStorage _tableStorage;

    public GetEquipmentSettingsQueryHandler(
        IDataContext dataContext,
        IAuthorizationService authorizationService,
        IDataClientTableQueryBuilder queryBuilder,
        ITableStorage tableStorage)
    {
        _dataContext = dataContext;
        _authorizationService = authorizationService;
        _queryBuilder = queryBuilder;
        _tableStorage = tableStorage;
    }

    public async Task<ErrorOr<List<EquipmentGroupResponse>>> Handle(
        GetEquipmentSettingsQuery request,
        CancellationToken ct = default)
    {
        // First we query the site and its related equipment groups
        Site? site = await _dataContext.Sites
            .AsNoTracking()
            .Where(s => s.Id == request.SiteId)
            .Include(s => s.Blocks)
            .Include(s => s.Customer)
            .Include(s => s.EquipmentGroups)
                .ThenInclude(eg => eg.EquipmentSections)
                    .ThenInclude(es => es.EquipmentContents)
            .FirstOrDefaultAsync(cancellationToken: ct);

        if (site is null)
            return SiteDomainErrors.NotFound;

        // Then we check user access to site data
        bool hasAccess = await _authorizationService
            .CheckUserAccessByGroup(
            userGroupsId: request.UserGroupsId,
            siteId: site.Id,
            ct: ct);

        if (hasAccess is false)
            return SiteDomainErrors.Unauthorized;

        string tableName = _queryBuilder.GetTableName(
            siteName: site.Name,
            customerName: site.Customer.Name);

        // Get all the equipment contents
        List<EquipmentContent> contents = site.EquipmentGroups
            .SelectMany(eg => eg.EquipmentSections)
            .SelectMany(es => es.EquipmentContents)
            .ToList();

        // Dictionary containing a list of tags for each block
        Dictionary<Guid, List<string>> blockToTags = contents
            .GroupBy(content => content.BlockId)
            .ToDictionary(
                group => group.Key,
                group => group.Select(content => content.Tag).ToList());

        // Build the query for each block in the dictionary using the list of tags
        foreach (KeyValuePair<Guid, List<string>> blockDict in blockToTags)
        {
            // Use the block id to get the block name
            string? blockName = site.Blocks
                .Where(block => block.Id == blockDict.Key)
                .Select(block => block.Name)
                .FirstOrDefault();

            if (blockName is null)
                continue;

            string query = _queryBuilder.GetSnapshotQuery(
                siteName: site.Name,
                blockName: blockName,
                tags: blockDict.Value);

            // Query the table storage for list of snapshots data
            List<TableEntity> snapshots = await _tableStorage.QueryEntitiesAsync(
                tableName, query, ct);

            // Then use this to fill the value of equipment content for most up-to-date data of the block
            foreach (TableEntity item in snapshots)
            {
                // Find the equipment content that matches the tag
                EquipmentContent? content = contents.Find(
                    content => content.BlockId == blockDict.Key &&
                    content.Tag == item.RowKey);

                if (content is null)
                    continue;

                string value = item["Value"].ToString() ?? "0";

                // Then update the value of the equipment content
                content.UpdateEquipmentValue(value: double.Parse(value));
            }
        }

        // Then map the entity to response
        return site.EquipmentGroups
        .Select(eg => new EquipmentGroupResponse(
            Id: eg.Id,
            Name: eg.Name,
            Width: eg.Width,
            Position: eg.Position,
            Notes: eg.Notes,
            DateModified: eg.DateModified ?? eg.DateCreated,
            ModifiedBy: eg.ModifiedBy ?? eg.CreatedBy,
            EquipmentSections: eg.EquipmentSections
            .Select(section => new EquipmentSectionResponse(
                id: section.Id,
                GroupId: eg.Id,
                Name: section.Name,
                SectionType: section.SectionType.ToString(),
                Position: section.Position,
                EquipmentContents: section.EquipmentContents
                .Select(content => new EquipmentContentResponse(
                    Id: content.Id,
                    SectionId: section.Id,
                    GroupId: eg.Id,
                    BlockId: content.BlockId,
                    Alias: content.Alias,
                    Type: content.Type.ToString(),
                    Format: content.Format.ToString(),
                    Row: content.Position.Row,
                    Column: content.Position.Column,
                    Max: content.Range.Max,
                    Min: content.Range.Min,
                    Interval: content.Range.Interval,
                    Tag: content.Tag,
                    Value: content.Value,
                    Suffix: content.Suffix,
                    DateModified: content.AuditInfo.DateModified ?? content.AuditInfo.DateCreated,
                    ModifiedBy: content.AuditInfo.ModifiedBy ?? content.AuditInfo.CreatedBy))
                    .OrderBy(content => content.Row)
                    .ThenBy(content => content.Column)
                    .ToList()
            ))
            .OrderBy(section => section.Position)
            .ToList()))
        .OrderBy(eg => eg.Position)
        .ToList();
    }
}