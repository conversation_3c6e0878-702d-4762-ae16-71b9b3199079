using ErrorOr;
using MediatR;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.EquipmentSection;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.SaveEquipmentSettings;

public record SaveEquipmentSettingsCommand(
    List<Guid> UserGroupsId,
    Guid SiteId,
    string CreatedBy,
    List<GroupSettings> EquipmentGroups
    ) : IRequest<ErrorOr<Unit>>;

public record GroupSettings(
  Guid Id,
  string Name,
  double Width,
  int Position,
  string Notes,
  List<SectionSettings> EquipmentSections
);

public record SectionSettings(
  Guid Id,
  string Name,
  SectionType SectionType,
  int Position,
  List<ContentSettings> EquipmentContents
);

public record ContentSettings(
  Guid Id,
  Guid BlockId,
  string Alias,
  ContentType Type,
  ContentFormat Format,
  int Row,
  int Column,
  double? Min,
  double? Max,
  double? Interval,
  string Tag,
  double Value,
  string? Suffix);


