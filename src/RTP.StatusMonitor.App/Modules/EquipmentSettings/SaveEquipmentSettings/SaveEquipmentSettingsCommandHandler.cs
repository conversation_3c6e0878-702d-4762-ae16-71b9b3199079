using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Domain.EquipmentGroup;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Domain.EquipmentSection;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.SaveEquipmentSettings;

public sealed class SaveEquipmentSettingsCommandHandler(
    IDataContext dataContext,
    IDateTimeProvider dateTimeProvider)
        : IRequestHandler<SaveEquipmentSettingsCommand, ErrorOr<Unit>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;

    public async Task<ErrorOr<Unit>> Handle(
        SaveEquipmentSettingsCommand request,
        CancellationToken ct = default)
    {
        // First we need to query the site data
        Site? site = await _dataContext.Sites
            .Where(s => s.Id == request.SiteId)
            .Include(s => s.GroupPermissions)
            .Include(s => s.Blocks)
            .Include(s => s.EquipmentGroups)
            .ThenInclude(eg => eg.EquipmentSections)
                .ThenInclude(es => es.EquipmentContents)
            .FirstOrDefaultAsync(ct);

        // When the site is not found
        // Then we return not found error
        if (site is null)
            return SiteErrors.NotFound;

        // Before saving new equipment groups for the site
        // We need to remove existing equipment groups
        if (request.EquipmentGroups.Count > 0 &&
            request.EquipmentGroups[0].EquipmentSections.Count > 0)
        {
            var firstSection = request.EquipmentGroups[0].EquipmentSections[0];

            if (firstSection.SectionType == SectionType.Availability)
            {
                site.EquipmentGroups.RemoveAll(eg => eg.EquipmentSections[0].SectionType == SectionType.Availability);
            }
            else
            {
                site.EquipmentGroups.RemoveAll(eg => eg.EquipmentSections[0].SectionType == SectionType.Constraints);
            }
        }
        else
        {
            site.EquipmentGroups.Clear();
        }

        // Then we can create equipment groups
        List<EquipmentGroup> equipmentGroups = new();
        foreach (GroupSettings group in request.EquipmentGroups)
        {
            List<EquipmentSection> equipmentSections = new();
            foreach (SectionSettings section in group.EquipmentSections)
            {
                ErrorOr<List<EquipmentContent>> equipmentContents = CreateEquipmentContents(
                    section.EquipmentContents,
                    site,
                    request.CreatedBy);

                if (equipmentContents.IsError)
                    return equipmentContents.Errors;

                equipmentSections.Add(new EquipmentSection(
                    id: Guid.NewGuid(),
                    name: section.Name,
                    sectionType: section.SectionType,
                    position: section.Position,
                    equipmentContents: equipmentContents.Value));
            }

            ErrorOr<EquipmentGroup> newEquipmentGroup = EquipmentGroup.Create(
                userGroupsId: request.UserGroupsId,
                id: Guid.NewGuid(),
                name: group.Name,
                width: group.Width,
                position: group.Position,
                notes: group.Notes ?? string.Empty,
                site: site,
                equipmentSections: equipmentSections,
                dateCreated: _dateTimeProvider.UtcNow,
                dateModified: null,
                createdBy: request.CreatedBy,
                modifiedBy: request.CreatedBy);

            equipmentGroups.Add(newEquipmentGroup.Value);
        }

        // Save changes to database
        await _dataContext.EquipmentGroups
            .AddRangeAsync(equipmentGroups, ct);

        bool isSuccess = await _dataContext.SaveChangesAsync(ct) > 0;

        if (!isSuccess)
            return EquipmentGroupErrors.PersistenceErrors;

        return Unit.Value;
    }

    private ErrorOr<List<EquipmentContent>> CreateEquipmentContents(
        List<ContentSettings> contents,
        Site site,
        string createdBy)
    {
        List<EquipmentContent> equipmentContents = new();

        foreach (var content in contents)
        {
            Block? block = site.Blocks.FirstOrDefault(b => b.Id == content.BlockId);

            ErrorOr<ContentRange> range = ContentRange.Create(
                min: content.Min,
                max: content.Max,
                interval: content.Interval);

            if (range.IsError)
                return range.Errors;

            ErrorOr<EquipmentContent> equipmentContent = EquipmentContent.Create(
                    id: Guid.NewGuid(),
                    alias: content.Alias,
                    type: content.Type,
                    format: content.Format,
                    range: range.Value,
                    position: new ContentPosition(
                        Row: content.Row,
                        Column: content.Column),
                    tag: content.Tag,
                    value: content.Value,
                    suffix: content.Suffix,
                    block: block,
                    auditInfo: new AuditInfo(
                        DateCreated: _dateTimeProvider.UtcNow,
                        DateModified: null,
                        CreatedBy: createdBy,
                        ModifiedBy: createdBy));

            if (equipmentContent.IsError)
                return equipmentContent.Errors;

            equipmentContents.Add(equipmentContent.Value);
        }

        return equipmentContents;
    }
}
