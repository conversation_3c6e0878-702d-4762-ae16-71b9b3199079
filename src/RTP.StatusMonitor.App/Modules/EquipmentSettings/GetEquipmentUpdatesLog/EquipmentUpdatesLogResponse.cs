using RTP.StatusMonitor.Domain.EquipmentContent.Events;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentUpdatesLog;

public record EquipmentUpdatesLogResponse(
    Guid Id,
    string EventType,
    ForecastUpdateEventContent Content,
    string State,
    string TriggeredBy,
    DateTime CreationDate);

public record ForecastUpdateEventContent(
    string Site,
    List<ForecastUpdate> Updates,
    UpdateAction Action,
    bool IsTest,
    string Note,
    string CreatedBy,
    long Timestamp);