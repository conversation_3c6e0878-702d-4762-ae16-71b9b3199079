using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.IntegrationEventLogEntry;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.EquipmentSettings.GetEquipmentUpdatesLog;

internal sealed class GetEquipmentUpdatesLogQueryHandler
    : IQueryHandler<GetEquipmentUpdatesLogQuery, List<EquipmentUpdatesLogResponse>>
{
    private readonly ISiteRepository _siteRepository;
    private readonly IDataContext _dataContext;
    private readonly IAuthorizationService _authorizationService;

    public GetEquipmentUpdatesLogQueryHandler(
        IDataContext dataContext,
        IAuthorizationService authorizationService,
        ISiteRepository siteRepository)
    {
        _dataContext = dataContext;
        _authorizationService = authorizationService;
        _siteRepository = siteRepository;
    }

    public async Task<ErrorOr<List<EquipmentUpdatesLogResponse>>> Handle(
        GetEquipmentUpdatesLogQuery query,
        CancellationToken ct)
    {
        // Check if user has permission to get event log
        var isAuthorized = await _authorizationService
            .CheckUserAccessByGroup(
                userGroupsId: query.UserGroupsId,
                siteId: query.SiteId,
                ct: ct);

        if (!isAuthorized)
            return SiteDomainErrors.Unauthorized;

        // Find the site by id
        Site? site = await _siteRepository
            .GetSiteByIdAsync(query.SiteId, ct);

        if (site is null)
            return SiteDomainErrors.NotFound;

        // Get the event logs from the database
        List<IntegrationEventLogEntry> integrationEventLogEntries = await _dataContext
            .IntegrationEventLogEntries.ToListAsync(ct);

        return integrationEventLogEntries
            .Select(x => new EquipmentUpdatesLogResponse(
                Id: x.Id,
                EventType: x.EventType,
                Content: JsonConvert
                .DeserializeObject<ForecastUpdateEventContent>(x.Content)
                    ?? throw new InvalidOperationException(),
                State: x.State,
                TriggeredBy: x.TriggeredBy,
                CreationDate: x.CreationDate))
            .Where(x => x.Content.Site == site.Name)
            .OrderByDescending(x => x.CreationDate)
            .ToList();
    }
}