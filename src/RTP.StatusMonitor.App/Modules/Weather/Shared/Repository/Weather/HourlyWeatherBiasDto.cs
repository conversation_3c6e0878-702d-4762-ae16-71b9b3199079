using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

/// <summary>
/// The weather bias for a unit at a specific hour (average of the hour)
/// </summary>
/// 
/// <param name="HourEnding">The bias of this hour</param>
/// <param name="Bias">The bias data point for this hour</param>
public record HourlyWeatherBiasDto(
    int HourEnding,
    AmbientBiasDataPoint? Bias);

