using RTP.StatusMonitor.App.Shared.Storage;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

public class HistoricalWeatherDto : BaseTableEntity
{
    /// <summary>
    /// The weather API data source (AccuWeather, IBM, etc...)
    /// </summary>
    public string Source { get; set; } = null!;

    /// <summary>
    /// The local time of the weather data
    /// </summary>
    public string LocalTimestamp { get; set; } = null!;

    /// <summary>
    /// The hour of the data (from 1 -> 24)
    /// </summary>
    public int HourEnding { get; set; }
    public string Description { get; set; } = string.Empty;
    public int Icon { get; set; }
    public double? Temp { get; set; }
    public double? Press { get; set; }
    public double? PressBaro { get; set; }
    public double? RH { get; set; }
    public double? WindSpeed { get; set; }
    public int? WindDirection { get; set; }
    public int? UVIndex { get; set; }
    public double? Precipitation { get; set; }
}