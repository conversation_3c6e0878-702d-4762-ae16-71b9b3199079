using Azure.Data.Tables;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.WeatherBias;
using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

public class WeatherReadRepository(
    IMemoryCache cache,
    IOptions<TableStorageOptions> tableStorageOptions,
    IOptions<BlobStorageOptions> blobStorageOptions,
    IDateTimeProvider dateTimeProvider
)
{
    private readonly long REVERSE_ROW_KEY_CONSTANT = (long)Math.Pow(10, 13);
    private readonly TimeSpan BIAS_CACHE_EXPIRATION = TimeSpan.FromMinutes(5);
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;
    private readonly BlobStorageOptions _blobStorageOptions = blobStorageOptions.Value;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IMemoryCache _cache = cache;

    /// <summary>
    /// Get the weather bias against the weather service for the given unit of a given bias type (Temperature, RH, Pressure, CIT) for the last n days
    /// and return the list of AVERAGE bias value for each hour ending (1-24)
    /// </summary>
    /// <param name="unitAlias">The alias of the unit (convention is Bellingham-1-1 for example) that querying bias for</param>
    /// <param name="weatherService">The weather service biasing against</param>
    /// <param name="tagSource">The source of the bias tag (RTP/Customer)
    /// <param name="biasType">The type of the bias (Temperature, RH, Pressure, CIT)</param>
    /// <param name="unixStartTime">The start time in unix milliseconds</param>
    /// <param name="unixEndTime">The end time in unix milliseconds</param>
    /// <param name="biasLimit">The bias limits to filter values before averaging (optional)</param>
    /// <returns></returns>
    public List<HourlyWeatherBiasDto> GetWeatherBiases(
        string unitAlias,
        WeatherService weatherService,
        WeatherTagSource tagSource,
        WeatherBiasType biasType,
        long unixStartTime,
        long unixEndTime,
        BiasLimit? biasLimit = null
    ) =>
        _cache.GetOrCreate(
            key: $"{unitAlias}-{weatherService}-{tagSource}-{biasType}-{unixStartTime}-{unixEndTime}-{biasLimit?.Min}-{biasLimit?.Max}",
            factory: entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = BIAS_CACHE_EXPIRATION;

                // Build the filter to query
                string partitionKey =
                    tagSource == WeatherTagSource.Customer
                        ? $"{unitAlias}-{weatherService}"
                        : $"{unitAlias}-{weatherService}-AS";

                string filter =
                    @$"PartitionKey eq '{partitionKey}' and 
                    RowKey gt '{REVERSE_ROW_KEY_CONSTANT - unixEndTime}' and 
                    RowKey lt '{REVERSE_ROW_KEY_CONSTANT - unixStartTime}'";

                // Query data from table storage
                // and transform the data into WeatherBiasTypeResult
                // Filter out bias values outside the min/max range before grouping and averaging
                return new TableClient(
                    endpoint: new Uri(_tableStorageOptions.Uri),
                    tableName: _tableStorageOptions.WeatherReadTable,
                    credential: new TableSharedKeyCredential(
                        _tableStorageOptions.AccountName,
                        _tableStorageOptions.AccountKey
                    )
                )
                    .Query<WeatherBiasDto>(filter)
                    .Select(entity => new HourlyWeatherBiasDto(
                        HourEnding: entity.HourEnding,
                        Bias: new AmbientBiasDataPoint(
                            weatherService,
                            tagSource,
                            biasType,
                            biasType switch
                            {
                                WeatherBiasType.Temp => entity.TempBias ?? 0,
                                WeatherBiasType.Press => entity.PressBias ?? 0,
                                WeatherBiasType.RH => entity.RHBias ?? 0,
                                WeatherBiasType.CIT => entity.CITBias ?? 0,
                                _ => 0,
                            }
                        )
                    ))
                    // Filter out bias values that are outside the specified limits before averaging
                    .Where(x =>
                        biasLimit == null
                        || (
                            x.Bias?.Value >= (biasLimit.Min ?? double.MinValue)
                            && x.Bias?.Value <= (biasLimit.Max ?? double.MaxValue)
                        )
                    )
                    .GroupBy(x => x.HourEnding)
                    .Select(x => new HourlyWeatherBiasDto(
                        HourEnding: x.Key,
                        Bias: new AmbientBiasDataPoint(
                            ApiSource: weatherService,
                            TagSource: tagSource,
                            BiasType: biasType,
                            Value: x.Average(x => x.Bias?.Value)
                        ).Format()
                    ))
                    .ToList();
            }
        ) ?? [];

    /// <summary>
    /// Get the latest weather bias against the weather service for the given unit of a given bias type (Temperature, RH, Pressure, CIT)
    /// </summary>
    ///
    /// <param name="unit">The unit to get the latest weather bias for</param>
    /// <param name="weatherService">The weather service biasing against</param>
    /// <param name="tagSource">The source of the bias tag (RTP/Customer)
    /// <param name="biasType">The type of the bias (Temperature, RH, Pressure, CIT)</param>
    ///
    /// <returns></returns>
    public WeatherBiasDto? GetLatestWeatherBias(
        Unit unit,
        WeatherService weatherService,
        WeatherTagSource tagSource
    ) =>
        _cache.GetOrCreate(
            key: $"{unit.Alias}-{weatherService}-{tagSource}-latest",
            factory: entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = BIAS_CACHE_EXPIRATION;

                // Build the filter to query
                string partitionKey =
                    tagSource == WeatherTagSource.Customer
                        ? $"{unit.Alias}-{weatherService}"
                        : $"{unit.Alias}-{weatherService}-AS";

                // Find the row scan range
                long unixEndTimeMs = (
                    (DateTimeOffset)_dateTimeProvider.UtcNow
                ).ToUnixTimeMilliseconds();
                long unixStartTimeMs = (
                    (DateTimeOffset)_dateTimeProvider.UtcNow.AddHours(-1)
                ).ToUnixTimeMilliseconds();

                string filter =
                    @$"PartitionKey eq '{partitionKey}' and 
                RowKey gt '{REVERSE_ROW_KEY_CONSTANT - unixEndTimeMs}' and 
                RowKey lt '{REVERSE_ROW_KEY_CONSTANT - unixStartTimeMs}'";

                return new TableClient(
                    endpoint: new Uri(_tableStorageOptions.Uri),
                    tableName: _tableStorageOptions.WeatherReadTable,
                    credential: new TableSharedKeyCredential(
                        _tableStorageOptions.AccountName,
                        _tableStorageOptions.AccountKey
                    )
                )
                    .Query<WeatherBiasDto>(filter)
                    .FirstOrDefault();
            }
        );

    /// <summary>
    /// Get the last modified time of the weather file for the given unit
    /// </summary>
    /// <param name="unit">The unit to get the weather data for</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public DateTimeOffset? GetWeatherFileLastModifiedTime(Unit unit, CancellationToken ct)
    {
        BlobClient blobClient = new(
            connectionString: _blobStorageOptions.ConnectionString,
            blobContainerName: unit.Block.Site.Customer.Name.ToLower(),
            blobName: WeatherBlobDirectory.Create(unit).Value
        );

        return !blobClient.Exists(ct)
            ? null
            : blobClient.GetProperties(cancellationToken: ct).Value.LastModified;
    }
}
