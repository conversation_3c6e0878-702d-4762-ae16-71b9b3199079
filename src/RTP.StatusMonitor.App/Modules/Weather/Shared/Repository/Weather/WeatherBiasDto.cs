using RTP.StatusMonitor.App.Shared.Storage;
namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

public class WeatherBiasDto : BaseTableEntity
{
    /// <summary>
    /// The source of the tag (RTP or Customer) => PI or AS
    /// </summary>
    public string Source { get; set; } = string.Empty;
    /// <summary>
    /// The local time of the weather data
    /// </summary>
    public string LocalTimestamp { get; set; } = string.Empty;
    public int HourEnding { get; set; }
    public double? Temp { get; set; }
    public double? Press { get; set; }
    public double? RH { get; set; }
    public double? CIT { get; set; }
    public double? TempBias { get; set; }
    public double? RHBias { get; set; }
    public double? PressBias { get; set; }
    public double? CITBias { get; set; }
}
