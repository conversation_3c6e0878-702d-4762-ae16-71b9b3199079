using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.WeatherBias;
using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

public static class WeatherBiasDtoMapping
{
    private static readonly long REVERSE_ROW_KEY_CONSTANT = (long)Math.Pow(10, 13);

    /// <summary>
    /// Map the actual instrumentation and observed weather data points to a weather bias entity
    /// to upload to the weather bias table
    /// </summary>
    /// <param name="actualInstrumentation">The actual weather data points from the instrumentation</param>
    /// <param name="observedWeather">The observed weather data points from external API</param>
    /// <param name="biasLimits">The bias limits for each bias type (computed bias values should not exceed the limit)</param>
    /// <param name="weatherApi">The weather API source (AccuWeather, IBM, etc...)</param>
    /// <param name="tagSource">The source of the tag (RTP or Customer)</param>
    /// <param name="unit">The unit to which the weather data points belong</param>
    /// <returns>The weather bias entity to upload to the weather bias table</returns>
    public static WeatherBiasDto ToWeatherBiasEntity(
        this List<ActualInstrumentationDataPoint> actualInstrumentation,
        List<ObservedWeatherDataPoint> observedWeather,
        Dictionary<WeatherBiasType, BiasLimit> biasLimits,
        WeatherService weatherApi,
        WeatherTagSource tagSource,
        Unit unit
    )
    {
        TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone);
        DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(
            DateTimeOffset.FromUnixTimeSeconds(DateTimeOffset.UtcNow.ToUnixTimeSeconds()).DateTime,
            timeZoneInfo
        );

        string partitionKey =
            tagSource == WeatherTagSource.Customer
                ? $"{unit.Alias}-{weatherApi}"
                : $"{unit.Alias}-{weatherApi}-AS";
        string rowKey = (
            REVERSE_ROW_KEY_CONSTANT - DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        ).ToString();
        string source = tagSource == WeatherTagSource.Customer ? "PI" : "AS";

        // Get the actual data from site
        ActualInstrumentationDataPoint? tempActual = actualInstrumentation
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.Temp && x.TagSource == tagSource)
            .Format();
        ActualInstrumentationDataPoint? pressActual = actualInstrumentation
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.Press && x.TagSource == tagSource)
            .Format();
        ActualInstrumentationDataPoint? rhActual = actualInstrumentation
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.RH && x.TagSource == tagSource)
            .Format();
        ActualInstrumentationDataPoint? CITActual = actualInstrumentation
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.CIT && x.TagSource == tagSource)
            .Format();

        // Get observed weather data from weather API
        ObservedWeatherDataPoint? tempObserved = observedWeather
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.Temp)
            .Format();
        ObservedWeatherDataPoint? rhObserved = observedWeather
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.RH)
            .Format();
        ObservedWeatherDataPoint? pressObserved = observedWeather
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.Press)
            .Format();
        ObservedWeatherDataPoint? CITObserved = observedWeather
            .SingleOrDefault(x => x.BiasType == WeatherBiasType.CIT)
            .Format();

        // Compute the biases and make sure it does not exceed the limit
        // Otherwise default to null
        double? tempBias = tempActual.Bias(tempObserved).Format()?.Value;
        // BiasLimit tempBiasLimit = biasLimits[WeatherBiasType.Temp];
        // if (tempBias.HasValue && (tempBias < tempBiasLimit.Min || tempBias > tempBiasLimit.Max))
        // {
        //     tempBias = null;
        // }

        double? rhBias = rhActual.Bias(rhObserved).Format()?.Value;
        // BiasLimit rhBiasLimit = biasLimits[WeatherBiasType.RH];
        // if (rhBias.HasValue && (rhBias < rhBiasLimit.Min || rhBias > rhBiasLimit.Max))
        // {
        //     rhBias = null;
        // }

        double? pressBias = pressActual.Bias(pressObserved).Format()?.Value;
        // BiasLimit pressBiasLimit = biasLimits[WeatherBiasType.Press];
        // if (
        //     pressBias.HasValue && (pressBias < pressBiasLimit.Min || pressBias > pressBiasLimit.Max)
        // )
        // {
        //     pressBias = null;
        // }

        double? citBias = CITActual.Bias(CITObserved).Format()?.Value;
        // BiasLimit citBiasLimit = biasLimits[WeatherBiasType.CIT];
        // if (citBias.HasValue && (citBias < citBiasLimit.Min || citBias < citBiasLimit.Max))
        // {
        //     citBias = null;
        // }

        return new()
        {
            PartitionKey = partitionKey,
            RowKey = rowKey,
            Source = source,
            LocalTimestamp = localTime.ToString("yyyy/MM/dd HH:mm:ss tt"),
            HourEnding = localTime.Hour == 0 ? 24 : localTime.Hour,
            Temp = tempActual?.Value,
            Press = pressActual?.Value,
            RH = rhActual?.Value,
            CIT = CITActual?.Value,
            TempBias = tempBias,
            RHBias = rhBias,
            PressBias = pressBias,
            CITBias = citBias,
        };
    }
}
