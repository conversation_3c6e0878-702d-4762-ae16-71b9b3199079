using RTP.StatusMonitor.App.Shared.Constants;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

/// <summary>
/// Represents a directory path for weather blob storage.
/// </summary>
public record WeatherBlobDirectory
{
    public string Value { get; private set; } = null!;

    /// <summary>
    /// Creates a new instance of the <see cref="WeatherBlobDirectory"/> class based on the specified unit.
    /// NOTE: Will return different file path for development environment to avoid overwriting production data.
    /// </summary>
    /// <param name="unit">The unit to create the directory path for.</param>
    /// <returns>A new instance of the <see cref="WeatherBlobDirectory"/> class.</returns>
    public static WeatherBlobDirectory Create(Unit unit)
    => Environment.GetEnvironmentVariable("AppEnvironment") == AppEnvironment.Development
        ? new()
        {
            Value = $"{unit.Block.Site.Name.Replace(" ", string.Empty).ToLower()}/block{unit.Block.Name}/forecast/current/{unit.Block.Site.Name}_{unit.Block.Name}_{unit.Name}_Weather_Test.txt"
        }
        : new()
        {
            Value = $"{unit.Block.Site.Name.Replace(" ", string.Empty).ToLower()}/block{unit.Block.Name}/forecast/current/{unit.Block.Site.Name}_{unit.Block.Name}_{unit.Name}_Weather.txt"
        };
}