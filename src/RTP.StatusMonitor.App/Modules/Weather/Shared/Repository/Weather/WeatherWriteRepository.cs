using System.Globalization;
using Azure.Storage.Blobs;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Options;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

public class WeatherWriteRepository(IOptions<BlobStorageOptions> blobStorageOptions)
{
    private readonly BlobStorageOptions _blobStorageOptions = blobStorageOptions.Value;

    /// <summary>
    /// This method will save forecast data with bias adj to blob storage
    /// </summary>
    /// <param name="data">Forecast data with bias adjustment</param>
    /// <param name="customerName">The name of the customer</param>
    /// <param name="directory">The unit that the forecast data is for</param>
    /// <exception cref="Exception"></exception>
    public async Task SaveWeatherForecastAsync(
        List<WeatherForecastDto> data,
        string customerName,
        WeatherBlobDirectory directory,
        CancellationToken ct
    )
    {
        try
        {
            using var writer = new StreamWriter(new MemoryStream());
            using var csv = new CsvWriter(
                writer: writer,
                configuration: new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    Delimiter = "\t",
                }
            );

            await csv.WriteRecordsAsync(data);

            await writer.FlushAsync();

            // Reset the memory stream position to the beginning
            writer.BaseStream.Position = 0;

            // Upload the weather bias model to blob storage
            await new BlobClient(
                connectionString: _blobStorageOptions.ConnectionString,
                blobContainerName: customerName.ToLower(),
                blobName: directory.Value
            ).UploadAsync(writer.BaseStream, true, ct);
        }
        catch (Exception e)
        {
            throw new InvalidOperationException(
                "Failed to save weather bias model to blob storage",
                e
            );
        }
    }
}
