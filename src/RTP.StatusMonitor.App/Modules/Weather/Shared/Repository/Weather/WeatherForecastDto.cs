namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;

/// <summary>
/// This is the format of the weather forecast file
/// </summary>
/// <param name="TimestampUtc">The UTC timestamp of the weather forecast</param>
/// <param name="LocalTimestamp">The local timestamp of the weather forecast</param>
/// <param name="Hour">The hour ending of the weather forecast</param>
/// <param name="Description">The description of the weather forecast</param>
/// <param name="Api">The API used to generate the weather forecast</param>
/// <param name="TempRaw">The raw temperature of the weather forecast</param>
/// <param name="TempBias">The bias of the temperature of the weather forecast</param>
/// <param name="TempAdj">The adjusted temperature of the weather forecast</param>
/// <param name="CITBias">The bias of the CIT of the weather forecast</param>
/// <param name="DewPoint">The dew point of the weather forecast</param>
/// <param name="RhRaw">The raw relative humidity of the weather forecast</param>
/// <param name="RhBias">The bias of the relative humidity of the weather forecast</param>
/// <param name="RhAdj">The adjusted relative humidity of the weather forecast</param>
/// <param name="PressBaro">The barometric pressure of the weather forecast</param>
/// <param name="PressRaw">The raw barometric pressure of the weather forecast</param>
/// <param name="PressBias">The bias of the barometric pressure of the weather forecast</param>
/// <param name="PressAdj">The adjusted barometric pressure of the weather forecast</param>
/// <param name="WindSpeed">The wind speed of the weather forecast</param>
/// <param name="WindDir">The wind direction of the weather forecast</param>
/// <param name="WindDirDegree">The wind direction degree of the weather forecast</param>
/// <param name="Precip">The precipitation of the weather forecast</param>
/// <param name="UvIndex">The UV index of the weather forecast</param>
public record WeatherForecastDto(
    DateTime TimestampUtc,
    DateTime LocalTimestamp,
    int Hour,
    string Description,
    string Api,
    double? TempRaw,
    double? TempBias,
    double? TempAdj,
    double? CITBias,
    double? DewPoint,
    double? RhRaw,
    double? RhBias,
    double? RhAdj,
    double? PressBaro,
    double? PressRaw,
    double? PressBias,
    double? PressAdj,
    double? WindSpeed,
    string? WindDir,
    double? WindDirDegree,
    double? Precip,
    double? UvIndex);
