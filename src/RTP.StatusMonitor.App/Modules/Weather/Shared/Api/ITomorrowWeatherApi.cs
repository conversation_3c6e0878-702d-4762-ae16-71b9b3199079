using Refit;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.TomorrowWeather;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

public interface ITomorrowWeatherApi
{
    [Get("/forecast?location={latitude},{longitude}&timesteps=1h&units={unit}&apikey={apiKey}")]
    internal Task<TomorrowForecastDto> GetWeatherForecast(
        double latitude,
        double longitude,
        string unit,
        string apiKey);

    [Get("/realtime?location={latitude},{longitude}&units={unit}&apikey={apiKey}")]
    internal Task<TomorrowCurrentConditionDto> GetCurrentCondition(
        double latitude,
        double longitude,
        string unit,
        string apiKey);

}