
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;

public record DailyForecastResponse(
    WeatherService Api,
    long UnixTimeInSeconds,
    string LocalTime,
    int? IconCode,
    string Description,
    string ShortDescription,
    string PrecipType,
    decimal? PrecipProbability,
    Temperature? TemperatureMin,
    Temperature? TemperatureMax,
    RelativeHumid? RelativeHumid,
    string UvDescription,
    string WindPhrase);
