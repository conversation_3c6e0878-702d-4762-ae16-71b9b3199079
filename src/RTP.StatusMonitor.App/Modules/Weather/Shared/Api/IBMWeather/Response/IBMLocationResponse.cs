namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather.Response;

public record IBMLocation
{
    public IBMLocationResponse location { get; set; } = new();
}

public record IBMLocationResponse
{
    public List<string> stationName { get; set; } = [];
    public List<string> stationId { get; set; } = [];
    public List<int> qcStatus { get; set; } = [];
    public List<int> updateTimeUtc { get; set; } = [];
    public List<string> partnerId { get; set; } = [];
    public List<double> latitude { get; set; } = [];
    public List<double> longitude { get; set; } = [];
    public List<double> distanceKm { get; set; } = [];
    public List<double> distanceMi { get; set; } = [];
}
