namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
public record IBMHourlyForecastResponse
{
    public List<int> cloudCover { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> dayOfWeek { get; set; } = Enumerable.Empty<string>().ToList();
    public List<string> dayOrNight { get; set; } = Enumerable.Empty<string>().ToList();
    public List<int> expirationTimeUtc { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> iconCode { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> iconCodeExtend { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> precipChance { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> precipType { get; set; } = Enumerable.Empty<string>().ToList();
    public List<double> pressureMeanSeaLevel { get; set; } = Enumerable.Empty<double>().ToList();
    // NOTE - The forecasted measurable precipitation (liquid or liquid equivalent) for the upcoming hour. Expressed in inches when units=e, expressed in millimeters when units=m
    public List<double> qpf { get; set; } = Enumerable.Empty<double>().ToList();
    public List<double> qpfSnow { get; set; } = Enumerable.Empty<double>().ToList();
    public List<int> relativeHumidity { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> temperature { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> temperatureDewPoint { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> temperatureFeelsLike { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> temperatureHeatIndex { get; set; } = Enumerable.Empty<int>().ToList();
    public List<int> temperatureWindChill { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> uvDescription { get; set; } = Enumerable.Empty<string>().ToList();
    public List<int> uvIndex { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> validTimeLocal { get; set; } = Enumerable.Empty<string>().ToList();
    public List<int> validTimeUtc { get; set; } = Enumerable.Empty<int>().ToList();
    public List<double> visibility { get; set; } = Enumerable.Empty<double>().ToList();
    public List<int> windDirection { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> windDirectionCardinal { get; set; } = Enumerable.Empty<string>().ToList();
    public List<int?> windGust { get; set; } = Enumerable.Empty<int?>().ToList();
    public List<int> windSpeed { get; set; } = Enumerable.Empty<int>().ToList();
    public List<string> wxPhraseLong { get; set; } = Enumerable.Empty<string>().ToList();
    public List<string> wxPhraseShort { get; set; } = Enumerable.Empty<string>().ToList();
    public List<int> wxSeverity { get; set; } = Enumerable.Empty<int>().ToList();
}
