namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;

public record IBMWeatherObservationResponse
(
    int? cloudCeiling,
    string cloudCoverPhrase,
    string dayOfWeek,
    string dayOrNight,
    int expirationTimeUtc,
    int iconCode,
    int iconCodeExtend,
    string? obsQualifierCode,
    int? obsQualifierSeverity,
    double precip1Hour,
    double precip6Hour,
    double precip24Hour,
    double pressureAltimeter,
    double pressureChange,
    double pressureMeanSeaLevel,
    int pressureTendencyCode,
    string pressureTendencyTrend,
    int relativeHumidity,
    double snow1Hour,
    double snow6Hour,
    double snow24Hour,
    string sunriseTimeLocal,
    int sunriseTimeUtc,
    string sunsetTimeLocal,
    int sunsetTimeUtc,
    int temperature,
    int temperatureChange24Hour,
    int temperatureDewPoint,
    int temperatureFeelsLike,
    int temperatureHeatIndex,
    int temperatureMax24Hour,
    int temperatureMaxSince7Am,
    int temperatureMin24Hour,
    int temperatureWindChill,
    string uvDescription,
    int uvIndex,
    string validTimeLocal,
    int validTimeUtc,
    double visibility,
    int windDirection,
    string windDirectionCardinal,
    int? windGust,
    int windSpeed,
    string wxPhraseLong,
    string wxPhraseMedium,
    string wxPhraseShort);
