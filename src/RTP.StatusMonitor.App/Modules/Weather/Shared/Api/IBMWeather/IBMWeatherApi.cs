using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Refit;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather.Response;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather;

public class IBMWeatherApi(
    IOptions<IBMWeatherApiOptions> weatherApiOptions,
    ILogger<IBMWeatherApi> logger,
    IMemoryCache memoryCache) : IWeatherApi
{
    private readonly IBMWeatherApiOptions _weatherApiOptions = weatherApiOptions.Value;
    private readonly IIBMWeatherApi _ibmWeatherApi = RestService.For<IIBMWeatherApi>(weatherApiOptions.Value.BaseUri);
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly ILogger<IBMWeatherApi> _logger = logger;
    public bool IsApplicable(WeatherService weatherService)
        => weatherService == WeatherService.IBM;

    /// <inheritdoc/>
    public async Task<List<WeatherStationByLatLngResponse>> GetNearbyWeatherStations(double latitude, double longitude)
    {
        try
        {
            // Given the lng and lat of a location
            // Then we can make API call to IBM weather service to get the nearby weather stations
            IBMLocation? ibmWeatherStations = await _ibmWeatherApi.GetNearbyWeatherStations(
                latitude: latitude,
                longitude: longitude,
                apiKey: _weatherApiOptions.ApiKey);

            // When we can't get the weather stations from IBM weather API
            // Then we log the error and return empty list
            if (ibmWeatherStations is null)
            {
                _logger.LogError("IBM Weather API Error.");
                return [];
            }

            // Then we need to map the IBMLocationDto to WeatherStationDto
            // And return the list of WeatherStationDto
            return ibmWeatherStations.location.stationName.Select((_, i) => new WeatherStationByLatLngResponse
            {
                StationName = ibmWeatherStations.location.stationId[i],
                ApiName = _weatherApiOptions.ApiName,
                Latitude = ibmWeatherStations.location.latitude[i],
                Longitude = ibmWeatherStations.location.longitude[i],
                DistanceFrom = ibmWeatherStations.location.distanceMi[i],
            }).ToList();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "IBM Weather API Error.");
            return Enumerable.Empty<WeatherStationByLatLngResponse>().ToList();
        }
    }

    /// <inheritdoc />
    public async Task<WeatherConditionResult?> GetCurrentConditionsAsync(
        double latitude,
        double longitude,
        double altitude,
        CancellationToken ct)
    {
        try
        {
            // Given the lng and lat of a location
            // Then we can make API call to IBM weather service to get the current weather observation of the location
            IBMWeatherObservationResponse ibmWeatherObservationImperial = await _ibmWeatherApi
                .GetCurrentObservations(latitude, longitude, "e", _weatherApiOptions.ApiKey);

            // When we can't get the current weather observation from IBM weather API
            // Then we log the error and return null
            if (ibmWeatherObservationImperial is null)
                return null;

            // Convert the temp to Fahrenheit
            Temperature temp = new(
                Value: ibmWeatherObservationImperial.temperature,
                EngUnits: TemperatureEngUnit.Fahrenheit);

            // Convert the mslp to absolute pressure
            MeanSeaLevelPressure mslp = new(
                Value: ibmWeatherObservationImperial.pressureMeanSeaLevel,
                EngUnits: PressureEngUnit.Millibar);

            // Convert from mslp to abs press (mbar) and then to psi
            AbsolutePressure absPress = mslp
                .ToAbsolutePressure(
                    altitude: AltitudeUnitConverter.ToMeters(altitude),
                    temp: new Temperature(
                        Value: ibmWeatherObservationImperial.temperature,
                        EngUnits: TemperatureEngUnit.Fahrenheit));

            return new WeatherConditionResult
            {
                ApiName = _weatherApiOptions.ApiName,
                // NOTE - Remove date time offset (ex: -0600) from (ex: "2024-03-06T11:00:00-0600")
                LocalTime = ibmWeatherObservationImperial.validTimeLocal[..^5],
                UnixTimeInSeconds = ibmWeatherObservationImperial.validTimeUtc,
                Description = ibmWeatherObservationImperial.wxPhraseLong,
                WeatherIcon = ibmWeatherObservationImperial.iconCode,
                RelativeHumidity = ibmWeatherObservationImperial.relativeHumidity,
                Temp = temp.Format(),
                MeanSeaLevelPressure = (MeanSeaLevelPressure)mslp.Format(),
                AbsolutePressure = (AbsolutePressure)absPress.Format(),
                WindSpd = new Velocity(
                    Value: ibmWeatherObservationImperial.windSpeed,
                    EngUnits: VelocityEngUnit.MilesPerHour),
                WindDirection = new WindDirectionDto
                {
                    Degrees = ibmWeatherObservationImperial.windDirection,
                    Localized = ibmWeatherObservationImperial.windDirectionCardinal,
                },
                WetBulbTemp = null,
                Precipitation = new Precipitation(
                    Value: ibmWeatherObservationImperial.precip1Hour,
                    EngUnits: PrecipitationEngUnit.Inches),
                UvIndex = ibmWeatherObservationImperial.uvIndex,
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "IBM Weather API Error.");
            return null;
        }
    }

    /// <inheritdoc />
    public List<HistoricalWeatherTableDto>? GetHistoricalWeather(
        string table, string query, CancellationToken ct) => throw new NotImplementedException();

    /// <inheritdoc />
    public async Task<List<WeatherConditionResult>> GetHourlyForecastAsync(
        double latitude,
        double longitude,
        double altitude,
        UnitSystem unitSystem,
        CancellationToken ct = default)
    {
        try
        {
            return await _memoryCache.GetOrCreateAsync(
                key: $"{WeatherService.IBM}-{latitude}-{longitude}-forecast",
                factory: async (entry) =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);

                    // Get forecast depending on the unit system
                    IBMHourlyForecastResponse? ibmForecast = await _ibmWeatherApi.GetHourlyWeatherForecast(
                        latitude: latitude,
                        longitude: longitude,
                        days: 15,
                        unit: unitSystem == UnitSystem.Imperial ? "e" : "m",
                        apiKey: _weatherApiOptions.ApiKey);

                    if (ibmForecast is null)
                    {
                        _logger.LogError("IBM Weather API Error.");
                        return [];
                    }

                    // Map the IBM forecast to the weather condition result
                    return unitSystem switch
                    {
                        UnitSystem.Imperial => [.. ibmForecast.validTimeLocal
                            .Select((t, index) =>
                            {
                                MeanSeaLevelPressure mslp = new(
                                    Value: ibmForecast.pressureMeanSeaLevel[index],
                                    EngUnits: PressureEngUnit.InchOfMercury);

                                // Convert from mslp to abs press (mbar) then to psi unit
                                AbsolutePressure absPress = mslp
                                        .ToAbsolutePressure(
                                            altitude: AltitudeUnitConverter.ToMeters(altitude),
                                            temp: new Temperature(
                                                Value: ibmForecast.temperature[index],
                                                EngUnits: TemperatureEngUnit.Fahrenheit));
                                AbsolutePressure absPressInPsi = new(
                                    Value: absPress.ToPsi().Value, EngUnits: PressureEngUnit.Psi);

                                return new WeatherConditionResult
                                {
                                    ApiName = _weatherApiOptions.ApiName,
                                    // NOTE - Remove the timezone (ex: -0600) from (ex: "2024-03-06T11:00:00-0600")
                                    LocalTime = t[..^5],
                                    UnixTimeInSeconds = ibmForecast.validTimeUtc[index],
                                    Description = ibmForecast.wxPhraseLong[index],
                                    WeatherIcon = ibmForecast.iconCode[index],
                                    RelativeHumidity = ibmForecast.relativeHumidity[index],
                                    Temp = new Temperature(
                                        Value: ibmForecast.temperature[index],
                                        EngUnits: TemperatureEngUnit.Fahrenheit
                                    ).Format(),
                                    MeanSeaLevelPressure = (MeanSeaLevelPressure)mslp.Format(),
                                    AbsolutePressure = (AbsolutePressure)absPressInPsi.Format(),
                                    WindSpd = new Velocity(
                                        Value: ibmForecast.windSpeed[index],
                                        EngUnits: VelocityEngUnit.MilesPerHour),
                                    WindDirection = new WindDirectionDto
                                    {
                                        Degrees = ibmForecast.windDirection[index],
                                        Localized = ibmForecast.windDirectionCardinal[index],
                                    },
                                    WetBulbTemp = null,
                                    Precipitation = new Precipitation(
                                        Value: ibmForecast.qpf[index],
                                        EngUnits: PrecipitationEngUnit.Inches),
                                    UvIndex = ibmForecast.uvIndex[index],
                                };
                            })],
                        UnitSystem.Metric => ibmForecast.validTimeLocal
                            .Select((t, index) =>
                            {
                                // IBM returns the mslp
                                MeanSeaLevelPressure mslp = new(
                                    Value: ibmForecast.pressureMeanSeaLevel[index],
                                    EngUnits: PressureEngUnit.Millibar);
                                Temperature temp = new(
                                    Value: ibmForecast.temperature[index],
                                    EngUnits: TemperatureEngUnit.Celsius);
                                AbsolutePressure absPress = mslp.ToAbsolutePressure(
                                        altitude: AltitudeUnitConverter.ToMeters(altitude),
                                        temp: temp);
                                Velocity windSpd = new(
                                    Value: ibmForecast.windSpeed[index],
                                    EngUnits: VelocityEngUnit.KilometersPerHour);
                                WindDirectionDto windDir = new()
                                {
                                    Degrees = ibmForecast.windDirection[index],
                                    Localized = ibmForecast.windDirectionCardinal[index],
                                };
                                Precipitation precip = new(
                                    Value: ibmForecast.qpf[index],
                                    EngUnits: PrecipitationEngUnit.Millimeters);
                                // Return the weather condition result
                                return new WeatherConditionResult
                                {
                                    ApiName = _weatherApiOptions.ApiName,
                                    LocalTime = t[..^5],
                                    UnixTimeInSeconds = ibmForecast.validTimeUtc[index],
                                    Description = ibmForecast.wxPhraseLong[index],
                                    WeatherIcon = ibmForecast.iconCode[index],
                                    RelativeHumidity = ibmForecast.relativeHumidity[index],
                                    Temp = temp.Format(),
                                    MeanSeaLevelPressure = (MeanSeaLevelPressure)mslp
                                        .ToMBar()
                                        .Format(),
                                    AbsolutePressure = (AbsolutePressure)absPress
                                        .ToMBar()
                                        .Format(),
                                    WindSpd = windSpd,
                                    WindDirection = windDir,
                                    WetBulbTemp = null,
                                    Precipitation = precip,
                                    UvIndex = ibmForecast.uvIndex[index],
                                };
                            }).ToList(),
                        _ => [],
                    };
                }
            ) ?? [];
        }
        catch (Exception e)
        {
            _logger.LogError(e, "IBM Weather API Error.");
            return [];
        }
    }

    public async Task<List<DailyForecastResponse>> GetDailyForecastAsync(
        double latitude,
        double longitude,
        UnitSystem unitSystem,
        CancellationToken ct = default)
    {
        try
        {
            return await _memoryCache.GetOrCreateAsync(
                key: $"{WeatherService.IBM}-daily-{latitude}-{longitude}-forecast",
                factory: async (entry) =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);

                    // Get forecast depending on the unit system
                    IBMDailyForecastResponse? ibmForecast = await _ibmWeatherApi.GetDailyWeatherForecast(
                        latitude: latitude,
                        longitude: longitude,
                        unit: unitSystem == UnitSystem.Imperial ? "e" : "m",
                        apiKey: _weatherApiOptions.ApiKey);

                    if (ibmForecast is null)
                    {
                        _logger.LogError("IBM Weather API Error.");
                        return [];
                    }

                    List<DailyForecastResponse> dailyForecast = [];
                    foreach (int dayIndex in Enumerable.Range(0, ibmForecast.DayOfWeek.Count))
                    {
                        if (ibmForecast.Daypart[0].IconCode[dayIndex * 2] is null)
                        {
                            continue;
                        }

                        // For daily forecast, we only need to get the day part (7AM-7PM) which is the even indices in the daypart arrays
                        dailyForecast.Add(unitSystem switch
                        {
                            UnitSystem.Imperial => new DailyForecastResponse(
                                Api: WeatherService.IBM,
                                UnixTimeInSeconds: ibmForecast.ValidTimeUtc[dayIndex],
                                LocalTime: ibmForecast.ValidTimeLocal[dayIndex],
                                IconCode: ibmForecast.Daypart[0].IconCode[dayIndex * 2],
                                Description: ibmForecast.Daypart[0].Narrative[dayIndex * 2] ?? string.Empty,
                                ShortDescription: ibmForecast.Daypart[0].WxPhraseLong[dayIndex * 2] ?? string.Empty,
                                PrecipType: ibmForecast.Daypart[0].PrecipType[dayIndex * 2] ?? string.Empty,
                                PrecipProbability: ibmForecast.Daypart[0].PrecipChance[dayIndex * 2],
                                TemperatureMin: new Temperature(ibmForecast.TemperatureMin[dayIndex], TemperatureEngUnit.Fahrenheit),
                                TemperatureMax: ibmForecast.TemperatureMax[dayIndex] is null ? null : new Temperature(ibmForecast.TemperatureMax[dayIndex]!.Value, TemperatureEngUnit.Fahrenheit),
                                RelativeHumid: ibmForecast.Daypart[0].RelativeHumidity[dayIndex * 2] is null ? null : new RelativeHumid(ibmForecast.Daypart[0].RelativeHumidity[dayIndex * 2]!.Value),
                                UvDescription: ibmForecast.Daypart[0].UvDescription[dayIndex * 2] ?? string.Empty,
                                WindPhrase: ibmForecast.Daypart[0].WindPhrase[dayIndex * 2] ?? string.Empty),
                            _ => new DailyForecastResponse(
                                Api: WeatherService.IBM,
                                UnixTimeInSeconds: ibmForecast.ValidTimeUtc[dayIndex],
                                LocalTime: ibmForecast.ValidTimeLocal[dayIndex],
                                IconCode: ibmForecast.Daypart[0].IconCode[dayIndex * 2],
                                Description: ibmForecast.Daypart[0].Narrative[dayIndex * 2] ?? string.Empty,
                                ShortDescription: ibmForecast.Daypart[0].WxPhraseLong[dayIndex * 2] ?? string.Empty,
                                PrecipType: ibmForecast.Daypart[0].PrecipType[dayIndex * 2] ?? string.Empty,
                                PrecipProbability: ibmForecast.Daypart[0].PrecipChance[dayIndex * 2],
                                TemperatureMin: new Temperature(ibmForecast.TemperatureMin[dayIndex], TemperatureEngUnit.Celsius),
                                TemperatureMax: ibmForecast.TemperatureMax[dayIndex] is null ? null : new Temperature(ibmForecast.TemperatureMax[dayIndex]!.Value, TemperatureEngUnit.Celsius),
                                RelativeHumid: ibmForecast.Daypart[0].RelativeHumidity[dayIndex * 2] is null ? null : new RelativeHumid(ibmForecast.Daypart[0].RelativeHumidity[dayIndex * 2]!.Value),
                                UvDescription: ibmForecast.Daypart[0].UvDescription[dayIndex * 2] ?? string.Empty,
                                WindPhrase: ibmForecast.Daypart[0].WindPhrase[dayIndex * 2] ?? string.Empty),
                        });
                    }

                    return dailyForecast;
                }
            ) ?? [];
        }
        catch (Exception e)
        {
            _logger.LogError(e, "IBM Weather API Error.");
            return [];
        }
    }
}

