using Refit;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather.Response;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api.IBMWeather;

public interface IIBMWeatherApi
{
    [Get("/v3/wx/observations/current?geocode={latitude},{longitude}&units={unit}&language=en-US&format=json&apiKey={apiKey}")]
    Task<IBMWeatherObservationResponse> GetCurrentObservations(
        double latitude,
        double longitude,
        string unit,
        string apiKey);

    [Get("/v3/location/near?geocode={latitude},{longitude}&product=pws&format=json&apiKey={apiKey}")]
    Task<IBMLocation> GetNearbyWeatherStations(
        double latitude,
        double longitude,
        string apiKey);

    [Get("/v3/wx/forecast/hourly/{days}day?geocode={latitude},{longitude}&format=json&units={unit}&language=en-US&apiKey={apiKey}")]
    Task<IBMHourlyForecastResponse> GetHourlyWeatherForecast(
        double latitude,
        double longitude,
        int days, // either 2 or 15 days
        string unit,
        string apiKey);

    [Get("/v3/wx/forecast/daily/15day?geocode={latitude},{longitude}&format=json&units={unit}&language=en-US&apiKey={apiKey}")]
    Task<IBMDailyForecastResponse> GetDailyWeatherForecast(
        double latitude,
        double longitude,
        string unit,
        string apiKey);
}
