using Refit;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

public interface IAccuWeatherApi
{
    [Get("/currentconditions/v1/{locationKey}?apikey={apiKey}&details=true")]
    Task<IEnumerable<AccuWeatherConditionResult>> GetCurrentConditions(
        string locationKey,
        string apiKey);

    [Get("/locations/v1/cities/neighbors/{locationKey}?apikey={apiKey}")]
    Task<IEnumerable<AwLocationResult>> GetLocation(
        string locationKey,
        string apiKey);

    [Get("/locations/v1/cities/geoposition/search?apikey={apiKey}&q={latitude},{longitude}")]
    Task<AwLocationResult> GetLocationKeyFromLatLng(
        string apiKey,
        double latitude,
        double longitude);
}
