using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

/// <summary>
/// Factory to get the weather service based on the weather service type
/// </summary>
public class WeatherServiceFactory(IEnumerable<IWeatherApi> weatherServices) : IWeatherServiceFactory
{
    // Get the list of all the weather services injected
    private readonly IEnumerable<IWeatherApi> _weatherServices = weatherServices;

    /// <summary>
    /// Depending on the weather service type, get the weather service that is applicable
    /// </summary>
    /// <param name="weatherService">the weather service type</param>
    /// <returns>the weather service</returns>
    public IWeatherApi Get(WeatherService weatherService)
        => _weatherServices.First(x => x.IsApplicable(weatherService));
}
