using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

public interface IWeatherApi
{
    /// <summary>
    /// Check if the weather service is applicable for the weather service type
    /// </summary>
    /// <param name="weatherService">The weather service type (AW, IBM, etc...)
    /// <returns>True if the weather service is applicable, false otherwise</returns>
    bool IsApplicable(WeatherService weatherService);

    /// <summary>
    /// Given the lat/lng of a location
    /// Then we should find all the nearby weather stations of the location 
    /// </summary>
    /// <param name="latitude">the latitude of the location</param>
    /// <param name="longitude">the longitude of the location</param>
    /// <returns>WeatherStationDto</returns>
    Task<List<WeatherStationByLatLngResponse>> GetNearbyWeatherStations(double latitude, double longitude);

    /// <summary>
    /// Given the lat/lng of a location
    /// Then we should get the current weather conditions of the location 
    /// </summary>
    /// <param name="latitude">the latitude of the location</param>
    /// <param name="longitude">the longitude of the location</param>
    /// <param name="latitude">the altitude of the location (used to convert mean sea level press to abs press) or vice versa</param>
    /// <returns>WeatherDto object</returns>
    Task<WeatherConditionResult?> GetCurrentConditionsAsync(
        double latitude,
        double longitude,
        double altitude,
        CancellationToken ct);

    /// <summary>
    /// Given a table and a query
    /// Then we should get the historical weather conditions from the table storage based on the query condition
    /// </summary>
    /// <returns>list of HistoricalWeatherTableDto</returns>
    List<HistoricalWeatherTableDto>? GetHistoricalWeather(
        string table,
        string query,
        CancellationToken ct);

    /// <summary>
    /// Given the lat/long of a location/weather station
    /// Then we should get the weather forecasts for that location
    /// </summary>
    /// <param name="latitude">the latitude of the location</param>
    /// <param name="longitude">the longitude of the location</param>
    /// <param name="altitude">the altitude of the location (this is to compute absolute pressure/mslp)</param>
    /// <returns>a list of weather forecasts data for the next 12hr</returns>
    Task<List<WeatherConditionResult>> GetHourlyForecastAsync(
        double latitude,
        double longitude,
        double altitude,
        UnitSystem unitSystem,
        CancellationToken ct = default);

    Task<List<DailyForecastResponse>> GetDailyForecastAsync(
        double latitude,
        double longitude,
        UnitSystem unitSystem,
        CancellationToken ct = default);
}

