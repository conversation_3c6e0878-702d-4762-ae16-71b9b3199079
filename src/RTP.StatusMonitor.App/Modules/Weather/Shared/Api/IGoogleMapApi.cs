using Refit;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.GoogleMap;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
public interface IGoogleMapApi
{
    [Get("/maps/api/elevation/json?locations={latitude},{longitude}&key={key}")]
    Task<GoogleMapElevationResponseDto> GetElevation(
        double latitude,
        double longitude,
        string key,
        CancellationToken ct);
}