using Refit;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.OpenWeather;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

public interface IOpenWeatherApi
{
    [Get("/onecall?lat={latitude}&lon={longitude}&exclude=current,minutely,daily,alerts&units={units}&appid={apiKey}")]
    internal Task<OpenWeatherForecastDto> GetHourlyForecast(
        double latitude,
        double longitude,
        string units, // metric or imperial
        string apiKey);

    [Get("/onecall?lat={latitude}&lon={longitude}&exclude=minutely,hourly,daily,alerts&units={units}&appid={apiKey}")]
    internal Task<OpenWeatherCurrentConditionDto> GetCurrentConditions(
        double latitude,
        double longitude,
        string units,
        string apiKey);
}