namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

public record AdministrativeArea
{
    public string ID { get; set; } = string.Empty;
    public string LocalizedName { get; set; } = string.Empty;
    public string EnglishName { get; set; } = string.Empty;
    public int Level { get; set; }
    public string LocalizedType { get; set; } = string.Empty;
    public string EnglishType { get; set; } = string.Empty;
    public string CountryID { get; set; } = string.Empty;
}

public record Country
{
    public string ID { get; set; } = string.Empty;
    public string LocalizedName { get; set; } = string.Empty;
    public string EnglishName { get; set; } = string.Empty;
}

public record Elevation
{
    public AccuWeatherMetric Metric { get; set; } = new AccuWeatherMetric();
    public AccuWeatherImperial Imperial { get; set; } = new AccuWeatherImperial();
}

public record GeoPosition
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public Elevation Elevation { get; set; } = new Elevation();
}

public record AccuWeatherImperial
{
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int UnitType { get; set; }
}

public record AccuWeatherMetric
{
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int UnitType { get; set; }
}

public record Region
{
    public string ID { get; set; } = string.Empty;
    public string LocalizedName { get; set; } = string.Empty;
    public string EnglishName { get; set; } = string.Empty;
}

public record SupplementalAdminArea
{
    public int Level { get; set; }
    public string LocalizedName { get; set; } = string.Empty;
    public string EnglishName { get; set; } = string.Empty;
}

public record TimeZone
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public double GmtOffset { get; set; }
    public bool IsDaylightSaving { get; set; }
    public DateTime NextOffsetChange { get; set; }
}

public record AwLocationResult
{
    public int Version { get; set; }
    public string Key { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Rank { get; set; }
    public string LocalizedName { get; set; } = string.Empty;
    public string EnglishName { get; set; } = string.Empty;
    public string PrimaryPostalCode { get; set; } = string.Empty;
    public Region Region { get; set; } = new Region();
    public Country Country { get; set; } = new Country();
    public AdministrativeArea AdministrativeArea { get; set; } = new AdministrativeArea();
    public TimeZone TimeZone { get; set; } = new TimeZone();
    public GeoPosition GeoPosition { get; set; } = new GeoPosition();
    public bool IsAlias { get; set; }
    public List<SupplementalAdminArea> SupplementalAdminAreas { get; set; } = [];
    public List<string> DataSets { get; set; } = [];
}
