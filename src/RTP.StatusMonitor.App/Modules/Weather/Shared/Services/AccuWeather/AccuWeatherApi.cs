using Azure.Data.Tables;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

public class AccuWeatherApi(
    IOptions<AccuWeatherApiOptions> weatherApiOptions,
    IOptions<TableStorageOptions> tableStorageOptions,
    ILogger<AccuWeatherApi> logger,
    IMemoryCache memoryCache) : IWeatherApi
{
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly TimeSpan CURRENT_CONDITION_CACHE_DURATION = TimeSpan.FromMinutes(5);
    private readonly AccuWeatherApiOptions _awWeatherApiOptions = weatherApiOptions.Value;
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;
    private readonly IAccuWeatherApi _accuWeatherApi = RestService.For<IAccuWeatherApi>(weatherApiOptions.Value.BaseUri);
    private readonly ILogger<AccuWeatherApi> _logger = logger;

    /// <inheritdoc/>
    public bool IsApplicable(WeatherService weatherService)
    => weatherService == WeatherService.AW;

    /// <inheritdoc/>
    public async Task<WeatherConditionResult?> GetCurrentConditionsAsync(
        double latitude,
        double longitude,
        double altitude,
        CancellationToken ct)
    {
        // Get location key of the lat/long from AccuWeather
        string? locationKey = await GetLocationKeyFromLatLng(latitude, longitude);

        // Query the weather conditions using the location key
        return locationKey is null
            ? null
            : await GetCurrentConditionAsync(
                int.Parse(locationKey),
                altitude,
                ct);
    }

    /// <summary>
    /// AccuWeather use location key instead of lat/long.
    /// Query the weather conditions using the location key.
    /// </summary>
    /// 
    /// <param name="locationKey">The location of a particular location (lat/long</param>
    /// <param name="altitude">The altitude of the location</param>
    /// <param name="ct"></param>
    /// 
    /// <returns></returns>
    private async Task<WeatherConditionResult?> GetCurrentConditionAsync(
        int locationKey,
        double altitude,
        CancellationToken ct)
    {
        try
        {
            return await _memoryCache.GetOrCreateAsync(
                key: $"{locationKey}",
                factory: async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = CURRENT_CONDITION_CACHE_DURATION;

                    // Get the weather conditions at the location
                    IEnumerable<AccuWeatherConditionResult> content = await _accuWeatherApi.GetCurrentConditions(
                        locationKey: locationKey.ToString(),
                        apiKey: _awWeatherApiOptions.ApiKey);

                    if (content is null)
                        return null;

                    // Only 1 current condition
                    AccuWeatherConditionResult condition = content.ToList()[0];
                    MeanSeaLevelPressure mslp = (MeanSeaLevelPressure)
                        new MeanSeaLevelPressure(
                            Value: condition.Pressure.Imperial.Value,
                            EngUnits: PressureEngUnit.InchOfMercury)
                        .Format();
                    AbsolutePressure? absPress = (AbsolutePressure)
                        mslp.ToAbsolutePressure(
                            altitude: AltitudeUnitConverter.ToMeters(altitude),
                            temp: new Domain.EngUnits.Types.Temperature(
                                Value: condition.Temperature.Imperial.Value,
                                EngUnits: TemperatureEngUnit.Fahrenheit))
                            .Format();
                    return new WeatherConditionResult
                    {
                        ApiName = _awWeatherApiOptions.ApiName,
                        LocalTime = condition.LocalObservationDateTime
                            .Substring(0, condition.LocalObservationDateTime.Length - 6),
                        UnixTimeInSeconds = condition.EpochTime,
                        Description = condition.WeatherText,
                        WeatherIcon = condition.WeatherIcon,
                        Temp = new Domain.EngUnits.Types.Temperature(
                            Value: condition.Temperature.Imperial.Value,
                            EngUnits: TemperatureEngUnit.Fahrenheit),
                        WetBulbTemp = new Domain.EngUnits.Types.Temperature(
                            Value: condition.WetBulbTemperature.Imperial.Value,
                            EngUnits: TemperatureEngUnit.Fahrenheit).Format(),
                        RelativeHumidity = new RelativeHumid(condition.RelativeHumidity).Value,
                        MeanSeaLevelPressure = mslp,
                        AbsolutePressure = absPress,
                        WindSpd = new Velocity(
                            Value: condition.Wind.Speed.Imperial.Value,
                            EngUnits: VelocityEngUnit.MilesPerHour),
                        WindDirection = new WindDirectionDto
                        {
                            Degrees = condition.Wind.Direction.Degrees,
                            Localized = condition.Wind.Direction.Localized
                        },
                        Precipitation = new Domain.EngUnits.Types.Precipitation(
                            Value: condition.PrecipitationSummary.Precipitation.Imperial.Value,
                            EngUnits: PrecipitationEngUnit.Inches),
                        UvIndex = condition.UVIndex
                    };
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting current weather conditions.");
            return null;
        }
    }

    public List<HistoricalWeatherTableDto>? GetHistoricalWeather(
        string table,
        string query,
        CancellationToken ct)
    {
        try
        {
            // Given the table storing historical weather data and the query to get the data

            // First we will create a client to access the table
            var tableClient = new TableClient(
                new Uri(_tableStorageOptions.Uri),
                table,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            // Then we will query the table for the data
            var res = tableClient.Query<HistoricalWeatherTableDto>(
                filter: query,
                maxPerPage: 1000,
                select: new[]
                {
                    "LocalTimestamp", "Temp", "RH",
                    "Press", "PressAbs", "UVIndex", "Icon",
                    "Precipitation", "WindSpeed", "WindDirection"
                },
                cancellationToken: ct);

            // Then we will return the data
            return res.ToList();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting historical weather conditions.");
            return null;
        }
    }

    public async Task<List<WeatherStationByLatLngResponse>> GetNearbyWeatherStations(
        double latitude,
        double longitude)
    {
        try
        {
            // Given the lng and lat
            // Then we need to get the location key from accuweather
            var locationKey = await GetLocationKeyFromLatLng(latitude, longitude);

            // When failed to get location key
            // Then return no weather station for this location
            if (locationKey is null)
                return Enumerable.Empty<WeatherStationByLatLngResponse>().ToList();

            // Then we can use the location key to get the nearby weather stations
            var content = await _accuWeatherApi.GetLocation(locationKey, _awWeatherApiOptions.ApiKey);

            // When the content is null
            // Then log the error and return empty list
            if (content is null)
            {
                _logger.LogError("AccuWeather API Error. Failed to get nearby weather stations for location key: {locationKey}", locationKey);
                return Enumerable.Empty<WeatherStationByLatLngResponse>().ToList();
            }

            // Otherwise, return the list of weather stations
            return content.Select(x => new WeatherStationByLatLngResponse
            {
                StationName = x.LocalizedName,
                ApiName = _awWeatherApiOptions.ApiName,
                LocationKey = x.Key,
                Latitude = x.GeoPosition.Latitude,
                Longitude = x.GeoPosition.Longitude,
                Elevation = new ElevationDto
                {
                    Imperial =
                    {
                        Value = x.GeoPosition.Elevation.Imperial.Value,
                        Unit = x.GeoPosition.Elevation.Imperial.Unit
                    },
                    Metric =
                    {
                        Value = x.GeoPosition.Elevation.Metric.Value,
                        Unit = x.GeoPosition.Elevation.Metric.Unit
                    }
                },
                State = x.Region.ID,
                City = x.Region.EnglishName
            }).ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }

    // TODO - need to implement forecast for AW (API limited to 12hrs)
    public async Task<List<WeatherConditionResult>> GetHourlyForecastAsync(
        double latitude,
        double longitude,
        double altitude,
        UnitSystem unitSystem,
        CancellationToken ct)
        => Task.FromResult(new List<WeatherConditionResult>()).Result;

    /// <summary>
    /// Get the location key from accuweather using lat/lng
    /// </summary>
    /// 
    /// <param name="latitude">the latitude of the current location</param>
    /// <param name="longitude">the longitude of the current location</param>
    /// 
    /// <returns>The location key of the first item in the list of weather stations</returns>
    public async Task<string?> GetLocationKeyFromLatLng(
        double latitude, double longitude)
    {
        try
        {
            // Given the lat/lng of a location
            // Then we need to get the location key from accuweather
            AwLocationResult? content = await _accuWeatherApi.GetLocationKeyFromLatLng(
                _awWeatherApiOptions.ApiKey, latitude, longitude);

            return content?.Key;
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                "AccuWeather API Error.Error getting location key from lat: {latitude}, lng: {longitude}",
                latitude,
                longitude);

            return null;
        }
    }

    public Task<List<DailyForecastResponse>> GetDailyForecastAsync(
        double latitude,
        double longitude,
        UnitSystem unitSystem,
        CancellationToken ct)
        => Task.FromResult(new List<DailyForecastResponse>());
}

