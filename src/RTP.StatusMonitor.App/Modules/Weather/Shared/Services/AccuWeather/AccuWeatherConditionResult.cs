namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

public record Direction(int Degrees, string Localized, string English);
public record Imperial(double Value, string Unit, int UnitType, string Phrase);

public record Metric(double Value, string Unit, int UnitType, string Phrase);

public record Precipitation(Metric Metric, Imperial Imperial);
public record Pressure(Metric Metric, Imperial Imperial);


public record Speed(Metric Metric, Imperial Imperial);
public record Temperature(Metric Metric, Imperial Imperial);

public record WetBulbTemperature(Metric Metric, Imperial Imperial);
public record Wind(Direction Direction, Speed Speed);

public record PrecipitationSummary
{
    public Precipitation Precipitation { get; set; } = null!;
}

public record AccuWeatherConditionResult
{
    public string LocalObservationDateTime { get; set; } = string.Empty;
    public int EpochTime { get; set; }
    public string WeatherText { get; set; } = string.Empty;
    public int WeatherIcon { get; set; }
    public Temperature Temperature { get; set; } = null!;
    public int RelativeHumidity { get; set; }
    public Pressure Pressure { get; set; } = null!;
    public Wind Wind { get; set; } = null!;
    public PrecipitationSummary PrecipitationSummary { get; set; } = null!;
    public WetBulbTemperature WetBulbTemperature { get; set; } = null!;
    public int UVIndex { get; set; }
}