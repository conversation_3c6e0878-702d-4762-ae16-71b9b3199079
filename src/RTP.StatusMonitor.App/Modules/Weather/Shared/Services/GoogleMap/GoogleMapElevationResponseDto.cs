namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.GoogleMap;

public class GoogleMapLocationResponseDto
{
    public double lat { get; set; }
    public double lng { get; set; }
}

public class GoogleMapElevationResultResponseDto
{
    public double elevation { get; set; }
    public GoogleMapLocationResponseDto location { get; set; } = new();
    public double resolution { get; set; }
}

public class GoogleMapElevationResponseDto
{
    public List<GoogleMapElevationResultResponseDto> results { get; set; } = Enumerable.Empty<GoogleMapElevationResultResponseDto>().ToList();
    public string status { get; set; } = string.Empty;
}
