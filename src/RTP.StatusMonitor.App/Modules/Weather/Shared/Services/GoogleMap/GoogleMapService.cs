using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.GoogleMap;

public class GoogleMapService : IMapService
{
    private readonly GoogleMapApiOptions _googleMapApiOptions;
    private readonly IGoogleMapApi _googleMapApi;
    private readonly ILogger<GoogleMapService> _logger;
    public GoogleMapService(
        IOptions<GoogleMapApiOptions> googleMapApiOptions,
        ILogger<GoogleMapService> logger)
    {
        _googleMapApi = RestService.For<IGoogleMapApi>(googleMapApiOptions.Value.BaseUri);
        _googleMapApiOptions = googleMapApiOptions.Value;
        _logger = logger;
    }
    public async Task<double> GetLocationElevation(Geolocation coordinates, CancellationToken ct)
    {
        try
        {
            // Given the lat/lng of a location, get the elevation of the location from Google Map API
            GoogleMapElevationResponseDto? content = await _googleMapApi.GetElevation(
                coordinates.Latitude,
                coordinates.Longitude,
                _googleMapApiOptions.ApiKey,
                ct);

            // If the response is null or empty, we log the error and return null
            if (content is null || !content.results.Any())
            {
                throw new Exception("Google Map API returned null or empty response");
            }

            return content.results[0].elevation;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while getting location elevation");
            throw new Exception("Error while getting location elevation from Google Map API");
        }
    }
}