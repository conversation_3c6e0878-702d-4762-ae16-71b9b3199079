namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.OpenWeather;

internal record OpenWeatherForecastDto(
    double lat,
    double lon,
    string timezone,
    int timezone_offset,
    List<Hourly> hourly);

internal record Hourly
{
    /// <summary>
    /// Epoch time (unix timestamp)
    /// </summary>
    public int dt { get; set; }
    public double temp { get; set; }
    public double feels_like { get; set; }
    public int pressure { get; set; }
    public int humidity { get; set; }
    public double dew_point { get; set; }
    public double uvi { get; set; }
    public int clouds { get; set; }
    public int visibility { get; set; }
    public double wind_speed { get; set; }
    public int wind_deg { get; set; }
    public double wind_gust { get; set; }
    public List<Weather> weather { get; set; } = new();
    public double pop { get; set; }
}

internal record Weather
{
    public int id { get; set; }
    public string main { get; set; } = string.Empty;
    public string description { get; set; } = string.Empty;
    public string icon { get; set; } = string.Empty;
}