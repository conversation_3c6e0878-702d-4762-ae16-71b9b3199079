using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using TimeZoneConverter;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.OpenWeather;

public class OpenWeatherApi : IWeatherApi
{
    private readonly OpenWeatherApiOptions _weatherApiOptions;
    private readonly IOpenWeatherApi _weatherApi;
    private readonly IMemoryCache _memoryCache;
    private readonly IDistanceService _distanceService;

    public OpenWeatherApi(
        IMemoryCache memoryCache,
        IOptions<OpenWeatherApiOptions> weatherApiOptions,
        IDistanceService distanceService)
    {
        _memoryCache = memoryCache;
        _weatherApiOptions = weatherApiOptions.Value;
        _weatherApi = RestService.For<IOpenWeatherApi>(
            _weatherApiOptions.BaseUri);
        _distanceService = distanceService;
    }

    /// <inheritdoc />
    public async Task<WeatherConditionResult?> GetCurrentConditionsAsync(
        double latitude,
        double longitude,
        double altitude,
        CancellationToken ct)
    {
        try
        {
            // Get forecast in imperial unit
            OpenWeatherCurrentConditionDto currentConditionImperial = await _weatherApi
                .GetCurrentConditions(
                    latitude: latitude,
                    longitude: longitude,
                    units: "imperial",
                    apiKey: _weatherApiOptions.ApiKey);

            OpenWeatherCurrentConditionDto currentConditionMetric = await _weatherApi
                .GetCurrentConditions(
                    latitude: latitude,
                    longitude: longitude,
                    units: "metric",
                    apiKey: _weatherApiOptions.ApiKey);

            if (currentConditionImperial is null || currentConditionMetric is null)
                return null;

            Temperature temp = new Temperature(
                    Value: currentConditionImperial.current.temp,
                    EngUnits: TemperatureEngUnit.Fahrenheit)
                .Format();
            MeanSeaLevelPressure mslp = (MeanSeaLevelPressure)
                new MeanSeaLevelPressure(
                    Value: currentConditionImperial.current.pressure,
                    EngUnits: PressureEngUnit.HectoPascal)
                .Format();
            AbsolutePressure absPress = (AbsolutePressure)mslp
                .ToAbsolutePressure(
                    altitude: AltitudeUnitConverter.ToMeters(altitude),
                    temp: temp)
                .Format();

            string timeZoneId = TZConvert.IanaToWindows(currentConditionImperial.timezone);
            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);

            return new WeatherConditionResult
            {
                ApiName = _weatherApiOptions.ApiName,
                // Convert epoch time to local time using timezone info
                LocalTime = TimeZoneInfo
                    .ConvertTimeFromUtc(
                        dateTime: DateTimeOffset.FromUnixTimeSeconds(currentConditionImperial.current.dt).UtcDateTime,
                        destinationTimeZone: timeZoneInfo)
                    .ToString(),
                UnixTimeInSeconds = currentConditionImperial.current.dt,
                Description = currentConditionImperial.current.weather[0].description,
                WeatherIcon = 0,
                RelativeHumidity = currentConditionImperial.current.humidity,
                Temp = temp.Format(),
                MeanSeaLevelPressure = mslp,
                AbsolutePressure = absPress,
                WindSpd = new Velocity(
                    Value: currentConditionImperial.current.wind_speed,
                    EngUnits: VelocityEngUnit.MilesPerHour),
                WindDirection = new WindDirectionDto
                {
                    Degrees = currentConditionImperial.current.wind_deg,
                    Localized = _distanceService.ConvertCompassDegreesToText(currentConditionImperial.current.wind_deg),
                },
                WetBulbTemp = null,
                // WetBulbTemperature = null,
                Precipitation = null,
                UvIndex = Convert.ToInt16(currentConditionImperial.current.uvi),
            };

        }
        catch { return null; }
    }

    /// <inheritdoc />
    public async Task<List<WeatherConditionResult>> GetHourlyForecastAsync(
       double latitude,
       double longitude,
       double altitude,
       UnitSystem unitSystem,
       CancellationToken ct)
    {
        try
        {
            return await _memoryCache.GetOrCreateAsync(
                key: $"{WeatherService.OW}-{latitude}-{longitude}-forecast",
                factory: async (entry) =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);

                    // Get forecast in imperial unit
                    OpenWeatherForecastDto forecastData = await _weatherApi
                        .GetHourlyForecast(
                            latitude: latitude,
                            longitude: longitude,
                            units: unitSystem == UnitSystem.Imperial ? "imperial" : "metric",
                            apiKey: _weatherApiOptions.ApiKey);

                    if (forecastData is null)
                    {
                        return Enumerable.Empty<WeatherConditionResult>().ToList();
                    }

                    // Get the timezone from the forecast data
                    string timeZoneId = TZConvert
                        .IanaToWindows(forecastData.timezone);

                    // Get the timezone info
                    TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);

                    // Map the forecast data to the weather condition result
                    return unitSystem switch
                    {
                        UnitSystem.Imperial => forecastData.hourly
                            .Select((hourlyForecast, index) =>
                            {
                                Temperature temp = new(
                                    Value: hourlyForecast.temp,
                                    EngUnits: TemperatureEngUnit.Fahrenheit);
                                MeanSeaLevelPressure mslp = new(
                                    Value: hourlyForecast.pressure,
                                    EngUnits: PressureEngUnit.HectoPascal);

                                return new WeatherConditionResult
                                {
                                    ApiName = _weatherApiOptions.ApiName,
                                    // Convert epoch time to local time using timezone info
                                    LocalTime = TimeZoneInfo
                                        .ConvertTimeFromUtc(
                                            dateTime: DateTimeOffset
                                                .FromUnixTimeSeconds(hourlyForecast.dt)
                                                .UtcDateTime,
                                            destinationTimeZone: timeZoneInfo)
                                        .ToString(),
                                    UnixTimeInSeconds = hourlyForecast.dt,
                                    Description = hourlyForecast.weather[0].description,
                                    // NOTE - no weather icon is provided by the OpenWeather API
                                    WeatherIcon = 0,
                                    RelativeHumidity = hourlyForecast.humidity,
                                    Temp = temp.Format(),
                                    MeanSeaLevelPressure = (MeanSeaLevelPressure)mslp.Format(),
                                    AbsolutePressure = (AbsolutePressure)mslp
                                        .ToAbsolutePressure(
                                            altitude: AltitudeUnitConverter.ToMeters(altitude),
                                            temp: temp)
                                        .ToMBar()
                                        .Format(),
                                    WindSpd = new Velocity(
                                        Value: forecastData.hourly[index].wind_speed,
                                        EngUnits: VelocityEngUnit.MilesPerHour),
                                    WindDirection = new WindDirectionDto
                                    {
                                        Degrees = hourlyForecast.wind_deg,
                                        Localized = _distanceService
                                            .ConvertCompassDegreesToText(hourlyForecast.wind_deg),
                                    },
                                    WetBulbTemp = null,
                                    Precipitation = null,
                                    UvIndex = Convert.ToInt16(hourlyForecast.uvi),
                                };
                            }).ToList(),
                        UnitSystem.Metric => forecastData.hourly
                            .Select((hourlyForecast, index) =>
                            {
                                Temperature temp = new(
                                    Value: hourlyForecast.temp,
                                    EngUnits: TemperatureEngUnit.Celsius);
                                MeanSeaLevelPressure mslp = new(
                                    Value: hourlyForecast.pressure,
                                    EngUnits: PressureEngUnit.HectoPascal);

                                // Convert epoch time to local time using timezone info
                                string localTime = TimeZoneInfo
                                    .ConvertTimeFromUtc(
                                        dateTime: DateTimeOffset.FromUnixTimeSeconds(hourlyForecast.dt).UtcDateTime,
                                        destinationTimeZone: timeZoneInfo)
                                    .ToString();

                                return new WeatherConditionResult
                                {
                                    ApiName = _weatherApiOptions.ApiName,

                                    LocalTime = localTime,
                                    UnixTimeInSeconds = hourlyForecast.dt,
                                    Description = hourlyForecast.weather[0].description,
                                    // NOTE - no weather icon is provided by the OpenWeather API
                                    WeatherIcon = 0,
                                    RelativeHumidity = hourlyForecast.humidity,
                                    Temp = temp.Format(),
                                    MeanSeaLevelPressure = (MeanSeaLevelPressure)mslp
                                        .ToMBar().Format(),
                                    AbsolutePressure = (AbsolutePressure)mslp
                                        .ToAbsolutePressure(
                                            altitude: AltitudeUnitConverter.ToMeters(altitude),
                                            temp: temp)
                                        .ToMBar()
                                        .Format(),
                                    WindSpd = new Velocity
                                    (
                                        Value: forecastData.hourly[index].wind_speed,
                                        EngUnits: VelocityEngUnit.MetersPerSecond
                                    ),
                                    WindDirection = new WindDirectionDto
                                    {
                                        Degrees = hourlyForecast.wind_deg,
                                        Localized = _distanceService
                                            .ConvertCompassDegreesToText(hourlyForecast.wind_deg),
                                    },
                                    WetBulbTemp = null,
                                    Precipitation = null,
                                    UvIndex = Convert.ToInt16(hourlyForecast.uvi),
                                };
                            }).ToList(),

                        _ => throw new ArgumentException("Invalid unit system")
                    };
                }) ?? [];
        }
        catch
        {
            return [];
        }
    }

    // TODO - to be implemented when needed
    public List<HistoricalWeatherTableDto>? GetHistoricalWeather(string table, string query, CancellationToken ct)
    => throw new NotImplementedException();

    // TODO - to be implemented when needed
    public Task<List<WeatherStationByLatLngResponse>> GetNearbyWeatherStations(double latitude, double longitude)
    => throw new NotImplementedException();

    public bool IsApplicable(WeatherService weatherService)
    => weatherService == WeatherService.OW;

    public Task<List<DailyForecastResponse>> GetDailyForecastAsync(
        double latitude,
        double longitude,
        UnitSystem unitSystem,
        CancellationToken ct)
        => Task.FromResult(new List<DailyForecastResponse>());
}
