
public class Current
{
    public int dt { get; set; }
    public int sunrise { get; set; }
    public int sunset { get; set; }
    public double temp { get; set; }
    public double feels_like { get; set; }
    public int pressure { get; set; }
    public int humidity { get; set; }
    public double dew_point { get; set; }
    public double uvi { get; set; }
    public int clouds { get; set; }
    public int visibility { get; set; }
    public double wind_speed { get; set; }
    public int wind_deg { get; set; }
    public double wind_gust { get; set; }
    public List<Weather> weather { get; set; } = new();
}

public class OpenWeatherCurrentConditionDto
{
    public double lat { get; set; }
    public double lon { get; set; }
    public string timezone { get; set; } = string.Empty;
    public int timezone_offset { get; set; }
    public Current current { get; set; } = new();
}

public class Weather
{
    public int id { get; set; }
    public string main { get; set; } = string.Empty;
    public string description { get; set; } = string.Empty;
    public string icon { get; set; } = string.Empty;
}

