using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Refit;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Config;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.TomorrowWeather;

public class TomorrowWeatherApi : IWeatherApi
{
    private readonly TomorrowWeatherApiOptions _weatherApiOptions;
    private readonly ITomorrowWeatherApi _weatherApi;
    private readonly IMemoryCache _memoryCache;
    private readonly IDistanceService _distanceService;

    public TomorrowWeatherApi(
        IMemoryCache memoryCache,
        IOptions<TomorrowWeatherApiOptions> weatherApiOptions,
        IDistanceService distanceService)
    {
        _memoryCache = memoryCache;
        _weatherApiOptions = weatherApiOptions.Value;
        _weatherApi = RestService.For<ITomorrowWeatherApi>(_weatherApiOptions.BaseUri);
        _distanceService = distanceService;
    }

    /// <inheritdoc />
    public async Task<WeatherConditionResult?> GetCurrentConditionsAsync(
        double latitude,
        double longitude,
        double altitude,
        CancellationToken ct)
    {
        try
        {
            TomorrowCurrentConditionDto currentConditionImperial = await _weatherApi.GetCurrentCondition(
                latitude: latitude,
                longitude: longitude,
                unit: "imperial",
                apiKey: _weatherApiOptions.ApiKey);
            TomorrowCurrentConditionDto currentConditionMetric = await _weatherApi.GetCurrentCondition(
                latitude: latitude,
                longitude: longitude,
                unit: "metric",
                apiKey: _weatherApiOptions.ApiKey);

            AbsolutePressure absPress = (AbsolutePressure)new AbsolutePressure(
                    Value: currentConditionImperial.data.values.pressureSurfaceLevel,
                    EngUnits: PressureEngUnit.InchOfMercury)
                .Format();
            return new WeatherConditionResult
            {
                ApiName = _weatherApiOptions.ApiName,
                // TODO - convert epoch time to utc and to local time
                // TODO - need TimeZoneInfo to convert to local time
                LocalTime = string.Empty,
                UnixTimeInSeconds = (int)((DateTimeOffset)currentConditionImperial.data.time).ToUnixTimeSeconds(),
                Description = string.Empty,
                WeatherIcon = currentConditionImperial.data.values.weatherCode,
                RelativeHumidity = Convert.ToInt16(currentConditionImperial.data.values.humidity),
                Temp = new Temperature(
                        Value: currentConditionImperial.data.values.temperature,
                        EngUnits: TemperatureEngUnit.Fahrenheit)
                    .Format(),
                AbsolutePressure = absPress,
                MeanSeaLevelPressure = null,
                WindSpd = new Velocity(
                    Value: currentConditionImperial.data.values.windSpeed,
                    EngUnits: VelocityEngUnit.MilesPerHour),
                WindDirection = new WindDirectionDto
                {
                    Degrees = Convert.ToInt16(currentConditionImperial.data.values.windDirection),
                    Localized = _distanceService.ConvertCompassDegreesToText(currentConditionImperial.data.values.windDirection),
                },
                WetBulbTemp = null,
                Precipitation = null,
                UvIndex = Convert.ToInt16(currentConditionImperial.data.values.uvIndex),
            };
        }
        catch { return null; }
    }


    /// <inheritdoc />
    public async Task<List<WeatherConditionResult>> GetHourlyForecastAsync(
        double latitude,
        double longitude,
        double altitude,
        UnitSystem unitSystem,
        CancellationToken ct)
    {
        try
        {
            return await _memoryCache.GetOrCreateAsync(
                key: $"{WeatherService.TOMORROW}-{latitude}-{longitude}-forecast",
                factory: async (entry) =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);

                    // Get forecast in imperial unit
                    TomorrowForecastDto forecastData = await _weatherApi.GetWeatherForecast(
                        latitude: latitude,
                        longitude: longitude,
                        unit: unitSystem == UnitSystem.Imperial ? "imperial" : "metric",
                        apiKey: _weatherApiOptions.ApiKey);

                    return unitSystem switch
                    {
                        UnitSystem.Imperial => forecastData.timelines.hourly
                        .Select((hourlyForecast, index) =>
                        {
                            List<Hourly> hourlyForecastsInImperial = forecastData.timelines.hourly;

                            // NOTE - API returns pressure surface level => absolute pressure => need to convert to mslp
                            AbsolutePressure absPressure = new(
                                    Value: hourlyForecastsInImperial[index].values.pressureSurfaceLevel,
                                    EngUnits: PressureEngUnit.InchOfMercury);

                            return new WeatherConditionResult
                            {
                                ApiName = _weatherApiOptions.ApiName,
                                // TODO - convert epoch time to utc and to local time
                                // TODO - need TimeZoneInfo to convert to local time
                                LocalTime = string.Empty,
                                UnixTimeInSeconds = (int)((DateTimeOffset)hourlyForecast.time).ToUnixTimeSeconds(),
                                Description = string.Empty,
                                WeatherIcon = hourlyForecast.values.weatherCode,
                                RelativeHumidity = Convert.ToInt16(hourlyForecast.values.humidity),
                                Temp = new Temperature(
                                        Value: hourlyForecastsInImperial[index].values.temperature,
                                        EngUnits: TemperatureEngUnit.Fahrenheit)
                                    .Format(),
                                AbsolutePressure = (AbsolutePressure)absPressure.Format(),
                                MeanSeaLevelPressure = null,
                                WindSpd = new Velocity(
                                    Value: hourlyForecastsInImperial[index].values.windSpeed,
                                    EngUnits: VelocityEngUnit.MilesPerHour),
                                WindDirection = new WindDirectionDto
                                {
                                    Degrees = Convert.ToInt16(hourlyForecast.values.windDirection),
                                    Localized = _distanceService.ConvertCompassDegreesToText(hourlyForecast.values.windDirection),
                                },
                                WetBulbTemp = null,
                                // WetBulbTemperature = null,
                                Precipitation = null,
                                UvIndex = Convert.ToInt16(hourlyForecast.values.uvIndex),
                            };
                        }).ToList(),
                        UnitSystem.Metric => forecastData.timelines.hourly
                        .Select((hourlyForecast, index) =>
                        {
                            List<Hourly> hourlyForecastsInImperial = forecastData.timelines.hourly;

                            // TODO - API returns pressure surface level (absolute pressure)
                            // => need to convert to mslp
                            AbsolutePressure absPressure = new(
                                    Value: hourlyForecastsInImperial[index].values.pressureSurfaceLevel,
                                    EngUnits: PressureEngUnit.HectoPascal);

                            return new WeatherConditionResult
                            {
                                ApiName = _weatherApiOptions.ApiName,
                                // TODO - convert epoch time to utc and to local time
                                // TODO - need TimeZoneInfo to convert to local time
                                LocalTime = string.Empty,
                                UnixTimeInSeconds = (int)((DateTimeOffset)hourlyForecast.time)
                                    .ToUnixTimeSeconds(),
                                Description = string.Empty,
                                WeatherIcon = hourlyForecast.values.weatherCode,
                                RelativeHumidity = Convert.ToInt16(hourlyForecast.values.humidity),
                                Temp = new Temperature(
                                        Value: hourlyForecastsInImperial[index].values.temperature,
                                        EngUnits: TemperatureEngUnit.Celsius)
                                    .Format(),
                                AbsolutePressure = (AbsolutePressure)absPressure
                                    .ToMBar()
                                    .Format(),
                                MeanSeaLevelPressure = null,
                                WindSpd = new Velocity(
                                    Value: hourlyForecastsInImperial[index].values.windSpeed,
                                    EngUnits: VelocityEngUnit.MetersPerSecond),
                                WindDirection = new WindDirectionDto
                                {
                                    Degrees = Convert.ToInt16(hourlyForecast.values.windDirection),
                                    Localized = _distanceService.ConvertCompassDegreesToText(hourlyForecast.values.windDirection),
                                },
                                WetBulbTemp = null,
                                Precipitation = null,
                                UvIndex = Convert.ToInt16(hourlyForecast.values.uvIndex),
                            };
                        }).ToList(),
                        _ => throw new ArgumentException("Invalid unit system")
                    };
                }) ?? [];
        }
        catch
        {
            return [];
        }
    }

    // TODO - to be implemented
    public List<HistoricalWeatherTableDto>? GetHistoricalWeather(string table, string query, CancellationToken ct)
    => throw new NotImplementedException();


    // TODO - to be implemented
    public Task<List<WeatherStationByLatLngResponse>> GetNearbyWeatherStations(double latitude, double longitude)
    => throw new NotImplementedException();

    public bool IsApplicable(WeatherService weatherService)
    => weatherService == WeatherService.TOMORROW;

    public Task<List<DailyForecastResponse>> GetDailyForecastAsync(
        double latitude,
        double longitude,
        UnitSystem unitSystem,
        CancellationToken ct)
    => throw new NotImplementedException();
}
