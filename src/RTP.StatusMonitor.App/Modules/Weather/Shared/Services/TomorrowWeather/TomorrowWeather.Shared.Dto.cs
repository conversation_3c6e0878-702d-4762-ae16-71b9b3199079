namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Services.TomorrowWeather;

internal record Values
{
    public double? cloudBase { get; set; }
    public double? cloudCeiling { get; set; }
    public double cloudCover { get; set; }
    public double dewPoint { get; set; }
    public double evapotranspiration { get; set; }
    public double freezingRainIntensity { get; set; }
    public double humidity { get; set; }
    public double iceAccumulation { get; set; }
    public double iceAccumulationLwe { get; set; }
    public double precipitationProbability { get; set; }
    public double pressureSurfaceLevel { get; set; }
    public double rainAccumulation { get; set; }
    public double rainAccumulationLwe { get; set; }
    public double rainIntensity { get; set; }
    public double sleetAccumulation { get; set; }
    public double sleetAccumulationLwe { get; set; }
    public double sleetIntensity { get; set; }
    public double snowAccumulation { get; set; }
    public double snowAccumulationLwe { get; set; }
    public double snowDepth { get; set; }
    public double snowIntensity { get; set; }
    public double temperature { get; set; }
    public double temperatureApparent { get; set; }
    public double uvHealthConcern { get; set; }
    public double uvIndex { get; set; }
    public double visibility { get; set; }
    public int weatherCode { get; set; }
    public double windDirection { get; set; }
    public double windGust { get; set; }
    public double windSpeed { get; set; }
}
