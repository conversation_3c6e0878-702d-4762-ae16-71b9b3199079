using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Config;

public class AccuWeatherApiOptions
{
    public const string SectionName = "AccuWeatherApiConfig";
    public string ApiName { get; init; } = WeatherService.AW.ToString();
    public string BaseUri { get; init; } = "http://dataservice.accuweather.com";
    public string ApiKey { get; init; } = null!;
}