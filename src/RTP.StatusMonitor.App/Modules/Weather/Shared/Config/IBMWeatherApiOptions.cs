using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Config;

public class IBMWeatherApiOptions
{
    public const string SectionName = "IBMWeatherApiConfig";
    public string ApiName { get; init; } = WeatherService.IBM.ToString();
    public string BaseUri { get; init; } = "https://api.weather.com";
    public string ApiKey { get; init; } = null!;
}
