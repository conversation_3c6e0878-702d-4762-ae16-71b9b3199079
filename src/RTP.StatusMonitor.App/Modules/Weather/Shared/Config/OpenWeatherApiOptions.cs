using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Config;

public class OpenWeatherApiOptions
{
    public const string SectionName = "OpenWeatherApiConfig";
    public string ApiName { get; init; } = WeatherService.OW.ToString();
    public string BaseUri { get; init; } = "https://api.openweathermap.org/data/3.0";
    public string ApiKey { get; init; } = null!;
}