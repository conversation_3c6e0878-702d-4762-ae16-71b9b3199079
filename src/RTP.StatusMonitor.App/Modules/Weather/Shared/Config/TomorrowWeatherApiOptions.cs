using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.Shared.Config;

public class TomorrowWeatherApiOptions
{
    public const string SectionName = "TomorrowWeatherApiConfig";
    public string ApiName { get; init; } = WeatherService.TOMORROW.ToString();
    public string BaseUri { get; init; } = "https://api.tomorrow.io/v4/weather";
    public string ApiKey { get; init; } = null!;
}