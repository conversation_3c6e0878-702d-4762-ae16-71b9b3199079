using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.Errors;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeatherStations;
public class AddSiteWeatherStationHandler(
    IDataContext context,
    ILogger<AddSiteWeatherStationHandler> logger) : ICommandHandler<SaveSiteWeatherStationsCommand>
{
    private readonly IDataContext _context = context;
    private readonly ILogger<AddSiteWeatherStationHandler> _logger = logger;

    public async Task<ErrorOr<Unit>> Handle(SaveSiteWeatherStationsCommand request, CancellationToken cancellationToken)
    {
        // Given the request to add a weather station to a site

        // First we need to query the site user want to add weather stations to
        Domain.Site.Site? site = await _context.Sites
            .Include(s => s.GroupPermissions)
            .Include(s => s.StationDetails)
            .FirstOrDefaultAsync(s => s.Id == request.SiteId);
        if (site is null)
        {
            return SiteErrors.NotFound;
        }

        // Then we need to check if user has permission to add weather stations to this site
        if (site.HasAccessToSite(request.UserGroupsIds) is false)
            return SiteErrors.Unauthorized;

        // Before we save the stations to site
        // We need to clear all the stations that are already in the site
        site.RemoveSavedWeatherStations();

        // Then we can add the new weather stations to site
        List<Error> domainErrors = [];
        List<StationDetails> weatherStations = [];
        foreach (StationDetailsCommand station in request.Stations)
        {
            ErrorOr<List<StationDetails>> stationDetails = site.AddStationToSite(
              id: Guid.NewGuid(),
              name: station.Name,
              api: station.Api,
              latitude: station.Latitude,
              longitude: station.Longitude,
              elevation: station.Elevation,
              state: station.State,
              city: station.City,
              distanceFrom: station.DistanceFrom,
              directionFrom: station.DirectionFrom,
              directionFromTag: station.DirectionFromTag);

            // When we successfully add weather station to site
            // Then we want to add it to the list of weather stations
            if (stationDetails.IsError is false)
            {
                weatherStations.AddRange(stationDetails.Value);
            }
        }

        // Then we can add the weather station to database
        try
        {
            _context.StationDetails.AddRange(weatherStations);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to add weather station to database");
            return SiteWeatherStationDomainError.PersistenceError;
        }

        return Unit.Value;
    }
}
