using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeatherStations;

public record StationDetailsCommand
{
    public string Name { get; set; } = string.Empty;
    public WeatherApi Api { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double Elevation { get; set; }
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public double DistanceFrom { get; set; }
    public double DirectionFrom { get; set; }
    public string DirectionFromTag { get; set; } = string.Empty;
}

public record SaveSiteWeatherStationsCommand : ICommand
{
    public List<Guid> UserGroupsIds { get; set; } = null!;
    public List<StationDetailsCommand> Stations { get; set; } = Enumerable.Empty<StationDetailsCommand>().ToList();
    public Guid SiteId { get; set; }
}
