using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsBySite;

public record ElevationDto
{
    public UnitOfMeasurement Metric { get; set; } = new();
    public UnitOfMeasurement Imperial { get; set; } = new();
}

public record WeatherStationsBySiteDto
{
    public string StationName { get; set; } = string.Empty;
    public string ApiName { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public ElevationDto Elevation { get; set; } = new();
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public double DistanceFrom { get; set; }
    public double DirectionFrom { get; set; }
    public string DirectionFromTag { get; set; } = string.Empty;
}
