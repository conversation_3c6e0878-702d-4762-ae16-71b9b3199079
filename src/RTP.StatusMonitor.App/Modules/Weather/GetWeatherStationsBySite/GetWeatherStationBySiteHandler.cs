using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsBySite;

public class GetWeatherStationBySiteHandler(IDataContext dataContext)
    : IQueryHandler<GetWeatherStationBySiteQuery, List<WeatherStationsBySiteDto>>
{
    private readonly IDataContext _dataContext = dataContext;

    public async Task<ErrorOr<List<WeatherStationsBySiteDto>>> Handle(
        GetWeatherStationBySiteQuery request,
        CancellationToken cancellationToken)
    {
        // First we need to get the site from database along with its station details
        Domain.Site.Site? site = await _dataContext.Sites
          .AsNoTracking()
          .Include(s => s.GroupPermissions)
          .Include(s => s.StationDetails)
          .FirstOrDefaultAsync(s => s.Id == request.SiteId, cancellationToken);


        // When we can't not find the site
        // Then we return a NotFound error
        if (site == null)
        {
            return SiteErrors.NotFound;
        }

        // Then we need to check user permission for the site
        // When the user does not have permission for the site
        // Then we return a Forbidden error
        if (site.HasAccessToSite(request.UserGroupsId) == false)
            return SiteErrors.Unauthorized;

        // Then we need to map the site to the dto
        // And return the result to the controller
        return MapStationDetailsToDto(site.StationDetails);
    }

    private static List<WeatherStationsBySiteDto> MapStationDetailsToDto(IReadOnlyCollection<StationDetails> stationDetails)
    {
        return stationDetails.Select(ws => new WeatherStationsBySiteDto
        {
            StationName = ws.Name,
            ApiName = ws.Api.ToString(),
            Latitude = ws.Latitude,
            Longitude = ws.Longitude,
            Elevation = new ElevationDto
            {
                Metric = new UnitOfMeasurement
                {
                    // TODO - REFACTOR TO A METHOD FOR DOING CONVERSIONS
                    Value = ws.Elevation * 0.3048,
                    Unit = "m"
                },
                Imperial = new UnitOfMeasurement
                {
                    Value = ws.Elevation,
                    Unit = "ft"
                }
            },
            State = ws.State,
            City = ws.City,
            DistanceFrom = ws.DistanceFrom,
            DirectionFrom = ws.DirectionFrom,
            DirectionFromTag = ws.DirectionFromTag
        }).ToList();
    }
}

