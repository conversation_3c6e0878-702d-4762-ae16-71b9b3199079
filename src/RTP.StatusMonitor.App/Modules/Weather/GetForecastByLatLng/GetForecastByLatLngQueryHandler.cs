using ErrorOr;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.GetForecastByLatLng;

public class GetForecastByLatLngQueryHandler(
    IWeatherServiceFactory weatherServiceFactory,
    IMapService mapService)
        : IQueryHandler<GetForecastByLatLngQuery, List<WeatherConditionResult>>
{
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly IMapService _mapService = mapService;

    // TODO - need to supply which unit system to use (metric or imperial)
    public async Task<ErrorOr<List<WeatherConditionResult>>> Handle(
        GetForecastByLatLngQuery request,
        CancellationToken ct)
    {
        double altitude = await _mapService.GetLocationElevation(
            coordinates: new(
                Latitude: request.Latitude,
                Longitude: request.Longitude), ct: ct);

        return await _weatherServiceFactory
            .Get(WeatherService.IBM)
            .GetHourlyForecastAsync(
                latitude: request.Latitude,
                longitude: request.Longitude,
                unitSystem: UnitSystem.Imperial,
                altitude: altitude,
                ct: ct);
    }
}
