using RTP.StatusMonitor.App.Shared.Util;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;

public class DistanceService : IDistanceService
{
    /// <inheritdoc	/>
    public string CalculateDirectionDegree(Geolocation baseCoordinates, Geolocation targetCoordinates)
    {
        // Give the lat and long of 2 locations
        (double x1, double y1) = baseCoordinates;
        (double x2, double y2) = targetCoordinates;

        // First we compute the radians between the two locations
        double radians = Math.Atan2(y2 - y1, x2 - x1);

        // Convert to compass reading
        // Then we convert the radians to degrees 
        double compassReading = radians * (180 / Math.PI);

        // Return degrees between 0 and 360 of the direction from the base to the target
        return Math.Round(compassReading, 0).ToString();
    }

    /// <inheritdoc	/>
    public double CalculateDistance(Geolocation baseCoordinates, Geolocation targetCoordinates, UnitOfLength unitOfLength)
    {
        var baseRadian = Math.PI * baseCoordinates.Latitude / 180;
        var targetRadian = Math.PI * targetCoordinates.Latitude / 180;
        var theta = baseCoordinates.Longitude - targetCoordinates.Longitude;
        var thetaRadian = Math.PI * theta / 180;

        double dist =
            Math.Sin(baseRadian) * Math.Sin(targetRadian) +
            Math.Cos(baseRadian) * Math.Cos(targetRadian) * Math.Cos(thetaRadian);
        dist = Math.Acos(dist);

        dist = dist * 180 / Math.PI;
        dist = dist * 60 * 1.1515; // Converts to miles

        return unitOfLength.ConvertFromMiles(dist);
    }

    /// <inheritdoc	/>
    public string CalculateCompassDirection(Geolocation baseCoordinate, Geolocation targetCoordinate)
    {
        // Give the lat and long of 2 locations
        var x1 = baseCoordinate.Latitude;
        var y1 = baseCoordinate.Longitude;
        var x2 = targetCoordinate.Latitude;
        var y2 = targetCoordinate.Longitude;

        // First we compute the radians between the two locations
        var radians = Math.Atan2(y2 - y1, x2 - x1);

        // Then we convert the radians to degrees between 0 and 360
        var compassDegree = radians * (180 / Math.PI);

        // Then we convert the degrees to compass reading
        return ConvertCompassDegreesToText(compassDegree);
    }

    /// <inheritdoc	/>
    public string ConvertCompassDegreesToText(double compassReading)
    {
        // Convert degrees to standard directions
        var coordNames = new List<string>() { "N", "NE", "E", "SE", "S", "SW", "W", "NW", "N" };
        int coordIndex = int.Parse(Math.Round(compassReading / 45).ToString());
        if (coordIndex < 0) // Takes care of negative coordIndex
        {
            coordIndex += 8;
        }

        return coordNames[coordIndex];
    }
}
