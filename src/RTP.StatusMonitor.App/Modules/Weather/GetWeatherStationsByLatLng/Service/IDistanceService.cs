using RTP.StatusMonitor.App.Shared.Util;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;

public interface IDistanceService
{
    /// <summary>
    /// Calculate the distance between 2 coordinates
    /// </summary>
    /// 
    /// <param name="baseCoordinates"></param>
    /// <param name="targetCoordinates"></param>
    /// <param name="unitOfLength"></param>
    /// 
    /// <returns>The distance between the 2 coordinates</returns>
    public double CalculateDistance(
        Geolocation baseCoordinates,
        Geolocation targetCoordinates,
        UnitOfLength unitOfLength);

    /// <summary>
    /// Calculate the direction in degrees between 2 coordinates
    /// </summary>
    /// 
    /// <param name="baseCoordinates">The coordinates of first location</param>
    /// <param name="targetCoordinates">The coordinates of second location</param>
    /// 
    /// <returns>The direction in degrees between the 2 locations</returns>
    public string CalculateDirectionDegree(
        Geolocation baseCoordinates,
        Geolocation targetCoordinates);

    /// <summary>
    /// Get the compass direction (N, NE, E, SE, S, SW, W, NW) from the base to the target
    /// </summary>
    /// 
    /// <param name="baseCoordinate">The coordinates of the base location</param>
    /// <param name="targetCoordinate">The coordinates of the target location</param>
    /// 
    /// <returns>The compass direction from the base to the target</returns>
    public string CalculateCompassDirection(
        Geolocation firstCoordinate,
        Geolocation targetCoordinates);

    /// <summary>
    /// Convert degrees to compass reading
    /// </summary>
    /// 
    /// <param name="compassReading">The degrees to convert</param>
    /// 
    /// <returns>The compass reading</returns>
    public string ConvertCompassDegreesToText(double compassReading);
}