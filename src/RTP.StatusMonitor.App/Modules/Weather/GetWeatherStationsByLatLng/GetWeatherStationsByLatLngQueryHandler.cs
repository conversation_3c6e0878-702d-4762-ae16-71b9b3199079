using ErrorOr;
using MediatR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using RTP.StatusMonitor.App.Shared.Util;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng.Service;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherStationsByLatLng;

public class GetWeatherStationsByLatLngQueryHandler(
    IMemoryCache memoryCache,
    IMapService googleMapService,
    IDistanceService distanceService,
    IWeatherServiceFactory weatherServiceFactory,
    ILogger<GetWeatherStationsByLatLngQueryHandler> logger)
        : IRequestHandler<GetWeatherStationsByLatLngQuery, ErrorOr<List<WeatherStationByLatLngResponse>>>
{
    private readonly double METER_TO_FEET_CONVERSION_FACTOR = 3.28084;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly IDistanceService _distanceService = distanceService;
    private readonly IMapService _googleMapService = googleMapService;
    private readonly ILogger<GetWeatherStationsByLatLngQueryHandler> _logger = logger;
    private readonly IMemoryCache _memoryCache = memoryCache;

    public async Task<ErrorOr<List<WeatherStationByLatLngResponse>>> Handle(
        GetWeatherStationsByLatLngQuery request,
        CancellationToken ct)
    {
        Geolocation currentCoordinates = new(
            request.Latitude,
            request.Longitude);

        // First we need to get the weather stations from AccuWeather
        var awStations = await GetAccuWeatherStations(
            currentCoordinates,
            request.IncludeCurrentCondition);

        // Then we need to get the weather stations from IBM
        var ibmStations = await GetIBMWeatherStations(
            currentCoordinates,
            request.IncludeCurrentCondition,
            request.IncludeForecast);

        return awStations.Concat(ibmStations).ToList();
    }

    private async Task<List<WeatherStationByLatLngResponse>> GetAccuWeatherStations(
        Geolocation location,
        bool includeCurrentCondition)
    {
        try
        {

            // First we fetch the weather stations from all API providers
            // And cache the result for 60 minutes since the weather stations don't change that often
            List<WeatherStationByLatLngResponse> awStations = await _memoryCache.GetOrCreateAsync(
                key: $"{location.Latitude}-{location.Longitude}-AW-stations",
                factory: async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60);

                    return await _weatherServiceFactory
                .Get(WeatherService.AW)
                .GetNearbyWeatherStations(
                    latitude: location.Latitude,
                    longitude: location.Longitude);
                });

            // Then we add distance and direction information to each station
            await Parallel.ForEachAsync(
                awStations, async (station, ct) =>
                {
                    // Get the unit of length based on the request
                    // UnitOfLength unitOfLength = request.IsMetric ? UnitOfLength.Kilometers : UnitOfLength.Miles;
                    UnitOfLength unitOfLength = UnitOfLength.Miles;

                    // First we need to calculate the distance from current coordinate to the station coordinate
                    var coordinate = new Geolocation
                    (station.Latitude, station.Longitude);
                    double distance = _distanceService.CalculateDistance(location, coordinate, unitOfLength);
                    station.DistanceFrom = Math.Round(distance, 1);

                    // Then we need to get the direction from the station coordinate to the current coordinate based on the coordinate
                    var stationCoordinates = new Geolocation
                    (station.Latitude, station.Longitude);

                    var directionDegree = _distanceService.CalculateDirectionDegree(
                        location, stationCoordinates);

                    _ = int.TryParse(directionDegree, out int directionDegreeFromLocation);
                    station.DirectionFrom = directionDegreeFromLocation;

                    // Then we need to get the compass direction as well (N, NE, E, SE, S, SW, W, NW)
                    station.DirectionFromTag = _distanceService.CalculateCompassDirection(location, stationCoordinates);

                    // Then we also need to query the current weather condition of the station if requested
                    // And add the current weather condition to the station
                    station.CurrentCondition = includeCurrentCondition ? await _memoryCache.GetOrCreateAsync(
                        key: $"{station.LocationKey}-AW-current",
                        factory: async entry =>
                        {
                            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);

                            return await _weatherServiceFactory
                            .Get(WeatherService.AW)
                            .GetCurrentConditionsAsync(
                                latitude: station.Latitude,
                                longitude: station.Longitude,

                                // FIXME - this is a hack to compile => Need to supply elevation for pressure computation
                                altitude: 0,
                                ct: ct);
                        }) : null;

                    // And add the forecast to the station (no forecast for AW)
                    station.Forecasts = Enumerable
                        .Empty<WeatherConditionResult>()
                        .ToList();
                });

            return awStations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying AccuWeather weather stations.");
            return Enumerable.Empty<WeatherStationByLatLngResponse>().ToList();
        }
    }

    private async Task<List<WeatherStationByLatLngResponse>> GetIBMWeatherStations(
        Geolocation location,
        bool includeCurrentCondition,
        bool includeForecast)
    {
        try
        {
            // Then we need to get the weather stations from IBM
            var ibmStations = await _memoryCache.GetOrCreateAsync($"{location.Latitude}-{location.Longitude}-IBM-stations", async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60);
                return await _weatherServiceFactory
            .Get(WeatherService.IBM)
            .GetNearbyWeatherStations(location.Latitude, location.Longitude);
            });

            // For IBM, we need to get the elevation of each station by using Google Map Elevation API and add it to the station
            var coordinates = ibmStations
                .ConvertAll(x => new Geolocation(x.Latitude, x.Longitude));

            // Then we add distance and direction information to each station
            await Parallel.ForEachAsync(
                ibmStations,
                async (station, ct) =>
                {
                    // Get the unit of length based on the request
                    // UnitOfLength unitOfLength = request.IsMetric ? UnitOfLength.Kilometers : UnitOfLength.Miles;
                    UnitOfLength unitOfLength = UnitOfLength.Miles;

                    // Then we need to get the direction from the station coordinate to the current coordinate based on the coordinate
                    var stationCoordinates = new Geolocation
                    (station.Latitude, station.Longitude);

                    var directionDegree = _distanceService.CalculateDirectionDegree(
                        location,
                        stationCoordinates);
                    _ = int.TryParse(
                        directionDegree,
                        out int directionDegreeFromLocation);
                    station.DirectionFrom = directionDegreeFromLocation;

                    // Then we need to get the compass direction as well (N, NE, E, SE, S, SW, W, NW)
                    station.DirectionFromTag = _distanceService
                        .CalculateCompassDirection(location, stationCoordinates);

                    // The elevation API return the elevation in the same order as the coordinates
                    // and they are in meters
                    double elevation = await _memoryCache.GetOrCreateAsync(
                        key: $"{station.Longitude}-{station.Latitude}-IBM-elevation",
                        factory: async entry =>
                        {
                            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
                            return await _googleMapService.GetLocationElevation(stationCoordinates, ct);
                        });

                    station.Elevation = new ElevationDto
                    {
                        Metric = new UnitOfMeasurement
                        {
                            Value = elevation,
                            Unit = "m"
                        },
                        Imperial = new UnitOfMeasurement
                        {
                            Value = elevation * METER_TO_FEET_CONVERSION_FACTOR,
                            Unit = "ft"
                        }
                    };

                    // Then we also need to query the current weather condition of the station
                    station.CurrentCondition = includeCurrentCondition ? await _memoryCache.GetOrCreateAsync(
                        key: $"{station.Longitude}-{station.Latitude}-IBM-current",
                        factory: async entry =>
                        {
                            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);

                            return await _weatherServiceFactory
                            .Get(WeatherService.IBM)
                            .GetCurrentConditionsAsync(
                                latitude: station.Latitude,
                                longitude: station.Longitude,
                                altitude: 0,
                                ct: ct);
                        }) : null;

                    // Then we also need to query the forecast of the station
                    station.Forecasts = includeForecast ? await _memoryCache.GetOrCreateAsync(
                        key: $"{station.Longitude}-{station.Latitude}-IBM-forecast",
                        factory: async entry =>
                        {
                            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60);

                            return await _weatherServiceFactory
                            .Get(WeatherService.IBM)
                            .GetHourlyForecastAsync(
                                latitude: station.Latitude,
                                longitude: station.Longitude,
                                // FIXME - need to obtain the elevation for accurate pressure calculation
                                altitude: 0,
                                unitSystem: UnitSystem.Imperial,
                                ct);
                        }) : Enumerable.Empty<WeatherConditionResult>().ToList();
                });

            return ibmStations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying IBM weather stations.");
            return Enumerable.Empty<WeatherStationByLatLngResponse>().ToList();
        }
    }
}
