using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;

public record HistoricalWeatherTableDto : ITableEntity
{
    public string PartitionKey { get; set; } = string.Empty;
    public string RowKey { get; set; } = string.Empty;
    public DateTimeOffset? Timestamp { get; set; }
    public string LocalTimestamp { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int HourEnding { get; set; }
    public double Temp { get; set; }
    public double Press { get; set; }
    public double PressAbs { get; set; }
    public int RH { get; set; }
    public double WindSpeed { get; set; }
    public int WindDirection { get; set; }
    public int UVIndex { get; set; }
    public double Precipitation { get; set; }
    public int Icon { get; set; }
    public ETag ETag { get; set; }
}
