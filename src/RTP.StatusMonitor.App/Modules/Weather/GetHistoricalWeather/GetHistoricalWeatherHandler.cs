using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;

public class GetHistoricalWeatherHandler(
    IWeatherServiceFactory weatherService,
    IMemoryCache memoryCache,
    IDataContext context)
        : IQueryHandler<GetHistoricalWeatherQuery, List<HistoricalWeatherDto>>
{
    private readonly IDataContext _context = context;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherService;
    private readonly IMemoryCache _memoryCache = memoryCache;

    public async Task<ErrorOr<List<HistoricalWeatherDto>>> Handle(
        GetHistoricalWeatherQuery request,
        CancellationToken ct)
    {
        // Given the site id of a site

        // Then we should find the site data
        var site = await _context.Sites
            .AsNoTracking()
            .Where(s => request.SiteId == s.Id)
            .Select(s => new { s.Id, s.Name })
            .FirstOrDefaultAsync(ct);

        // When the site is not found
        // Then we should return an error
        if (site is null)
            return SiteErrors.NotFound;

        // Then we find the range of the row key by reversing the unix timestamp
        var startRowKey = Math.Pow(10, 13) - request.StartDate;
        var endRowKey = Math.Pow(10, 13) - request.EndDate;


        // Then we build the query to get the historical weather data
        var query = $"PartitionKey eq '{site.Name}-AW' and RowKey ge '{endRowKey}' and RowKey le '{startRowKey}'";
        var tableName = "currentWeather";

        // Then we should get the historical weather data from Azure Table Storage
        var historicalWeatherData = _memoryCache.GetOrCreate($"{site.Id}-{startRowKey}-{endRowKey}", entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
            return _weatherServiceFactory
                .Get(WeatherService.AW)
                .GetHistoricalWeather(tableName, query, ct);
        });

        // When the result of the query is null
        // Then we somehow failed to get the historical weather data
        if (historicalWeatherData is null)
            return WeatherErrors.UnexpectedError;

        // Then we need to map from the list of data from the table to the list of historical weather dto
        return historicalWeatherData.Select(data => new HistoricalWeatherDto
        {
            LocalTimestamp = data.LocalTimestamp,
            Temp = data.Temp,
            Press = data.Press,
            PressAbs = data.PressAbs,
            RH = data.RH,
            WindSpeed = data.WindSpeed,
            WindDirection = data.WindDirection,
            UVIndex = data.UVIndex,
            Precipitation = data.Precipitation,
            Icon = data.Icon
        }).ToList();
    }
}
