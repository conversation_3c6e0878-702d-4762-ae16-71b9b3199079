namespace RTP.StatusMonitor.App.Modules.Weather.GetHistoricalWeather;

public record HistoricalWeatherDto
{
    public string LocalTimestamp { get; set; } = string.Empty;
    public double Temp { get; set; }
    public double Press { get; set; }
    public double PressAbs { get; set; }
    public int RH { get; set; }
    public double WindSpeed { get; set; }
    public int WindDirection { get; set; }
    public int UVIndex { get; set; }
    public double Precipitation { get; set; }
    public int Icon { get; set; }
}
