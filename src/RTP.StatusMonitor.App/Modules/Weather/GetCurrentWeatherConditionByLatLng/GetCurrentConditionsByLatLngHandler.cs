using ErrorOr;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditionByLatLng;
public class GetCurrentConditionsByLatLngHandler(
    IWeatherServiceFactory weatherServiceFactory)
        : IQueryHandler<GetCurrentConditionsByLatLngQuery, WeatherConditionResult?>
{
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;

    public async Task<ErrorOr<WeatherConditionResult?>> Handle(
        GetCurrentConditionsByLatLngQuery request,
        CancellationToken ct)
        => Enum.Parse<WeatherService>(request.Api) switch
        {
            WeatherService.AW => await _weatherServiceFactory
                .Get(WeatherService.AW)
                .GetCurrentConditionsAsync(
                    latitude: request.Latitude,
                    longitude: request.Longitude,
                    // FIXME: This is a hack to compile
                    altitude: 0,
                    ct: ct),
            WeatherService.IBM => await _weatherServiceFactory
                .Get(WeatherService.IBM)
                .GetCurrentConditionsAsync(
                    latitude: request.Latitude,
                    longitude: request.Longitude,
                    // FIXME: This is a hack to compile
                    altitude: 0,
                    ct: ct),
            _ => await _weatherServiceFactory
                .Get(WeatherService.AW)
                .GetCurrentConditionsAsync(
                    latitude: request.Latitude,
                    longitude: request.Longitude,
                    // FIXME: This is a hack to compile
                    altitude: 0,
                    ct: ct),
        };
}
