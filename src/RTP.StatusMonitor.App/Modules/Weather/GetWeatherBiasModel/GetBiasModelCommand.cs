using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherBiasModel;

public record GetBiasModelCommand(Guid UnitId, WeatherSettingsCommand WeatherSettings)
    : ICommand<List<WeatherBiasModelResponse>>;

public record WeatherSettingsCommand(
    ForecastSettingsCommand ForecastSettings,
    List<WeatherBiasSettingsCommand> WeatherBiasSettings
);

public record ForecastSettingsCommand(
    bool IsForecastEnabled,
    int ForecastDays,
    string ForecastServiceSource
);

public record WeatherBiasSettingsCommand(
    Guid Id,
    Guid UnitId,
    bool IsBiasEnabled,
    string BiasType,
    string TagSource,
    string BiasServiceSource,
    double? Min,
    double? Max,
    bool IsWindEffectEnabled,
    string Expression
);
