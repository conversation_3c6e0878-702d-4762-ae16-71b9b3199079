using System.Collections.Concurrent;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.WeatherBias;
using RTP.StatusMonitor.Domain.WeatherBias.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherBiasModel;

internal sealed class GetBiasModelCommandHandler(
    DataContext dataContext,
    IWeatherServiceFactory weatherServiceFactory,
    WeatherReadRepository weatherBiasReadRepository
) : ICommandHandler<GetBiasModelCommand, List<WeatherBiasModelResponse>>
{
    private readonly DataContext _dataContext = dataContext;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly WeatherReadRepository _weatherBiasReadRepo = weatherBiasReadRepository;

    public async Task<ErrorOr<List<WeatherBiasModelResponse>>> Handle(
        GetBiasModelCommand request,
        CancellationToken cancellationToken
    )
    {
        Unit? unit = await _dataContext
            .Units.AsNoTracking()
            .Include(u => u!.Block)
            .ThenInclude(b => b!.Site)
            .ThenInclude(s => s!.Customer)
            .SingleOrDefaultAsync(u => u.Id == request.UnitId, cancellationToken);

        if (unit is null)
        {
            return UnitErrors.NotFound;
        }

        List<WeatherConditionResult> forecastData = await _weatherServiceFactory
            .Get(
                Enum.Parse<WeatherService>(
                    request.WeatherSettings.ForecastSettings.ForecastServiceSource
                )
            )
            .GetHourlyForecastAsync(
                latitude: unit.Block.Site.Latitude,
                longitude: unit.Block.Site.Longitude,
                altitude: unit.Block.Site.Altitude,
                unitSystem: unit.Block.Site.IsMetric ? UnitSystem.Metric : UnitSystem.Imperial,
                ct: cancellationToken
            );

        List<WeatherBiasSettingsCommand> unitBiasSettings =
        [
            .. request.WeatherSettings.WeatherBiasSettings.Where(wbs => wbs.UnitId == unit.Id),
        ];

        // Query the bias for a specific type (Temp bias from RTP for example)
        ConcurrentBag<HourlyWeatherBiasDto> biasesOfType = [];
        Parallel.ForEach(
            unitBiasSettings,
            biasSettings =>
            {
                // If the bias is not enabled, skip it
                if (!biasSettings.IsBiasEnabled)
                {
                    return;
                }

                List<HourlyWeatherBiasDto> biasResultsForType =
                    _weatherBiasReadRepo.GetWeatherBiases(
                        unitAlias: unit.Alias,
                        weatherService: Enum.Parse<WeatherService>(biasSettings.BiasServiceSource),
                        tagSource: Enum.Parse<WeatherTagSource>(biasSettings.TagSource),
                        biasType: Enum.Parse<WeatherBiasType>(biasSettings.BiasType),
                        unixStartTime: DateTimeOffset.UtcNow.AddDays(-7).ToUnixTimeMilliseconds(),
                        unixEndTime: DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                        biasLimit: BiasLimit.Create(biasSettings.Min, biasSettings.Max).Value
                    );

                biasResultsForType.ForEach(r => biasesOfType.Add(r));
            }
        );

        // Group the bias by each type
        Dictionary<WeatherBiasType, List<HourlyWeatherBiasDto>> biasesGroupByType = biasesOfType
            .Where(b => b.Bias is not null)
            .GroupBy(b => b.Bias!.BiasType)
            .ToDictionary(key => key.Key, value => value.ToList());

        // Get the end time of the forecast
        // NOTE - 0-index => 2 days means 2 days from now - today, tomorrow and the day after
        DateTime startOfTomorrow = new(
            DateTime.Now.AddDays(1).Year,
            DateTime.Now.AddDays(1).Month,
            DateTime.Now.AddDays(1).Day,
            0,
            0,
            0
        );
        DateTime forecastEndTime = startOfTomorrow.AddDays(
            request.WeatherSettings.ForecastSettings.ForecastDays
        );

        return forecastData
            .Where(f =>
                TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                    TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone)
                ) <= forecastEndTime
            )
            .Select(f =>
            {
                DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                    TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone)
                );
                int hour = localTime.Hour;
                int hourEnding = hour == 0 ? 24 : hour;

                AmbientBiasDataPoint? tempBias = GetBiasValueForHourEnding(
                    biasesGroupByType,
                    WeatherBiasType.Temp,
                    hourEnding
                );
                AmbientBiasDataPoint? rhBias = GetBiasValueForHourEnding(
                    biasesGroupByType,
                    WeatherBiasType.RH,
                    hourEnding
                );
                AmbientBiasDataPoint? pressBias = GetBiasValueForHourEnding(
                    biasesGroupByType,
                    WeatherBiasType.Press,
                    hourEnding
                );
                AmbientBiasDataPoint? citBias = GetBiasValueForHourEnding(
                    biasesGroupByType,
                    WeatherBiasType.CIT,
                    hourEnding
                );

                ObservedWeatherDataPoint observedTemp = new(
                    ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                    BiasType: WeatherBiasType.Temp,
                    Value: f.Temp?.Format().Value
                );
                ObservedWeatherDataPoint observedRh = new(
                    ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                    BiasType: WeatherBiasType.RH,
                    Value: f.RelativeHumidity
                );
                ObservedWeatherDataPoint observedPress = new(
                    ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                    BiasType: WeatherBiasType.Press,
                    Value: unit.Block.Site.IsMetric
                        ? f.AbsolutePressure?.ToMBar().Format().Value
                        : f.AbsolutePressure?.ToPsi().Format().Value
                );
                ObservedWeatherDataPoint observedTempCit = new(
                    ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                    BiasType: WeatherBiasType.CIT,
                    Value: f.Temp?.Format().Value
                );
                return new WeatherBiasModelResponse(
                    UnitId: unit.Id,
                    TimestampUtc: DateTimeOffset
                        .FromUnixTimeSeconds(f.UnixTimeInSeconds)
                        .UtcDateTime,
                    LocalTimestamp: localTime,
                    HourEnding: hourEnding,
                    Description: f.Description,
                    Api: f.ApiName,
                    TempRaw: f.Temp?.Format().Value,
                    TempBias: tempBias?.Value,
                    TempAdj: observedTemp.ApplyBias(tempBias)?.Value,
                    CITBias: citBias?.Value,
                    DewPoint: null,
                    RhRaw: f.RelativeHumidity,
                    RhBias: rhBias?.Value,
                    RhAdj: observedRh.ApplyBias(rhBias)?.Value,
                    PressBaro: unit.Block.Site.IsMetric
                        ? f.MeanSeaLevelPressure?.ToMBar().Format().Value
                        : f.MeanSeaLevelPressure?.ToInHg().Format().Value,
                    PressRaw: unit.Block.Site.IsMetric
                        ? f.AbsolutePressure?.ToMBar().Format().Value
                        : f.AbsolutePressure?.ToPsi().Format().Value,
                    PressBias: pressBias?.Value,
                    PressAdj: observedPress.ApplyBias(pressBias)?.Value,
                    WindSpeed: f.WindSpd?.Value,
                    WindDir: f.WindDirection?.Localized,
                    WindDirDegree: f.WindDirection?.Degrees,
                    Precip: f.Precipitation?.Value,
                    UvIndex: f.UvIndex
                );
            })
            .ToList();
    }

    private static AmbientBiasDataPoint? GetBiasValueForHourEnding(
        Dictionary<WeatherBiasType, List<HourlyWeatherBiasDto>> biasesGroupByType,
        WeatherBiasType biasType,
        int hourEnding
    )
    {
        if (!biasesGroupByType.ContainsKey(biasType))
            return null;

        return biasType switch
        {
            WeatherBiasType.Temp => biasesGroupByType[biasType]
                .SingleOrDefault(b => b.HourEnding == hourEnding)
                ?.Bias,
            WeatherBiasType.RH => biasesGroupByType[biasType]
                .SingleOrDefault(b => b.HourEnding == hourEnding)
                ?.Bias,
            WeatherBiasType.Press => biasesGroupByType[biasType]
                .SingleOrDefault(b => b.HourEnding == hourEnding)
                ?.Bias,
            WeatherBiasType.CIT => biasesGroupByType[biasType]
                .SingleOrDefault(b => b.HourEnding == hourEnding)
                ?.Bias,
            _ => null,
        };
    }
}
