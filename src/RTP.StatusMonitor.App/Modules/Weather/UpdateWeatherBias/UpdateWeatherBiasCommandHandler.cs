using System.Collections.Concurrent;
using System.Globalization;
using Azure.Data.Tables;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Domain.WeatherBias.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.UpdateWeatherBias;

public record BiasTag(
    UnitId UnitId,
    string Tag,
    WeatherBiasType BiasType,
    WeatherTagSource TagSource,
    BiasLimit Limit
);

internal sealed class UpdateWeatherBiasCommandHandler(
    DataContext dataContext,
    IWeatherServiceFactory weatherServiceFactory,
    DataClientReadRepository dataClientStorage,
    IOptions<TableStorageOptions> tableStorageOptions
) : ICommandHandler<UpdateWeatherBiasCommand>
{
    private readonly long REVERSE_ROW_KEY_CONSTANT = (long)Math.Pow(10, 13);
    private readonly DataContext _dataContext = dataContext;
    private readonly DataClientReadRepository _dataClientStorage = dataClientStorage;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        UpdateWeatherBiasCommand request,
        CancellationToken cancellationToken
    )
    {
        // Query all units
        List<Unit> units = await _dataContext
            .Units.AsNoTracking()
            .Include(u => u!.Block)
            .ThenInclude(b => b!.Site)
            .ThenInclude(s => s!.Customer)
            .ToListAsync(cancellationToken);

        // Query weather data for all sites (exclude the demo sites)
        Dictionary<SiteId, List<WeatherConditionResult>> observedWeatherGroupedBySite =
            await GetSiteWeatherData(
                sites:
                [
                    .. units
                        .Select(u => u.Block.Site)
                        .Where(s => s.Description != "Demo")
                        .Distinct(),
                ],
                ct: cancellationToken
            );

        // Query all tag bias settings
        IQueryable<BiasTag> query =
            from bts in _dataContext.BiasTagSettings
            join wbs in _dataContext.WeatherBiasSettings
                on new
                {
                    UnitId = bts.UnitId.Value,
                    bts.BiasType,
                    bts.TagSource,
                } equals new
                {
                    wbs.UnitId,
                    wbs.BiasType,
                    wbs.TagSource,
                }
            select new BiasTag(bts.UnitId, bts.Tag, bts.BiasType, bts.TagSource, wbs.BiasLimit);

        List<BiasTag> biasTagSettings = await query.AsNoTracking().ToListAsync(cancellationToken);

        // Group them by unit
        Dictionary<UnitId, List<BiasTag>> biasTagSettingsGroupedByUnit = biasTagSettings
            .GroupBy(bts => bts.UnitId)
            .ToDictionary(g => g.Key, g => g.ToList());

        TableClient tableClient = new(
            endpoint: new Uri(_tableStorageOptions.Uri),
            tableName: _tableStorageOptions.WeatherWriteTable,
            credential: new(_tableStorageOptions.AccountName, _tableStorageOptions.AccountKey)
        );

        // Upload the observed weather data to table storage
        UploadCurrentWeatherData(tableClient, units, observedWeatherGroupedBySite);

        // Process the bias for each unit in parallel
        Parallel.ForEach(
            biasTagSettingsGroupedByUnit,
            (unitBiasTagSettings) =>
            {
                UnitId unitId = unitBiasTagSettings.Key;
                // Each unit will have a list of bias settings for RH, Temp, Press, etc...
                List<BiasTag> biasTagSettingsForSingleUnit = unitBiasTagSettings.Value;

                // Get the block of the unit (must exist)
                Block block = units.Where(u => u.Id == unitId.Value).Select(u => u.Block).Single();

                // If the site has no observed weather data, skip
                if (!observedWeatherGroupedBySite.ContainsKey(new SiteId(block.Site.Id)))
                {
                    return;
                }

                List<WeatherConditionResult> observedWeathersForEachSite =
                    observedWeatherGroupedBySite[new SiteId(block.Site.Id)];
                foreach (WeatherConditionResult siteCurrentWeather in observedWeathersForEachSite)
                {
                    // Get the observed weather data from the weather API
                    List<ObservedWeatherDataPoint> observedWeather =
                    [
                        siteCurrentWeather.ToObservedWeatherData(
                            altitude: block.Site.Altitude,
                            biasType: WeatherBiasType.Temp
                        ),
                        siteCurrentWeather.ToObservedWeatherData(
                            altitude: block.Site.Altitude,
                            biasType: WeatherBiasType.RH
                        ),
                        siteCurrentWeather.ToObservedWeatherData(
                            altitude: block.Site.Altitude,
                            biasType: WeatherBiasType.Press
                        ),
                        siteCurrentWeather.ToObservedWeatherData(
                            altitude: block.Site.Altitude,
                            biasType: WeatherBiasType.CIT
                        ),
                    ];

                    // Query the actual instrumentation data of the block
                    List<ActualInstrumentationDataPoint> actualInstrumentation =
                        GetInstrumentationData(
                            block: block,
                            unitBiasTagSettings: biasTagSettingsForSingleUnit
                        );

                    // Get the bias limits of each metric type for current unit
                    Dictionary<WeatherBiasType, BiasLimit> biasLimits =
                        biasTagSettingsForSingleUnit.ToDictionary(k => k.BiasType, v => v.Limit);

                    // Compute bias for each unit (bias for each metric(rh, temp, press. etc...) and each source (pi/rtp))and save it
                    tableClient.UpsertEntity(
                        actualInstrumentation.ToWeatherBiasEntity(
                            observedWeather,
                            biasLimits,
                            Enum.Parse<WeatherService>(siteCurrentWeather.ApiName),
                            WeatherTagSource.RTP,
                            units.Single(u => u.Id == unitId.Value)
                        )
                    );
                    tableClient.UpsertEntity(
                        actualInstrumentation.ToWeatherBiasEntity(
                            observedWeather,
                            biasLimits,
                            Enum.Parse<WeatherService>(siteCurrentWeather.ApiName),
                            WeatherTagSource.Customer,
                            units.Single(u => u.Id == unitId.Value)
                        )
                    );
                }
            }
        );

        return MediatR.Unit.Value;
    }

    /// <summary>
    /// Get the actual instrumentation data of the block
    /// along with the current weather condition from the weather API
    /// </summary>
    /// <param name="block">The block to get the instrumentation data from</param>
    /// <param name="unitBiasTagSettings">The bias tag settings of the single unit so we know which tag to get</param>
    /// <returns>A tuple contains the instrumentation data and current weather condition</returns>
    private List<ActualInstrumentationDataPoint> GetInstrumentationData(
        Block block,
        List<BiasTag> unitBiasTagSettings
    )
    {
        // Query the data for the tag from data client
        List<DataClientHistoricalWriteSchemaDto> snapshots = _dataClientStorage.GetLatestData(
            block: block,
            uniqueTags: [.. unitBiasTagSettings.Distinct().Select(bts => bts.Tag)]
        );

        List<ActualInstrumentationDataPoint> actualInstrumentation = [];
        snapshots
            .ToList()
            .ForEach(snapshot =>
            {
                BiasTag? biasTagSetting = unitBiasTagSettings.FirstOrDefault(bts =>
                    bts.Tag == snapshot.Tag
                );

                if (biasTagSetting is not null)
                {
                    actualInstrumentation.Add(
                        new(
                            TagSource: biasTagSetting.TagSource,
                            BiasType: biasTagSetting.BiasType,
                            Value: snapshot.Value
                        )
                    );
                }
            });

        return actualInstrumentation;
    }

    /// <summary>
    /// Query site weather conditions from the weather API
    /// </summary>
    /// <param name="sites">The list of sites to query data for</param>
    /// <returns>The weather conditions for each site</returns>
    private async Task<Dictionary<SiteId, List<WeatherConditionResult>>> GetSiteWeatherData(
        List<Site> sites,
        CancellationToken ct
    )
    {
        ConcurrentDictionary<SiteId, List<WeatherConditionResult>> weatherGroupedBySite = new();
        await Parallel.ForEachAsync(
            sites,
            async (site, _) =>
            {
                List<WeatherConditionResult> currentConditions = [];
                foreach (WeatherService api in Enum.GetValues<WeatherService>().ToList())
                {
                    // NOTE - Tomorrow has a rate limit of 3 request/second
                    // NOTE - OpenWeather has a rate limit of 1000 requests/day
                    // NOTE - currently not supporting these APIs
                    if (api is WeatherService.TOMORROW or WeatherService.OW)
                    {
                        continue;
                    }

                    WeatherConditionResult? currentCondition = await _weatherServiceFactory
                        .Get(api)
                        .GetCurrentConditionsAsync(
                            latitude: site.Latitude,
                            longitude: site.Longitude,
                            altitude: site.Altitude,
                            ct: ct
                        );

                    if (currentCondition is not null)
                    {
                        currentConditions.Add(currentCondition);
                    }
                }

                weatherGroupedBySite.TryAdd(new SiteId(site.Id), currentConditions);
            }
        );

        return weatherGroupedBySite.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// Upload the current weather of each site to table storage in parallel
    /// </summary>
    /// <param name="units">The units to upload the weather data for</param>
    /// <param name="currentWeatherGroupedBySite">The weather data grouped by site</param>
    private void UploadCurrentWeatherData(
        TableClient tableClient,
        List<Unit> units,
        Dictionary<SiteId, List<WeatherConditionResult>> currentWeatherGroupedBySite
    ) =>
        Parallel.ForEach(
            currentWeatherGroupedBySite,
            (siteWeather) =>
            {
                Site site = units
                    .Where(u => u.Block.Site.Id == siteWeather.Key.Value)
                    .Select(u => u.Block.Site)
                    .First();

                foreach (WeatherConditionResult data in siteWeather.Value)
                {
                    DateTime localTime = DateTime.Parse(
                        data.LocalTime,
                        CultureInfo.InvariantCulture
                    );

                    tableClient.UpsertEntity(
                        new HistoricalWeatherDto()
                        {
                            PartitionKey = $"{site.Name}-{data.ApiName}",
                            RowKey = (
                                REVERSE_ROW_KEY_CONSTANT
                                - DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                            ).ToString(),
                            Source = data.ApiName,
                            LocalTimestamp = localTime.ToString("yyyy/MM/dd HH:mm:ss tt"),
                            HourEnding = localTime.Hour == 0 ? 24 : localTime.Hour,
                            Description = data.Description,
                            Icon = data.WeatherIcon,
                            Temp = data.Temp?.Format().Value,
                            Press = data.MeanSeaLevelPressure?.ToPsi().Format().Value,
                            PressBaro = data.AbsolutePressure?.ToInHg().Format().Value,
                            RH = data.RelativeHumidity,
                            WindSpeed = data.WindSpd?.Value,
                            WindDirection = data.WindDirection?.Degrees,
                            UVIndex = data.UvIndex,
                            Precipitation = data.Precipitation?.Value,
                        }
                    );
                }
            }
        );
}
