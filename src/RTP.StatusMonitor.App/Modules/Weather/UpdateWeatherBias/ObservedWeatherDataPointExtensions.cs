using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.WeatherBias.Types;

namespace RTP.StatusMonitor.App.Modules.Weather.UpdateWeatherBias;

public static class ObservedWeatherDataPointExtensions
{
    /// <summary>
    /// Map the current weather condition to an observed weather data point
    /// </summary>
    /// <param name="currentCondition">The current weather condition</param>
    /// <param name="altitude">The altitude of the location</param>
    /// <param name="biasType">The type of the weather to bias (Temp/CIT/Press/RH/etc...)</param>
    /// <returns>The observed weather data point</returns>
    public static ObservedWeatherDataPoint ToObservedWeatherData(
        this WeatherConditionResult currentCondition,
        double altitude,
        WeatherBiasType biasType
    )
    {
        AbsolutePressure? absPress =
            currentCondition.MeanSeaLevelPressure is not null && currentCondition.Temp is not null
                ? currentCondition.MeanSeaLevelPressure?.ToAbsolutePressure(
                    altitude: AltitudeUnitConverter.ToMeters(altitude),
                    temp: currentCondition.Temp
                )
                : null;

        return new(
            ApiSource: Enum.Parse<WeatherService>(currentCondition.ApiName),
            BiasType: biasType,
            Value: biasType switch
            {
                WeatherBiasType.Temp => currentCondition.Temp?.ToFahrenheit().Value,
                WeatherBiasType.RH => currentCondition.RelativeHumidity,
                WeatherBiasType.Press => absPress?.ToPsi().Value,
                WeatherBiasType.CIT => currentCondition.Temp?.ToFahrenheit().Value,
                _ => null,
            }
        );
    }
}
