namespace RTP.StatusMonitor.App.Modules.Weather.GetForecastBySite;


public record ForecastBySiteResponse(
    DateTime TimestampUtc,
    DateTime LocalTimestamp,
    int HourEnding,
    string Description,
    string Api,
    double? TempRaw,
    double? DewPoint,
    double? RhRaw,

    /// <summary>
    /// Mean sea level pressure in inHg
    /// </summary>
    double? PressBaro,

    /// <summary>
    /// Absolute pressure in psi
    /// </summary>
    double? PressRaw,

    double? WindSpeed,
    string? WindDir,
    double? WindDirDegree,
    double? Precip,
    int? UvIndex);