using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetForecastBySite;

public class GetForecastBySiteQueryHandler(
    IWeatherServiceFactory weatherServiceFactory,
    IDataContext dataContext)
        : IQueryHandler<GetForecastBySiteQuery, List<ForecastBySiteResponse>>
{
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly IDataContext _dataContext = dataContext;

    public async Task<ErrorOr<List<ForecastBySiteResponse>>> Handle(
        GetForecastBySiteQuery request,
        CancellationToken ct)
    {
        Site? site = await _dataContext.Sites
        .SingleOrDefaultAsync(s => s.Id == request.SiteId, ct);

        if (site is null)
            return SiteDomainErrors.NotFound;

        List<GetCurrentWeatherConditions.Dto.WeatherConditionResult> data = await _weatherServiceFactory
            .Get(WeatherService.IBM)
            .GetHourlyForecastAsync(
                latitude: site.Latitude,
                longitude: site.Longitude,
                unitSystem: site.IsMetric
                    ? UnitSystem.Metric
                    : UnitSystem.Imperial,
                altitude: site.Altitude,
                ct: ct);

        return data.Select(x =>
        {
            // Convert to local time using the site's timezone
            DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeSeconds(x.UnixTimeInSeconds).UtcDateTime,
                TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone));

            return new ForecastBySiteResponse(
                TimestampUtc: DateTimeOffset.FromUnixTimeSeconds(x.UnixTimeInSeconds).UtcDateTime,
                LocalTimestamp: localTime,
                HourEnding: localTime.Hour,
                Description: x.Description,
                Api: x.ApiName,
                TempRaw: x.Temp?.Value,
                DewPoint: null,
                RhRaw: x.RelativeHumidity,
                PressBaro: x.MeanSeaLevelPressure?.Value,
                PressRaw: x.AbsolutePressure?.Value,
                WindSpeed: x.WindSpd?.Value,
                WindDir: x.WindDirection?.Localized,
                WindDirDegree: x.WindDirection?.Degrees,
                Precip: x.Precipitation?.Value,
                UvIndex: x.UvIndex);
        }).ToList();
    }
}
