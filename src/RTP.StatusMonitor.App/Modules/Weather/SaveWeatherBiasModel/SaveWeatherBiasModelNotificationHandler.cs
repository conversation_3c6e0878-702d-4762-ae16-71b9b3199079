using System.Collections.Concurrent;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.GetWeatherBiasModel;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.Domain.EngUnits;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.WeatherBias;
using RTP.StatusMonitor.Domain.WeatherBias.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.SaveWeatherBiasModel;

internal sealed class SaveWeatherBiasModelNotificationHandler(
    IDataContext dataContext,
    IWeatherServiceFactory weatherServiceFactory,
    WeatherReadRepository weatherBiasReadRepo,
    WeatherWriteRepository weatherBiasWriteRepo
) : INotificationHandler<SaveWeatherBiasModelNotification>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    private readonly WeatherReadRepository _weatherBiasReadRepo = weatherBiasReadRepo;
    private readonly WeatherWriteRepository _weatherBiasWriteRepo = weatherBiasWriteRepo;

    public async Task Handle(
        SaveWeatherBiasModelNotification notification,
        CancellationToken cancellationToken
    )
    {
        // Get the unit and site information
        Domain.Units.Unit? unit = await _dataContext
            .Units.AsNoTracking()
            .Include(u => u!.Block)
            .ThenInclude(b => b!.Site)
            .ThenInclude(s => s!.Customer)
            .SingleOrDefaultAsync(u => u.Id == notification.UnitId, cancellationToken);

        if (unit is null)
        {
            return;
        }

        // Get the weather settings for the site
        SiteWeatherSettings? weatherSettings = await _dataContext
            .SiteWeatherSettings.AsNoTracking()
            .Where(sws => sws.SiteId == unit.Block.SiteId)
            .Include(sws => sws.WeatherBiasSettings)
            .SingleOrDefaultAsync(cancellationToken);

        if (weatherSettings?.ForecastSettings.IsForecastEnabled != true)
        {
            return;
        }

        // Get the bias settings for the unit
        List<WeatherBiasSettings>? unitBiasSettings =
        [
            .. weatherSettings.WeatherBiasSettings.Where(wbs => wbs.UnitId == unit.Id),
        ];

        // Get the forecast data from the weather service
        List<WeatherConditionResult> forecastData = await _weatherServiceFactory
            .Get(weatherSettings.ForecastSettings.ForecastServiceSource)
            .GetHourlyForecastAsync(
                latitude: unit.Block.Site.Latitude,
                longitude: unit.Block.Site.Longitude,
                altitude: unit.Block.Site.Altitude,
                unitSystem: unit.Block.Site.IsMetric ? UnitSystem.Metric : UnitSystem.Imperial,
                ct: cancellationToken
            );

        // If there is no forecast data, then skip the processing
        if (forecastData.Count == 0)
            return;

        // Query the bias for a specific type (Temp bias from RTP for example)
        ConcurrentBag<HourlyWeatherBiasDto> biasesOfType = [];
        Parallel.ForEach(
            unitBiasSettings,
            (biasSettings) =>
            {
                // If the bias is not enabled, skip it
                if (!biasSettings.IsBiasEnabled)
                {
                    return;
                }

                List<HourlyWeatherBiasDto> biasResultsForType =
                    _weatherBiasReadRepo.GetWeatherBiases(
                        unitAlias: unit.Alias,
                        weatherService: biasSettings.BiasServiceSource,
                        tagSource: biasSettings.TagSource,
                        biasType: biasSettings.BiasType,
                        unixStartTime: DateTimeOffset.UtcNow.AddDays(-7).ToUnixTimeMilliseconds(),
                        unixEndTime: DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                        biasLimit: BiasLimit
                            .Create(biasSettings.BiasLimit.Min, biasSettings.BiasLimit.Max)
                            .Value
                    );

                biasResultsForType.ForEach(r => biasesOfType.Add(r));
            }
        );

        // Group the bias by each type
        Dictionary<WeatherBiasType, List<HourlyWeatherBiasDto>> biasesGroupByType = biasesOfType
            .Where(b => b.Bias is not null)
            .GroupBy(b => b.Bias!.BiasType)
            .ToDictionary(key => key.Key, value => value.ToList());

        // Get the end time of the forecast
        // NOTE - 0-index => 2 days means 2 days from now - today, tomorrow and the day after
        DateTime startOfTomorrow = new(
            DateTime.Now.AddDays(1).Year,
            DateTime.Now.AddDays(1).Month,
            DateTime.Now.AddDays(1).Day,
            0,
            0,
            0
        );
        DateTime forecastEndTime = startOfTomorrow.AddDays(
            weatherSettings.ForecastSettings.ForecastDays
        );

        List<WeatherBiasModelResponse> data =
        [
            .. forecastData
                .Where(f =>
                    TimeZoneInfo.ConvertTimeFromUtc(
                        DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                        TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone)
                    ) <= forecastEndTime
                )
                .Select(f =>
                {
                    DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(
                        DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                        TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone)
                    );
                    int hour = localTime.Hour;
                    int hourEnding = hour == 0 ? 24 : hour;

                    AmbientBiasDataPoint? tempBias = GetBiasValueForHourEnding(
                        biasesGroupByType,
                        WeatherBiasType.Temp,
                        hourEnding
                    );
                    AmbientBiasDataPoint? rhBias = GetBiasValueForHourEnding(
                        biasesGroupByType,
                        WeatherBiasType.RH,
                        hourEnding
                    );
                    AmbientBiasDataPoint? pressBias = GetBiasValueForHourEnding(
                        biasesGroupByType,
                        WeatherBiasType.Press,
                        hourEnding
                    );
                    AmbientBiasDataPoint? citBias = GetBiasValueForHourEnding(
                        biasesGroupByType,
                        WeatherBiasType.CIT,
                        hourEnding
                    );

                    ObservedWeatherDataPoint observedTemp = new(
                        ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                        BiasType: WeatherBiasType.Temp,
                        Value: f.Temp?.Format().Value
                    );
                    ObservedWeatherDataPoint observedRh = new(
                        ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                        BiasType: WeatherBiasType.RH,
                        Value: f.RelativeHumidity
                    );
                    ObservedWeatherDataPoint observedPress = new(
                        ApiSource: Enum.Parse<WeatherService>(f.ApiName),
                        BiasType: WeatherBiasType.Press,
                        Value: unit.Block.Site.IsMetric
                            ? f.AbsolutePressure?.ToMBar().Format().Value
                            : f.AbsolutePressure?.ToPsi().Format().Value
                    );

                    return new WeatherBiasModelResponse(
                        UnitId: unit.Id,
                        TimestampUtc: DateTimeOffset
                            .FromUnixTimeSeconds(f.UnixTimeInSeconds)
                            .UtcDateTime,
                        LocalTimestamp: localTime,
                        HourEnding: hourEnding,
                        Description: f.Description,
                        Api: f.ApiName,
                        TempRaw: f.Temp?.Format().Value,
                        TempBias: tempBias?.Value,
                        TempAdj: observedTemp.ApplyBias(tempBias)?.Value,
                        CITBias: citBias?.Value,
                        DewPoint: null,
                        RhRaw: f.RelativeHumidity,
                        RhBias: rhBias?.Value,
                        RhAdj: observedRh.ApplyBias(rhBias)?.Value,
                        PressBaro: unit.Block.Site.IsMetric
                            ? f.MeanSeaLevelPressure?.ToMBar().Format().Value
                            : f.MeanSeaLevelPressure?.ToInHg().Format().Value,
                        PressRaw: unit.Block.Site.IsMetric
                            ? f.AbsolutePressure?.ToMBar().Format().Value
                            : f.AbsolutePressure?.ToPsi().Format().Value,
                        PressBias: pressBias?.Value,
                        PressAdj: observedPress.ApplyBias(pressBias)?.Value,
                        WindSpeed: f.WindSpd?.Value,
                        WindDir: f.WindDirection?.Localized,
                        WindDirDegree: f.WindDirection?.Degrees,
                        Precip: f.Precipitation?.Value,
                        UvIndex: f.UvIndex
                    );
                }),
        ];

        await _weatherBiasWriteRepo.SaveWeatherForecastAsync(
            data: data.ConvertAll(d => new WeatherForecastDto(
                TimestampUtc: d.TimestampUtc,
                LocalTimestamp: d.LocalTimestamp,
                Hour: d.HourEnding,
                Description: d.Description,
                Api: d.Api,
                TempRaw: d.TempRaw,
                TempBias: d.TempBias,
                TempAdj: d.TempAdj,
                CITBias: d.CITBias,
                DewPoint: d.DewPoint,
                RhRaw: d.RhRaw,
                RhBias: d.RhBias,
                RhAdj: d.RhAdj,
                PressBaro: d.PressBaro,
                PressRaw: d.PressRaw,
                PressBias: d.PressBias,
                PressAdj: d.PressAdj,
                WindSpeed: d.WindSpeed,
                WindDir: d.WindDir,
                WindDirDegree: d.WindDirDegree,
                Precip: d.Precip,
                UvIndex: d.UvIndex
            )),
            customerName: unit.Block.Site.Customer.Name,
            directory: WeatherBlobDirectory.Create(unit),
            ct: cancellationToken
        );
    }

    /// <summary>
    /// Get the bias value for a specific hour ending
    /// </summary>
    /// <param name="biasesGroupByType">The biases grouped by type</param>
    /// <param name="biasType">The type of bias to get</param>
    /// <param name="hourEnding">The hour ending to get the bias for</param>
    /// <returns>The bias value for the specific hour ending</returns>
    private static AmbientBiasDataPoint? GetBiasValueForHourEnding(
        Dictionary<WeatherBiasType, List<HourlyWeatherBiasDto>> biasesGroupByType,
        WeatherBiasType biasType,
        int hourEnding
    ) =>
        biasesGroupByType.TryGetValue(biasType, out List<HourlyWeatherBiasDto>? value)
            ? biasType switch
            {
                WeatherBiasType.Temp => value
                    .SingleOrDefault(b => b.HourEnding == hourEnding)
                    ?.Bias,
                WeatherBiasType.RH => value.SingleOrDefault(b => b.HourEnding == hourEnding)?.Bias,
                WeatherBiasType.Press => value
                    .SingleOrDefault(b => b.HourEnding == hourEnding)
                    ?.Bias,
                WeatherBiasType.CIT => value.SingleOrDefault(b => b.HourEnding == hourEnding)?.Bias,
                _ => null,
            }
            : null;
}
