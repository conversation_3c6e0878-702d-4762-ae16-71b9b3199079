using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.SaveWeatherBiasModel;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.RunWeatherBiasModel;

internal sealed class RunWeatherBiasModelCommandHandler(
    IDataContext dataContext,
    IMediator mediator
) : ICommandHandler<RunWeatherBiasModelCommand>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IMediator _mediator = mediator;

    public async Task<ErrorOr<Unit>> Handle(
        RunWeatherBiasModelCommand request,
        CancellationToken cancellationToken
    )
    {
        List<Domain.Units.Unit> units = await _dataContext
            .Units.AsNoTracking()
            .Include(u => u!.Block)
            .ThenInclude(b => b!.Site)
            .ThenInclude(s => s!.Customer)
            .ToListAsync(cancellationToken);

        foreach (Domain.Units.Unit unit in units)
        {
            await _mediator.Publish(
                new SaveWeatherBiasModelNotification(UnitId: unit.Id),
                cancellationToken
            );

            // Delay for 0.5 second to avoid rate limiting
            await Task.Delay(500, cancellationToken);
        }

        return Unit.Value;
    }
}
