using System.Collections.Concurrent;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions;

public class GetCurrentWeatherConditionsQueryHandler
    : IQueryHandler<GetCurrentWeatherConditionsQuery, List<CurrentConditionsResponse>>
{
    private readonly IDataContext _context;
    private readonly IWeatherServiceFactory _weatherServiceFactory;

    public GetCurrentWeatherConditionsQueryHandler(
        IDataContext context,
        IWeatherServiceFactory weatherServiceFactory)
    {
        _context = context;
        _weatherServiceFactory = weatherServiceFactory;
    }

    public async Task<ErrorOr<List<CurrentConditionsResponse>>> Handle(
        GetCurrentWeatherConditionsQuery request,
        CancellationToken ct)
    {
        // Query site data
        var sites = await _context.Sites
            .Where(x => request.SiteIds.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.LocationKey,
                x.Latitude,
                x.Longitude,
                x.Altitude
            })
            .ToListAsync(cancellationToken: ct);

        ConcurrentBag<WeatherConditionResult> weatherConditions = new();
        await Parallel.ForEachAsync(sites, async (site, ct) =>
        {
            // Query current condition from AccuWeather
            WeatherConditionResult? weatherCondition = await _weatherServiceFactory
                .Get(Domain.SiteWeatherSettings.WeatherService.AW)
                .GetCurrentConditionsAsync(
                    latitude: site.Latitude,
                    longitude: site.Longitude,
                    altitude: site.Altitude,
                    ct: ct);

            if (weatherCondition is null)
                return;

            weatherConditions.Add(weatherCondition);
        });

        // Then we should return the WeatherDto
        // return weatherConditions.ToList();
        return weatherConditions.Select(
            wc => new CurrentConditionsResponse(
                ApiName: wc.ApiName,
                LocalObservationTime: wc.LocalTime,
                EpochTime: wc.UnixTimeInSeconds,
                WeatherText: wc.Description,
                WeatherIcon: wc.WeatherIcon,
                Temperature: wc.Temp?.Value,
                RelativeHumidity: wc.RelativeHumidity,
                Pressure: wc.AbsolutePressure?.Value,
                WindSpeed: wc.WindSpd?.Value,
                WindDirectionDegrees: wc.WindDirection?.Degrees,
                WindDirection: wc.WindDirection?.Localized ?? string.Empty,
                Precipitation: wc.Precipitation?.Value,
                WetBulbTemperature: wc.WetBulbTemp?.Value)).ToList();
    }
}
