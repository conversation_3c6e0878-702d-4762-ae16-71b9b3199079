using RTP.StatusMonitor.Domain.EngUnits.Types;

namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
public record WeatherConditionResult
{
    public string ApiName { get; set; } = string.Empty;

    /// <summary>
    /// The local time in the format "yyyy-MM-dd HH:mm:ss" where weather data was observed
    /// </summary>
    public string LocalTime { get; set; } = string.Empty;

    /// <summary>
    /// The unix time (UTC) in seconds (ex 1720000000)
    /// </summary>
    public int UnixTimeInSeconds { get; set; }
    public string Description { get; set; } = string.Empty;
    public int WeatherIcon { get; set; }
    public Temperature? Temp { get; set; }
    public int RelativeHumidity { get; set; }
    public MeanSeaLevelPressure? MeanSeaLevelPressure { get; set; }
    public AbsolutePressure? AbsolutePressure { get; set; }
    public Velocity? WindSpd { get; set; }
    public WindDirectionDto? WindDirection { get; set; }
    public Precipitation? Precipitation { get; set; }
    public Temperature? WetBulbTemp { get; set; }
    public int? UvIndex { get; set; } = null;
}
