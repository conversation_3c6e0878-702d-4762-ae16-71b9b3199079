namespace RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions;

public record CurrentConditionsResponse(
    string ApiName,
    string LocalObservationTime,
    int EpochTime,
    string WeatherText,
    int WeatherIcon,
    double? Temperature,
    int RelativeHumidity,
    double? Pressure,
    double? WindSpeed,
    string WindDirection,
    double? WindDirectionDegrees,
    double? Precipitation,
    double? WetBulbTemperature);