using ErrorOr;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.GetSiteWeatherSettings;

internal sealed class GetSiteWeatherSettingsQueryHandler(
    ISiteWeatherSettingsRepository siteWeatherSettingsRepository)
        : IQueryHandler<GetSiteWeatherSettingsQuery, SiteWeatherSettingsResponse>
{
    private readonly ISiteWeatherSettingsRepository _siteWeatherSettingsRepository = siteWeatherSettingsRepository;

    public async Task<ErrorOr<SiteWeatherSettingsResponse>> Handle(GetSiteWeatherSettingsQuery query, CancellationToken ct)
    {
        // Query site weather settings
        SiteWeatherSettings? weatherSettings = await _siteWeatherSettingsRepository
            .GetWeatherSettingsBySiteId(query.SiteId!.Value, ct);

        // Return the response if the site weather settings is found
        return weatherSettings is null
            ? SiteWeatherSettingsErrors.NotFound
            : new SiteWeatherSettingsResponse(
                Id: weatherSettings.Id,
                ForecastSettings: new(
                    IsForecastEnabled: weatherSettings.ForecastSettings.IsForecastEnabled,
                    ForecastDays: weatherSettings.ForecastSettings.ForecastDays,
                    IsMetric: weatherSettings.ForecastSettings.IsMetric,
                    ForecastServiceSource: weatherSettings.ForecastSettings.ForecastServiceSource.ToString()),
                SiteId: weatherSettings.SiteId,
                LightningStrikeSettings: new(
                    IsLightningStrikeEnabled: weatherSettings.LightningStrikeSettings.IsLightningStrikesEnabled,
                    LightningRadius: weatherSettings.LightningStrikeSettings.LightningRadius),
                WeatherBiasSettings: weatherSettings.WeatherBiasSettings.Select(wbs => new WeatherBiasSettingsResponse(
                    Id: wbs.Id,
                    UnitId: wbs.UnitId,
                    IsBiasEnabled: wbs.IsBiasEnabled,
                    BiasType: wbs.BiasType.ToString(),
                    TagSource: wbs.TagSource.ToString(),
                    BiasServiceSource: wbs.BiasServiceSource.ToString(),
                    Min: wbs.BiasLimit.Min,
                    Max: wbs.BiasLimit.Max,
                    IsWindEffectEnabled: wbs.WindEffect.IsWindEffectsEnabled,
                    Expression: wbs.WindEffect.Expression)).ToList(),
                    BiasTagSettings: weatherSettings.BiasTagSettings
                        .Where(bts => bts.Tag != null)
                        .Select(bts => new BiasTagSettingsResponse(
                            Id: bts.Id,
                            UnitId: bts.UnitId.Value,
                            Tag: bts.Tag ?? string.Empty,
                            Expression: bts.ComputedExpression.Value,
                            BiasType: bts.BiasType.ToString(),
                            TagSource: bts.TagSource.ToString())).ToList());
    }
}
