using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Weather.GetSiteWeatherSettings;

public record GetSiteWeatherSettingsQuery
    : IQuery<SiteWeatherSettingsResponse>, IAuthorizedQuery<SiteWeatherSettingsResponse>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = new();
    public Guid? BlockId { get; set; } = null; // not applicable for this query
    public Guid? UnitId { get; set; } = null; // not applicable for this query
    public Guid? SiteId { get; set; }
}


