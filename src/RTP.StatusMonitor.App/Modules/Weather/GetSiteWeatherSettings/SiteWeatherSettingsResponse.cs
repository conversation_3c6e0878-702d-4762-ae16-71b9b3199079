namespace RTP.StatusMonitor.App.Modules.Weather.GetSiteWeatherSettings;

public record SiteWeatherSettingsResponse(
    Guid Id,
    ForecastSettingsResponse ForecastSettings,
    LightningStrikeSettingsResponse LightningStrikeSettings,
    List<WeatherBiasSettingsResponse> WeatherBiasSettings,
    List<BiasTagSettingsResponse> BiasTagSettings,
    Guid SiteId);

public record ForecastSettingsResponse(
    bool IsForecastEnabled,
    int ForecastDays,
    bool IsMetric,
    string ForecastServiceSource);

public record LightningStrikeSettingsResponse(
    bool IsLightningStrikeEnabled,
    int LightningRadius);

public record WeatherBiasSettingsResponse(
    Guid Id,
    Guid UnitId,
    bool IsBiasEnabled,
    string BiasType,
    string TagSource,
    string BiasServiceSource,
    double? Min,
    double? Max,
    bool IsWindEffectEnabled,
    string Expression);

public record BiasTagSettingsResponse(
    Guid Id,
    Guid UnitId,
    string Tag,
    string Expression,
    string BiasType,
    string TagSource);

