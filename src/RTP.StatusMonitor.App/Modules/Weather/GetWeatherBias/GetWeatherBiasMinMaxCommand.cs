using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherBias;

public sealed record GetWeatherBiasMinMaxCommand(
    Guid UnitId,
    List<WeatherBiasSettingsCommand> WeatherBiasSettings
) : IQuery<List<WeatherBiasResponse>>;

public sealed record WeatherBiasSettingsCommand(
    WeatherService BiasServiceSource,
    WeatherTagSource TagSource,
    WeatherBiasType BiasType
);
