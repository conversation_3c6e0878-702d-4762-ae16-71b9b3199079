using System.Collections.Concurrent;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Weather.GetWeatherBias;

internal sealed class GetWeatherBiasMinMaxCommandHandler
    : IQueryHandler<GetWeatherBiasMinMaxCommand, List<WeatherBiasResponse>>
{
    private readonly DataContext _dataContext;
    private readonly WeatherReadRepository _weatherBiasReadRepo;

    public GetWeatherBiasMinMaxCommandHandler(
        DataContext dataContext,
        WeatherReadRepository weatherBiasReadRepo
    )
    {
        _dataContext = dataContext;
        _weatherBiasReadRepo = weatherBiasReadRepo;
    }

    public async Task<ErrorOr<List<WeatherBiasResponse>>> Handle(
        GetWeatherBiasMinMaxCommand request,
        CancellationToken cancellationToken
    )
    {
        Unit? unit = await _dataContext
            .Units.AsNoTracking()
            .Include(u => u!.Block)
            .ThenInclude(b => b!.Site)
            .ThenInclude(s => s!.Customer)
            .SingleOrDefaultAsync(u => u.Id == request.UnitId, cancellationToken);

        if (unit is null)
        {
            return UnitErrors.NotFound;
        }

        List<WeatherBiasSettings> biasSettings = await _dataContext
            .WeatherBiasSettings.AsNoTracking()
            .Where(wbs => wbs.UnitId == unit.Id)
            .ToListAsync(cancellationToken);

        if (biasSettings is null)
        {
            return SiteWeatherSettingsErrors.NotFound;
        }

        // Query the bias for a specific type (Temp bias from RTP for example)
        ConcurrentBag<HourlyWeatherBiasDto> biases = [];
        Parallel.ForEach(
            request.WeatherBiasSettings,
            wbs =>
            {
                // Find the min/max setting of the bias tag
                BiasLimit? biasLimit = biasSettings
                    .FirstOrDefault(bs =>
                        bs.BiasType == wbs.BiasType
                        && bs.BiasServiceSource == wbs.BiasServiceSource
                        && bs.TagSource == wbs.TagSource
                    )
                    ?.BiasLimit;

                if (biasLimit is null)
                {
                    return;
                }

                // Get the according hourly biases based on the settings
                List<HourlyWeatherBiasDto> hourlyBiases = _weatherBiasReadRepo.GetWeatherBiases(
                    unitAlias: unit.Alias,
                    weatherService: wbs.BiasServiceSource,
                    tagSource: wbs.TagSource,
                    biasType: wbs.BiasType,
                    unixStartTime: DateTimeOffset.UtcNow.AddDays(-7).ToUnixTimeMilliseconds(),
                    unixEndTime: DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    biasLimit: BiasLimit.Create(biasLimit.Min, biasLimit.Max).Value
                );
                hourlyBiases.ForEach(bias => biases.Add(bias));
            }
        );

        // Group the biases by type and find min/max of each type
        return biases
            .Where(b => b.Bias is not null)
            .GroupBy(b => b.Bias!.BiasType)
            .Select(g => new WeatherBiasResponse(
                BiasType: g.Key.ToString(),
                Min: WeatherBiasFormatter.Format(biasType: g.Key, value: g.Min(b => b.Bias?.Value)),
                Max: WeatherBiasFormatter.Format(biasType: g.Key, value: g.Max(b => b.Bias?.Value))
            ))
            .ToList();
    }
}
