using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeather;

public record SaveSiteWeatherCommand(
    Guid SiteWeatherSettingId,
    Guid SiteId,
    ForecastSettingsCommand ForecastSettings,
    LightningStrikeSettingsCommand LightningStrikeSettings,
    List<WeatherBiasSettingsCommand> WeatherBiasSettings,
    List<BiasTagSettingsCommand> BiasTagSettings
) : ICommand;

public record ForecastSettingsCommand(
    bool IsForecastEnabled,
    int ForecastDays,
    bool IsMetric,
    string ForecastServiceSource
);

public record LightningStrikeSettingsCommand(bool IsLightningStrikeEnabled, int LightningRadius);

public record WeatherBiasSettingsCommand(
    Guid Id,
    Guid UnitId,
    bool IsBiasEnabled,
    string BiasType,
    string TagSource,
    string BiasServiceSource,
    double? Min,
    double? Max,
    bool IsWindEffectEnabled,
    string Expression
);

public record BiasTagSettingsCommand(
    Guid Id,
    Guid UnitId,
    string Tag,
    string BiasType,
    string TagSource,
    string Expression
);
