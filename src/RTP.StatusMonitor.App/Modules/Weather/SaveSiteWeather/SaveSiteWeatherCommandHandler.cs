using ErrorOr;
using MediatR;
using RTP.StatusMonitor.App.Modules.Weather.SaveWeatherBiasModel;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Weather.SaveSiteWeather;

internal sealed class SaveSiteWeatherCommandHandler : ICommandHandler<SaveSiteWeatherCommand>
{
    private readonly ISiteWeatherSettingsRepository _siteWeatherSettingsRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ISiteRepository _siteRepository;
    private readonly IMediator _mediator;

    public SaveSiteWeatherCommandHandler(
        ISiteWeatherSettingsRepository siteWeatherSettingsRepository,
        IUnitOfWork unitOfWork,
        ISiteRepository siteRepository,
        IMediator mediator
    )
    {
        _siteWeatherSettingsRepository = siteWeatherSettingsRepository;
        _siteRepository = siteRepository;
        _unitOfWork = unitOfWork;
        _mediator = mediator;
    }

    public async Task<ErrorOr<Unit>> Handle(
        SaveSiteWeatherCommand command,
        CancellationToken cancellationToken
    )
    {
        (
            Guid siteWeatherSettingId,
            Guid _,
            ForecastSettingsCommand forecastSettings,
            LightningStrikeSettingsCommand lightningStrikeSettings,
            List<WeatherBiasSettingsCommand> weatherBiasSettings,
            List<BiasTagSettingsCommand> biasTagSettings
        ) = command;

        // Query site data to attach to the site weather settings
        Site? site = await _siteRepository.GetSiteByIdAsync(command.SiteId, cancellationToken);

        if (site is null)
        {
            return SiteDomainErrors.NotFound;
        }

        ErrorOr<Unit> result = await ForecastSettings
            .Create(
                forecastSettings.IsForecastEnabled,
                forecastSettings.ForecastDays,
                forecastSettings.IsMetric,
                forecastSettings.ForecastServiceSource
            )
            .Else(forecastSettingsErrors => forecastSettingsErrors)
            .ThenAsync(forecastSettings =>
                CreateBiasSettingsForSite(siteWeatherSettingId, weatherBiasSettings)
                    .Else(biasSettingsErrors => biasSettingsErrors)
                    .ThenAsync(sbs =>
                        CreateBiasTagSettingsForSite(siteWeatherSettingId, biasTagSettings)
                            .Else(biasTagSettingsErrors => biasTagSettingsErrors)
                            .ThenAsync(async bts =>
                            {
                                SiteWeatherSettings siteWeatherSettings = new(
                                    id: siteWeatherSettingId,
                                    forecastSettings: forecastSettings,
                                    lightningStrikeSettings: new(
                                        IsLightningStrikesEnabled: lightningStrikeSettings.IsLightningStrikeEnabled,
                                        LightningRadius: lightningStrikeSettings.LightningRadius
                                    ),
                                    weatherBiasSettings: sbs,
                                    biasTagSettings: bts,
                                    site: site
                                );

                                await _siteWeatherSettingsRepository.SaveSiteWeatherSetttings(
                                    siteWeatherSettings,
                                    cancellationToken
                                );

                                await _unitOfWork.SaveChangesAsync(cancellationToken);

                                return Unit.Value;
                            })
                    )
            );

        // If updating the site weather settings was successful, update the latest bias model for all block to blob storage
        if (!result.IsError)
        {
            // Update the weather with bias to blob storage for each unit
            foreach (Domain.Units.Unit unit in site.Blocks.SelectMany(b => b.Units).ToList())
            {
                await _mediator.Publish(
                    new SaveWeatherBiasModelNotification(UnitId: unit.Id),
                    cancellationToken
                );

                // Delay for 0.5 second to avoid rate limiting
                await Task.Delay(500, cancellationToken);
            }
        }

        return result;
    }

    /// <summary>
    /// Create the bias tag settings for the site.
    /// If any of the bias tag settings are invalid, return the errors.
    /// </summary>
    /// <param name="siteWeatherSettingsId">The site weather settings id to which the bias tag settings belong</param>
    /// <returns>The list of bias settings for the site or the errors if any of the bias settings are invalid</returns>
    private static ErrorOr<List<BiasTagSettings>> CreateBiasTagSettingsForSite(
        Guid siteWeatherSettingsId,
        List<BiasTagSettingsCommand> biasTagSettingCommands
    )
    {
        IEnumerable<ErrorOr<BiasTagSettings>> biasTagSettings = biasTagSettingCommands.Select(bts =>
            BiasTagSettings.Create(
                id: bts.Id,
                unitId: bts.UnitId,
                tag: bts.Tag,
                expression: new(bts.Expression),
                biasType: bts.BiasType,
                tagSource: bts.TagSource,
                siteWeatherSettingsId: siteWeatherSettingsId
            )
        );

        return biasTagSettings.Any(bts => bts.IsError)
            ? biasTagSettings.SelectMany(bts => bts.Errors).ToList()
            : biasTagSettings.Select(bts => bts.Value).ToList();
    }

    /// <summary>
    /// Attempt to create bias settings for the site.
    /// Return the errors if any of the bias settings are invalid.
    /// </summary>
    /// <returns>The list of bias settings for the site or the errors if any of the bias settings are invalid</returns>
    private static ErrorOr<List<WeatherBiasSettings>> CreateBiasSettingsForSite(
        Guid siteWeatherSettingsId,
        List<WeatherBiasSettingsCommand> biasSettingsCommands
    )
    {
        List<WeatherBiasSettings> siteBiasSettings = [];
        foreach (var biasSettings in biasSettingsCommands)
        {
            ErrorOr<BiasLimit> limit = BiasLimit.Create(biasSettings.Min, biasSettings.Max);

            if (limit.IsError)
            {
                return limit.Errors;
            }

            ErrorOr<WeatherBiasSettings> weatherBiasSettings = WeatherBiasSettings.Create(
                id: biasSettings.Id,
                unitId: biasSettings.UnitId,
                isBiasEnabled: biasSettings.IsBiasEnabled,
                weatherBiasType: biasSettings.BiasType,
                tagSource: biasSettings.TagSource,
                biasServiceSource: biasSettings.BiasServiceSource,
                biasLimit: limit.Value,
                windEffect: new WindEffect(
                    biasSettings.IsWindEffectEnabled,
                    biasSettings.Expression
                ),
                siteWeatherSettingsId: siteWeatherSettingsId
            );

            if (weatherBiasSettings.IsError)
                return weatherBiasSettings.Errors;

            siteBiasSettings.Add(weatherBiasSettings.Value);
        }

        return siteBiasSettings;
    }
}
