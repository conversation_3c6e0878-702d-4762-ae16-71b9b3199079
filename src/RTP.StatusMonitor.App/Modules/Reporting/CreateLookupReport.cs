using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record CreateLookupReportCommand(
    LookupReportCommand LookupReport,
    string RequestedBy) : ICommand;

internal sealed class CreateLookupReportCommandHandler(
    IDataContext dataContext,
    IUnitOfWork unitOfWork,
    IDateTimeProvider dateTimeProvider,
    IReportRepository reportRepository)
        : ICommandHandler<CreateLookupReportCommand>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IReportRepository _reportRepository = reportRepository;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IUnitOfWork _unitOfWork = unitOfWork;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        CreateLookupReportCommand request,
        CancellationToken ct)
    {
        Block? block = await _dataContext.Blocks
            .Include(b => b.Site)
            .Include(b => b.Units)
            .FirstOrDefaultAsync(b => b.Id == request.LookupReport.BlocksInReport.First(), ct);

        if (block is null)
            return BlockDomainErrors.NotFound;

        // Create the filters 
        List<ErrorOr<ReportFilter>> filters = request.LookupReport
            .MainFilters
            .Select(filter => ReportFilter.Create(
                siteId: filter.SiteId,
                blockId: filter.BlockId,
                unitId: filter.UnitId,
                filter: new(filter.Filter),
                ruleGroup: filter.RuleGroup))
            .ToList();

        // Create the report columns (add variable grouping as well for lookup)
        List<ErrorOr<ReportColumn>> columns = request.LookupReport
            .ReportColumns
            .Select(col =>
                ReportColumn.Create(
                    reportColumnId: col.ReportColumnId,
                    siteId: col.SiteId,
                    blockId: col.BlockId,
                    unitId: col.UnitId,
                    name: col.Name,
                    sources: col.Sources,
                    tag: col.Tag,
                    calculation: new(col.Calculation),
                    filter: new(col.Filter),
                    position: col.Position,
                    noOfDecimals: col.NoOfDecimals,
                    engUnits: col.EngUnits,
                    color: col.Color))
            .ToList();

        List<VariableGroupCommand> variableGroups =
        [
            request.LookupReport.VariableGrouping.Row,
            request.LookupReport.VariableGrouping.Column
        ];

        List<ErrorOr<ReportColumn>> columnsFromVariableGrouping = variableGroups
            .Select(vg => ReportColumn.Create(
                    reportColumnId: Guid.NewGuid(),
                    siteId: block.SiteId,
                    blockId: request.LookupReport.BlocksInReport.First(),
                    unitId: block.Units.First().Id,
                    name: vg.Name,
                    sources: [request.LookupReport.VariableGrouping.Source],
                    tag: vg.Tag,
                    calculation: new(string.Empty),
                    filter: new(string.Empty),
                    position: 0,
                    noOfDecimals: 0,
                    engUnits: "",
                    color: ""))
            .ToList();

        // Create the report schedule
        ErrorOr<ReportSchedule> schedule = ReportSchedule
            .Create(
                request.LookupReport.IsEnabled,
                request.LookupReport.ProcessInterval);

        // Create the time grouping
        ErrorOr<ReportTimeGrouping> timeGrouping = ReportTimeGrouping
            .Create(
                request.LookupReport.GroupingInterval,
                ["Avg"]);

        // Create the variable grouping
        ErrorOr<ReportVariableGrouping> variableGrouping = VariableGroup
            .Create(
                tag: request.LookupReport.VariableGrouping.Row.Tag,
                name: request.LookupReport.VariableGrouping.Row.Name,
                min: request.LookupReport.VariableGrouping.Row.Min,
                max: request.LookupReport.VariableGrouping.Row.Max,
                interval: request.LookupReport.VariableGrouping.Row.Interval)
            .Then(row => VariableGroup
                .Create(
                    tag: request.LookupReport.VariableGrouping.Column.Tag,
                    name: request.LookupReport.VariableGrouping.Column.Name,
                    min: request.LookupReport.VariableGrouping.Column.Min,
                    max: request.LookupReport.VariableGrouping.Column.Max,
                    interval: request.LookupReport.VariableGrouping.Column.Interval)
                .Then(column => ReportVariableGrouping
                    .Create(
                        source: request.LookupReport.VariableGrouping.Source,
                        row: row,
                        column: column)));

        // Create the layout for lookup report
        ErrorOr<ReportLayout> reportLayout = ReportLayout
            .CreateLookupLayout(ReportType.Lookup, request.LookupReport.ReportLayout);

        // Make sure everything is valid
        ErrorOr<Success> results = ErrorOrExt
            .Combine(filters)
            .Then(_ => ErrorOrExt.Combine(columns.Concat(columnsFromVariableGrouping)))
            .Then(_ => ErrorOrExt.Combine(schedule, timeGrouping, variableGrouping, reportLayout));
        if (results.IsError)
            return results.Errors;

        List<Report> existingReports = await _reportRepository.GetReportsAsync(ct);

        // Create the report
        ErrorOr<Report> report = request.LookupReport.DateRangeInfo switch
        {
            ReportFixedDateRangeCommand fixedDateRange
                => FixedReportDateRange
            .Create(
                fixedDateRange.StartDate,
                fixedDateRange.EndDate)
            .Else(fixedDateRangeErrors => fixedDateRangeErrors)
            .Then(fixedDateRange =>
                Report.CreateLookupReport(
                    reportId: request.LookupReport.ReportId,
                    name: request.LookupReport.Name,
                    description: request.LookupReport.Description,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    variableGrouping: variableGrouping.Value,
                    columns: columns
                        .Concat(columnsFromVariableGrouping)
                        .Select(c => c.Value)
                        .DistinctBy(c => c.Name)
                        .ToList(),
                    schedule: schedule.Value,
                    container: request.LookupReport.Container,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(request.LookupReport.FileFormat),
                    userGroupsAllowed: request.LookupReport.UserGroupsAllowed,
                    blocksInReport: request.LookupReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: request.RequestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: request.RequestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: fixedDateRange,
                    existingReports: existingReports.ToList())
            ),
            ReportRelativeDateRangeCommand relativeDateRange
                => RelativeReportDateRange.Create(
                relativeDateRange.StartOffset,
                relativeDateRange.EndOffset)
            .Else(errors => errors)
            .Then(relativeDateRange =>
                Report.CreateLookupReport(
                    reportId: request.LookupReport.ReportId,
                    name: request.LookupReport.Name,
                    description: request.LookupReport.Description,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    variableGrouping: variableGrouping.Value,
                    columns: columns
                        .Concat(columnsFromVariableGrouping)
                        .Select(c => c.Value)
                        .DistinctBy(c => c.Name)
                        .ToList(),
                    schedule: schedule.Value,
                    container: request.LookupReport.Container,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(request.LookupReport.FileFormat),
                    userGroupsAllowed: request.LookupReport.UserGroupsAllowed,
                    blocksInReport: request.LookupReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: request.RequestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: request.RequestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: relativeDateRange,
                    existingReports: existingReports.ToList())),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };

        if (report.IsError)
            return report.Errors;

        await _reportRepository.AddReportAsync(report.Value, ct);

        await _unitOfWork.SaveChangesAsync(ct);

        return MediatR.Unit.Value;
    }
}
