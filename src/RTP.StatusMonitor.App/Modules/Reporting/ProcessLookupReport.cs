using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record ProcessLookupReportCommand(Guid ReportId) : ICommand<LookupReportResponse>;

public record LookupReportResponse(
    Guid Id,
    string Name,
    string Description,
    string ReportType,
    List<ReportFilter> MainFilters,
    string GroupingInterval,
    ReportVariableGrouping VariableGrouping,
    List<LookupReportRow> Rows,
    string StartDate,
    string EndDate
);

internal sealed class ProcessLookupReportCommandHandler(
    DataContext dataContext,
    LookupReportProcessor lookupReportProcessor,
    IDateTimeProvider dateTimeProvider
) : ICommandHandler<ProcessLookupReportCommand, LookupReportResponse>
{
    private readonly DataContext _dataContext = dataContext;
    private readonly LookupReportProcessor _lookupReportProcessor = lookupReportProcessor;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;

    public async Task<ErrorOr<LookupReportResponse>> Handle(
        ProcessLookupReportCommand request,
        CancellationToken cancellationToken
    )
    {
        // Find the historical report to process
        Report? lookupReport = await _dataContext
            .Reports.AsNoTracking()
            .Include(r => r.DateRangeInfo)
            .Include(r => r.ReportColumns)
            .FirstOrDefaultAsync(r => r.Id == request.ReportId, cancellationToken);

        if (lookupReport is null)
        {
            return ReportErrors.NotFound;
        }

        // Process the lookup report
        List<LookupReportRow> lookupRows = await _lookupReportProcessor.ProcessReportAsync(
            lookupReport,
            cancellationToken
        );

        // Lookup reports can ONLY have one block
        Block? block = await _dataContext
            .Blocks.Where(b => lookupReport.BlocksInReport.Contains(b.Id))
            .Include(b => b.Site)
            .FirstOrDefaultAsync(cancellationToken);

        if (block is null)
        {
            return ReportErrors.NotFound;
        }

        // Find the start and end date to query based on the report date range
        (DateTime startDate, DateTime endDate) = lookupReport.GetReportDateRange(
            block.Site,
            _dateTimeProvider.UtcNow
        );

        return new LookupReportResponse(
            Id: lookupReport.Id,
            Name: lookupReport.Name,
            Description: lookupReport.Description,
            ReportType: lookupReport.ReportType.ToString(),
            MainFilters: [.. lookupReport.Filters],
            GroupingInterval: lookupReport.TimeGrouping.ToString(),
            VariableGrouping: lookupReport.VariableGrouping!,
            Rows: lookupRows,
            StartDate: startDate.ToString("yyyy-MM-dd"),
            EndDate: endDate.ToString("yyyy-MM-dd")
        );
    }
}
