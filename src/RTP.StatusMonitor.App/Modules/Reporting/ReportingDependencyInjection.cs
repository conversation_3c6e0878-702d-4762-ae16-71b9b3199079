using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.App.Modules.Reporting.Shared;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public static class ReportingDependencyInjection
{
    /// <summary>
    /// This method will register all services in supporting of reporting modules.
    /// </summary>
    /// <param name="services">The service collection to register the services in.</param>
    /// <returns>The service collection with the reporting services registered.</returns>
    public static IServiceCollection AddReportingServices(this IServiceCollection services)
    {
        // Data services
        services.AddTransient<ActualDataAggregation>();
        services.AddTransient<AirsonicDataAggregation>();
        services.AddTransient<DayAheadForecastDataAggregation>();
        services.AddTransient<AccuracyDataAggregation>();

        // Processing services
        services.AddTransient<HistoricalReportProcessor>();
        services.AddTransient<ForecastReportProcessor>();
        services.AddTransient<LookupReportProcessor>();

        // Global filter
        services.AddTransient<ReportGlobalFilter>();

        return services;
    }
}
