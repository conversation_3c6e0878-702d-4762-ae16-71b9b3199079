using Azure.Storage.Blobs;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.App.Modules.Reporting.Shared;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record ProcessScheduledReportCommand(Guid ReportId) : ICommand;

internal sealed class ProcessScheduledReportCommandHandler(
    IOptions<BlobStorageOptions> blobStorageOptions,
    IDataContext dataContext,
    IDateTimeProvider dateTimeProvider,
    ReportProcessor reportProcessor
) : ICommandHandler<ProcessScheduledReportCommand>
{
    public async Task<ErrorOr<Unit>> Handle(
        ProcessScheduledReportCommand request,
        CancellationToken cancellationToken
    )
    {
        // Find the report
        Report? report = await dataContext
            .Reports.Include(x => x.DateRangeInfo)
            .Include(x => x.ReportColumns.OrderBy(rc => rc.Position))
            .FirstOrDefaultAsync(x => x.Id == request.ReportId, cancellationToken);

        if (report is null)
        {
            return ReportErrors.NotFound;
        }

        List<Block> blocksInReport =
        [
            .. dataContext
                .Blocks.AsNoTracking()
                .Where(b => report.BlocksInReport.Contains(b.Id))
                .Include(b => b.Site),
        ];

        List<Site> reportTargertedSites = [.. blocksInReport.Select(b => b.Site)];
        // Make sure report only target single site so we can save it in the site folder
        // Otherwise, DO NOT save
        if (reportTargertedSites.Distinct().Count() > 1)
        {
            return Unit.Value;
        }

        // Process report
        ReportProcessingResult reportData;

        // We are processing only a single report so can use the report as key to get the result (dictionary only has 1 value)
        switch (report.ReportType)
        {
            case ReportType.Forecast:
                {
                    Dictionary<Report, ReportProcessingResult> forecastData = await reportProcessor.ProcessReportsAsync(
                        [report.Id],
                        cancellationToken
                    );
                    reportData = forecastData[report];
                    break;
                }
            case ReportType.Historical:
                {
                    Dictionary<Report, ReportProcessingResult> historicalData =
                        await reportProcessor.ProcessReportsAsync(
                            [report.Id],
                            cancellationToken
                        );
                    reportData = historicalData[report];
                    break;
                }
            case ReportType.Accuracy:
                {
                    Dictionary<Report, ReportProcessingResult> accuracyData = await reportProcessor.ProcessReportsAsync(
                        [report.Id],
                        cancellationToken
                    );
                    reportData = accuracyData[report];
                    break;
                }
            case ReportType.Lookup:
                {
                    Dictionary<Report, ReportProcessingResult> lookupData = await reportProcessor.ProcessReportsAsync(
                        [report.Id],
                        cancellationToken
                    );
                    reportData = lookupData[report];
                    break;
                }
            default:
                throw new InvalidOperationException("Report type not supported");
        }

        // Format the report depending on the report type
        ReportFormatOutput formattedReport = report.ReportType switch
        {
            ReportType.Forecast => ForecastReportFormatter.FormatReport(
                report,
                Enum.Parse<ForecastLayoutType>(report.ReportLayout.Value),
                report.FileFormat,
                (ForecastProcessingResult)reportData
            ),
            ReportType.Historical => HistoricalReportFormatter.FormatReport(
                report,
                Enum.Parse<HistoricalLayoutType>(report.ReportLayout.Value),
                report.FileFormat,
                (HistoricalProcessingResult)reportData
            ),
            // Accuracy report only has 1 layout
            ReportType.Accuracy => AccuracyReportFormatter.FormatReport(
                report,
                report.FileFormat,
                (AccuracyProcessingResult)reportData
            ),
            // Lookup currently only supports single layout 
            ReportType.Lookup => LookupReportFormatter.FormatReport(
                report,
                report.FileFormat,
                (LookupProcessingResult)reportData
            ),
            _ => throw new InvalidOperationException("Report type currently not supported"),
        };

        // Upload to blob storage
        try
        {
            // First check if the container exists
            BlobServiceClient blobServiceClient = new(blobStorageOptions.Value.ConnectionString);

            if (
                string.IsNullOrEmpty(report.Container)
                || !await blobServiceClient
                    .GetBlobContainerClient(report.Container)
                    .ExistsAsync(cancellationToken)
            )
            {
                return BlobStorageErrors.ContainerNotFound;
            }

            // Create blob name
            string siteName = reportTargertedSites[0].Name.Replace(" ", string.Empty).ToLower();
            string blobName = report.FileFormat switch
            {
                ReportFileFormat.CSV => $"{siteName}/reports/{report.Name}.csv",
                ReportFileFormat.Excel => $"{siteName}/reports/{report.Name}.xlsx",
                _ => throw new InvalidOperationException("File format not supported"),
            };

            // Upload to blob storage
            BlobClient blobClient = blobServiceClient
                .GetBlobContainerClient(report.Container)
                .GetBlobClient(blobName);

            await blobClient.UploadAsync(
                new MemoryStream(formattedReport.Content),
                true,
                cancellationToken
            );

            // Record the processing completion
            report.RecordLastProcessingTime(dateTimeProvider.UtcNow);

            // Save the changes
            await dataContext.SaveChangesAsync(cancellationToken);

            return Unit.Value;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(ex.Message);
        }
    }
}
