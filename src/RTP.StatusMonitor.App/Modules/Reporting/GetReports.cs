using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Reporting.ProcessReport;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting;
public record GetReportsQuery(List<Guid> UserGroupIds) : IQuery<List<ReportSummaryResponse>>;
public record ReportSummaryResponse
(
    Guid ReportId,
    string Name,
    string Description,
    string ReportType,
    List<ReportFilterResponse> MainFilters,
    string GroupingInterval,
    List<string> GroupingAggregates,
    ReportVariableGroupingResponse? VariableGrouping,
    List<ReportColumnSummaryResponse> ReportColumns,
    string DateRangeType,
    DateTime? StartDate,
    DateTime? EndDate,
    int? StartOffset,
    int? EndOffset,
    bool IsEnabled,
    string Container,
    string ProcessInterval,
    string? LastProcessedUtc,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    string DateCreated,
    string CreatedBy,
    string DateModified,
    string ModifiedBy
);
public record ReportVariableGroupingResponse(
    string Source,
    VariableGroup Row,
    VariableGroup Column);

public record ReportColumnSummaryResponse
(
    Guid ReportColumnId,
    Guid SiteId,
    Guid BlockId,
    Guid UnitId,
    string Name,
    List<string> Sources,
    string Tag,
    string Calculation,
    string Filter,
    int Position,
    int NoOfDecimals,
    string EngUnits,
    string Color
);

internal sealed class GetReportsQueryHandler(DataContext dbContext)
        : IQueryHandler<GetReportsQuery, List<ReportSummaryResponse>>
{
    private readonly DataContext _dbContext = dbContext;
    public async Task<ErrorOr<List<ReportSummaryResponse>>> Handle(
        GetReportsQuery request,
        CancellationToken ct)
    {
        List<Report> reports = await _dbContext.Reports
            .AsNoTracking()
            .Include(r => r.DateRangeInfo)
            .Include(r => r.ReportColumns)
            .ToListAsync(ct);

        return reports
            .Where(r => r.UserGroupsAllowed.Intersect(request.UserGroupIds).Any())
            .Select(MapToApiResponse)
            .OrderBy(r => r.Name)
            .ToList();
    }

    /// <summary>
    /// Maps a Report to a ReportResponse
    /// </summary>
    /// <param name="report">The report to map</param>
    /// <returns>The mapped report response</returns>
    public static ReportSummaryResponse MapToApiResponse(Report report)
     => new(
         ReportId: report.Id,
         Name: report.Name,
         Description: report.Description,
         ReportType: report.ReportType.ToString(),
         MainFilters: [..report.Filters
            .Select(i => new ReportFilterResponse(
                i.SiteId,
                i.BlockId,
                i.UnitId,
                i.Filter,
                i.RuleGroup.ToString()))],
         GroupingInterval: report.TimeGrouping.GroupingInterval.ToString(),
         GroupingAggregates: report.TimeGrouping.GroupingAggregates
             .Select(ga => ga.ToString())
             .ToList(),
         VariableGrouping: report.ReportType == ReportType.Lookup
            ? new ReportVariableGroupingResponse(
                Source: report.VariableGrouping!.Source.ToString(),
                Row: report.VariableGrouping!.Row,
                Column: report.VariableGrouping!.Column) : null,
         ReportColumns: [.. report.ReportColumns
             .Select(rc => new ReportColumnSummaryResponse(
                 ReportColumnId: rc.Id,
                 SiteId: rc.SiteId,
                 BlockId: rc.BlockId,
                 UnitId: rc.UnitId,
                 Name: rc.Name,
                 Sources: rc.Sources
                     .Select(s => s.ToString())
                     .ToList(),
                 Tag: rc.Tag,
                 Calculation: rc.Calculation.Value,
                 Filter: rc.Filter.Value,
                 Position: rc.Position,
                 NoOfDecimals: rc.NoOfDecimals,
                 EngUnits: rc.EngUnits,
                 Color: rc.Color))
             .OrderBy(rc => rc.Position)],
         BlocksInReport: [.. report.BlocksInReport],
         DateRangeType: report.DateRangeInfo switch
         {
             FixedReportDateRange fixedDateRangeInfo => ReportDateRangeType.Fixed.ToString(),
             RelativeReportDateRange relativeDateRangeInfo => ReportDateRangeType.Relative.ToString(),
             _ => string.Empty
         },
         StartOffset: report.DateRangeInfo switch
         {
             RelativeReportDateRange relativeDateRangeInfo => relativeDateRangeInfo.StartOffset,
             _ => null
         },
         EndOffset: report.DateRangeInfo switch
         {
             RelativeReportDateRange relativeDateRangeInfo => relativeDateRangeInfo.EndOffset,
             _ => null
         },
         StartDate: report.DateRangeInfo switch
         {
             FixedReportDateRange fixedDateRangeInfo => fixedDateRangeInfo.StartDate,
             _ => null
         },
         EndDate: report.DateRangeInfo switch
         {
             FixedReportDateRange fixedDateRangeInfo => fixedDateRangeInfo.EndDate,
             _ => null
         },
         IsEnabled: report.Schedule.IsEnabled,
         ProcessInterval: report.Schedule.ProcessInterval.ToString(),
         Container: report.Container,
         ReportLayout: report.ReportLayout.Value,
         FileFormat: report.FileFormat.ToString(),
         LastProcessedUtc: report.LastProcessedUtc?.ToString("yyyy-MM-dd HH:mm:ss") ?? null,
         UserGroupsAllowed: [.. report.UserGroupsAllowed],
         DateCreated: report.AuditInfo.DateCreated.HasValue
             ? report.AuditInfo.DateCreated.Value.ToString("yyyy-MM-dd HH:mm:ss")
             : string.Empty,
         CreatedBy: report.AuditInfo.CreatedBy ?? string.Empty,
         DateModified: report.AuditInfo.DateModified.HasValue
             ? report.AuditInfo.DateModified.Value.ToString("yyyy-MM-dd HH:mm:ss")
             : string.Empty,
         ModifiedBy: report.AuditInfo.ModifiedBy ?? string.Empty);
}
