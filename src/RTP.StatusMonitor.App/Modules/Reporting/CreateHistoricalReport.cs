using ErrorOr;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record CreateHistoricalReportCommand(
    HistoricalOrAccuracyReportCommand HistoricalReport,
    string RequestedBy) : ICommand;

internal sealed class CreateHistoricalReportCommandHandler(
    IUnitOfWork unitOfWork,
    IDateTimeProvider dateTimeProvider,
    IReportRepository reportRepository) : ICommandHandler<CreateHistoricalReportCommand>
{
    private readonly IReportRepository _reportRepository = reportRepository;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IUnitOfWork _unitOfWork = unitOfWork;

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        CreateHistoricalReportCommand request,
        CancellationToken ct)
    {
        // Create the report columns 
        List<ErrorOr<ReportColumn>> columns = request.HistoricalReport.ReportColumns
            .Select(col =>
                ReportColumn.Create(
                    reportColumnId: col.ReportColumnId,
                    siteId: col.SiteId,
                    blockId: col.BlockId,
                    unitId: col.UnitId,
                    name: col.Name,
                    sources: col.Sources,
                    tag: col.Tag,
                    calculation: new(col.Calculation),
                    filter: new(col.Filter),
                    position: col.Position,
                    noOfDecimals: col.NoOfDecimals,
                    engUnits: col.EngUnits,
                    color: col.Color))
            .ToList();

        // Create the filters 
        List<ErrorOr<ReportFilter>> filters = request.HistoricalReport.MainFilters
            .Select(filter => ReportFilter.Create(
                siteId: filter.SiteId,
                blockId: filter.BlockId,
                unitId: filter.UnitId,
                filter: new(filter.Filter),
                ruleGroup: filter.RuleGroup))
            .ToList();

        // Create the schedule 
        ErrorOr<ReportSchedule> schedule = ReportSchedule
            .Create(
                request.HistoricalReport.IsEnabled,
                request.HistoricalReport.ProcessInterval);

        // Create the time grouping
        ErrorOr<ReportTimeGrouping> grouping = ReportTimeGrouping
            .Create(
                request.HistoricalReport.GroupingInterval,
                request.HistoricalReport.GroupingAggregates);

        // Create the report layout for historical report
        ErrorOr<ReportLayout> reportLayout = request.HistoricalReport.ReportType switch
        {
            ReportType.Historical => ReportLayout.CreateHistoricalLayout(ReportType.Historical, request.HistoricalReport.ReportLayout),
            ReportType.Accuracy => ReportLayout.CreateAccuracyLayout(ReportType.Accuracy, request.HistoricalReport.ReportLayout),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };

        // Make sure everything is valid
        ErrorOr<Success> results = ErrorOrExt.Combine(columns)
            .Then(columnsResult => ErrorOrExt.Combine(filters))
            .Then(filtersResult => ErrorOrExt.Combine(schedule, grouping));
        if (results.IsError)
        {
            return results.Errors;
        }

        // Get the existing reports
        List<Report> existingReports = await _reportRepository.GetReportsAsync(ct);

        // Create the report
        ErrorOr<Report> report = request.HistoricalReport.DateRangeInfo switch
        {
            ReportFixedDateRangeCommand fixedDateRange
                => FixedReportDateRange
            .Create(
                fixedDateRange.StartDate,
                fixedDateRange.EndDate)
            .Else(fixedDateRangeErrors => fixedDateRangeErrors)
            .Then(fixedDateRange =>
                Report.CreateHistoricalOrAccuracyReport(
                    reportId: request.HistoricalReport.ReportId,
                    name: request.HistoricalReport.Name,
                    description: request.HistoricalReport.Description,
                    reportType: request.HistoricalReport.ReportType,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: grouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    userGroupsAllowed: request.HistoricalReport.UserGroupsAllowed,
                    blocksInReport: request.HistoricalReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: request.RequestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: request.RequestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(request.HistoricalReport.FileFormat),
                    container: request.HistoricalReport.Container,
                    dateRangeInfo: fixedDateRange,
                    existingReports: existingReports.ToList())
            ),
            ReportRelativeDateRangeCommand relativeDateRange
                => RelativeReportDateRange.Create(
                relativeDateRange.StartOffset,
                relativeDateRange.EndOffset)
            .Else(errors => errors)
            .Then(relativeDateRange =>
                Report.CreateHistoricalOrAccuracyReport(
                    reportId: request.HistoricalReport.ReportId,
                    name: request.HistoricalReport.Name,
                    description: request.HistoricalReport.Description,
                    reportType: request.HistoricalReport.ReportType,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: grouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    userGroupsAllowed: request.HistoricalReport.UserGroupsAllowed,
                    blocksInReport: request.HistoricalReport.BlocksInReport,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(request.HistoricalReport.FileFormat),
                    container: request.HistoricalReport.Container,
                    auditInfo: new AuditInfo(
                        CreatedBy: request.RequestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: request.RequestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: relativeDateRange,
                    existingReports: [.. existingReports])),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };

        if (report.IsError)
            return report.Errors;

        await _reportRepository.AddReportAsync(report.Value, ct);

        await _unitOfWork.SaveChangesAsync(ct);

        return MediatR.Unit.Value;
    }
}


