using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.ProcessReport;

public record ReportResponse(
    Guid Id,
    string Name,
    string Description,
    string ReportType,
    List<ReportFilterResponse> MainFilters,
    string GroupingInterval,
    List<string> GroupingAggregates,
    ReportVariableGrouping? VariableGrouping,
    List<ReportColumnResponse> ReportColumns,
    ReportDateRangeType DateRangeType,
    int? StartOffset,
    int? EndOffset,
    DateTime? StartDate,
    DateTime? EndDate,
    bool IsEnabled,
    string ProcessInterval,
    List<Guid> UserGroupsAllowed,
    string DateModified,
    string ModifiedBy);

public record ReportFilterResponse(
    Guid? SiteId,
    Guid? BlockId,
    Guid? UnitId,
    string Filter,
    string RuleGroup);

public record ReportColumnResponse(
    Guid Id,
    Guid SiteId,
    Guid BlockId,
    Guid UnitId,
    string Name,
    string AggregateType,
    List<string> Sources,
    string Tag,
    string Calculation,
    string Filter,
    string EngUnits,
    int Position,
    string Color,
    int NoOfDecimals,
    List<object> Values,
    List<DateTime> Timestamps);
