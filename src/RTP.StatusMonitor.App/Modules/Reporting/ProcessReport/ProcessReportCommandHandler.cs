using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Reporting.Shared;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.ProcessReport;
internal sealed class ProcessReportCommandHandler(
    IDataContext dataContext,
    HistoricalReportProcessor processHistoricalReport,
    ForecastReportProcessor processForecastReport)
        : ICommandHandler<ProcessReportCommand, ReportResponse>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly HistoricalReportProcessor _processHistoricalReport = processHistoricalReport;
    private readonly ForecastReportProcessor _processForecastReport = processForecastReport;

    public async Task<ErrorOr<ReportResponse>> <PERSON>le(
        ProcessReportCommand request,
        CancellationToken ct)
    {
        // Find the report requested
        Report? report = await _dataContext.Reports
            .Include(r => r.DateRangeInfo)
            .Include(r => r.ReportColumns)
            .FirstOrDefaultAsync(r => r.Id == request.ReportId, ct);

        if (report is null)
            return ReportErrors.NotFound;

        // Get the data for the report
        List<ReportColumnData> reportContents = report.ReportType switch
        {
            ReportType.Forecast => await _processForecastReport.ProcessReportAsync(report, ct),
            ReportType.Historical or
            ReportType.Accuracy => await _processHistoricalReport.ProcessReportAsync(report, ct),
            _ => throw new InvalidOperationException("Report type not supported")
        };

        // Update the last processed utc
        report.RecordLastProcessingTime(DateTime.UtcNow);

        // Save the changes
        await _dataContext.SaveChangesAsync(ct);

        return new ReportResponse(
            Id: report.Id,
            Name: report.Name,
            Description: report.Description,
            ReportType: report.ReportType.ToString(),
            MainFilters: [.. report.Filters
                .Select(x => new ReportFilterResponse(
                    SiteId: x.SiteId,
                    BlockId: x.BlockId,
                    UnitId: x.UnitId,
                    Filter: x.Filter,
                    RuleGroup: x.RuleGroup.ToString()))],
            GroupingInterval: report.TimeGrouping.GroupingInterval.ToString(),
            GroupingAggregates: [.. report.TimeGrouping.GroupingAggregates.Select(x => x.ToString())],
            VariableGrouping: report.VariableGrouping,
            ReportColumns: [.. reportContents
                .Select(content => MapToReportCoumnResponse(
                        content,
                        report.ReportType,
                        [.. report.ReportColumns]))
                .OrderBy(content => content.Position)],
            DateRangeType: report.DateRangeInfo switch
            {
                FixedReportDateRange => ReportDateRangeType.Fixed,
                RelativeReportDateRange => ReportDateRangeType.Relative,
                _ => throw new InvalidOperationException("No date range type found")
            },
            StartOffset: report.DateRangeInfo switch
            {
                RelativeReportDateRange relative => relative.StartOffset,
                _ => null
            },
            EndOffset: report.DateRangeInfo switch
            {
                RelativeReportDateRange relative => relative.EndOffset,
                _ => null
            },
            StartDate: report.DateRangeInfo switch
            {
                FixedReportDateRange fixedRange => fixedRange.StartDate,
                _ => null
            },
            EndDate: report.DateRangeInfo switch
            {
                FixedReportDateRange fixedRange => fixedRange.EndDate,
                _ => null
            },
            IsEnabled: report.Schedule.IsEnabled,
            ProcessInterval: report.Schedule.ProcessInterval.ToString(),
            UserGroupsAllowed: report.UserGroupsAllowed.ToList(),
            DateModified: report.AuditInfo.DateModified?.ToString() ?? string.Empty,
            ModifiedBy: report.AuditInfo.ModifiedBy?.ToString() ?? string.Empty
        );
    }

    /// <summary>
    /// Map from ReportColumnData dto to response type for API endpoint
    /// </summary>
    public static ReportColumnResponse MapToReportCoumnResponse(
        ReportColumnData content,
        ReportType reportType,
        List<ReportColumn> reportColumns)
    {
        // Find the report column in the list of report columns
        // or create a new report column if it doesn't exist
        ReportColumn reportCol = reportColumns.Find(x => x.Id == content.Id)
            ?? ReportColumn.Create(
                reportColumnId: content.Id,
                siteId: Guid.Empty,
                blockId: Guid.Empty,
                unitId: Guid.Empty,
                name: content.Name,
                sources: [],
                tag: content.Tag,
                calculation: new ComputedExpression(string.Empty),
                filter: new FilterExpression(string.Empty),
                position: 0,
                noOfDecimals: 0,
                engUnits: string.Empty,
                color: string.Empty).Value;

        // Sort the data by timestamp in descending order
        var sortedData = reportType == ReportType.Forecast
            ? [.. content.Timestamps
                .Zip(content.Values,
                    (t, v) => new { Timestamp = t, Value = v })
                .OrderBy(x => x.Timestamp)]
            : content.Timestamps
                .Zip(content.Values,
                    (t, v) => new { Timestamp = t, Value = v })
                .OrderByDescending(x => x.Timestamp)
                .ToList();

        return new ReportColumnResponse(
            Id: content.Id,
            SiteId: reportCol.SiteId,
            BlockId: reportCol.BlockId,
            UnitId: reportCol.UnitId,
            Name: content.Name,
            AggregateType: content.AggregateType.ToString() ?? string.Empty,
            Sources: [.. reportCol.Sources.Select(x => x.ToString())],
            Tag: content.Tag,
            Calculation: reportCol.Calculation.Value,
            Filter: reportCol.Filter.Value,
            EngUnits: reportCol.EngUnits,
            Position: reportCol.Position,
            Color: reportCol.Color,
            NoOfDecimals: reportCol.NoOfDecimals,
            Values: [.. sortedData.Select(x => x.Value)],
            Timestamps: [.. sortedData.Select(x => x.Timestamp)]);
    }
}
