using ErrorOr;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record CreateForecastReportCommand(
    ForecastReportCommand ForecastReport, string RequestedBy)
    : ICommand;

internal sealed class CreateForecastReportCommandHandler(
    IUnitOfWork unitOfWork,
    IDateTimeProvider dateTimeProvider,
    IReportRepository reportRepository) : ICommandHandler<CreateForecastReportCommand>
{
    private readonly IReportRepository _reportRepository = reportRepository;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IUnitOfWork _unitOfWork = unitOfWork;
    public async Task<ErrorOr<MediatR.Unit>> Handle(
        CreateForecastReportCommand request,
        CancellationToken ct)
    {
        // Create the report columns 
        List<ErrorOr<ReportColumn>> columns = request.ForecastReport.ReportColumns
            .Select(col =>
                ReportColumn.Create(
                    reportColumnId: col.ReportColumnId,
                    siteId: col.SiteId,
                    blockId: col.BlockId,
                    unitId: col.UnitId,
                    name: col.Name,
                    sources: col.Sources,
                    tag: col.Tag,
                    calculation: new(col.Calculation),
                    filter: new(col.Filter),
                    position: col.Position,
                    noOfDecimals: col.NoOfDecimals,
                    engUnits: col.EngUnits,
                    color: col.Color))
            .ToList();

        // Create the report schedule
        ErrorOr<ReportSchedule> schedule = ReportSchedule
            .Create(
                request.ForecastReport.IsEnabled,
                request.ForecastReport.ProcessInterval);

        // Create the report layout for forecast report
        ErrorOr<ReportLayout> reportLayout = ReportLayout
            .CreateForecastLayout(ReportType.Forecast, request.ForecastReport.ReportLayout);

        // Create the relative date range for forecast report
        ErrorOr<RelativeReportDateRange> relativeDateRange = RelativeReportDateRange
            .Create(
                startOffset: request.ForecastReport.RelativeDateRange.StartOffset,
                endOffset: request.ForecastReport.RelativeDateRange.EndOffset);

        // Make sure all the columns, schedule and relative date range are valid
        ErrorOr<Success> results = ErrorOrExt
            .Combine(columns)
            .Then(_ => ErrorOrExt.Combine(schedule, relativeDateRange, reportLayout));
        if (results.IsError)
        {
            return results.Errors;
        }

        List<Report> existingReports = await _reportRepository.GetReportsAsync(ct);

        ErrorOr<Report> report = Report.CreateForecastReport(
            reportId: request.ForecastReport.ReportId,
            name: request.ForecastReport.Name,
            description: request.ForecastReport.Description,
            columns: columns.Select(col => col.Value).ToList(),
            schedule: schedule.Value,
            container: request.ForecastReport.Container,
            blocksInReport: request.ForecastReport.BlocksInReport,
            userGroupsAllowed: request.ForecastReport.UserGroupsAllowed,
            reportLayout: reportLayout.Value,
            fileFormat: Enum.Parse<ReportFileFormat>(request.ForecastReport.FileFormat),
            auditInfo: new AuditInfo(
                CreatedBy: request.RequestedBy,
                DateCreated: _dateTimeProvider.UtcNow,
                ModifiedBy: request.RequestedBy,
                DateModified: _dateTimeProvider.UtcNow),
            dateRangeInfo: relativeDateRange.Value,
            existingReports: existingReports);

        if (report.IsError)
        {
            return report.Errors;
        }

        await _reportRepository.AddReportAsync(report.Value, ct);

        await _unitOfWork.SaveChangesAsync(ct);

        return MediatR.Unit.Value;
    }
}
