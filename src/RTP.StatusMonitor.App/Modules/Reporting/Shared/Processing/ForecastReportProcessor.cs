using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;

/// <summary>
/// Processes report with real time forecast data sources (latest forecast)
/// </summary>
public class ForecastReportProcessor(
    ForecastRepository forecastRepository,
    DataContext dataContext,
    IDateTimeProvider dateTimeProvider
)
{
    public async Task<List<ReportColumnData>> ProcessReportAsync(
        Report report,
        CancellationToken ct
    )
    {
        // Query the blocks in the report
        List<Block> blocks = await dataContext
            .Blocks.AsNoTracking()
            .Where(b => report.ReportColumns.Select(col => col.BlockId).Contains(b.Id))
            .Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .ToListAsync(ct);

        // Group the report columns by block
        Dictionary<Block, List<ReportColumn>> reportColsGroupedByBlock =
            report.ReportColumns.GroupColumnsBy(col => blocks.Single(b => b.Id == col.BlockId));

        // Then iterate through each group (by block) of report column and process the report
        List<ReportColumnData> responses = [];
        foreach ((Block block, List<ReportColumn> reportCols) in reportColsGroupedByBlock)
        {
            // Extract all unique variables from the series
            List<string> uniqueVariables = reportCols
                .Select(s => s.Tag)
                .Concat(reportCols.SelectMany(s => ExpressionParser.Parse(s.Calculation)))
                .Concat(reportCols.SelectMany(s => ExpressionParser.Parse(s.Filter)))
                .Distinct()
                .ToList();

            // Get the real time forecast
            List<ForecastDataDto> realTimeForecast =
                await forecastRepository.GetLatestForecastAsync(block, uniqueVariables, ct);

            //  Convert to time series data and interpolate for analytics
            // NOTE forecast report is always hourly => no need to resample
            // Dump data into data table for analytics
            SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(
                realTimeForecast.Select(x => x.ToTimeSeriesData())
            );

            // Evaluate the expression for each chart series
            foreach (ReportColumn column in reportCols)
            {
                // Get all the data for the current column
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: column.Tag,
                    calculation: column.Calculation,
                    filter: column.Filter
                );

                // Filter the data of the column using the date range of the report
                TimeSeriesData filteredColumnData = report.FilterByReportDateRange(
                    data,
                    SiteLocalTime.Create(block.Site, dateTimeProvider.UtcNow)
                );

                // After filtering, add the data to the responses
                responses.Add(
                    new ReportColumnData(
                        Id: column.Id,
                        Name: column.Name,
                        Tag: column.Tag,
                        AggregateType: null, // irrelevant for forecast
                        Timestamps: [.. filteredColumnData.Timestamps],
                        Values: [.. filteredColumnData.Values]
                    )
                );
            }
        }

        // Order by column position
        return
        [
            .. responses.OrderBy(x =>
                report.ReportColumns.ToList().Find(y => y.Id == x.Id)?.Position
            ),
        ];
    }
}
