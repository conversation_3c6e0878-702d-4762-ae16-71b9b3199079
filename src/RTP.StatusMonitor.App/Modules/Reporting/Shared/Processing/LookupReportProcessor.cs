using System.Diagnostics;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;

public record LookupReportRow(string Row, string Column, string Tag, string EngUnit, string Cell);

public class LookupReportProcessor(
    ActualDataAggregation actualDataService,
    ReportGlobalFilter reportGlobalFilter
)
{
    /// <summary>
    /// Process the lookup report
    /// </summary>
    /// <param name="lookupReport">The lookup report</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns>The lookup report rows</returns>
    public async Task<List<LookupReportRow>> ProcessReportAsync(
        Report lookupReport,
        CancellationToken ct
    )
    {
        // Process the global filter of the report to get the dates that satisfy the report filters
        Maybe<List<DateTime>> datesSatisfiedReportFilters =
            await reportGlobalFilter.GetDatesSatisfiedReportFilters(lookupReport, ct);

        List<ReportColumnData> reportColumnData = await actualDataService.AggregateDataAsync(
            lookupReport,
            datesSatisfiedReportFilters,
            ct
        );

        Dictionary<DateTime, Dictionary<string, string>> timestampLookup =
            CreateTimestampLookupTable(
                [.. reportColumnData.Where(col => col.AggregateType == AggregateType.Avg)]
            );

        if (lookupReport.VariableGrouping is null)
        {
            return [];
        }

        // Get the lookup columns
        List<ReportColumn> lookupColumns = lookupReport
            .ReportColumns.Where(c =>
                c.Name != lookupReport.VariableGrouping.Column.Name
                && c.Name != lookupReport.VariableGrouping.Row.Name
            )
            .ToList();

        // No lookup columns, no work to do
        if (lookupColumns.Count == 0)
        {
            return [];
        }

        // Create the column groups
        var columnGroups = CreateColumnGroups(lookupReport.VariableGrouping.Column);

        // Create the row groups
        var rowGroups = CreateRowGroups(columnGroups, lookupReport.VariableGrouping.Row);

        // Populate the lookup table
        var lookup = PopulateLookupTable(rowGroups, timestampLookup, lookupColumns);

        // Convert the lookup table to the report format
        return ConvertLookupTableToReportFormat(lookup, [.. lookupReport.ReportColumns]);
    }

    /// <summary>
    /// Convert the lookup table to the report format
    /// </summary>
    /// <param name="lookup">The lookup table</param>
    /// <param name="reportColumns">The report columns</param>
    /// <returns>The lookup rows</returns>
    private static List<LookupReportRow> ConvertLookupTableToReportFormat(
        Dictionary<string, Dictionary<string, Dictionary<string, string>>> lookup,
        List<ReportColumn> reportColumns
    )
    {
        List<LookupReportRow> result = [];
        foreach (var columnGroup in lookup)
        {
            foreach (var tagGroup in columnGroup.Value)
            {
                foreach (var cell in tagGroup.Value)
                {
                    result.Add(
                        new LookupReportRow(
                            Row: columnGroup.Key,
                            Column: tagGroup.Key,
                            Tag: cell.Key,
                            EngUnit: reportColumns.FirstOrDefault(c => c.Name == cell.Key)?.EngUnits
                                ?? "",
                            Cell: cell.Value
                        )
                    );
                }
            }
        }

        return result;
    }

    /// <summary>
    /// Create a timestamp lookup table from the report column data
    /// </summary>
    /// <param name="columns">The columns of the report with data</param>
    /// <returns>The timestamp lookup table</returns>
    public static Dictionary<DateTime, Dictionary<string, string>> CreateTimestampLookupTable(
        List<ReportColumnData> columns
    )
    {
        Dictionary<DateTime, Dictionary<string, string>> timestampLookup = [];

        foreach (ReportColumnData column in columns)
        {
            for (int i = 0; i < column.Timestamps.Length; i++)
            {
                DateTime timestamp = column.Timestamps[i];
                if (!timestampLookup.ContainsKey(timestamp))
                {
                    timestampLookup[timestamp] = [];
                }

                timestampLookup[timestamp][column.Name] = column.Values[i].ToString() ?? "";
            }
        }

        return timestampLookup;
    }

    /// <summary>
    /// Create the column groups
    /// </summary>
    /// <param name="grouping">The variable grouping</param>
    /// <returns>The column groups</returns>
    private static Dictionary<string, Dictionary<string, object>> CreateColumnGroups(
        VariableGroup grouping
    )
    {
        Dictionary<string, Dictionary<string, object>> columnGroups = [];
        int groupCount = (int)Math.Floor((double)(grouping.Max - grouping.Min) / grouping.Interval);

        for (int i = 0; i < groupCount; i++)
        {
            double start = grouping.Min + (i * grouping.Interval);
            double end = start + grouping.Interval;
            string key = $"{grouping.Name}*{start}*{end}";
            columnGroups[key] = [];
        }

        return columnGroups;
    }

    /// <summary>
    /// Create the row groups
    /// </summary>
    /// <param name="columnGroups">The column groups</param>
    /// <param name="rowGrouping">The row grouping</param>
    /// <returns>The row groups</returns>
    private static Dictionary<
        string,
        Dictionary<string, Dictionary<string, object>>
    > CreateRowGroups(
        Dictionary<string, Dictionary<string, object>> columnGroups,
        VariableGroup rowGrouping
    )
    {
        Dictionary<string, Dictionary<string, object>> rowGroups = CreateColumnGroups(rowGrouping);
        Dictionary<string, Dictionary<string, Dictionary<string, object>>> result = [];

        foreach (string columnGroup in columnGroups.Keys)
        {
            result[columnGroup] = rowGroups;
        }

        return result;
    }

    /// <summary>
    /// Populate the lookup table
    /// </summary>
    /// <param name="groupedByRows">The grouped by rows</param>
    /// <param name="timestampLookup">The timestamp lookup</param>
    /// <param name="lookupCols">The lookup columns</param>
    /// <returns>The lookup table</returns>
    private static Dictionary<
        string,
        Dictionary<string, Dictionary<string, string>>
    > PopulateLookupTable(
        Dictionary<string, Dictionary<string, Dictionary<string, object>>> groupedByRows,
        Dictionary<DateTime, Dictionary<string, string>> timestampLookup,
        List<ReportColumn> lookupCols
    )
    {
        Dictionary<string, Dictionary<string, Dictionary<string, string>>> lookup = [];

        foreach (var columnGroup in groupedByRows)
        {
            string[] columnParts = columnGroup.Key.Split('*');
            string column = columnParts[0];
            double columnStart = double.Parse(columnParts[1]);
            double columnEnd = double.Parse(columnParts[2]);

            foreach (var rowGroup in columnGroup.Value)
            {
                string[] rowParts = rowGroup.Key.Split('*');
                string row = rowParts[0];
                double rowStart = double.Parse(rowParts[1]);
                double rowEnd = double.Parse(rowParts[2]);

                Dictionary<string, List<double>> values = AggregateValues(
                    timestampLookup,
                    lookupCols,
                    column,
                    row,
                    columnStart,
                    columnEnd,
                    rowStart,
                    rowEnd
                );

                Dictionary<string, string> averages = [];
                foreach (KeyValuePair<string, List<double>> kvp in values)
                {
                    double average = kvp.Value.Average();
                    int count = kvp.Value.Count;
                    string formattedValue = double.IsNaN(average)
                        ? ""
                        : average.ToString($"F{lookupCols[0].NoOfDecimals}");
                    averages[kvp.Key] = count <= 10 ? $"*{formattedValue}*" : formattedValue;
                }

                string rowKey = $"{row} {rowStart}-{rowEnd}";
                string columnKey = $"{column} {columnStart}-{columnEnd}";

                if (!lookup.ContainsKey(rowKey))
                {
                    lookup[rowKey] = [];
                }
                lookup[rowKey][columnKey] = averages;
            }
        }

        return lookup;
    }

    /// <summary>
    /// Aggregate the values
    /// </summary>
    /// <param name="timestampLookup">The timestamp lookup</param>
    /// <param name="lookupCols">The lookup columns</param>
    /// <param name="column">The column</param>
    /// <param name="row">The row</param>
    /// <returns>The aggregated values</returns>
    private static Dictionary<string, List<double>> AggregateValues(
        Dictionary<DateTime, Dictionary<string, string>> timestampLookup,
        List<ReportColumn> lookupCols,
        string column,
        string row,
        double columnStart,
        double columnEnd,
        double rowStart,
        double rowEnd
    )
    {
        Dictionary<string, List<double>> result = [];

        foreach (Dictionary<string, string> timestamp in timestampLookup.Values)
        {
            if (!timestamp.ContainsKey(column) || !timestamp.ContainsKey(row))
            {
                continue;
            }

            double columnValue = double.Parse(timestamp[column]);
            double rowValue = double.Parse(timestamp[row]);

            if (
                rowStart <= rowValue
                && rowValue < rowEnd
                && columnStart <= columnValue
                && columnValue < columnEnd
            )
            {
                foreach (ReportColumn lookupCol in lookupCols)
                {
                    if (timestamp.ContainsKey(lookupCol.Name))
                    {
                        var lookupValue = double.Parse(timestamp[lookupCol.Name]);
                        if (!result.ContainsKey(lookupCol.Name))
                        {
                            result[lookupCol.Name] = [];
                        }
                        result[lookupCol.Name].Add(lookupValue);
                    }
                }
            }
        }

        return result;
    }
}
