using RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;

/// <summary>
/// Processes any report with historical data sources (Day Ahead Forecast, Actual, Airsonic, etc...)
/// </summary>
public class HistoricalReportProcessor(
    ActualDataAggregation actualDataService,
    AirsonicDataAggregation airsonicDataService,
    DayAheadForecastDataAggregation daForecastDataService,
    AccuracyDataAggregation accuracyDataService,
    ReportGlobalFilter reportGlobalFilter
)
{
    public async Task<List<ReportColumnData>> ProcessReportAsync(
        Report report,
        CancellationToken ct
    )
    {
        // Get the dates that satisfy the report filters if any
        Maybe<List<DateTime>> datesSatisfiedReportFilters =
            await reportGlobalFilter.GetDatesSatisfiedReportFilters(report, ct);

        // Aggregate the data for the report
        List<Task<List<ReportColumnData>>> tasks =
        [
            airsonicDataService.AggregateDataAsync(report, datesSatisfiedReportFilters, ct),
            actualDataService.AggregateDataAsync(report, datesSatisfiedReportFilters, ct),
            daForecastDataService.AggregateDataAsync(report, datesSatisfiedReportFilters, ct),
            accuracyDataService.AggregateDataAsync(report, datesSatisfiedReportFilters, ct),
        ];

        List<ReportColumnData>[] results = await Task.WhenAll(tasks);

        // Create a lookup dictionary for the report columns by their id and position
        Dictionary<Guid, int> columnPositionLookup = report.ReportColumns.ToDictionary(
            col => col.Id,
            col => col.Position
        );

        // Order the results by the position of the report column
        List<ReportColumnData> orderedByColumnPosition =
        [
            .. results.SelectMany(x => x).OrderBy(x => columnPositionLookup[x.Id]),
        ];

        // The aggregate order is important for the report
        List<AggregateType> aggregateOrder =
        [
            AggregateType.Min,
            AggregateType.Max,
            AggregateType.Avg,
            AggregateType.StdDev,
            AggregateType.Count,
        ];

        // Order the column data by the aggregate order
        IOrderedEnumerable<ReportColumnData> dataOrderedByAggregateType = orderedByColumnPosition.OrderBy(col =>
                aggregateOrder.FindIndex(agg => agg == col.AggregateType)
            );
        
        // Then order by column position
        return
        [
            .. dataOrderedByAggregateType.OrderBy(x =>
                report.ReportColumns.ToList().Find(y => y.Id == x.Id)?.Position
            ),
        ];
    }
}
