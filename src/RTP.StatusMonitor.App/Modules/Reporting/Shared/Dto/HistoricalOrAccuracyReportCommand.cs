using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;

public record HistoricalOrAccuracyReportCommand(
    Guid ReportId,
    string Name,
    string Description,
    ReportType ReportType,
    List<ReportFilterCommand> MainFilters,
    string GroupingInterval,
    List<string> GroupingAggregates,
    List<ReportColumnCommand> ReportColumns,
    bool IsEnabled,
    string ProcessInterval,
    string Container,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    ReportDateRangeInfoCommand DateRangeInfo,
    string DateCreated,
    string CreatedBy) : ReportCommand(
        ReportId: ReportId,
        Name: Name,
        Description: Description,
        ReportType: ReportType,
        ReportColumns: ReportColumns,
        IsEnabled: IsEnabled,
        ProcessInterval: ProcessInterval,
        Container: Container,
        ReportLayout: ReportLayout,
        FileFormat: FileFormat,
        UserGroupsAllowed: UserGroupsAllowed,
        BlocksInReport: BlocksInReport,
        DateCreated: DateCreated,
        CreatedBy: CreatedBy);

