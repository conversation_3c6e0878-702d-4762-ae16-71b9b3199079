using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;

public abstract record ReportCommand(
    Guid ReportId,
    string Name,
    string Description,
    ReportType ReportType,
    List<ReportColumnCommand> ReportColumns,
    bool IsEnabled,
    string ProcessInterval,
    string Container,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    string DateCreated,
    string CreatedBy);

public record ReportVariableGroupingCommand
(
    string Source,
    VariableGroupCommand Row,
    VariableGroupCommand Column
);

public record VariableGroupCommand
(
    string Tag,
    string Name,
    int Min,
    int Max,
    int Interval
);

