using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;

public record ForecastReportCommand(
    Guid ReportId,
    string Name,
    string Description,
    List<ReportColumnCommand> ReportColumns,
    ReportRelativeDateRangeCommand RelativeDateRange,
    bool IsEnabled,
    string ProcessInterval,
    string Container,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    string DateCreated,
    string CreatedBy) : ReportCommand(
        ReportId: ReportId,
        Name: Name,
        Description: Description,
        ReportType: ReportType.Forecast,
        ReportColumns: ReportColumns,
        IsEnabled: IsEnabled,
        Container: Container,
        ProcessInterval: ProcessInterval,
        ReportLayout: ReportLayout,
        FileFormat: FileFormat,
        UserGroupsAllowed: UserGroupsAllowed,
        BlocksInReport: BlocksInReport,
        DateCreated: DateCreated,
        CreatedBy: CreatedBy);