using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
public record LookupReportCommand(
    Guid ReportId,
    string Name,
    string Description,
    List<ReportFilterCommand> MainFilters,
    string GroupingInterval,
    ReportVariableGroupingCommand VariableGrouping,
    List<ReportColumnCommand> ReportColumns,
    ReportDateRangeInfoCommand DateRangeInfo,
    bool IsEnabled,
    string ProcessInterval,
    string Container,
    string ReportLayout,
    string FileFormat,
    List<Guid> UserGroupsAllowed,
    List<Guid> BlocksInReport,
    string DateCreated,
    string CreatedBy) : ReportCommand(
        ReportId: ReportId,
        Name: Name,
        Description: Description,
        ReportType: ReportType.Lookup,
        ReportColumns: ReportColumns,
        IsEnabled: IsEnabled,
        ProcessInterval: ProcessInterval,
        Container: Container,
        ReportLayout: ReportLayout,
        FileFormat: FileFormat,
        UserGroupsAllowed: UserGroupsAllowed,
        BlocksInReport: BlocksInReport,
        DateCreated: DateCreated,
        CreatedBy: CreatedBy);