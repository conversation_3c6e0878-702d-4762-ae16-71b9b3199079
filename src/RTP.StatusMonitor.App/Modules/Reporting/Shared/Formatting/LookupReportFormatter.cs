using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;

/// <summary>
/// Format the lookup report data as either HTML or Excel content
/// </summary>
public static class LookupReportFormatter
{
    /// <summary>
    /// Format the lookup report based on file format (the lookup report will have unified layout)
    /// </summary>
    /// <param name="report">The report to format</param>
    /// <param name="fileFormat">The file format of the report</param>
    /// <param name="lookupReportData">The lookup report data to format</param>
    /// <returns>The formatted report</returns>
    public static ReportFormatOutput FormatReport(
        Report report,
        ReportFileFormat fileFormat,
        LookupProcessingResult lookupReportData
    ) =>
        fileFormat switch
        {
            ReportFileFormat.HTML => FormatHtml(report, lookupReportData),
            ReportFileFormat.Excel => FormatExcel(report, lookupReportData),
            _ => throw new InvalidOperationException(
                $"File format {fileFormat} not supported for lookup report"
            ),
        };

    /// <summary>
    /// Formats the report data in Excel format with hours as columns and variables/aggregates as rows.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="lookupReportData">The report data to format.</param>
    /// <returns>The formatted report.</returns>
    private static ReportFormatOutput FormatExcel(Report report, LookupProcessingResult lookupReportData)
    {
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="lookupReportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    private static ReportFormatOutput FormatHtml(Report report, LookupProcessingResult lookupReportData)
    {
    }
}
