using System.Text;
using ClosedXML.Excel;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;

public static class HistoricalReportFormatter
{
    /// <summary>
    /// Format the report based on the layout and file format
    /// </summary>
    /// <param name="report">The report to format</param>
    /// <param name="layoutType">The layout type of the report</param>
    /// <param name="fileFormat">The file format of the report</param>
    /// <param name="reportData">The report data to format</param>
    /// <returns>The formatted report</returns>
    /// <exception cref="InvalidOperationException">Thrown when the layout type is not supported for the file format</exception>
    public static ReportFormatOutput FormatReport(
        Report report,
        HistoricalLayoutType layoutType,
        ReportFileFormat fileFormat,
        HistoricalProcessingResult reportData
    ) =>
        (fileFormat, layoutType) switch
        {
            (ReportFileFormat.HTML, _) => FormatHtmlVariableByAggregate(report, reportData.Data),
            (ReportFileFormat.Excel, HistoricalLayoutType.VariableByAggregate) =>
                FormatExcelVariableByAggregate(report, reportData.Data),
            (ReportFileFormat.Excel, HistoricalLayoutType.AggregateByVariable) =>
                FormatExcelAggregateByVariable(report, reportData.Data),
            _ => throw new InvalidOperationException(
                $"File format {fileFormat} not yet supported for historical report"
            ),
        };

    /// <summary>
    /// Groups the report data by timestamp.
    /// </summary>
    /// <param name="reportData">The report data to group.</param>
    /// <returns>A dictionary with the timestamp as the key and a list of report data as the value.</returns>
    private static Dictionary<DateTime, List<ReportColumnData>> GroupReportDataByTimestamp(
        List<ReportColumnData> reportData
    )
    {
        Dictionary<DateTime, List<ReportColumnData>> timestampGroups = [];

        // Get all timestamps
        List<DateTime> timestamps =
        [
            .. reportData.SelectMany(d => d.Timestamps).Distinct().OrderBy(t => t),
        ];

        // Group data by timestamp
        foreach (ReportColumnData data in reportData)
        {
            for (int i = 0; i < timestamps.Count; i++)
            {
                if (timestampGroups.ContainsKey(timestamps[i]) == false)
                {
                    timestampGroups[timestamps[i]] = new List<ReportColumnData>();
                }
                timestampGroups[timestamps[i]].Add(data);
            }
        }

        return timestampGroups;
    }

    /// <summary>
    /// Adds the report table header to the worksheet.
    /// </summary>
    /// <param name="worksheet">The worksheet to add the header to.</param>
    /// <param name="report">The report to add the header for.</param>
    /// <returns>The current row after adding the header.</returns>
    private static int AddReportTableHeader(IXLWorksheet worksheet, Report report)
    {
        int currentRow = 1;

        // Add report name
        worksheet.Range(currentRow, 1, currentRow, 10).Merge();
        worksheet.Cell(currentRow, 1).Value = $"{report.Name}";
        worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
        worksheet.Cell(currentRow, 1).Style.Font.FontSize = 15;
        currentRow++;

        // Add report description
        if (!string.IsNullOrEmpty(report.Description))
        {
            worksheet.Range(currentRow, 1, currentRow, 10).Merge();
            worksheet.Cell(currentRow, 1).Value = report.Description;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 12;
        }

        return currentRow;
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>The formatted report.</returns>
    private static ReportFormatOutput FormatExcelVariableByAggregate(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        // No data, return empty content
        if (reportData.Count == 0)
        {
            return new ReportFormatOutput(
                report.Id,
                report.Name,
                report.FileFormat,
                Array.Empty<byte>()
            );
        }

        // Group columns by their name
        Dictionary<string, List<ReportColumnData>> columnNameGroups = reportData
            .GroupBy(d => d.Name)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.AggregateType).ToList());

        // Group data by timestamp
        Dictionary<DateTime, List<ReportColumnData>> timestampGroups = GroupReportDataByTimestamp(
            reportData
        );

        // Create excel file
        using XLWorkbook workbook = new();
        IXLWorksheet worksheet = workbook.Worksheets.Add(
            report
                .Name.Replace("[", "_")
                .Replace("]", "_")
                .Replace("*", "_")
                .Replace("?", "_")
                .Replace("/", "_")
                .Replace("\\", "_")
                .Replace(":", "_")
        );

        int currentRow = 1;

        // Add title
        currentRow = AddReportTableHeader(worksheet, report);

        // Add two blank rows
        currentRow += 2;

        // Get all the aggregate types included in the report
        List<AggregateType> aggregateTypes = report
            .TimeGrouping.GroupingAggregates.OrderBy(t => t)
            .ToList();

        // Calculate total number of rows we'll need
        int totalRows = columnNameGroups.Count * timestampGroups.Count;

        // Write the column headers (Timestamp | Name | Aggregates...)
        worksheet.Cell(currentRow, 1).Value = "Timestamp";
        worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
        worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;

        worksheet.Cell(currentRow, 2).Value = "Name";
        worksheet.Cell(currentRow, 2).Style.Font.Bold = true;
        worksheet.Cell(currentRow, 2).Style.Font.FontSize = 14;

        for (int i = 0; i < aggregateTypes.Count; i++)
        {
            worksheet.Cell(currentRow, i + 3).Value = aggregateTypes[i].ToString();
            worksheet
                .Cell(currentRow, i + 3)
                .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
            worksheet.Cell(currentRow, i + 3).Style.Font.Bold = true;
            worksheet.Cell(currentRow, i + 3).Style.Font.FontSize = 14;
        }

        int startRow = currentRow;
        currentRow++;

        // Write data for each timestamp and variable
        foreach (var timestampGroup in timestampGroups)
        {
            // Write data for each variable
            for (int i = 0; i < columnNameGroups.Count; i++)
            {
                KeyValuePair<string, List<ReportColumnData>> columnGroup =
                    columnNameGroups.ElementAt(i);

                // Write the timestamp as first column
                worksheet.Cell(currentRow, 1).Value = report.TimeGrouping.GroupingInterval switch
                {
                    ReportGroupingInterval.Minute => timestampGroup.Key.ToString(
                        "MM/dd/yyyy HH:mm"
                    ),
                    ReportGroupingInterval.Hourly => timestampGroup.Key.ToString(
                        "MM/dd/yyyy hh tt"
                    ),
                    ReportGroupingInterval.Daily => timestampGroup.Key.ToString("MM/dd/yyyy"),
                    ReportGroupingInterval.Weekly => timestampGroup.Key.ToString("MM/dd/yyyy"),
                    ReportGroupingInterval.Monthly => timestampGroup.Key.ToString("MM/yyyy"),
                    _ => timestampGroup.Key.ToString("MM/dd/yyyy HH:mm"),
                };
                worksheet
                    .Cell(currentRow, 1)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                // Write the variable name as second column
                worksheet.Cell(currentRow, 2).Value = columnGroup.Key;
                worksheet
                    .Cell(currentRow, 2)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                // Write the values for each aggregate type
                for (int j = 0; j < aggregateTypes.Count; j++)
                {
                    ReportColumnData? data = timestampGroup.Value.FirstOrDefault(d =>
                        d.Name == columnGroup.Key && d.AggregateType == aggregateTypes[j]
                    );

                    if (data is not null && data.Values.Any())
                    {
                        worksheet.Cell(currentRow, j + 3).Value = data.Values.First().ToString();
                    }
                    else
                    {
                        worksheet.Cell(currentRow, j + 3).Value = "N/A";
                    }
                }
                currentRow++;
            }
        }

        // Create and format the table
        var tableRange = worksheet.Range(startRow, 1, currentRow - 1, aggregateTypes.Count + 2);
        var table = tableRange.CreateTable();

        // Style the table
        table.Theme = XLTableTheme.TableStyleMedium2;
        table.ShowAutoFilter = true;
        table.ShowTotalsRow = false;

        // Format all cells in the table
        tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        tableRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        tableRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;

        // Auto-fit columns
        worksheet.Columns().AdjustToContents();

        // Freeze the header row
        worksheet.SheetView.FreezeRows(startRow);

        // Save the workbook to a memory stream
        using MemoryStream ms = new();
        workbook.SaveAs(ms);

        // Reset the stream position to the beginning
        ms.Position = 0;

        return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, ms.ToArray());
    }

    /// <summary>
    /// Formats the report data in the Aggregate (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>The formatted report.</returns>
    private static ReportFormatOutput FormatExcelAggregateByVariable(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        // No data, return empty content
        if (reportData.Count == 0)
        {
            return new ReportFormatOutput(
                report.Id,
                report.Name,
                report.FileFormat,
                Array.Empty<byte>()
            );
        }

        // Group columns by their name
        Dictionary<string, List<ReportColumnData>> columnNameGroups = reportData
            .GroupBy(d => d.Name)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.AggregateType).ToList());

        // Group data by timestamp
        Dictionary<DateTime, List<ReportColumnData>> timestampGroups = GroupReportDataByTimestamp(
            reportData
        );

        // Get all the aggregate types included in the report
        List<AggregateType> aggregateTypes = report
            .TimeGrouping.GroupingAggregates.OrderBy(t => t)
            .ToList();

        // Create excel file
        using XLWorkbook workbook = new();
        IXLWorksheet worksheet = workbook.Worksheets.Add(report.Name);

        int currentRow = 1;

        // Add title
        currentRow = AddReportTableHeader(worksheet, report);

        // Add two blank rows
        currentRow += 3;

        // Write the column headers (Timestamp | Variables...)
        worksheet.Cell(currentRow, 1).Value = "Timestamp";
        worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
        worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;

        worksheet.Cell(currentRow, 2).Value = "Aggregate";
        worksheet.Cell(currentRow, 2).Style.Font.Bold = true;
        worksheet.Cell(currentRow, 2).Style.Font.FontSize = 14;

        // Write variable names as column headers
        int columnIndex = 3;
        foreach (var columnGroup in columnNameGroups)
        {
            worksheet.Cell(currentRow, columnIndex).Value = columnGroup.Key;
            worksheet
                .Cell(currentRow, columnIndex)
                .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
            worksheet.Cell(currentRow, columnIndex).Style.Font.Bold = true;
            worksheet.Cell(currentRow, columnIndex).Style.Font.FontSize = 14;
            columnIndex++;
        }

        int startRow = currentRow;
        currentRow++;

        // Write data for each timestamp
        int groupStartRow = 0;
        foreach (var timestampGroup in timestampGroups)
        {
            groupStartRow = currentRow;

            // Write data for each aggregate type
            foreach (var aggregateType in aggregateTypes)
            {
                // Format timestamp based on grouping interval
                string timestampStr = report.TimeGrouping.GroupingInterval switch
                {
                    ReportGroupingInterval.Minute => timestampGroup.Key.ToString(
                        "MM/dd/yyyy HH:mm"
                    ),
                    ReportGroupingInterval.Hourly => timestampGroup.Key.ToString(
                        "MM/dd/yyyy hh tt"
                    ),
                    ReportGroupingInterval.Daily => timestampGroup.Key.ToString("MM/dd/yyyy"),
                    ReportGroupingInterval.Weekly => timestampGroup.Key.ToString("MM/dd/yyyy"),
                    ReportGroupingInterval.Monthly => timestampGroup.Key.ToString("MM/yyyy"),
                    _ => timestampGroup.Key.ToString("MM/dd/yyyy HH:mm"),
                };

                // Write timestamp and aggregate type
                worksheet.Cell(currentRow, 1).Value = timestampStr;
                worksheet
                    .Cell(currentRow, 1)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                worksheet.Cell(currentRow, 2).Value = aggregateType.ToString();
                worksheet
                    .Cell(currentRow, 2)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                // Write values for each variable
                columnIndex = 3;
                foreach (var variableGroup in columnNameGroups)
                {
                    ReportColumnData? data = timestampGroup.Value.FirstOrDefault(d =>
                        d.Name == variableGroup.Key && d.AggregateType == aggregateType
                    );

                    if (data is not null && data.Values.Any())
                    {
                        worksheet.Cell(currentRow, columnIndex).Value = data
                            .Values.First()
                            .ToString();
                    }
                    else
                    {
                        worksheet.Cell(currentRow, columnIndex).Value = "N/A";
                    }
                    columnIndex++;
                }
                currentRow++;
            }
        }

        // Create and format the table
        var tableRange = worksheet.Range(startRow, 1, currentRow - 1, columnNameGroups.Count + 2);
        var table = tableRange.CreateTable();

        // Style the table
        table.Theme = XLTableTheme.TableStyleMedium2;
        table.ShowAutoFilter = true;
        table.ShowTotalsRow = false;

        // Format all cells in the table
        tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        tableRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        tableRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;

        // Auto-fit columns
        worksheet.Columns().AdjustToContents();

        // Freeze the header row
        worksheet.SheetView.FreezeRows(startRow);

        // Save the workbook to a memory stream
        using MemoryStream ms = new();
        workbook.SaveAs(ms);

        // Reset the stream position to the beginning
        ms.Position = 0;

        return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, ms.ToArray());
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    private static ReportFormatOutput FormatHtmlVariableByAggregate(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        // No data, return empty content
        if (reportData.Count == 0)
        {
            return new ReportFormatOutput(
                report.Id,
                report.Name,
                report.FileFormat,
                Array.Empty<byte>()
            );
        }

        var html = new StringBuilder();

        // Get all timestamps and group the timestamps by itself
        var dateGroups = reportData
            .SelectMany(data => data.Timestamps)
            .Distinct()
            .OrderBy(t => t)
            .GroupBy(t => t)
            .ToDictionary(g => g.Key, g => g.OrderBy(t => t.Hour).ToList());

        // Group variables by their base name (without aggregate type)
        var variableGroups = reportData
            .GroupBy(d => d.Tag)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.AggregateType).ToList());

        html.AppendLine(
            @"<div style='
            font-family: Arial, sans-serif;
            margin: 0 auto;
            padding: 10px;
        '>"
        );

        // Add report title
        html.AppendLine(
            $@"<h3 style='
            color: #4299e1;
            font-size: 24px;
            margin-bottom: 15px;
            padding: 10px;
        '>{report.Name}</h3>"
        );

        // Create a table for the card layout (4 columns)
        html.AppendLine(@"<table cellspacing='10' cellpadding='0' style='width: 100%;'>");

        // Process dates in groups of 4
        var datesProcessed = 0;
        foreach (var dateGroup in dateGroups)
        {
            if (datesProcessed % 4 == 0)
            {
                html.AppendLine("<tr>");
            }

            // Start date card cell
            html.AppendLine(@"<td style='width: 25%; vertical-align: top;'>");

            // Card container
            html.AppendLine(
                $@"<div style='
                background: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                margin-bottom: 10px;
                overflow: hidden;
            '>"
            );

            // Card header
            html.AppendLine(
                $@"<div style='
                background-color: #1d7fb8;
                color: white;
                padding: 8px 10px;
                font-size: 14px;
                font-weight: 600;
            '>{dateGroup.Key:MMMM dd, yyyy}</div>"
            );

            // Card body
            html.AppendLine(@"<div style='padding: 8px;'>");

            // Data table
            html.AppendLine(
                @"<table style='
                width: 100%;
                border-collapse: collapse;
                margin: 0;
                font-size: 12px;
            '>"
            );

            // Headers
            html.AppendLine(@"<thead style='background-color: #f7fafc;'>");
            html.AppendLine("<tr>");
            html.AppendLine(
                @"<th style='
                padding: 4px;
                text-align: left;
                border: 1px solid #e2e8f0;
                color: #1d7fb8;
                font-weight: 600;
            '>Variable</th>"
            );

            // Get unique aggregate types
            var aggregateTypes = reportData
                .Select(d => d.AggregateType)
                .Distinct()
                .OrderBy(t => t)
                .ToList();

            // Add aggregate type headers
            foreach (var aggregateType in aggregateTypes)
            {
                string displayType = aggregateType?.ToString() ?? "Value";
                html.AppendLine(
                    $@"<th style='
                    padding: 4px;
                    text-align: center;
                    border: 1px solid #e2e8f0;
                    color: #1d7fb8;
                    font-weight: 600;
                '>{displayType}</th>"
                );
            }
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");

            // Table body
            html.AppendLine("<tbody>");

            // Add rows for each variable
            foreach (var variableGroup in variableGroups)
            {
                html.AppendLine("<tr>");

                // Variable name
                html.AppendLine(
                    $@"<td style='
                    padding: 4px;
                    text-align: left;
                    border: 1px solid #e2e8f0;
                    color: #2d3748;
                    font-weight: 500;
                '>{variableGroup.Key}</td>"
                );

                // Add cells for each aggregate type
                foreach (var aggregateType in aggregateTypes)
                {
                    var data = variableGroup.Value.FirstOrDefault(d =>
                        d.AggregateType == aggregateType
                    );

                    if (data == null)
                    {
                        html.AppendLine(
                            @"<td style='
                            padding: 4px;
                            text-align: center;
                            border: 1px solid #e2e8f0;
                            color: #718096;
                        '>-</td>"
                        );
                        continue;
                    }

                    // Find the value for this timestamp
                    var timestamp = dateGroup.Value.First(); // Take first timestamp of the day
                    var valueIndex = Array.IndexOf(data.Timestamps, timestamp);

                    if (valueIndex == -1)
                    {
                        html.AppendLine(
                            @"<td style='
                            padding: 4px;
                            text-align: center;
                            border: 1px solid #e2e8f0;
                            color: #718096;
                        '>-</td>"
                        );
                        continue;
                    }

                    var value = data.Values[valueIndex];
                    var reportColumn = report.ReportColumns.FirstOrDefault(x => x.Id == data.Id);
                    string formattedValue = FormatValue(value, reportColumn?.NoOfDecimals ?? 0);

                    html.AppendLine(
                        $@"<td style='
                        padding: 4px;
                        text-align: center;
                        border: 1px solid #e2e8f0;
                        color: #2d3748;
                    '>{formattedValue}</td>"
                    );
                }

                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");
            html.AppendLine("</div>"); // Close card body
            html.AppendLine("</div>"); // Close card container
            html.AppendLine("</td>"); // Close card cell

            datesProcessed++;
            if (datesProcessed % 4 == 0)
            {
                html.AppendLine("</tr>");
            }
        }

        // Close the last row if it's not complete
        if (datesProcessed % 4 != 0)
        {
            // Add empty cells to complete the row
            for (int i = 0; i < 4 - datesProcessed % 4; i++)
            {
                html.AppendLine("<td style='width: 25%;'></td>");
            }
            html.AppendLine("</tr>");
        }

        html.AppendLine("</table>"); // Close card layout table
        html.AppendLine("</div>"); // Close main container

        return new ReportFormatOutput(
            report.Id,
            report.Name,
            report.FileFormat,
            Encoding.Default.GetBytes(html.ToString())
        );
    }

    /// <summary>
    /// Formats the value to a string with the appropriate number of decimals
    /// </summary>
    /// <param name="value">The value to format</param>
    /// <param name="decimals">The number of decimals to include</param>
    /// <returns>The formatted value</returns>
    private static string FormatValue(object? value, int decimals)
    {
        if (value == null)
            return string.Empty;
        return double.TryParse(value.ToString(), out double numberValue)
            ? decimals > 0
                ? numberValue.ToString($"F{decimals}")
                : numberValue.ToString("F0")
            : value.ToString() ?? string.Empty;
    }
}
