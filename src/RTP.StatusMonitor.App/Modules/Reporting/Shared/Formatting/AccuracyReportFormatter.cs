using System.Text;
using ClosedXML.Excel;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;

public static class AccuracyReportFormatter
{
    /// <summary>
    /// Format the report based on the layout and file format
    /// </summary>
    /// <param name="report">The report to format</param>
    /// <param name="fileFormat">The file format of the report</param>
    /// <param name="reportData">The report data to format</param>
    /// <returns>The formatted report</returns>
    /// <exception cref="InvalidOperationException">Thrown when the layout type is not supported for the file format</exception>
    public static ReportFormatOutput FormatReport(
        Report report,
        ReportFileFormat fileFormat,
        AccuracyProcessingResult reportData
    ) =>
        fileFormat switch
        {
            ReportFileFormat.HTML => FormatHtml(report, reportData.Data),
            ReportFileFormat.Excel => FormatExcel(report, reportData.Data),
            _ => throw new InvalidOperationException(
                $"File format {fileFormat} not yet supported for accuracy report"
            ),
        };

    /// <summary>
    /// Formats the report data in Excel format with hours as columns and variables/aggregates as rows.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>The formatted report.</returns>
    private static ReportFormatOutput FormatExcel(Report report, List<ReportColumnData> reportData)
    {
        // No data, return empty content
        if (reportData.Count == 0)
        {
            return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, []);
        }

        // Create lookup table for report data (date => column name => aggregate type => hour ending => value)
        Dictionary<
            DateTime,
            Dictionary<string, Dictionary<AggregateType, Dictionary<int, string>>>
        > lookupTable = CreateAccuracyLookupTable([.. report.ReportColumns], reportData);

        // Create report columns lookup by name
        Dictionary<string, ReportColumn> reportColumnsLookup = report.ReportColumns.ToDictionary(x => x.Name
        );

        // Create excel file
        using XLWorkbook workbook = new();
        IXLWorksheet worksheet = workbook.Worksheets.Add(report.Name);

        // Set default font size for all cells
        worksheet.Style.Font.FontSize = 12;

        int currentRow = 1;

        // Write data for each date
        foreach (
            (
                DateTime date,
                Dictionary<string, Dictionary<AggregateType, Dictionary<int, string>>> columnData
            ) in lookupTable
        )
        {
            // Add title
            worksheet.Cell(currentRow, 1).Value = $"{report.Name} for {date:MMM-dd}";
            worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 18;
            currentRow += 2;

            // Write headers (Variable | Aggregate | Hour1-24)
            int columnIndex = 1;

            worksheet.Cell(currentRow, columnIndex).Value = "Variable";
            columnIndex++;

            worksheet.Cell(currentRow, columnIndex).Value = "Aggregate";
            worksheet
                .Cell(currentRow, columnIndex)
                .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            columnIndex++;

            // Add hour ending columns
            for (int hour = 1; hour <= 24; hour++)
            {
                worksheet.Cell(currentRow, columnIndex).Value = $"{hour}00";
                worksheet
                    .Cell(currentRow, columnIndex)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                columnIndex++;
            }

            // Add observation column
            worksheet.Cell(currentRow, columnIndex).Value = "Observations";
            worksheet
                .Cell(currentRow, columnIndex)
                .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            currentRow++;

            // Write data for each variable (order by position in report)
            foreach (
                var (columnName, aggregateData) in columnData.OrderBy(x =>
                    reportColumnsLookup[x.Key].Position
                )
            )
            {
                bool isFirstInGroup = true;

                // Write data for each aggregate (order by Min/Max/Avg/StdDev/Count)
                foreach (
                    (
                        AggregateType aggregateType,
                        Dictionary<int, string> hourlyValues
                    ) in aggregateData.OrderBy(x =>
                        report.TimeGrouping.GroupingAggregates.ToList().IndexOf(x.Key)
                    )
                )
                {
                    // Since accuracy report always include count, need to check if it is requested for display
                    if (
                        aggregateType == AggregateType.Count
                        && !report.TimeGrouping.GroupingAggregates.Contains(AggregateType.Count)
                    )
                    {
                        continue;
                    }

                    columnIndex = 1;

                    // Add variable name
                    worksheet.Cell(currentRow, columnIndex).Value = isFirstInGroup
                        ? columnName
                        : string.Empty;
                    worksheet.Cell(currentRow, columnIndex).Style.Font.Bold = true;
                    columnIndex++;

                    // Add aggregate type
                    worksheet.Cell(currentRow, columnIndex).Value = aggregateType
                        .ToString()
                        .ToUpper();
                    worksheet
                        .Cell(currentRow, columnIndex)
                        .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    columnIndex++;

                    // Write the hour ending (1-24) if available
                    for (int hour = 1; hour <= 24; hour++)
                    {
                        // Find the total count of the hour ending for the column
                        int? totalCount = lookupTable[date]
                            [columnName][AggregateType.Count]
                            .ContainsKey(hour)
                            ? (int)
                            double.Parse(
                                lookupTable[date][columnName][AggregateType.Count][hour]
                            )
                            : null;

                        worksheet.Cell(currentRow, columnIndex).Value = hourlyValues.ContainsKey(
                            hour
                        )
                            ? hourlyValues[hour]
                            : string.Empty;
                        worksheet.Cell(currentRow, columnIndex).Style.Font.FontColor =
                            !reportColumnsLookup[columnName].IsDayAheadForecastColumn()
                            && totalCount is not null
                            && totalCount < 10
                                ? XLColor.Red
                                : XLColor.Black;
                        worksheet
                            .Cell(currentRow, columnIndex)
                            .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        columnIndex++;
                    }

                    // Find the total points of the column for the date group
                    if (isFirstInGroup)
                    {
                        int totalPoints = lookupTable[date]
                            [columnName][AggregateType.Count]
                            .Values.Sum(x => (int)double.Parse(x));

                        worksheet.Cell(currentRow, columnIndex).Value = totalPoints;
                        worksheet.Cell(currentRow, columnIndex).Style.Font.Bold = true;
                        worksheet
                            .Cell(currentRow, columnIndex)
                            .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    }

                    currentRow++;
                    isFirstInGroup = false;
                }
            }

            // Auto-fit columns
            worksheet.Columns().AdjustToContents();
            // Add 2 units of padding to each column
            worksheet.Columns().ToList().ForEach(col => col.Width += 2);

            currentRow += 2; // Add space before next date group
        }

        // Save the workbook to a memory stream
        using MemoryStream ms = new();
        workbook.SaveAs(ms);
        ms.Position = 0;

        return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, ms.ToArray());
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    private static ReportFormatOutput FormatHtml(Report report, List<ReportColumnData> reportData)
    {
        // No data, return empty content
        if (reportData.Count == 0)
        {
            return new ReportFormatOutput(
                report.Id,
                report.Name,
                report.FileFormat,
                Array.Empty<byte>()
            );
        }

        StringBuilder html = new();

        // Create lookup table for accuracy report
        var lookupTable = CreateAccuracyLookupTable(report.ReportColumns.ToList(), reportData);

        // Create a lookup table for column name
        Dictionary<string, ReportColumn> reportColumnsLookup = report.ReportColumns.ToDictionary(x => x.Name
        );

        // Render each date as a separate table
        foreach (var (date, columnData) in lookupTable)
        {
            html.AppendLine(
                @"<div style='
                color: black;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 20px;
                font-family: Arial, sans-serif;
                '>"
            );

            // Add title
            html.AppendLine(
                $@"<h2 style='
                font-size: 20px;
                font-weight: 600;
                color: #1d7fb8;
                margin-bottom: 15px;
            '>{report.Name} for {date:MMM-dd}</h2>"
            );

            // Add table wrapper
            html.AppendLine(
                @"<div style='
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 10px;
                overflow-y: hidden;
            '>"
            ); // Add wrapper for horizontal scroll

            // Add table
            html.AppendLine(
                @"<table style='
                width: 100%; 
                min-width: 1200px; 
                border-collapse: collapse;
                border: 1px solid #E6E6E6;
                line-height: 1.6;
                font-size: 14px;
            '>"
            );

            // Add headers
            html.AppendLine("<thead style='color: #1d7fb8'>");
            html.AppendLine("<tr style='border: 1px dotted #E6E6E6;'>");

            // Add variable and aggregate columns
            html.AppendLine(
                @"<th style='
                text-align: left;
                border: 1px dotted #E6E6E6;
                border-collapse: separate;
                padding: 8px;
                background-color: #f8f9fa;
                font-weight: 600;
            '>Variable</th>"
            );

            html.AppendLine(
                @"<th style='
                text-align: left;
                border: 1px dotted #E6E6E6;
                border-collapse: separate;
                padding: 8px;
                background-color: #f8f9fa;
                font-weight: 600;
            '>Aggregate</th>"
            );

            // Add hour headers
            for (int hour = 1; hour <= 24; hour++)
            {
                html.AppendLine(
                    @$"<th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    padding: 8px;
                    background-color: #f8f9fa;
                    font-weight: 600;
                    text-align: center;
                '>{hour}00</th>"
                );
            }

            // Add observation column
            html.AppendLine(
                @$"<th style='
                border: 1px dotted #E6E6E6;
                border-collapse: separate;
                padding: 8px;
                background-color: #f8f9fa;
                font-weight: 600;
                text-align: center;
            '>Observations</th>"
            );

            // Close headers
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");

            // Add body
            html.AppendLine("<tbody>");

            // Write data for each variable and its aggregates
            foreach (var column in columnData.OrderBy(x => reportColumnsLookup[x.Key].Position))
            {
                // Find the total points of the column for the date group
                Dictionary<int, string> hourlyEndingCounts = lookupTable[date][column.Key][
                    AggregateType.Count
                ];

                int totalPoints = hourlyEndingCounts.Values.Sum(x => (int)double.Parse(x));

                bool isFirstInGroup = true;

                // Get the variable name
                var variableName = column.Key;

                // Get the aggregate data
                var aggregateData = column.Value.OrderBy(x =>
                    report.TimeGrouping.GroupingAggregates.ToList().IndexOf(x.Key)
                );

                // Write data for each aggregate
                foreach (var (aggregateType, hourlyValues) in aggregateData)
                {
                    // Since accuracy report always include count, need to check if it is requested for display
                    if (
                        aggregateType == AggregateType.Count
                        && !report.TimeGrouping.GroupingAggregates.Contains(AggregateType.Count)
                    )
                    {
                        continue;
                    }

                    html.AppendLine("<tr>");

                    // Write variable name (only for first aggregate in group)
                    html.AppendLine(
                        @$"<td style='
                        padding: 8px;
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        text-align: left;
                        font-weight: {(isFirstInGroup ? "600" : "normal")};
                        background-color: {(isFirstInGroup ? "#f8f9fa" : "white")};
                    '>{(isFirstInGroup ? variableName : "")}</td>"
                    );

                    // Write aggregate type
                    html.AppendLine(
                        @$"<td style='
                        padding: 8px;
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        text-align: left;
                        color: #666;
                        background-color: {(isFirstInGroup ? "#f8f9fa" : "white")};
                    '>{aggregateType}</td>"
                    );

                    // Write hourly values
                    for (int hour = 1; hour <= 24; hour++)
                    {
                        string value = hourlyValues.ContainsKey(hour) ? hourlyValues[hour] : "";

                        // Get the total count of this hour for the column
                        int? currentHourTotalCount = column.Value[aggregateType].ContainsKey(hour)
                            ? (int)double.Parse(column.Value[AggregateType.Count][hour])
                            : null;

                        // Make the cell yellow if count of this hour < 10 (except for day ahead since there is only 1 value for each hour)
                        if (
                            reportColumnsLookup[variableName].Sources.Contains(DataSource.Actual)
                            && currentHourTotalCount is not null
                            && currentHourTotalCount < 10
                        )
                        {
                            html.AppendLine(
                                @$"
                            <td style='
                                padding: 8px;
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                                text-align: center;
                                background-color: yellow;
                            '>{value}</td>"
                            );
                        }
                        else
                        {
                            html.AppendLine(
                                @$"
                            <td style='
                                padding: 8px;
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                                text-align: center;
                                background-color: {(isFirstInGroup ? "#f8f9fa" : "white")};
                            '>{value}</td>"
                            );
                        }
                    }

                    // Add observation (total count of the hour ending for the date group)
                    if (isFirstInGroup)
                    {
                        html.AppendLine(
                            @$"
                        <td style='
                            padding: 8px;
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                            text-align: center;
                            font-weight: 600;
                            background-color: #f8f9fa;
                        '>{totalPoints}</td>"
                        );
                    }

                    html.AppendLine("</tr>");
                    isFirstInGroup = false;
                }
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");
            html.AppendLine("</div>"); // Close table wrapper
            html.AppendLine("</div>"); // Close date section
        }

        return new ReportFormatOutput(
            report.Id,
            report.Name,
            report.FileFormat,
            Encoding.UTF8.GetBytes(html.ToString())
        );
    }

    /// <summary>
    /// Create a lookup table for accuracy report data
    /// Lookup by date => by column name => by aggregate type => by hour ending (1 -> 24)=> value
    /// </summary>
    /// <param name="reportData">The report data to create lookup table for</param>
    /// <returns>The lookup table</returns>
    private static Dictionary<
        DateTime,
        Dictionary<string, Dictionary<AggregateType, Dictionary<int, string>>>
    > CreateAccuracyLookupTable(List<ReportColumn> reportColumns, List<ReportColumnData> reportData)
    {
        // Create lookup table for report columns
        Dictionary<Guid, ReportColumn> reportColumnsLookup = reportColumns.ToDictionary(x => x.Id);

        // Create lookup table for report data
        Dictionary<
            DateTime,
            Dictionary<string, Dictionary<AggregateType, Dictionary<int, string>>>
        > lookupTable = new();

        foreach (ReportColumnData data in reportData)
        {
            for (int i = 0; i < data.Timestamps.Length; i++)
            {
                DateTime timestamp = data.Timestamps[i];

                // Check if the report column exists
                if (!reportColumnsLookup.ContainsKey(data.Id))
                {
                    continue;
                }

                // Get the report column
                ReportColumn reportColumn = reportColumnsLookup[data.Id];

                // Calculate the hour ending and date grouped
                var (hourEnding, dateGrouped) = CalculateHourEnding(
                    reportColumn.Sources.ToList(),
                    timestamp
                );

                // Get the value of current timestamp
                object value = data.Values[i];

                // Check if the date grouped exists
                if (!lookupTable.ContainsKey(dateGrouped))
                {
                    lookupTable[dateGrouped] =
                        new Dictionary<
                            string,
                            Dictionary<AggregateType, Dictionary<int, string>>
                        >();
                }

                // Check if the variable exists
                if (!lookupTable[dateGrouped].ContainsKey(data.Name))
                {
                    lookupTable[dateGrouped][data.Name] =
                        new Dictionary<AggregateType, Dictionary<int, string>>();
                }

                // Check if the aggregate type exists
                if (!lookupTable[dateGrouped][data.Name].ContainsKey(data.AggregateType!.Value))
                {
                    lookupTable[dateGrouped][data.Name][data.AggregateType.Value] =
                        new Dictionary<int, string>();
                }

                // Format the value
                string formattedValue = FormatValue(value, reportColumn.NoOfDecimals);

                lookupTable[dateGrouped][data.Name][data.AggregateType.Value][hourEnding] =
                    formattedValue;
            }
        }

        // For column with no data
        // Add empty placeholder for each date so that it can be displayed in the report as empty
        foreach (ReportColumnData data in reportData.Where(x => x.Timestamps.Length == 0))
        {
            // Get all the dates used as keys in the lookup table
            List<DateTime> dateKeys = lookupTable.Keys.ToList();

            // Add the column to each date key
            foreach (DateTime dateKey in dateKeys)
            {
                // Add the column to the for each date
                if (!lookupTable[dateKey].ContainsKey(data.Name))
                {
                    lookupTable[dateKey][data.Name] =
                        new Dictionary<AggregateType, Dictionary<int, string>>();
                }

                // Add the aggregate type to the column for each date
                if (!lookupTable[dateKey][data.Name].ContainsKey(data.AggregateType!.Value))
                {
                    lookupTable[dateKey][data.Name][data.AggregateType!.Value] =
                        new Dictionary<int, string>();
                }
            }
        }

        return lookupTable;
    }

    /// <summary>
    /// Format the value based on the no of decimals
    /// </summary>
    /// <param name="value">The value to format</param>
    /// <param name="noOfDecimals">The no of decimals to format to</param>
    /// <returns>The formatted value</returns>
    private static string FormatValue(object? value, int noOfDecimals)
    {
        if (value == null)
            return "";

        if (double.TryParse(value.ToString(), out double numberValue))
        {
            return noOfDecimals > 0
                ? numberValue.ToString($"F{noOfDecimals}")
                : numberValue.ToString("F0");
        }

        return value.ToString() ?? "";
    }

    /// <summary>
    /// Calculate the hour ending and date grouped for the report column
    /// </summary>
    /// <param name="timestamp">The timestamp to calculate for</param>
    /// <returns>The hour ending and date grouped</returns>
    private static (int HourEnding, DateTime DateGrouped) CalculateHourEnding(
        List<DataSource> sources,
        DateTime timestamp
    )
    {
        int hourEnding = timestamp.Hour;

        // NOTE - Hour ending of day ahead data is 0000
        if (sources.Count == 1 && sources[0] == DataSource.DayAhead)
        {
            if (hourEnding == 0)
            {
                return (24, timestamp.Date.AddDays(-1));
            }
            else
            {
                return (hourEnding, timestamp.Date);
            }
        }

        return (hourEnding + 1, timestamp.Date);
    }
}
