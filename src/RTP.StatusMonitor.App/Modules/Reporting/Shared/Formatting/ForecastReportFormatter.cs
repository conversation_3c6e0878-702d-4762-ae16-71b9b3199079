using System.Text;
using ClosedXML.Excel;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;

public static class ForecastReportFormatter
{
    /// <summary>
    /// Format the report based on the layout and file format
    /// </summary>
    /// <param name="report">The report to format</param>
    /// <param name="layoutType">The layout type of the report</param>
    /// <param name="fileFormat">The file format of the report</param>
    /// <param name="reportData">The report data to format</param>
    /// <returns>The formatted report</returns>
    /// <exception cref="InvalidOperationException">Thrown when the layout type is not supported for the file format</exception>
    public static ReportFormatOutput FormatReport(
        Report report,
        ForecastLayoutType layoutType,
        ReportFileFormat fileFormat,
        ForecastProcessingResult reportData
    ) =>
        (layoutType, fileFormat) switch
        {
            (ForecastLayoutType.HourByVariable, ReportFileFormat.Excel) =>
                FormatExcelHourByVariable(report, reportData.Data),
            (ForecastLayoutType.VariableByHour, ReportFileFormat.CSV) => FormatCsvVariableByHour(
                report,
                reportData.Data
            ),
            (ForecastLayoutType.HourByVariable, ReportFileFormat.HTML) => FormatHtmlHourByVariable(
                report,
                reportData.Data
            ),
            (ForecastLayoutType.VariableByHour, ReportFileFormat.HTML) => FormatHtmlVariableByHour(
                report,
                reportData.Data
            ),
            _ => throw new InvalidOperationException(
                $"Layout {layoutType} not yet supported for {fileFormat} file format"
            ),
        };

    /// <summary>
    /// Formats the report data in the Variable (row) by Hour (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    private static ReportFormatOutput FormatCsvVariableByHour(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        using MemoryStream ms = new();
        using StreamWriter writer = new(ms, Encoding.UTF8);

        // Write headers
        List<string> headers =
        [
            "Unit Short Name",
            "Date",
            "Value",
            // Add hour headers
            .. Enumerable.Range(1, 24).Select(hour => $"{hour}00"),
        ];
        writer.WriteLine(string.Join(",", headers));

        // Build lookup table for forecast data
        Dictionary<DateTime, Dictionary<string, Dictionary<int, object?>>> forecastLookupByDate =
            CreateForecastLookupTable(reportData);

        // Get all dates of the forecast
        List<DateTime> forecastDates = [.. forecastLookupByDate.Keys];

        List<int> hourEndings = [.. Enumerable.Range(1, 24)];

        // Format table for each date of the forecast
        foreach (DateTime date in forecastDates)
        {
            // column -> hour ending -> value
            Dictionary<string, Dictionary<int, object?>>? lookupByColumn =
                forecastLookupByDate.ToLookUp(date);

            // If there is no date for the forecast, just skip
            if (lookupByColumn is null)
            {
                continue;
            }

            // Process each variable
            foreach (ReportColumn column in report.ReportColumns)
            {
                // Split the column name into unit name and tag
                // If the column name does not contains '-', then there will be no unit name
                (string unitShortName, string tag) = column.Name.Contains('-')
                    ? column.Name.Split('-', StringSplitOptions.RemoveEmptyEntries) switch
                    {
                        var s => (s[0], s[1]),
                    }
                    : (string.Empty, column.Name);

                Dictionary<int, object?>? lookupByHourEnding = lookupByColumn.ToLookUp(column.Name);

                // Create a csv row
                List<object> row = [unitShortName, date.ToString("M/d/yyyy"), tag];

                // If the column does not exist in the forecast data, add empty row for each hour
                if (lookupByHourEnding is null)
                {
                    hourEndings.ForEach(_ => row.Add(string.Empty));

                    // Continue to next iteration
                    continue;
                }

                // Add the hour ending data to the current row
                foreach (int hourEnding in hourEndings)
                {
                    object? value = lookupByHourEnding.ToLookUp(hourEnding);

                    row.Add(
                        value switch
                        {
                            // hour ending does not exist
                            null => string.Empty,
                            // format numerical value
                            _ when double.TryParse(value.ToString(), out double numericValue) =>
                                double.Parse(numericValue.ToString($"F{column.NoOfDecimals}")),
                            // non-numerical value
                            _ => value.ToString() ?? string.Empty,
                        }
                    );
                }

                writer.WriteLine(string.Join(",", row));
            }
        }

        writer.Flush();

        return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, ms.ToArray());
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    /// <exception cref="InvalidOperationException">Thrown when the report layout type is unknown.</exception>
    private static ReportFormatOutput FormatExcelHourByVariable(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        // Create Excel file
        using XLWorkbook workbook = new();
        IXLWorksheet worksheet = workbook.Worksheets.Add(report.Name);

        // Build the lookup table for forecast data
        // date -> column name -> hour ending -> value
        Dictionary<DateTime, Dictionary<string, Dictionary<int, object?>>> forecastLookup =
            CreateForecastLookupTable(reportData);

        // Create dictionary to lookup report by name
        Dictionary<string, ReportColumn> columnLookupByName = report.ReportColumns.ToDictionary(
            k => k.Name,
            v => v
        );

        List<DateTime> forecastDates = [.. forecastLookup.Keys];

        // Track current row
        int currentRow = 1;

        // Render each date as a separate table
        foreach (DateTime date in forecastDates)
        {
            // Add title
            worksheet.Cell(currentRow, 1).Value = $"{report.Name} for {date:MMM-dd}";
            worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
            worksheet.Cell(currentRow, 1).Style.Font.FontSize = 20;
            currentRow++;

            // Add the header for each date group
            // Calculate the maximum number of rows needed for headers (the height of the header row)
            int headerRowHeight = reportData.Max(data =>
                data.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length
            );

            // Add headers for this date section
            // First column is always "Hour" (offset by the number of rows needed for the header so it is aligned at the bottom)
            int offset = headerRowHeight - 1;
            worksheet.Cell(currentRow + offset, 1).Value = "Hour";
            worksheet.Cell(currentRow + offset, 1).Style.Font.FontSize = 12;

            // For each column, split the tag and add each word in a separate row
            for (int colIndex = 0; colIndex < report.ReportColumns.Count; colIndex++)
            {
                ReportColumn column = report.ReportColumns[colIndex];

                string[] words = column.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries);

                // Calculate the offset needed to align words at the bottom
                int rowsOffset = headerRowHeight - words.Length;

                // Add each word in a separate row, starting after the empty rows
                for (int wordIndex = 0; wordIndex < words.Length; wordIndex++)
                {
                    int rowPosition = currentRow + rowsOffset + wordIndex;
                    worksheet.Cell(rowPosition, colIndex + 2).Value = words[wordIndex];
                    worksheet
                        .Cell(rowPosition, colIndex + 2)
                        .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    worksheet.Cell(rowPosition, colIndex + 2).Style.Font.FontSize = 12;
                }
            }

            // Update current row to account for header height
            currentRow += headerRowHeight;

            // Add an empty line for seperation
            currentRow++;

            Dictionary<string, Dictionary<int, object?>> lookupByColumn = forecastLookup[date];

            // Get all hour endings of the forecast for the date
            List<int> hourEndings =
            [
                .. lookupByColumn.Values.SelectMany(x => x.Keys).Distinct().Order(),
            ];

            // Add data rows for this date
            foreach (int hourEnding in hourEndings)
            {
                // Add hour ending
                worksheet.Cell(currentRow, 1).Value = hourEnding;
                worksheet
                    .Cell(currentRow, 1)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
                worksheet.Cell(currentRow, 1).Style.Font.FontSize = 12;

                // Add values for each column
                for (int col = 0; col < report.ReportColumns.Count; col++)
                {
                    ReportColumn column = report.ReportColumns[col];

                    // If there is no data for current column, show empty cell
                    if (!lookupByColumn.TryGetValue(column.Name, out var lookupByHourEnding))
                    {
                        worksheet.Cell(currentRow, col + 2).Value = string.Empty;
                        continue;
                    }

                    // If there is no hour ending for current date of the tag, skip (dont even show empty cell)
                    if (!lookupByHourEnding.TryGetValue(hourEnding, out object? value))
                    {
                        continue;
                    }

                    // Convert value to number if possible
                    if (double.TryParse(value?.ToString(), out double numberValue))
                    {
                        worksheet.Cell(currentRow, col + 2).Value = double.Parse(
                            numberValue.ToString($"F{column.NoOfDecimals}")
                        );
                    }
                    else
                    {
                        worksheet.Cell(currentRow, col + 2).Value =
                            value?.ToString() ?? string.Empty;
                    }

                    worksheet
                        .Cell(currentRow, col + 2)
                        .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                }

                currentRow++;
            }

            currentRow++;

            // Add MIN/MAX/AVG rows
            foreach (string summaryType in new[] { "MIN", "AVG", "MAX" })
            {
                // Add label in first column
                worksheet.Cell(currentRow, 1).Value = summaryType;
                worksheet.Cell(currentRow, 1).Style.Font.FontSize = 12;
                worksheet
                    .Cell(currentRow, 1)
                    .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

                // Calculate and add summary values for each column
                for (int col = 0; col < report.ReportColumns.Count; col++)
                {
                    ReportColumn column = report.ReportColumns[col];

                    // If column does not exist, show empty cell
                    if (
                        !lookupByColumn.TryGetValue(
                            column.Name,
                            out Dictionary<int, object?>? lookupByHourEnding
                        )
                    )
                    {
                        worksheet.Cell(currentRow, col + 2).Value = string.Empty;
                        continue;
                    }

                    // Get all the values
                    List<double> values =
                    [
                        .. lookupByHourEnding
                            .Values.Where(v => v != null && double.TryParse(v.ToString(), out _))
                            .Select(v => double.Parse(v!.ToString()!)),
                    ];

                    // Find the decimals
                    int noOfDecimals = columnLookupByName.ToLookUp(column.Name)?.NoOfDecimals ?? 0;

                    // Calculate summary value
                    string summaryValue =
                        values.Count == 0
                            ? string.Empty
                            : summaryType switch
                            {
                                "MIN" => values.Min().ToString($"F{noOfDecimals}"),
                                "MAX" => values.Max().ToString($"F{noOfDecimals}"),
                                "AVG" => values.Average().ToString($"F{noOfDecimals}"),
                                _ => string.Empty,
                            };

                    // Set the cell value and formatting
                    worksheet.Cell(currentRow, col + 2).Value =
                        summaryValue.Length == 0 ? summaryValue : double.Parse(summaryValue);
                    worksheet
                        .Cell(currentRow, col + 2)
                        .Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                }
                currentRow++;
            }

            // Add two blank rows between date groups
            currentRow += 2;
        }

        // Save to memory stream
        using MemoryStream ms = new();
        workbook.SaveAs(ms);

        // Reset the stream position to the beginning
        ms.Position = 0;

        return new ReportFormatOutput(report.Id, report.Name, report.FileFormat, ms.ToArray());
    }

    /// <summary>
    /// Formats the report data in the Hour (row) by Variable (column) layout.
    /// </summary>
    /// <param name="report">The report to format.</param>
    /// <param name="reportData">The report data to format.</param>
    /// <returns>A byte array containing the formatted report.</returns>
    private static ReportFormatOutput FormatHtmlHourByVariable(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        // Build lookup dictionary for forecast data
        // date -> column -> hour ending -> value
        Dictionary<DateTime, Dictionary<string, Dictionary<int, object?>>> forecastLookup =
            CreateForecastLookupTable(reportData);

        // Create dictionary to lookup report by name
        Dictionary<string, ReportColumn> columnLookupByName = report.ReportColumns.ToDictionary(
            k => k.Name,
            v => v
        );

        List<DateTime> forecastDates = [.. forecastLookup.Keys];

        StringBuilder html = new();

        // Render each date group as a separate table
        foreach (DateTime date in forecastDates)
        {
            html.AppendLine(
                @"
                <div style='
                    color: black;
                    border-radius: 8px;
                    padding: 10px;
                    margin-bottom: 20px;
                '>"
            );

            // Create the title for current date's forecast table
            html.AppendLine(
                $@"
                <h3 style='
                    color: #4299e1;
                    font-size: 24px;
                    margin-bottom: 15px;
                    padding: 10px;
                '>{report.Name} for {date:MMM-dd}</h3>"
            );

            // Add wrapper for horizontal scroll
            html.AppendLine(
                @"
            <div style='
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 10px;
                /* Prevent vertical scroll */
                overflow-y: hidden;
            '>"
            );

            html.AppendLine(
                @"
            <table style='
                width: 100%; 
                min-width: 800px; 
                border-collapse: collapse;
                border: 1px solid #E6E6E6;
                line-height: 1.6;
            '>"
            );

            // Add header for the report table
            html.AppendLine("<thead style='color: #1d7fb8'>");

            // Compute the height of the header row
            int headerRowHeight = report
                .ReportColumns.Select(column =>
                    column.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length
                )
                .Max();

            // Create header rows
            for (int headerRow = 0; headerRow < headerRowHeight; headerRow++)
            {
                html.AppendLine(
                    @"
                <tr 
                    style='
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                '>"
                );

                // First column is "Hour" (only in the last row)
                if (headerRow == headerRowHeight - 1)
                {
                    html.AppendLine(
                        @"
                    <th 
                        style='
                            text-align: left;
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                    '>Hour</th>"
                    );
                }
                else
                {
                    html.AppendLine(
                        @"
                    <th 
                        style='
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                    '></th>"
                    );
                }

                // Add column headers
                foreach (string columnNames in report.ReportColumns.Select(c => c.Name))
                {
                    string[] words = columnNames.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    int rowsOffset = headerRowHeight - words.Length;

                    if (headerRow < rowsOffset)
                    {
                        // Empty cell for alignment
                        html.AppendLine(
                            @"
                        <th 
                            style='
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                        '></th>"
                        );
                    }
                    else
                    {
                        string word = words[headerRow - rowsOffset];
                        html.AppendLine(
                            @$"
                        <th 
                            style='
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                        '>{word}</th>"
                        );
                    }
                }

                html.AppendLine("</tr>");
            }
            html.AppendLine("</thead>");

            // Add data rows
            html.AppendLine("<tbody style='text-align: center'>");

            // column -> hour ending -> value
            Dictionary<string, Dictionary<int, object?>> lookupByColumn = forecastLookup[date];

            // Get all hour endings of the forecast for the date
            List<int> hourEndings = lookupByColumn
                .Values.SelectMany(x => x.Keys)
                .Distinct()
                .OrderBy(x => x)
                .ToList();

            foreach (int hourEnding in hourEndings)
            {
                html.AppendLine("<tr>");

                // Add hour ending cell
                html.AppendLine(
                    @$"
                    <td style='
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        font-weight: 600;
                        text-align: left;
                    '>{hourEnding}</td>"
                );

                // Each tag is added as a row in the table
                foreach (ReportColumn column in report.ReportColumns)
                {
                    // For column with no data, show empty cell
                    if (!lookupByColumn.ContainsKey(column.Name))
                    {
                        html.AppendLine(
                            @$"
                            <td style='
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                            '></td>"
                        );
                        continue;
                    }

                    // If there is no hour ending for current date of the tag, skip (dont even show empty cell)
                    Dictionary<int, object?> lookupByHourEnding = lookupByColumn[column.Name];
                    if (!lookupByHourEnding.ContainsKey(hourEnding))
                    {
                        continue;
                    }

                    object? value = lookupByHourEnding.ToLookUp(hourEnding);
                    // The hour ending exist but the value is null
                    if (value is null)
                    {
                        html.AppendLine(
                            @$"
                        <td style='
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                        '></td>"
                        );
                    }
                    else
                    {
                        // Find the decimals
                        int noOfDecimals =
                            columnLookupByName.ToLookUp(column.Name)?.NoOfDecimals ?? 0;

                        // Format the value to desired decimals
                        string formattedValue = double.TryParse(
                            value.ToString(),
                            out double numberValue
                        )
                            ? numberValue.ToString($"F{noOfDecimals}")
                            : value.ToString()!;

                        html.AppendLine(
                            @$"
                        <td style='
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                        '>{formattedValue}</td>"
                        );
                    }
                }
                html.AppendLine("</tr>");
            }
            html.AppendLine("</tbody>");

            // Add summary rows
            html.AppendLine("<tfoot>");
            foreach (string summaryType in new[] { "MIN", "AVG", "MAX" })
            {
                html.AppendLine(
                    @"
                    <tr style='
                        font-weight: 600;
                        color: #1d7fb8;
                    '>"
                );
                html.AppendLine(
                    @$"
                    <td style='
                        font-weight: 600;
                        color: #1d7fb8;
                        text-align: left;
                    '>{summaryType}</td>"
                );

                foreach (ReportColumn column in report.ReportColumns)
                {
                    // If the column does not exist, show empty cell
                    if (!lookupByColumn.TryGetValue(column.Name, out var lookupByHourEnding))
                    {
                        html.AppendLine(
                            $@"
                            <td style='
                                text-align: center;
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                            '></td>"
                        );
                        continue;
                    }

                    // Get all the values
                    List<double> values = lookupByHourEnding
                        .Values.Where(v => v is not null && double.TryParse(v.ToString(), out _))
                        .Select(v => double.Parse(v!.ToString()!))
                        .ToList();

                    // Find the decimals
                    int noOfDecimals = columnLookupByName.ToLookUp(column.Name)?.NoOfDecimals ?? 0;

                    // Compute the summary
                    string summaryValue =
                        values.Count == 0
                            ? string.Empty
                            : summaryType switch
                            {
                                "MIN" => values.Min().ToString($"F{noOfDecimals}"),
                                "MAX" => values.Max().ToString($"F{noOfDecimals}"),
                                "AVG" => values.Average().ToString($"F{noOfDecimals}"),
                                _ => string.Empty,
                            };

                    html.AppendLine(
                        $@"<td 
                        style='
                            text-align: center;
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                        '>{summaryValue}</td>"
                    );
                }
                html.AppendLine("</tr>");
            }
            html.AppendLine("</tfoot>");

            html.AppendLine("</table>");
            html.AppendLine("</div>");
            html.AppendLine("</div>");
        }

        return new ReportFormatOutput(
            report.Id,
            report.Name,
            report.FileFormat,
            Encoding.Default.GetBytes(html.ToString())
        );
    }

    /// <summary>
    /// Formats the report data in the Variable (row) by Hour (column) layout in HTML
    /// </summary>
    /// <param name="report"></param>
    /// <param name="reportData"></param>
    /// <returns></returns>
    private static ReportFormatOutput FormatHtmlVariableByHour(
        Report report,
        List<ReportColumnData> reportData
    )
    {
        StringBuilder html = new();

        // Build lookup table for forecast report
        Dictionary<DateTime, Dictionary<string, Dictionary<int, object?>>> forecastLookup =
            CreateForecastLookupTable(reportData);

        Dictionary<string, ReportColumn> columnLookupByName = report.ReportColumns.ToDictionary(
            k => k.Name,
            v => v
        );

        List<DateTime> forecastDates = forecastLookup.Keys.ToList();

        // Get all hour endings within a day
        List<int> hourEndings = Enumerable.Range(1, 24).ToList();

        // Render each date as a separate table
        foreach (DateTime date in forecastDates)
        {
            html.AppendLine(
                @"
                <div style='
                    color: black;
                    border-radius: 8px;
                    padding: 10px;
                    margin-bottom: 20px;
                '>"
            );

            html.AppendLine(
                $@"
                <h2 style='
                    font-size: 20px;
                    font-weight: 600
                '>{report.Name} for {date:MMM-dd}</h2>"
            );

            // Add wrapper for horizontal scroll
            html.AppendLine(
                @"
                <div style='
                    width: 100%;
                    overflow-x: auto;
                    -webkit-overflow-scrolling: touch;
                    margin-bottom: 10px;
                    /* Prevent vertical scroll */
                    overflow-y: hidden;
                '>"
            );

            html.AppendLine(
                @"
                <table style='
                    width: 100%; 
                    min-width: 800px; 
                    border-collapse: collapse;
                    border: 1px solid #E6E6E6;
                    line-height: 1.6;
                '>"
            );

            // Add headers
            html.AppendLine("<thead style='color: #1d7fb8'>");

            html.AppendLine("<tr style='border: 1px dotted #E6E6E6; '>");

            html.AppendLine(
                @"
                <th style='
                    text-align: left;
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                '>Unit Short Name</th>"
            );
            html.AppendLine(
                @"
                <th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                '>Date</th>"
            );
            html.AppendLine(
                @"
                <th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                '>Value</th>"
            );

            Enumerable
                .Range(1, 24)
                .ToList()
                .ForEach(hour =>
                    html.AppendLine(
                        @$"
                <th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                '>{hour}00</th>"
                    )
                );

            html.AppendLine("</tr>");

            html.AppendLine("</thead>");

            // Add data rows (table body)
            html.AppendLine("<tbody style='text-align: center'>");

            Dictionary<string, Dictionary<int, object?>> lookupByColumn = forecastLookup[date];

            // Render table body
            foreach (ReportColumn column in report.ReportColumns)
            {
                // Split the column name into unit name and tag
                // If the column name does not contains '-', then there will be no unit name
                (string unitShortName, string tag) = column.Name.Contains('-')
                    ? column.Name.Split('-', StringSplitOptions.RemoveEmptyEntries) switch
                    {
                        var s => (s[0], s[1]),
                    }
                    : (string.Empty, column.Name);

                html.AppendLine("<tr>");

                // Unit short name
                html.AppendLine(
                    @$"<td 
                    style='
                        padding: 0 1rem;
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        text-align: left;
                        font-weight: 600;
                    '>{unitShortName}</td>"
                );

                html.AppendLine(
                    @$"<td
                    style='
                        padding: 0 1rem;
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        text-align: left;
                    '>{date.ToString("yyyy/MM/dd")}</td>"
                ); // Date

                // Tag name
                html.AppendLine(
                    @$"<td
                    style='
                        padding: 0 1rem;
                        border: 1px dotted #E6E6E6;
                        border-collapse: separate;
                        text-align: left;
                    '>{tag}</td>"
                );

                // Write value from hour 1 to 24
                foreach (int hourEnding in hourEndings)
                {
                    Dictionary<int, object?>? lookupByHourEnding = lookupByColumn.ToLookUp(
                        column.Name
                    );

                    // If the column does not exist, show empty row
                    if (lookupByHourEnding is null)
                    {
                        html.AppendLine(
                            @$"
                        <td style='
                            border: 1px dotted #E6E6E6;
                            border-collapse: separate;
                            padding: 0 1rem;
                        '></td>"
                        );
                        continue;
                    }

                    // Otherwise, display the value for the hour ending (if exist)
                    object? value = lookupByHourEnding.ToLookUp(hourEnding);

                    // Either the value is null or simply data for the hour ending do not exist
                    // Then write empty cell
                    if (value is null)
                    {
                        html.AppendLine(
                            @$"
                            <td style='
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                                padding: 0 1rem;
                            '></td>"
                        );
                    }
                    // Otherwise, format the value and display in the cell
                    else
                    {
                        string formattedValue = string.Empty;
                        if (double.TryParse(value.ToString(), out double numberValue))
                        {
                            formattedValue = numberValue.ToString($"F{column.NoOfDecimals}");
                        }
                        else
                        {
                            formattedValue = value?.ToString() ?? string.Empty;
                        }

                        html.AppendLine(
                            @$"
                            <td style='
                                border: 1px dotted #E6E6E6;
                                border-collapse: separate;
                                padding: 0 1rem;
                            '>{formattedValue}</td>"
                        );
                    }
                }
            }

            html.AppendLine("</tbody>");

            html.AppendLine("</table>");

            html.AppendLine("</div>");
            html.AppendLine("</div>");
        }

        return new ReportFormatOutput(
            report.Id,
            report.Name,
            ReportFileFormat.HTML,
            Encoding.Default.GetBytes(html.ToString())
        );
    }

    /// <summary>
    /// Create a lookup table for forecast report
    /// date -> column name -> hour ending -> value
    /// </summary>
    /// <param name="reportColumns">All report columns</param>
    /// <param name="reportData">All report data</param>
    /// <returns>The lookup table</returns>
    private static Dictionary<
        DateTime,
        Dictionary<string, Dictionary<int, object?>>
    > CreateForecastLookupTable(List<ReportColumnData> reportData)
    {
        // date -> column name -> hour ending -> value
        Dictionary<DateTime, Dictionary<string, Dictionary<int, object?>>> lookupTable = [];

        // Populate the lookup table
        foreach (ReportColumnData reportColumn in reportData)
        {
            for (int i = 0; i < reportColumn.Timestamps.Length; i++)
            {
                DateTime timestamp = reportColumn.Timestamps[i];
                object? value = reportColumn.Values[i];

                // NOTE - for forecast data, 12AM is considered hour ending 24 of previous day
                (DateTime date, int hourEnding) =
                    timestamp.Hour == 0
                        ? (timestamp.AddDays(-1).Date, 24)
                        : (timestamp.Date, timestamp.Hour);

                // Create key for each date if not exist
                lookupTable.TryAdd(date, new Dictionary<string, Dictionary<int, object?>>());

                // column name -> hour ending -> value
                Dictionary<string, Dictionary<int, object?>> lookupByColumn = lookupTable[date];

                // Create key for each column if not exist
                lookupByColumn.TryAdd(reportColumn.Name, new Dictionary<int, object?>());

                // hour ending -> value
                Dictionary<int, object?> lookupByHourEnding = lookupByColumn[reportColumn.Name];

                // Create key for each hour ending
                lookupByHourEnding.TryAdd(hourEnding, value);
            }
        }

        return lookupTable;
    }
}
