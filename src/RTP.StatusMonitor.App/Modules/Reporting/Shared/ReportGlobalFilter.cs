using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared;

/// <summary>
/// Service for evaluating and applying report main filters to get corresponding dates
/// </summary>
public class ReportGlobalFilter(
    DataClientReadRepository dataClientReadRepository,
    IBlockRepository blockRepository,
    IDateTimeProvider dateTimeProvider
)
{
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly IBlockRepository _blockRepository = blockRepository;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;

    /// <summary>
    /// Get the dates that satisfy all the main report filters
    /// </summary>
    /// <param name="report">The report to filter</param>
    /// <param name="ct"></param>
    /// <returns>The list of dates that satisfied all the filters</returns>
    public async Task<Maybe<List<DateTime>>> GetDatesSatisfiedReportFilters(
        Report report,
        CancellationToken ct
    )
    {
        // If there is no filters, then return nothing
        if (report.Filters.Count == 0)
            return new Nothing<List<DateTime>>();

        // Query the blocks in the report
        List<Block> blocks = await _blockRepository.FilterBlocksById(
            blockIds: report.ReportColumns.Select(col => col.BlockId).Distinct().ToList(),
            ct: ct
        );

        List<List<DateTime>> datesSatifiedReportFilters = new();
        foreach (var filter in report.Filters)
        {
            // Find the block this filter is applying to
            Block blockToApplyFilter = blocks.Single(b => b.Id == filter.BlockId);

            // Get the date range to query data
            (DateTime startDate, DateTime endDate) = report.DateRangeInfo.GetDateRange(
                report,
                SiteLocalTime.Create(blockToApplyFilter.Site, _dateTimeProvider.UtcNow)
            );

            // Get the date range that satisfies the filter
            List<DateTime> dates = await GetDatesSatisfiedEachFilter(
                reportFilter: filter,
                block: blockToApplyFilter,
                startDate: startDate,
                endDate: endDate,
                ct: ct
            );

            // Add the dates that satisfy the filter
            datesSatifiedReportFilters.Add(dates);
        }

        // Combine the results using union or intersection depending on the rule
        return new Something<List<DateTime>>(report.ApplyReportFilters(datesSatifiedReportFilters));
    }

    /// <summary>
    /// Apply individual filter of the report (specific to the block) and get back the filtered dates
    /// for that filter
    /// </summary>
    /// <param name="reportFilter">The report filter</param>
    /// <param name="block">The block to apply the filter</param>
    /// <param name="startDate">The start date of the date range (this is already convert to local time of the site)</param>
    /// <param name="endDate">The end date of the date range (this is already convert to local time of the site)</param>
    /// <param name="ct"></param>
    /// <returns>The list of dates that satisfied this specific filter</returns>
    private async Task<List<DateTime>> GetDatesSatisfiedEachFilter(
        ReportFilter reportFilter,
        Block block,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct
    )
    {
        // Construct the filter expression of the report filter (this will include prefix such as [Min-Tag1] or [Max-Tag2] etc...)
        FilterExpression filterExpression = new(reportFilter.Filter);

        // Group all the tags by their prefix (Min, Max, Avg, etc...)
        Dictionary<string, List<string>> tagsGroupedByPrefix = ExpressionParser.ParseWithPrefix(
            filterExpression
        );

        // Get the daily statistics data for the date range
        List<StatisticsDataClientDto> dailyStats =
            await _dataClientReadRepository.GetHistoricalDailyDataAsync(
                block: block,
                uniqueTags: tagsGroupedByPrefix.Values.SelectMany(x => x).ToList(),
                startDate: startDate,
                endDate: endDate,
                ct: ct
            );

        // Group the statistics data by tag
        Dictionary<string, List<StatisticsDataClientDto>> statsDataGroupedByTag = dailyStats
            .GroupBy(x => x.Tag)
            .ToDictionary(x => x.Key, x => x.ToList());

        // Get the needed statistics data for each tag
        List<TimeSeriesData> timeSeriesData = new();
        foreach (var (tag, statsData) in statsDataGroupedByTag)
        {
            foreach (var (prefix, tags) in tagsGroupedByPrefix)
            {
                if (tags.Contains(tag))
                {
                    // Get only the statistics required as indicated by the prefix
                    object[] values = prefix switch
                    {
                        "Min" => statsData.Select(v => (object)v.Min).ToArray(),
                        "Max" => statsData.Select(v => (object)v.Max).ToArray(),
                        "Avg" => statsData.Select(v => (object)v.Avg).ToArray(),
                        "StdDev" => statsData.Select(v => (object)v.StdDev).ToArray(),
                        _ => throw new NotImplementedException(),
                    };

                    timeSeriesData.Add(
                        new TimeSeriesData(
                            Tag: $"{prefix}-{tag}",
                            Values: values,
                            Timestamps: statsData
                                .Select(v => DateTime.Parse(v.LocalTimestamp))
                                .ToArray()
                        )
                    );
                }
            }
        }

        // Put the time series data into a table for analytics to evaluate the filter
        // and get the dates that satisfy the filter
        return reportFilter.ApplyFilter(timeSeriesData);
    }
}
