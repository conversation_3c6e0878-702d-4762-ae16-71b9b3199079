using System.Collections.Concurrent;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DataClient;
using RTP.StatusMonitor.App.Shared.Api.InternalData;
using RTP.StatusMonitor.App.Shared.Api.InternalData.Requests;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;

public class ActualDataAggregation(
    IDataContext dataContext,
    IDateTimeProvider dateTimeProvider,
    InternalDataApi internalDataApi,
    DataClientReadRepository dataClientReadRepository
)
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly InternalDataApi _internalDataApi = internalDataApi;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;

    /// <summary>
    /// Aggregate actual data for a report
    /// </summary>
    /// <param name="report">The report to aggregate the data for</param>
    /// <param name="datesSatisfiedReportFilters">The dates that satisfy the report filters</param>
    /// <param name="ct"></param>
    /// <returns>The aggregated data for the report</returns>
    public async Task<List<ReportColumnData>> AggregateDataAsync(
        Report report,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct
    )
    {
        // Group the report columns by block to query data for each block
        Dictionary<Block, List<ReportColumn>> columnsGroupedByBlock =
            await GroupActualColumnByBlock(report, ct);

        // Query data for each block and aggregate the data
        List<ReportColumnData> reportColumnData = [];
        foreach ((Block block, List<ReportColumn> actualColumns) in columnsGroupedByBlock)
        {
            // Process columns that should use raw data
            Task<List<ReportColumnData>> rawDataTask = Task.Run(async () =>
            {
                // Find columns that should use raw data
                List<ReportColumn> columnsUsingRawData = actualColumns
                    .Where(c => c.ShouldProcessWithRawData(report.TimeGrouping.GroupingInterval))
                    .ToList();

                // Nothing is requested, then don't make API calls
                if (columnsUsingRawData.Count == 0)
                {
                    return [];
                }

                // Get the raw data for the columns and aggregate the data
                return await GetAndAggregateRawDataAsync(
                    report,
                    block,
                    columnsUsingRawData,
                    datesSatisfiedReportFilters,
                    ct
                );
            });

            // Process columns that should use pre-aggregated data
            Task<List<ReportColumnData>> preAggregatedDataTask = Task.Run(async () =>
            {
                List<ReportColumn> columnsUsingPreAggregatedData =
                [
                    .. actualColumns.Where(c =>
                        !c.ShouldProcessWithRawData(report.TimeGrouping.GroupingInterval)
                    ),
                ];

                // Nothing is requested, then don't make API calls
                if (columnsUsingPreAggregatedData.Count == 0)
                {
                    return [];
                }

                // Get the pre-aggregated data for the columns
                return await GetPreAggregatedDataAsync(
                    report,
                    block,
                    columnsUsingPreAggregatedData,
                    datesSatisfiedReportFilters,
                    ct
                );
            });

            // Wait for both tasks to complete
            await Task.WhenAll(rawDataTask, preAggregatedDataTask);

            // Add the column data for the current block to the report column data
            reportColumnData.AddRange(rawDataTask.Result.Concat(preAggregatedDataTask.Result));
        }

        // Order the report column data by the aggregate order
        return reportColumnData;
    }

    /// <summary>
    /// Get the raw data for the report and aggregate the data
    /// </summary>
    /// <param name="report">The report to get the data for</param>
    /// <param name="block">The block to get the data for</param>
    /// <param name="columns">The columns to get the data for</param>
    /// <param name="ct"></param>
    private async Task<List<ReportColumnData>> GetAndAggregateRawDataAsync(
        Report report,
        Block block,
        List<ReportColumn> columns,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct
    )
    {
        // Get the start and end date to query based on the report date range
        (DateTime startDate, DateTime endDate) = report.GetReportDateRange(
            block.Site,
            _dateTimeProvider.UtcNow
        );

        // Get the dates to query
        List<DateTime> datesToQuery = datesSatisfiedReportFilters switch
        {
            Something<List<DateTime>> dates => dates.Value,
            Nothing<List<DateTime>> _ =>
            [
                .. Enumerable
                    .Range(0, (endDate - startDate).Days + 1)
                    .Select(i => startDate.AddDays(i)),
            ],
            _ => [],
        };

        // Build the request for actual data
        List<DataClientSeriesRequestBody> actualDataRequested =
        [
            .. columns.Select(col => new DataClientSeriesRequestBody(
                Id: col.Id,
                Name: col.Name,
                Tag: col.Tag,
                Filter: col.Filter.Value,
                Calculation: col.Calculation.Value
            )),
        ];

        // Get the actual data for each date in parallel
        ConcurrentBag<DataClientHistoricalResponse> actualData = [];
        await Parallel.ForEachAsync(
            datesToQuery,
            async (currentDate, _) =>
            {
                // Get the actual data for the current date
                List<DataClientHistoricalResponse> actualDataForCurrentDate =
                    await _internalDataApi.GetActualHistoricalDataAsync(
                        blockId: block.Id,
                        startTime: currentDate.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss"),
                        endTime: currentDate.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss"),
                        interval: TimeSeriesResamplingInterval.None,
                        request: actualDataRequested,
                        ct: ct
                    );

                // Add the actual data for the current date to the concurrent bag
                for (int i = 0; i < actualDataForCurrentDate.Count; i++)
                {
                    actualData.Add(actualDataForCurrentDate[i]);
                }
            }
        );

        return AggregateRawData([.. actualData], report);
    }

    /// <summary>
    /// Aggregate the actual data
    /// </summary>
    /// <param name="actualData">The actual data to aggregate</param>
    /// <returns>The aggregated data</returns>
    private static List<ReportColumnData> AggregateRawData(
        List<DataClientHistoricalResponse> actualData,
        Report report
    )
    {
        // Get the aggregate types of the report (accuracy report includes count to get the total observations)
        List<AggregateType> aggregateTypes =
            report.ReportType == ReportType.Accuracy
                ?
                [
                    .. report
                        .TimeGrouping.GroupingAggregates.Concat([AggregateType.Count])
                        .Distinct(),
                ]
                : [.. report.TimeGrouping.GroupingAggregates];

        // Get the interval of the report
        TimeSeriesResamplingInterval interval =
            report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Group the actual data by column id
        Dictionary<Guid, DataClientHistoricalResponse> groupedData = actualData
            .GroupBy(data => data.Id)
            .ToDictionary(
                g => g.Key,
                g => new DataClientHistoricalResponse(
                    Id: g.Key,
                    Name: g.First().Name,
                    Values: [.. g.SelectMany(d => d.Values)],
                    Timestamps: [.. g.SelectMany(d => d.Timestamps)]
                )
            );

        // After we obtain the whole set of data
        // Group the data for each column and get statistics (min/max/avg/stddev/points)
        ConcurrentBag<ReportColumnData> reportColumnData = [];
        Parallel.ForEach(
            groupedData,
            (kvp, ct) =>
            {
                Guid colId = kvp.Key;
                DataClientHistoricalResponse columnData = kvp.Value;

                // Convert actual data to time series data for analytics
                TimeSeriesData timeSeriesData = new(
                    Tag: columnData.Name,
                    Values: [.. columnData.Values.Select(v => (object)v)],
                    Timestamps: [.. columnData.Timestamps.Select(DateTime.Parse)]
                );

                // Downsample the data to requested interval and get statistics
                List<(DateTime, double[])> stats =
                [
                    .. timeSeriesData
                        .Downsample(
                            interval,
                            [
                                .. aggregateTypes
                                    .Where(agg => agg != AggregateType.Count)
                                    .Select(aggregateType =>
                                        TimeSeriesDownsampling.AggregateFunctionsLookup[
                                            aggregateType
                                        ]
                                    ),
                            ]
                        )
                        .OrderBy(s => s.Item1),
                ];

                // Get the statistics values
                DateTime[] timestamps = [.. stats.Select(s => s.Item1)];

                // Aggregate the data for the column to get statistics based on requested interval
                List<ReportColumnData> statisticsColumns =
                [
                    .. aggregateTypes
                        .Where(agg => agg != AggregateType.Count)
                        .Select(
                            (aggregateType, index) =>
                            {
                                // Find the decimal requested for the column from the report
                                int noOfDecimals =
                                    report
                                        .ReportColumns.FirstOrDefault(col => col.Id == colId)
                                        ?.NoOfDecimals ?? 0;

                                return new ReportColumnData(
                                    Id: colId,
                                    Name: columnData.Name,
                                    Tag: columnData.Name,
                                    AggregateType: aggregateType,
                                    Timestamps: timestamps,
                                    Values:
                                    [
                                        .. stats.Select(s =>
                                            (object)Math.Round(s.Item2[index], noOfDecimals)
                                        ),
                                    ]
                                );
                            }
                        ),
                ];

                // Add the count values to the statistics columns
                if (aggregateTypes.Contains(AggregateType.Count))
                {
                    statisticsColumns.Add(
                        new ReportColumnData(
                            Id: colId,
                            Name: columnData.Name,
                            Tag: columnData.Name,
                            AggregateType: AggregateType.Count,
                            Timestamps: timestamps,
                            Values:
                            [
                                .. GetCountValues(timeSeriesData, stats, interval)
                                    .Select(v => (object)v),
                            ]
                        )
                    );
                }

                // Then add the statistics to the report column data
                for (int i = 0; i < statisticsColumns.Count; i++)
                {
                    reportColumnData.Add(statisticsColumns[i]);
                }
            }
        );

        return [.. reportColumnData];
    }

    /// <summary>
    /// Get the count values for the report
    /// </summary>
    /// <param name="timeSeriesData">The time series data</param>
    /// <param name="stats">The statistics data</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The count values</returns>
    private static int[] GetCountValues(
        TimeSeriesData timeSeriesData,
        List<(DateTime, double[])> stats,
        TimeSeriesResamplingInterval interval
    ) =>
        interval switch
        {
            TimeSeriesResamplingInterval.Minute => stats
                .Select(s =>
                    timeSeriesData.Timestamps.Count(t =>
                        t.Date == s.Item1.Date
                        && t.Hour == s.Item1.Hour
                        && t.Minute == s.Item1.Minute
                    )
                )
                .ToArray(),
            TimeSeriesResamplingInterval.Hour => stats
                .Select(s =>
                    timeSeriesData.Timestamps.Count(t =>
                        t.Date == s.Item1.Date && t.Hour == s.Item1.Hour
                    )
                )
                .ToArray(),
            TimeSeriesResamplingInterval.Day => stats
                .Select(s => timeSeriesData.Timestamps.Count(t => t.Date == s.Item1.Date))
                .ToArray(),
            TimeSeriesResamplingInterval.Week => stats
                .Select(s => timeSeriesData.Timestamps.Count(t => t.IsSameWeekAs(s.Item1)))
                .ToArray(),
            TimeSeriesResamplingInterval.Month => stats
                .Select(s => timeSeriesData.Timestamps.Count(t => t.Month == s.Item1.Month))
                .ToArray(),
            _ => stats.Select(s => timeSeriesData.Timestamps.Length).ToArray(),
        };

    /// <summary>
    /// Get the pre-aggregated (either hourly or daily) data from storage for the report
    /// </summary>
    /// <param name="report">The report to get the data for</param>
    /// <param name="block">The block to get the data for</param>
    /// <param name="columns">The columns to get the data for</param>
    /// <param name="ct"></param>
    /// <returns>The pre-aggregated data</returns>
    private async Task<List<ReportColumnData>> GetPreAggregatedDataAsync(
        Report report,
        Block block,
        List<ReportColumn> columns,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct
    )
    {
        // Get the aggregate types of the report (accuracy report includes count to get the total observations)
        List<AggregateType> aggregateTypes =
            report.ReportType == ReportType.Accuracy
                ? report
                    .TimeGrouping.GroupingAggregates.Concat([AggregateType.Count])
                    .Distinct()
                    .ToList()
                : [.. report.TimeGrouping.GroupingAggregates];

        // Find the tags of the columns
        List<string> tags = columns.Select(col => col.Tag).ToList();

        // Get the interval of the report
        TimeSeriesResamplingInterval interval =
            report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Get the start and end date to query data based on the report date range
        (DateTime startDate, DateTime endDate) = report.GetReportDateRange(
            block.Site,
            _dateTimeProvider.UtcNow
        );

        // Get the statistics data from storage
        List<StatisticsDataClientDto> statisticsData = interval switch
        {
            TimeSeriesResamplingInterval.Hour =>
                await _dataClientReadRepository.GetHistoricalHourlyDataAsync(
                    block,
                    tags,
                    startDate,
                    endDate,
                    ct
                ),
            TimeSeriesResamplingInterval.Day =>
                await _dataClientReadRepository.GetHistoricalDailyDataAsync(
                    block,
                    tags,
                    startDate,
                    endDate,
                    ct
                ),
            _ => [],
        };

        // Group the statistics data by tag
        Dictionary<string, List<StatisticsDataClientDto>> statisticsDataGroupedByTag =
            statisticsData
                .Where(stat =>
                    datesSatisfiedReportFilters switch
                    {
                        Something<List<DateTime>> dates => dates.Value.Contains(
                            DateTime.Parse(stat.LocalTimestamp)
                        ),
                        _ => true,
                    }
                )
                .GroupBy(stat => stat.Tag)
                .ToDictionary(g => g.Key, g => g.ToList());

        // Create the report column data
        List<ReportColumnData> reportColumnData = [];
        foreach (
            (string tag, List<StatisticsDataClientDto> statsData) in statisticsDataGroupedByTag
        )
        {
            ReportColumn? reportColumn = columns.FirstOrDefault(col => col.Tag == tag);
            int noOfDecimals = reportColumn?.NoOfDecimals ?? 0;

            if (reportColumn is not null)
            {
                reportColumnData.AddRange(
                    aggregateTypes.Select(agg => new ReportColumnData(
                        Id: reportColumn.Id,
                        Name: reportColumn.Name,
                        Tag: reportColumn.Tag,
                        AggregateType: agg,
                        Timestamps: statsData
                            .Select(stat => DateTime.Parse(stat.LocalTimestamp))
                            .ToArray(),
                        Values: statsData
                            .Select(stat =>
                                agg switch
                                {
                                    AggregateType.Min => Math.Round(stat.Min, noOfDecimals),
                                    AggregateType.Max => Math.Round(stat.Max, noOfDecimals),
                                    AggregateType.Avg => Math.Round(stat.Avg, noOfDecimals),
                                    AggregateType.StdDev => Math.Round(stat.StdDev, noOfDecimals),
                                    _ => (object)stat.Points,
                                }
                            )
                            .ToArray()
                    ))
                );
            }
        }

        return reportColumnData;
    }

    /// <summary>
    /// Group the actual columns by block
    /// </summary>
    /// <param name="report">The report to get the data for</param>
    /// <param name="ct"></param>
    /// <returns>The grouped data</returns>
    private async Task<Dictionary<Block, List<ReportColumn>>> GroupActualColumnByBlock(
        Report report,
        CancellationToken ct
    )
    {
        List<Block> blocks = await _dataContext
            .Blocks.AsNoTracking()
            .Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .Where(b => report.ReportColumns.Select(col => col.BlockId).Contains(b.Id))
            .ToListAsync(ct);

        return report
            .ReportColumns.Where(col =>
                col.Sources.Count == 1 && col.Sources[0] == DataSource.Actual
            )
            .GroupBy(col => blocks.Single(b => b.Id == col.BlockId))
            .ToDictionary(g => g.Key, g => g.ToList());
    }
}
