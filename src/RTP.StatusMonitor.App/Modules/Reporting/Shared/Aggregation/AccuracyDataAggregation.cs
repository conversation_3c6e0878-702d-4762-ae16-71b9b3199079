using System.Collections.Concurrent;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Analytics;
using RTP.StatusMonitor.App.Shared.Api.InternalData;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;

public class AccuracyDataAggregation(
    IDateTimeProvider dateTimeProvider,
    IDataContext dataContext,
    InternalDataApi internalDataApi)
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly InternalDataApi _internalDataApi = internalDataApi;

    public async Task<List<ReportColumnData>> AggregateDataAsync(
        Report report,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct)
    {
        // Group the multi-source columns by block (will filter out single source columns)
        Dictionary<Block, List<ReportColumn>> columnsGroupedByBlock = await GroupMultiSourceColumnByBlock(report, ct);

        // Query data for each block 
        List<ReportColumnData> reportColumnData = [];
        foreach ((Block block, List<ReportColumn> columns) in columnsGroupedByBlock)
        {
            // Query and aggregate data for current block
            List<ReportColumnData> columnDataForCurrentBlock = await GetAndAggregateDataAsync(
                report: report,
                block: block,
                multiSourceColumns: columns,
                datesSatisfiedReportFilters: datesSatisfiedReportFilters);

            // Add the column data for current block to list of all column data
            reportColumnData.AddRange(columnDataForCurrentBlock);
        }

        return reportColumnData;
    }

    /// <summary>
    /// Query raw actual data for a block for a list of dates
    /// and a list of actual data requested
    /// </summary>
    /// <param name="block">The block to get the actual data for</param>
    /// <param name="dates">The list of dates to get the actual data for</param>
    /// <param name="multiSourceSeriesRequested">The series (tag/filter/calculation) to get the actual data for</param>
    /// <returns></returns>
    public async Task<List<ReportColumnData>> GetAndAggregateDataAsync(
        Report report,
        Block block,
        List<ReportColumn> multiSourceColumns,
        Maybe<List<DateTime>> datesSatisfiedReportFilters)
    {
        // Find the start and end date to query based on the report date range settings
        var (startDate, endDate) = report.GetReportDateRange(block.Site, _dateTimeProvider.UtcNow);

        // From the date range, select only the dates that satisfy the report filters
        List<DateTime> datesToQuery = datesSatisfiedReportFilters switch
        {
            Something<List<DateTime>> dates => dates.Value,
            _ => Enumerable.Range(0, (endDate - startDate).Days + 1)
               .Select(i => startDate.AddDays(i))
               .ToList(),
        };

        // Generate the series request body
        List<AnalyticsSeriesRequestBody> multiSourceSeriesRequested = GenerateSeriesRequestBody(
            interval: report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval(),
            columns: multiSourceColumns);

        ConcurrentBag<AnalyticsResponse> multiSourceData = [];

        // Nothing is requested, then don't make API calls
        if (multiSourceSeriesRequested.Count == 0)
            return [];

        // Query the data for each date
        await Parallel.ForEachAsync(
            datesToQuery,
            async (currentDate, _) =>
            {
                // Make HTTP request to get the actual data
                List<AnalyticsResponse> multiSourceDataForCurrentDate = await _internalDataApi
                    .GetHistoricalMultiSourceDataAsync(
                        blockId: block.Id,
                        startTime: currentDate
                            .StartOfDay()
                            .ToString("yyyy-MM-dd HH:mm:ss"),
                        endTime: currentDate
                            .EndOfDay()
                            .ToString("yyyy-MM-dd HH:mm:ss"),
                        interval: TimeSeriesResamplingInterval.Minute,
                        request: multiSourceSeriesRequested);

                // Add the actual data for current date to list of all actual data
                multiSourceDataForCurrentDate
                    .ForEach(data => multiSourceData.Add(data));
            });

        // Map the report time grouping interval to a TimeSeriesResamplingInterval
        var interval = report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Get the aggregate types from the report
        List<AggregateType> aggregateTypes = report.TimeGrouping.GroupingAggregates.ToList();

        // Aggregate the data
        return AggregateData(interval, aggregateTypes, multiSourceData);
    }

    /// <summary>
    /// Aggregate the data for the report
    /// </summary>
    /// <param name="interval">The interval of the data</param>
    /// <param name="aggregateTypes">The aggregate types to get the data for</param>
    /// <param name="multiSourceData">The multi-source data to aggregate</param>
    /// <returns>The aggregated data</returns>
    private static List<ReportColumnData> AggregateData(
        TimeSeriesResamplingInterval interval,
        List<AggregateType> aggregateTypes,
        ConcurrentBag<AnalyticsResponse> multiSourceData)
    {
        // Group the actual data by id and combine the values and timestamps
        // NOTE - the Id of the response series will also be the column id
        Dictionary<Guid, AnalyticsResponse> multiSourceDataGroupedById = multiSourceData
            .GroupBy(data => data.Id)
            .ToDictionary(
                g => g.Key,
                g => new AnalyticsResponse(
                Id: g.Key,
                Name: g.First().Name,
                Values: g.SelectMany(d => d.Values).ToList(),
                Timestamps: g.SelectMany(d => d.Timestamps).ToList()));

        // After we obtain the whole set of data 
        // Group the data for each column and get statistics (min/max/avg/stddev/points)
        List<ReportColumnData> reportColumnData = new();
        foreach (var (colId, columnData) in multiSourceDataGroupedById)
        {
            // Convert actual data to time series data for analytics
            TimeSeriesData timeSeriesData = new(
                Tag: columnData.Name,
                Values: columnData.Values
                    .Select(v => (object)v)
                    .ToArray(),
                Timestamps: columnData.Timestamps
                    .Select(t => DateTime.Parse(t))
                    .ToArray());

            // No need to further downsampling, data is already in minute interval
            if (interval == TimeSeriesResamplingInterval.Minute)
            {
                // Data sorted by timestamp
                var sortedData = timeSeriesData.Timestamps
                    .Zip(timeSeriesData.Values, (time, value) => (time, value))
                    .OrderBy(z => z.time)
                    .ToList();

                reportColumnData.Add(new ReportColumnData(
                    Id: colId,
                    Name: columnData.Name,
                    Tag: columnData.Name,
                    AggregateType: AggregateType.Avg,
                    Timestamps: sortedData.Select(z => z.time).ToArray(),
                    Values: sortedData.Select(z => z.value).ToArray()));
            }
            else
            {
                // Downsample the data to requested interval and get statistics
                List<(DateTime, double[])> stats = timeSeriesData.Downsample(
                    interval,
                    aggregateTypes
                        .Where(agg => agg != AggregateType.Count)
                        .Select(agg => TimeSeriesDownsampling
                            .AggregateFunctionsLookup[agg])
                        .ToArray())
                    .OrderBy(s => s.Item1)
                    .ToList();

                // Get the timestamps
                DateTime[] timestamps = stats
                    .Select(s => s.Item1)
                    .ToArray();

                // Create the statistics columns based on the aggregate functions
                List<ReportColumnData> statisticsColumns = aggregateTypes
                    .Where(agg => agg != AggregateType.Count)
                    .Select((aggregateType, index) => new ReportColumnData(
                            Id: colId,
                            Name: columnData.Name,
                            Tag: columnData.Name,
                            AggregateType: aggregateType,
                            Timestamps: timestamps.ToArray(),
                            Values: stats.Select(s => (object)s.Item2[index]).ToArray())
                    ).ToList();

                // Add the count values to the statistics columns
                statisticsColumns.Add(new ReportColumnData(
                    Id: colId,
                    Name: columnData.Name,
                    Tag: columnData.Name,
                    AggregateType: AggregateType.Count,
                    Timestamps: timestamps,
                    Values: GetCountValues(timeSeriesData, stats, interval)
                        .Select(v => (object)v)
                        .ToArray()));

                // Then add the statistics to the report column data
                statisticsColumns.ForEach(col => reportColumnData.Add(col));
            }
        }

        // Each column in the report MUST have a unique name
        return reportColumnData
            .DistinctBy(c => $"{c.Name}-{c.AggregateType}")
            .ToList();
    }

    /// <summary>
    /// Get the count values for the report
    /// </summary>
    /// <param name="timeSeriesData">The time series data</param>
    /// <param name="stats">The statistics data</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The count values</returns>
    private static int[] GetCountValues(
        TimeSeriesData timeSeriesData,
        List<(DateTime, double[])> stats,
        TimeSeriesResamplingInterval interval) => interval switch
        {
            TimeSeriesResamplingInterval.Minute => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour &&
                        t.Minute == s.Item1.Minute)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Hour => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Day => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Date == s.Item1.Date)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Week => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.IsSameWeekAs(s.Item1))
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Month => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Month == s.Item1.Month)
                    .Count())
                .ToArray(),
            _ => stats
                .Select(s => timeSeriesData.Timestamps.Length)
                .ToArray()
        };

    /// <summary>
    /// Generates a list of AnalyticsSeriesRequestBody objects based on the specified time series resampling interval and list of report columns.
    /// </summary>
    /// <param name="interval">The time series resampling interval.</param>
    /// <param name="columns">The list of report columns.</param>
    /// <returns>A list of AnalyticsSeriesRequestBody objects.</returns>
    private static List<AnalyticsSeriesRequestBody> GenerateSeriesRequestBody(
        TimeSeriesResamplingInterval interval,
        List<ReportColumn> columns)
    {
        // Operating modes columns
        List<AnalyticsSeriesRequestBody> operatingModesColumns = columns
            .Select(col => new AnalyticsSeriesRequestBody(
                Id: col.Id,
                Name: col.Name,
                Filter: col.Filter.Value,
                Calculation: col.Calculation.Value))
            .ToList();

        List<AnalyticsSeriesRequestBody> supportingColumns = columns
            .SelectMany(c => ExpressionParser.Parse(c.Filter)
                .Concat(ExpressionParser.Parse(c.Calculation)))
            .Select(tagWithPrefix => new AnalyticsSeriesRequestBody(
                Id: Guid.NewGuid(),
                Name: tagWithPrefix,
                Filter: string.Empty,
                Calculation: $"[{tagWithPrefix}]"))
            .ToList();

        // If the interval is minute, we need to get the data for supporting columns as well for detailed analysis
        return interval == TimeSeriesResamplingInterval.Minute
            ? operatingModesColumns
                .Concat(supportingColumns)
                .ToList()
            : operatingModesColumns;
    }

    /// <summary>
    /// Groups the actual columns in the report by block.
    /// </summary>
    /// <param name="report">The report containing the columns to process.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>A dictionary where the key is the unit and the value is a list of report columns.</returns>
    public async Task<Dictionary<Block, List<ReportColumn>>> GroupMultiSourceColumnByBlock(
        Report report,
        CancellationToken ct)
    {
        // Get all the blocks in the report
        List<Block> blocks = await _dataContext.Blocks
            .AsNoTracking()
                .Include(b => b.Site)
                    .ThenInclude(s => s.Customer)
            .Where(b => report.ReportColumns
                .Select(col => col.BlockId)
                .Contains(b.Id))
            .ToListAsync(ct);

        // Get all the multi-source columns in the report to process and group them by their block
        return report
            .ReportColumns
            .Where(col => col.Sources.Count > 1)
            .GroupColumnsBy(col => blocks.Single(b => b.Id == col.BlockId));
    }
}
