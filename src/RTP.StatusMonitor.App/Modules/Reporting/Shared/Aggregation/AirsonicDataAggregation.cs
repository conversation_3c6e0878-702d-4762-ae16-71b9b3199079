using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Api.Airsonic;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;
using System.Collections.Concurrent;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;

public class AirsonicDataAggregation(
    IDataContext dataContext,
    IDateTimeProvider dateTimeProvider,
    AirsonicApi airsonicApi,
    AirsonicReadRepository airsonicReadRepository)
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly AirsonicApi _airsonicApi = airsonicApi;
    private readonly AirsonicReadRepository _airsonicReadRepository = airsonicReadRepository;

    /// <summary>
    /// Aggregate the data for the report
    /// </summary>
    /// <param name="report">The report to aggregate the data for</param>
    /// <param name="datesSatisfiedReportFilters">The dates that satisfy the report filters</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns>The aggregated data</returns>
    public async Task<List<ReportColumnData>> AggregateDataAsync(
        Report report,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct)
    {
        // Group the report column for airsonic by unit. This will filter out any other data sources
        Dictionary<Unit, List<ReportColumn>> columnsGroupedByUnit = await GroupAirsonicColumnByUnit(report, ct);

        List<ReportColumnData> reportColumnData = new();
        foreach (var (unit, airsonicColumns) in columnsGroupedByUnit)
        {
            // Process columns that should use raw data
            Task<List<ReportColumnData>> rawDataTask = Task.Run(async () =>
            {
                List<ReportColumn> columnsUsingRawData = [.. airsonicColumns.Where(c => c.ShouldProcessWithRawData(report.TimeGrouping.GroupingInterval))];

                // Nothing is requested, then don't make API calls
                if (columnsUsingRawData.Count == 0)
                    return [];

                return await GetAndAggregateRawDataAsync(
                    report, unit,
                    columnsUsingRawData,
                    datesSatisfiedReportFilters, ct);
            });

            // Process columns that should use pre-aggregated data
            Task<List<ReportColumnData>> preAggregatedDataTask = Task.Run(async () =>
            {
                List<ReportColumn> columnsUsingPreAggregatedData = [.. airsonicColumns
                    .Where(c => !c.ShouldProcessWithRawData(report.TimeGrouping.GroupingInterval))];

                // Nothing is requested, then don't make API calls
                if (columnsUsingPreAggregatedData.Count == 0)
                    return [];

                return await GetPreAggregatedDataAsync(
                    report, unit, columnsUsingPreAggregatedData, ct);
            });

            // Wait for both tasks to complete
            await Task.WhenAll(rawDataTask, preAggregatedDataTask);

            // Concatenate the raw data and pre-aggregated data
            List<ReportColumnData> columnDataForCurrentUnit = [
                .. rawDataTask.Result,
                .. preAggregatedDataTask.Result];

            // Add the unit data to the report column data
            reportColumnData.AddRange(columnDataForCurrentUnit);
        }

        return reportColumnData;
    }

    /// <summary>
    /// Get the raw data for the report and aggregate the data
    /// </summary>
    /// <param name="report">The report to get the data for</param>
    /// <param name="unit">The unit to get the data for</param>
    /// <param name="columns">The columns to get the data for</param>
    /// <param name="datesSatisfiedReportFilters">The dates that satisfy the report filters</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns>The raw data processed for the report</returns>
    private async Task<List<ReportColumnData>> GetAndAggregateRawDataAsync(
        Report report,
        Unit unit,
        List<ReportColumn> columns,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct)
    {
        // Based on the date range of the report, get the start and end date
        (DateTime startDate, DateTime endDate) = report
            .GetReportDateRange(unit.Block.Site, _dateTimeProvider.UtcNow);

        // Find the dates that satisfy the report filters
        List<DateTime> datesToQuery = datesSatisfiedReportFilters switch
        {
            Something<List<DateTime>> dates => dates.Value,
            _ => [.. Enumerable
                .Range(0, (endDate - startDate).Days + 1)
                .Select(i => startDate.AddDays(i))],
        };

        // Build the request for the airsonic data
        List<AirsonicSeriesRequestBody> airsonicDataRequested = [.. columns
            .Select(col => new AirsonicSeriesRequestBody(
                Id: col.Id,
                Name: col.Name,
                Alias: col.Tag,
                Filter: col.Filter.Value,
                Calculation: col.Calculation.Value))];

        // Get the airsonic data
        ConcurrentBag<AirsonicRawDataResponse> airsonicData = [];
        await Parallel.ForEachAsync(datesToQuery, async (currentDate, _) =>
        {
            List<AirsonicRawDataResponse> airsonicDataForCurrentDate = await _airsonicApi
                .GetHistoricalDataAsync(
                    unitId: unit.Id,
                    startTime: currentDate.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss"),
                    endTime: currentDate.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss"),
                    interval: TimeSeriesResamplingInterval.None,
                    request: airsonicDataRequested);

            airsonicDataForCurrentDate.ForEach(data => airsonicData.Add(data));
        });

        // Aggregate the raw data
        return AggregateRawData([.. airsonicData], report);
    }

    /// <summary>
    /// Aggregate the raw data
    /// </summary>
    /// <param name="airsonicData">The raw data to aggregate</param>
    /// <param name="aggregateTypes">The aggregate types to use</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The aggregated data</returns>
    private static List<ReportColumnData> AggregateRawData(
        List<AirsonicRawDataResponse> airsonicData,
        Report report)
    {
        List<AggregateType> aggregateTypes = [.. report.TimeGrouping.GroupingAggregates];
        TimeSeriesResamplingInterval interval = report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Group the data by column id
        Dictionary<Guid, AirsonicRawDataResponse> groupedData = airsonicData
            .GroupBy(data => data.Id)
            .ToDictionary(
                g => g.Key,
                g => new AirsonicRawDataResponse(
                    Id: g.Key,
                    Name: g.First().Name,
                    Alias: g.First().Alias,
                    Values: [.. g.SelectMany(d => d.Values)],
                    Timestamps: [.. g.SelectMany(d => d.Timestamps)]));

        // Create the report column data
        ConcurrentBag<ReportColumnData> reportColumnData = [];
        Parallel.ForEach(groupedData, (kvp, ct) =>
        {
            Guid colId = kvp.Key;
            AirsonicRawDataResponse columnData = kvp.Value;

            // Create the time series data
            TimeSeriesData timeSeriesData = new(
                Tag: columnData.Name,
                Values: [.. columnData.Values],
                Timestamps: [.. columnData.Timestamps.Select(DateTime.Parse)]);

            // Downsample the time series data
            List<(DateTime, double[])> stats = [.. timeSeriesData
                .Downsample(interval, [.. aggregateTypes
                    .Where(agg => agg != AggregateType.Count)
                    .Select(aggregateType => TimeSeriesDownsampling.AggregateFunctionsLookup[aggregateType])])
                .OrderBy(s => s.Item1)];

            // Get the timestamps (this will help with the count values)
            DateTime[] timestamps = [.. stats.Select(s => s.Item1)];

            // Create the report column data
            List<ReportColumnData> statisticsColumns = [.. aggregateTypes
                .Where(agg => agg != AggregateType.Count)
                .Select(
                (aggregateType, index) =>
                {
                    int noOfDecimals = report.ReportColumns
                        .FirstOrDefault(col => col.Id == colId)?.NoOfDecimals ?? 0;

                    return new ReportColumnData(
                            Id: colId,
                            Name: columnData.Name,
                            Tag: columnData.Alias,
                            AggregateType: aggregateType,
                            Timestamps: timestamps,
                            // NOTE - we need to round the values to the number of decimals requested
                            Values: [.. stats.Select(s => (object)Math.Round(s.Item2[index], noOfDecimals))]);
                })];

            if (aggregateTypes.Contains(AggregateType.Count))
            {
                statisticsColumns.Add(new ReportColumnData(
                    Id: colId,
                    Name: columnData.Name,
                    Tag: columnData.Name,
                    AggregateType: AggregateType.Count,
                    Timestamps: timestamps,
                    Values: [.. GetCountValues(timeSeriesData, stats, interval).Select(v => (object)v)]));
            }

            for (int i = 0; i < statisticsColumns.Count; i++)
            {
                reportColumnData.Add(statisticsColumns[i]);
            }
        });

        return [.. reportColumnData];
    }

    /// <summary>
    /// Get the count values for the report
    /// </summary>
    /// <param name="timeSeriesData">The time series data</param>
    /// <param name="stats">The statistics data</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The count values</returns>
    private static int[] GetCountValues(
        TimeSeriesData timeSeriesData,
        List<(DateTime, double[])> stats,
        TimeSeriesResamplingInterval interval) => interval switch
        {
            TimeSeriesResamplingInterval.Minute => [.. stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour &&
                        t.Minute == s.Item1.Minute)
                    .Count())],
            TimeSeriesResamplingInterval.Hour => [.. stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour)
                    .Count())],
            TimeSeriesResamplingInterval.Day => [.. stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Date == s.Item1.Date)
                    .Count())],
            TimeSeriesResamplingInterval.Week => [.. stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.IsSameWeekAs(s.Item1))
                    .Count())],
            TimeSeriesResamplingInterval.Month => [.. stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Month == s.Item1.Month)
                    .Count())],
            _ => [.. stats.Select(s => timeSeriesData.Timestamps.Length)]
        };

    /// <summary>
    /// Get the pre-aggregated data
    /// </summary>
    /// <param name="report">The report to get the data for</param>
    /// <param name="unit">The unit to get the data for</param>
    /// <param name="columns">The columns to get the data for</param>
    /// <param name="startDate">The start date of the data</param>
    /// <param name="endDate">The end date of the data</param>
    /// <param name="interval">The interval of the data</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns></returns>
    private async Task<List<ReportColumnData>> GetPreAggregatedDataAsync(
        Report report,
        Unit unit,
        List<ReportColumn> columns,
        CancellationToken ct)
    {
        List<string> tags = columns.Select(col => col.Tag).ToList();

        // Map the report interval to the time series resampling interval
        TimeSeriesResamplingInterval interval = report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Get the start and end date based on the report date range
        var (startDate, endDate) = report.GetReportDateRange(unit.Block.Site, _dateTimeProvider.UtcNow);

        // Get the statistics data based on the report interval
        List<StatisticsAirsonicDto> statisticsData = interval switch
        {
            TimeSeriesResamplingInterval.Hour => await _airsonicReadRepository.GetHistoricalHourlyDataAsync(unit, tags, startDate, endDate, ct),
            TimeSeriesResamplingInterval.Day => await _airsonicReadRepository.GetHistoricalDailyDataAsync(unit, tags, startDate, endDate, ct),
            _ => new List<StatisticsAirsonicDto>(),
        };

        Dictionary<string, List<StatisticsAirsonicDto>> statisticsDataGroupedByTag = statisticsData
            .GroupBy(stat => stat.Tag)
            .ToDictionary(g => g.Key, g => g.ToList());

        List<ReportColumnData> reportColumnData = new();
        foreach (var (tag, statsData) in statisticsDataGroupedByTag)
        {
            // Get the report column
            ReportColumn? reportColumn = columns.FirstOrDefault(col => col.Tag == tag);
            int noOfDecimals = reportColumn?.NoOfDecimals ?? 0;

            // If the report column exists, create the report column data
            if (reportColumn is not null)
            {
                reportColumnData.AddRange(
                    report.TimeGrouping.GroupingAggregates
                        .Select(agg =>
                            new ReportColumnData(
                                Id: reportColumn.Id,
                                Name: reportColumn.Name,
                                Tag: reportColumn.Tag,
                                AggregateType: agg,
                                Timestamps: statsData
                                    .Select(stat => DateTime.Parse(stat.LocalTimestamp))
                                    .ToArray(),
                                Values: statsData
                                .Select(stat => agg switch
                                {
                                    AggregateType.Min => Math.Round(stat.Min, noOfDecimals),
                                    AggregateType.Max => Math.Round(stat.Max, noOfDecimals),
                                    AggregateType.Avg => Math.Round(stat.Avg, noOfDecimals),
                                    AggregateType.StdDev => Math.Round(stat.StdDev, noOfDecimals),
                                    _ => (object)stat.Points
                                })
                                .ToArray())
                ));
            }
        }

        return reportColumnData;
    }

    /// <summary>
    /// Group the airsonic columns by unit
    /// </summary>
    /// <param name="report">The report to group the columns for</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns>The columns grouped by unit</returns>
    private async Task<Dictionary<Unit, List<ReportColumn>>> GroupAirsonicColumnByUnit(
        Report report,
        CancellationToken ct = default)
    {
        List<Unit> units = await _dataContext.Units
            .AsNoTracking()
            .Include(u => u.Block)
                .ThenInclude(b => b.Site)
            .Where(u => report.ReportColumns
                .Select(col => col.UnitId)
                .Contains(u.Id))
            .ToListAsync(ct);

        return report.ReportColumns
            .Where(col => col.Sources.Count == 1 && col.Sources[0] == DataSource.Airsonic)
            .GroupBy(col => units.Single(u => u.Id == col.UnitId))
            .ToDictionary(g => g.Key, g => g.ToList());
    }
}
