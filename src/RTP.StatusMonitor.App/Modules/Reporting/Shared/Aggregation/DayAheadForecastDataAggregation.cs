using System.Collections.Concurrent;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Forecast.Shared.Dtos;
using RTP.StatusMonitor.App.Shared.Api.Forecast;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.Shared.Aggregation;

public class DayAheadForecastDataAggregation(
    IDateTimeProvider dateTimeProvider,
    IDataContext dataContext,
    ForecastApi forecastApi)
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly ForecastApi _forecastApi = forecastApi;
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;

    public async Task<List<ReportColumnData>> AggregateDataAsync(
        Report report,
        Maybe<List<DateTime>> datesSatisfiedReportFilters,
        CancellationToken ct)
    {
        // Group the report columns by block (will filter out any other data sources)
        Dictionary<Block, List<ReportColumn>> columnsGroupedByBlock = await GroupDaForecastColumnByBlock(report, ct);

        // Query data for each block 
        List<ReportColumnData> reportColumnData = new();
        foreach (var (block, columns) in columnsGroupedByBlock)
        {
            // Nothing is requested, then don't make API calls
            if (columns.Count == 0)
                continue;

            // Query data for each block 
            List<ReportColumnData> columnDataForCurrentBlock = await GetAndAggregateDataAsync(
                report: report,
                block: block,
                dayAheadForecastColumns: columns,
                datesSatisfiedReportFilters: datesSatisfiedReportFilters);

            // Add the column data for current block to list of all column data
            reportColumnData.AddRange(columnDataForCurrentBlock);
        }

        return reportColumnData;
    }

    /// <summary>
    /// Query raw actual data for a block for a list of dates
    /// and a list of actual data requested
    /// </summary>
    /// <param name="block">The block to get the actual data for</param>
    /// <param name="dates">The list of dates to get the actual data for</param>
    /// <param name="dayAheadForecastRequested">The series (tag/filter/calculation) to get the actual data for</param>
    /// <returns></returns>
    public async Task<List<ReportColumnData>> GetAndAggregateDataAsync(
        Report report,
        Block block,
        List<ReportColumn> dayAheadForecastColumns,
        Maybe<List<DateTime>> datesSatisfiedReportFilters)
    {
        // Find the start and end date to query based on the report date range settings
        (DateTime startDate, DateTime endDate) = report.GetReportDateRange(block.Site, _dateTimeProvider.UtcNow);

        // From the date range, select only the dates that satisfy the report filters
        List<DateTime> datesToQuery = datesSatisfiedReportFilters switch
        {
            Something<List<DateTime>> dates => dates.Value,
            _ => Enumerable.Range(0, (endDate - startDate).Days + 1)
               .Select(i => startDate.AddDays(i))
               .ToList(),
        };

        // Get the interval of the report
        TimeSeriesResamplingInterval interval = report.TimeGrouping.GroupingInterval.ToTimeSeriesResamplingInterval();

        // Query the data for each date
        ConcurrentBag<ForecastDataResponse> dayAheadForecast = new();
        await Parallel.ForEachAsync(
            datesToQuery,
            async (date, _) =>
            {
                // Build the request
                List<ForecastSeriesRequestBody> dayAheadForecastRequested = dayAheadForecastColumns
                    .Select(col => new ForecastSeriesRequestBody(
                        Id: col.Id,
                        Name: col.Name,
                        Tag: col.Tag,
                        Filter: col.Filter.Value,
                        Calculation: col.Calculation.Value))
                    .ToList();


                // Make HTTP request to get the actual data
                List<ForecastDataResponse> forecastData = await _forecastApi
                    .GetDayAheadForecastAsync(
                        blockId: block.Id,
                        startDate: date.StartOfDay().ToString("yyyy-MM-dd"),
                        endDate: date.EndOfDay().ToString("yyyy-MM-dd"),
                        interval: TimeSeriesResamplingInterval.None,
                        request: dayAheadForecastRequested);

                foreach (var data in forecastData)
                {
                    if (interval is not TimeSeriesResamplingInterval.Hour)
                    {
                        // Skip the first element (timestamp, value) of each data in the set
                        // NOTE - Hour 00:00:00 of current date (not counted as day ahead of current date)
                        var timestamps = data.Timestamps.Skip(1);

                        // For the last element of timestamp, make it end of day
                        // NOTE - This is to include the first hour of next date (00:00:00 -> hour 24) in the data
                        timestamps = timestamps
                            .Take(timestamps.Count() - 1)
                            .Append(date.EndOfDay().ToString("yyyy-MM-dd 23:59:59"));

                        dayAheadForecast.Add(new ForecastDataResponse(
                            Id: data.Id,
                            Name: data.Name,
                            Values: data.Values.Skip(1).ToList(),
                            Timestamps: timestamps.ToList()));
                    }
                    // NOTE - for day ahead forecast => skip the first element (hour 00:00:00) of each data in the set since this is considered hour 24 of the previous day not current date
                    else
                    {
                        dayAheadForecast.Add(new ForecastDataResponse(
                            Id: data.Id,
                            Name: data.Name,
                            Values: data.Values.Skip(1).ToList(),
                            Timestamps: data.Timestamps.Skip(1).ToList()));
                    }
                }
            });

        // Get the aggregate types of the report (accuracy report includes count to get the total observations)
        List<AggregateType> aggregateTypes = report.ReportType == ReportType.Accuracy
            ? report.TimeGrouping.GroupingAggregates
                .Concat(new[] { AggregateType.Count })
                .Distinct()
                .ToList()
            : report.TimeGrouping.GroupingAggregates.ToList();

        // Aggregate the data based on the requested aggregate types and interval
        return AggregateData(dayAheadForecast, aggregateTypes, interval);
    }

    /// <summary>
    /// Aggregate the day ahead forecast data
    /// </summary>
    /// <param name="dayAheadForecast">The day ahead forecast data to aggregate</param>
    /// <param name="aggregateTypes">The aggregate types to get the statistics (min/max/avg/stddev/points)</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The aggregated data</returns>
    public static List<ReportColumnData> AggregateData(
        ConcurrentBag<ForecastDataResponse> dayAheadForecast,
        List<AggregateType> aggregateTypes,
        TimeSeriesResamplingInterval interval)
    {
        // Group the actual data by id and combine the values and timestamps
        // NOTE - the Id of the response series will also be the column id
        Dictionary<Guid, ForecastDataResponse> dayAheadForecastGroupedById = dayAheadForecast
            .GroupBy(data => data.Id)
            .ToDictionary(
                g => g.Key,
                g => new ForecastDataResponse(
                Id: g.Key,
                Name: g.First().Name,
                Values: g.SelectMany(d => d.Values).ToList(),
                Timestamps: g.SelectMany(d => d.Timestamps).ToList()));

        // After we obtain the whole set of data 
        // Group the data for each column and get statistics (min/max/avg/stddev/points)
        List<ReportColumnData> reportColumnData = new();
        foreach (var (colId, columnData) in dayAheadForecastGroupedById)
        {
            // Convert actual data to time series data for analytics
            TimeSeriesData timeSeriesData = new(
                Tag: columnData.Name,
                Values: columnData.Values.ToArray(),
                Timestamps: columnData.Timestamps
                    .Select(t => DateTime.Parse(t))
                    .ToArray());

            // NOTE - if the interval is hourly, then no need to downsample (da forecast is hourly data), and there can only be one aggregate type (avg)
            if (interval == TimeSeriesResamplingInterval.Hour)
            {
                reportColumnData.Add(new ReportColumnData(
                    Id: colId,
                    Name: columnData.Name,
                    Tag: columnData.Name,
                    AggregateType: AggregateType.Avg,
                    Timestamps: timeSeriesData.Timestamps,
                    Values: timeSeriesData.Values.ToArray()));

                reportColumnData.Add(new ReportColumnData(
                    Id: colId,
                    Name: columnData.Name,
                    Tag: columnData.Name,
                    AggregateType: AggregateType.Count,
                    Timestamps: timeSeriesData.Timestamps,
                    // NOTE - 1 value for each timestamp
                    Values: timeSeriesData.Timestamps
                        .Select(v => (object)1)
                        .ToArray()));
            }
            else
            {
                // Downsample the data to requested interval and get statistics
                List<(DateTime, double[])> stats = timeSeriesData
                    .Downsample(interval, aggregateTypes
                            .Where(agg => agg != AggregateType.Count)
                            .Select(agg => TimeSeriesDownsampling
                                .AggregateFunctionsLookup[agg])
                            .ToArray())
                    .OrderBy(s => s.Item1)
                    .ToList();

                // Get the statistics values
                DateTime[] timestamps = stats.Select(s => s.Item1).ToArray();

                // Aggregate the data for the column to get statistics based on requested interval
                List<ReportColumnData> statisticsColumns = aggregateTypes
                .Select((aggregateType, index) => aggregateType switch
                {
                    AggregateType.Count => new ReportColumnData(
                        Id: colId,
                        Name: columnData.Name,
                        Tag: columnData.Name,
                        AggregateType: aggregateType,
                        Timestamps: timestamps,
                        Values: GetCountValues(timeSeriesData, stats, interval)
                            .Select(v => (object)v)
                            .ToArray()),
                    _ => new ReportColumnData(
                        Id: colId,
                        Name: columnData.Name,
                        Tag: columnData.Name,
                        AggregateType: aggregateType,
                        Timestamps: timestamps,
                        Values: stats
                            .Select(s => (object)s.Item2[index])
                            .ToArray())
                })
                .ToList();

                // Then add the statistics to the report column data
                statisticsColumns.ForEach(col => reportColumnData.Add(col));
            }
        }

        return reportColumnData;
    }

    /// <summary>
    /// Get the count values for the report
    /// </summary>
    /// <param name="timeSeriesData">The time series data</param>
    /// <param name="stats">The statistics data</param>
    /// <param name="interval">The interval of the data</param>
    /// <returns>The count values</returns>
    private static int[] GetCountValues(
        TimeSeriesData timeSeriesData,
        List<(DateTime, double[])> stats,
        TimeSeriesResamplingInterval interval) => interval switch
        {
            TimeSeriesResamplingInterval.Minute => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour &&
                        t.Minute == s.Item1.Minute)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Hour => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(
                        t => t.Date == s.Item1.Date &&
                        t.Hour == s.Item1.Hour)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Day => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Date == s.Item1.Date)
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Week => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.IsSameWeekAs(s.Item1))
                    .Count())
                .ToArray(),
            TimeSeriesResamplingInterval.Month => stats
                .Select(s => timeSeriesData
                    .Timestamps
                    .Where(t => t.Month == s.Item1.Month)
                    .Count())
                .ToArray(),
            _ => stats
                .Select(s => timeSeriesData.Timestamps.Length)
                .ToArray()
        };

    /// <summary>
    /// Groups the day ahead forecast columns in the report by block.
    /// This will filter out any other data sources
    /// </summary>
    /// <param name="report">The report containing the columns to process.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>A dictionary where the key is the unit and the value is a list of report columns.</returns>
    public async Task<Dictionary<Block, List<ReportColumn>>> GroupDaForecastColumnByBlock(
        Report report,
        CancellationToken ct
    )
    {
        // Get all the blocks in the report
        List<Block> blocks = await _dataContext.Blocks
            .AsNoTracking()
                .Include(b => b.Site)
                    .ThenInclude(s => s.Customer)
            .Where(b => report.ReportColumns
                .Select(col => col.BlockId)
                .Contains(b.Id))
            .ToListAsync(ct);

        // Get all the actual columns in the report to process and group them by their id
        return report
            .ReportColumns
            .Where(col => col.Sources.Count == 1 &&
                col.Sources[0] == DataSource.DayAhead)
            .GroupColumnsBy(col => blocks.Single(b => b.Id == col.BlockId));
    }
}
