using Azure.Storage.Files.DataLake;
using Azure.Storage.Files.DataLake.Models;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.BlobStorage;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting.GetSavedReports;

internal sealed class GetSavedReportFilesQueryHandler(
    DataContext context,
    IOptions<BlobStorageOptions> blobStorageOptions,
    IBlobStorageService blobStorageService,
    IMemoryCache memoryCache
) : IQueryHandler<GetSavedReportFilesQuery, List<FileDetail>>
{
    private readonly DataContext _context = context;
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly IBlobStorageService _blobStorageService = blobStorageService;
    private readonly BlobStorageOptions _blobStorageOptions = blobStorageOptions.Value;

    public async Task<ErrorOr<List<FileDetail>>> Handle(
        GetSavedReportFilesQuery request,
        CancellationToken cancellationToken
    )
    {
        // query customer name, site name block name, unit name, and timezone
        // for all sites user has access to
        Site? site = await _context
            .Sites.AsNoTracking()
            .Include(s => s.Customer)
            .FirstOrDefaultAsync(site => site.Id == request.SiteId, cancellationToken);

        if (site is null)
        {
            return SiteDomainErrors.NotFound;
        }

        // All reports of the site is stored on blob in {container}/{site}/reports (ex: vistra/belling/reports)
        string blobDirectory = $"{site.Name.ToLower().Replace(" ", string.Empty)}/reports";

        List<FileDetail> fileDetails = [];
        try
        {
            // Create data lake directory client to interact with the blob
            string container = site.Customer.Name.ToLower();
            DataLakeDirectoryClient directoryClient = new(
                connectionString: _blobStorageOptions.ConnectionString,
                container,
                blobDirectory
            );

            // No reports if the folder does not exist
            if (!await directoryClient.ExistsAsync(cancellationToken))
            {
                return new List<FileDetail>();
            }

            // get the SAS URI for the directory and cache it for 1 hour
            Uri? sasUri = _memoryCache.GetOrCreate(
                $"{container}-{blobDirectory}",
                entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                    return _blobStorageService.GetServiceSasUri(container, blobDirectory);
                }
            );

            // Directory client is not authorized to create service SAS
            if (sasUri is null)
            {
                return new List<FileDetail>();
            }

            // Get files properties in data lake directory
            DataLakeFileSystemClient filesystem = new(
                _blobStorageOptions.ConnectionString,
                container
            );
            Azure.Pageable<PathItem> paths = filesystem.GetPaths(
                path: blobDirectory,
                cancellationToken: cancellationToken
            );

            foreach (PathItem item in paths)
            {
                // get the properties of the file
                Azure.Response<PathProperties> properties = await filesystem
                    .GetFileClient(item.Name)
                    .GetPropertiesAsync(cancellationToken: cancellationToken);

                if (properties.Value.IsDirectory)
                {
                    continue;
                }

                // if the file is not a directory, add the file details to the list
                fileDetails.Add(
                    new FileDetail(
                        FileName: item.Name,
                        FileUrl: sasUri.ToString().Replace(blobDirectory, item.Name),
                        LastUpdated: properties.Value.LastModified.UtcDateTime
                    )
                );
            }
        }
        catch (Exception e)
        {
            throw new InvalidOperationException(e.Message);
        }

        // Get the file details in the forecast directory
        return fileDetails.ConvertAll(file => new FileDetail(
            FileName: file.FileName.Replace($"{blobDirectory}/", string.Empty),
            FileUrl: file.FileUrl,
            LastUpdated: TimeZoneInfo.ConvertTimeFromUtc(
                file.LastUpdated,
                TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone)
            )
        ));
    }
}
