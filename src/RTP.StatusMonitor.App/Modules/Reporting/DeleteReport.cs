using ErrorOr;
using MediatR;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Reports;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record DeleteReportCommand(
    Guid ReportId,
    List<string> UserRoles,
    string RequestedBy) : ICommand;

internal sealed class DeleteReportCommandHandler(
    IReportRepository reportRepository,
    IUnitOfWork unitOfWork)
        : ICommandHandler<DeleteReportCommand>
{
    private readonly IReportRepository _reportRepository = reportRepository;
    private readonly IUnitOfWork _unitOfWork = unitOfWork;

    public async Task<ErrorOr<Unit>> Handle(
        DeleteReportCommand request,
        CancellationToken ct)
    {
        // Find the report to delete
        Maybe<Report> reportToDelete = await _reportRepository
            .GetReportByIdAsync(request.ReportId, ct);

        // Make sure there is one to perform deletion
        if (reportToDelete is Nothing<Report>)
            return ReportErrors.NotFound;

        // Make sure user has sufficient permission to perform this operation
        if (reportToDelete is Something<Report> something)
        {
            // Make sure user has sufficient permission to perform this operation
            if (something.Value.HasWritePermission(
                appRoles: request.UserRoles,
                requestedBy: request.RequestedBy) is false)
            {
                return ReportErrors.InsufficientWritePermission;
            }

            // Delete the report
            _reportRepository.DeleteReport(something.Value);

            // Save changes
            await _unitOfWork.SaveChangesAsync(ct);
        }

        return Unit.Value;
    }
}
