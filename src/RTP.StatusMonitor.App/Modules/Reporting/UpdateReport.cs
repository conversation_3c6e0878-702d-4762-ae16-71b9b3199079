using ErrorOr;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Dto;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Domain.Shared;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record UpdateReportCommand(
    Guid ReportId,
    ReportCommand UpdatedReport,
    string RequestedBy) : ICommand;

internal sealed class UpdateReportCommandHandler
    : ICommandHandler<UpdateReportCommand>
{
    private readonly IReportRepository _reportRepository;
    private readonly IDateTimeProvider _dateTimeProvider;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateReportCommandHandler(
        IUnitOfWork unitOfWork,
        IDateTimeProvider dateTimeProvider,
        IReportRepository reportRepository)
    {
        _unitOfWork = unitOfWork;
        _dateTimeProvider = dateTimeProvider;
        _reportRepository = reportRepository;
    }

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        UpdateReportCommand request,
        CancellationToken ct)
    {
        // Get the report to update
        Maybe<Report> reportToUpdate = await _reportRepository
            .GetReportByIdAsync(request.ReportId, ct);

        // Make sure the report exists
        if (reportToUpdate is Nothing<Report>)
            return ReportErrors.NotFound;

        var reportToUpdateValue = ((Something<Report>)reportToUpdate).Value;

        // Get all existing reports
        List<Report> existingReports = await _reportRepository
            .GetReportsAsync(ct);

        // Filter out the report about to be updated to check for duplicates
        List<Report> filteredReports = existingReports
            .Where(r => r.Id != request.ReportId)
            .ToList();

        var updatedReport = request.UpdatedReport switch
        {
            ForecastReportCommand forecastReport => CreateForecastReport(
                forecastReport,
                filteredReports,
                request.RequestedBy),
            HistoricalOrAccuracyReportCommand historicalReport => CreateHistoricalReport(
                historicalReport,
                filteredReports,
                request.RequestedBy),
            LookupReportCommand lookupReport => CreateLookupReport(
                lookupReport,
                filteredReports,
                request.RequestedBy),
            _ => throw new InvalidOperationException("Invalid report type")
        };

        if (updatedReport.IsError)
            return updatedReport.Errors;

        // Delete the report to update
        _reportRepository.DeleteReport(reportToUpdateValue);

        // Add the updated report
        await _reportRepository.AddReportAsync(updatedReport.Value, ct);

        await _unitOfWork.SaveChangesAsync(ct);

        return MediatR.Unit.Value;
    }

    /// <summary>
    /// Create a forecast report.
    /// </summary>
    public ErrorOr<Report> CreateForecastReport(
        ForecastReportCommand forecastReport,
        IEnumerable<Report> existingReports,
        string requestedBy)
    {
        // Create the report columns
        List<ErrorOr<ReportColumn>> columns = forecastReport
            .ReportColumns
            .Select(col => ReportColumn.Create(
                reportColumnId: col.ReportColumnId,
                siteId: col.SiteId,
                blockId: col.BlockId,
                unitId: col.UnitId,
                name: col.Name,
                sources: col.Sources,
                tag: col.Tag,
                calculation: new(col.Calculation),
                filter: new(col.Filter),
                position: col.Position,
                noOfDecimals: col.NoOfDecimals,
                engUnits: col.EngUnits,
                color: col.Color))
            .ToList();

        // Create the reporting schedule
        ErrorOr<ReportSchedule> schedule = ReportSchedule.Create(
            forecastReport.IsEnabled, forecastReport.ProcessInterval);

        // Create the relative date range for forecast report
        ErrorOr<RelativeReportDateRange> relativeDateRange = RelativeReportDateRange.Create(
                forecastReport.RelativeDateRange.StartOffset,
                forecastReport.RelativeDateRange.EndOffset);

        // Create the report layout for forecast report
        ErrorOr<ReportLayout> reportLayout = ReportLayout
            .CreateForecastLayout(ReportType.Forecast, forecastReport.ReportLayout);

        // Check if the schedule and relative date range are valid
        return ErrorOrExt
            .Combine(schedule, relativeDateRange, reportLayout)
            .Then(_ => ErrorOrExt.Combine(columns))
            .Then(_ => Report.CreateForecastReport(
                reportId: forecastReport.ReportId,
                name: forecastReport.Name,
                description: forecastReport.Description,
                container: forecastReport.Container,
                columns: columns.Select(c => c.Value).ToList(),
                schedule: schedule.Value,
                blocksInReport: forecastReport.BlocksInReport,
                userGroupsAllowed: forecastReport.UserGroupsAllowed,
                auditInfo: new AuditInfo(
                    CreatedBy: requestedBy,
                    DateCreated: _dateTimeProvider.UtcNow,
                    ModifiedBy: requestedBy,
                    DateModified: _dateTimeProvider.UtcNow),
                dateRangeInfo: relativeDateRange.Value,
                reportLayout: reportLayout.Value,
                fileFormat: Enum.Parse<ReportFileFormat>(forecastReport.FileFormat),
                existingReports: existingReports.ToList()))
            .Else(errors => errors);
    }

    /// <summary>
    /// Create historical report.
    /// </summary>
    public ErrorOr<Report> CreateHistoricalReport(
        HistoricalOrAccuracyReportCommand historicalReport,
        IEnumerable<Report> existingReports,
        string requestedBy)
    {
        // Create the report columns
        List<ErrorOr<ReportColumn>> columns = historicalReport
            .ReportColumns
            .Select(col => ReportColumn.Create(
                reportColumnId: col.ReportColumnId,
                siteId: col.SiteId,
                blockId: col.BlockId,
                unitId: col.UnitId,
                name: col.Name,
                sources: col.Sources,
                tag: col.Tag,
                calculation: new(col.Calculation),
                filter: new(col.Filter),
                position: col.Position,
                noOfDecimals: col.NoOfDecimals,
                engUnits: col.EngUnits,
                color: col.Color))
            .ToList();

        // Create the reporting schedule
        ErrorOr<ReportSchedule> schedule = ReportSchedule.Create(
            historicalReport.IsEnabled, historicalReport.ProcessInterval);

        // Create the filters 
        List<ErrorOr<ReportFilter>> filters = historicalReport.MainFilters
            .Select(filter => ReportFilter.Create(
                siteId: filter.SiteId,
                blockId: filter.BlockId,
                unitId: filter.UnitId,
                filter: new(filter.Filter),
                ruleGroup: filter.RuleGroup))
            .ToList();

        // Create the time grouping
        ErrorOr<ReportTimeGrouping> timeGrouping = ReportTimeGrouping
            .Create(
                historicalReport.GroupingInterval,
                historicalReport.GroupingAggregates);

        // Create the layout for either historical or accuracy report
        ErrorOr<ReportLayout> reportLayout = historicalReport.ReportType switch
        {
            ReportType.Historical => ReportLayout.CreateHistoricalLayout(ReportType.Historical, historicalReport.ReportLayout),
            ReportType.Accuracy => ReportLayout.CreateAccuracyLayout(ReportType.Accuracy, historicalReport.ReportLayout),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };

        // Make sure everything is valid
        ErrorOr<Success> results = ErrorOrExt
            .Combine(filters)
            .Then(_ => ErrorOrExt.Combine(columns))
            .Then(_ => ErrorOrExt.Combine(schedule, timeGrouping, reportLayout));
        if (results.IsError)
            return results.Errors;

        // Create the relative date range for forecast report
        return historicalReport.DateRangeInfo switch
        {
            ReportFixedDateRangeCommand fixedDateRange
                => FixedReportDateRange
            .Create(
                fixedDateRange.StartDate,
                fixedDateRange.EndDate)
            .Else(fixedDateRangeErrors => fixedDateRangeErrors)
            .Then(fixedDateRange =>
                Report.CreateHistoricalOrAccuracyReport(
                    reportId: historicalReport.ReportId,
                    name: historicalReport.Name,
                    description: historicalReport.Description,
                    reportType: historicalReport.ReportType,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    container: historicalReport.Container,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(historicalReport.FileFormat),
                    userGroupsAllowed: historicalReport.UserGroupsAllowed,
                    blocksInReport: historicalReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: requestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: requestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: fixedDateRange,
                    existingReports: existingReports.ToList())),
            ReportRelativeDateRangeCommand relativeDateRange
                => RelativeReportDateRange.Create(
                relativeDateRange.StartOffset,
                relativeDateRange.EndOffset)
            .Else(errors => errors)
            .Then(relativeDateRange =>
                Report.CreateHistoricalOrAccuracyReport(
                    reportId: historicalReport.ReportId,
                    name: historicalReport.Name,
                    description: historicalReport.Description,
                    reportType: historicalReport.ReportType,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    container: historicalReport.Container,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(historicalReport.FileFormat),
                    userGroupsAllowed: historicalReport.UserGroupsAllowed,
                    blocksInReport: historicalReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: requestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: requestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: relativeDateRange,
                    existingReports: existingReports.ToList())),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };
    }

    /// <summary>
    /// Create lookup report.
    /// </summary>
    public ErrorOr<Report> CreateLookupReport(
        LookupReportCommand lookupReport,
        IEnumerable<Report> existingReports,
        string requestedBy)
    {
        // Create the reporting schedule
        ErrorOr<ReportSchedule> schedule = ReportSchedule.Create(
            lookupReport.IsEnabled, lookupReport.ProcessInterval);

        // Create the filters 
        List<ErrorOr<ReportFilter>> filters = lookupReport.MainFilters
            .Select(filter => ReportFilter.Create(
                siteId: filter.SiteId,
                blockId: filter.BlockId,
                unitId: filter.UnitId,
                filter: new(filter.Filter),
                ruleGroup: filter.RuleGroup))
            .ToList();

        // Create the time grouping
        ErrorOr<ReportTimeGrouping> timeGrouping = ReportTimeGrouping
            .Create(
                lookupReport.GroupingInterval,
                new List<string>() { "Avg" });

        // Create the report columns
        List<ErrorOr<ReportColumn>> columns = lookupReport
            .ReportColumns
            .Select(col => ReportColumn.Create(
                reportColumnId: col.ReportColumnId,
                siteId: col.SiteId,
                blockId: col.BlockId,
                unitId: col.UnitId,
                name: col.Name,
                sources: col.Sources,
                tag: col.Tag,
                calculation: new(col.Calculation),
                filter: new(col.Filter),
                position: col.Position,
                noOfDecimals: col.NoOfDecimals,
                engUnits: col.EngUnits,
                color: col.Color))
            .ToList();

        // Create the variable grouping
        ErrorOr<ReportVariableGrouping> variableGrouping = VariableGroup
            .Create(
                tag: lookupReport.VariableGrouping.Row.Tag,
                name: lookupReport.VariableGrouping.Row.Name,
                min: lookupReport.VariableGrouping.Row.Min,
                max: lookupReport.VariableGrouping.Row.Max,
                interval: lookupReport.VariableGrouping.Row.Interval)
            .Then(row => VariableGroup
                .Create(
                    tag: lookupReport.VariableGrouping.Column.Tag,
                    name: lookupReport.VariableGrouping.Column.Name,
                    min: lookupReport.VariableGrouping.Column.Min,
                    max: lookupReport.VariableGrouping.Column.Max,
                    interval: lookupReport.VariableGrouping.Column.Interval)
                .Then(column => ReportVariableGrouping
                    .Create(
                        source: lookupReport.VariableGrouping.Source,
                        row: row,
                        column: column)));

        // Create the report layout for lookup report
        ErrorOr<ReportLayout> reportLayout = ReportLayout
            .CreateLookupLayout(ReportType.Lookup, lookupReport.ReportLayout);

        // Make sure everything is valid
        ErrorOr<Success> results = ErrorOrExt
            .Combine(filters)
            .Then(_ => ErrorOrExt.Combine(columns))
            .Then(_ => ErrorOrExt.Combine(schedule, timeGrouping, variableGrouping, reportLayout));
        if (results.IsError)
            return results.Errors;

        // Create the relative date range for forecast report
        return lookupReport.DateRangeInfo switch
        {
            ReportFixedDateRangeCommand fixedDateRange
                => FixedReportDateRange
            .Create(
                fixedDateRange.StartDate,
                fixedDateRange.EndDate)
            .Else(fixedDateRangeErrors => fixedDateRangeErrors)
            .Then(fixedDateRange =>
                Report.CreateLookupReport(
                    reportId: lookupReport.ReportId,
                    name: lookupReport.Name,
                    description: lookupReport.Description,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    variableGrouping: variableGrouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    container: lookupReport.Container,
                    userGroupsAllowed: lookupReport.UserGroupsAllowed,
                    blocksInReport: lookupReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: requestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: requestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: fixedDateRange,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(lookupReport.FileFormat),
                    existingReports: existingReports.ToList())
            ),
            ReportRelativeDateRangeCommand relativeDateRange
                => RelativeReportDateRange.Create(
                relativeDateRange.StartOffset,
                relativeDateRange.EndOffset)
            .Else(errors => errors)
            .Then(relativeDateRange =>
                Report.CreateLookupReport(
                    reportId: lookupReport.ReportId,
                    name: lookupReport.Name,
                    description: lookupReport.Description,
                    filters: filters.Select(f => f.Value).ToList(),
                    timeGrouping: timeGrouping.Value,
                    variableGrouping: variableGrouping.Value,
                    columns: columns.Select(c => c.Value).ToList(),
                    schedule: schedule.Value,
                    container: lookupReport.Container,
                    userGroupsAllowed: lookupReport.UserGroupsAllowed,
                    blocksInReport: lookupReport.BlocksInReport,
                    auditInfo: new AuditInfo(
                        CreatedBy: requestedBy,
                        DateCreated: _dateTimeProvider.UtcNow,
                        ModifiedBy: requestedBy,
                        DateModified: _dateTimeProvider.UtcNow),
                    dateRangeInfo: relativeDateRange,
                    reportLayout: reportLayout.Value,
                    fileFormat: Enum.Parse<ReportFileFormat>(lookupReport.FileFormat),
                    existingReports: existingReports.ToList())),
            _ => throw new InvalidOperationException("Invalid historical report type")
        };
    }
}
