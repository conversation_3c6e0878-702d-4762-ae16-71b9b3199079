using Azure.Storage.Blobs;
using Azure.Storage.Files.DataLake;
using Azure.Storage.Sas;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Reporting;

public record GetReportContentQuery(Guid ReportId) : IQuery<Response>;

public record Response(
    string Uri,
    string FileName,
    string FileFormat,
    string LastModified,
    string UriExpiration);

public sealed class GetReportContentQueryHandler(
    IOptions<BlobStorageOptions> blobStorageOptions,
    DataContext dataContext,
    IMemoryCache memoryCache,
    ILogger<GetReportContentQueryHandler> log) : IQueryHandler<GetReportContentQuery, Response>
{
    private readonly DataContext _dataContext = dataContext;
    private readonly IOptions<BlobStorageOptions> _blobStorageOptions = blobStorageOptions;
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly ILogger<GetReportContentQueryHandler> _logger = log;

    public async Task<ErrorOr<Response>> Handle(
        GetReportContentQuery request,
        CancellationToken ct = default)
    {
        // Find the report
        Report? report = await _dataContext.Reports
            .AsNoTracking()
            .Include(x => x.DateRangeInfo)
            .Include(x => x.ReportColumns)
            .FirstOrDefaultAsync(x => x.Id == request.ReportId, ct);

        if (report is null)
            return ReportErrors.NotFound;

        if (string.IsNullOrEmpty(report.Container))
            return BlobStorageErrors.ContainerNotFound;

        try
        {
            // Create blob service client to check if container exists
            BlobServiceClient blobServiceClient = new(_blobStorageOptions.Value.ConnectionString);
            BlobContainerClient container = blobServiceClient.GetBlobContainerClient(report.Container);

            if (!container.Exists(ct))
                return BlobStorageErrors.ContainerNotFound;

            // Create file system client
            DataLakeFileSystemClient fileSystemClient = new(
                _blobStorageOptions.Value.ConnectionString, report.Container);

            // Get the file path
            string filePath = report.FileFormat switch
            {
                ReportFileFormat.Excel => $"reports/{report.Name}.xlsx",
                ReportFileFormat.CSV => $"reports/{report.Name}.csv",
                _ => throw new ArgumentException($"Invalid file format: {report.FileFormat}")
            };
            DataLakeFileClient fileClient = fileSystemClient.GetFileClient(filePath);

            // Check if file exists
            if (!fileClient.Exists(ct))
            {
                return BlobStorageErrors.BlobNotFound;
            }

            // Check the cache for the report
            return await _memoryCache.GetOrCreateAsync<ErrorOr<Response>>(
                $"report-content-{report.Id}",
                async entry =>
            {
                // Cache the report content for 30 minutes
                entry.SetAbsoluteExpiration(TimeSpan.FromMinutes(30));

                if (!fileClient.CanGenerateSasUri)
                {
                    _logger.LogError("FileClient must be authorized with Shared Key credentials to create a service SAS.");
                    return BlobStorageErrors.DownloadFailed;
                }

                // Get file properties
                var properties = await fileClient.GetPropertiesAsync(cancellationToken: ct);

                // Create SAS token valid for 30 minutes
                DataLakeSasBuilder sasBuilder = new()
                {
                    FileSystemName = fileSystemClient.Name,
                    Resource = "f", // 'f' for file
                    Path = filePath,
                    ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(30)
                };

                // Set read-only permissions
                sasBuilder.SetPermissions(DataLakeSasPermissions.Read);

                // Generate the SAS URI
                Uri sasUri = fileClient.GenerateSasUri(sasBuilder);

                return new Response(
                    Uri: sasUri.ToString(),
                    FileName: fileClient.Name,
                    FileFormat: report.FileFormat switch
                    {
                        ReportFileFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        ReportFileFormat.CSV => "text/csv",
                        _ => throw new ArgumentException($"Invalid file format: {report.FileFormat}")
                    },
                    LastModified: properties.Value.LastModified.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    UriExpiration: sasBuilder.ExpiresOn.ToString("yyyy-MM-ddTHH:mm:ssZ"));
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get report {reportName}", report.Name);
            return BlobStorageErrors.DownloadFailed;
        }
    }
}
