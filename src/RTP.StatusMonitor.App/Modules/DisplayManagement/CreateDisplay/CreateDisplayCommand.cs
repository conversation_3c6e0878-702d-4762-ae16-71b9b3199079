using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.CreateDisplay;

public record CreateDisplayCommand : IQuery<CreatedDisplayResponse>
{
    // NOTE - list of groups the user belongs to
    public List<Guid> UserGroups { get; set; } = null!;
    public List<string> AppRoles { get; set; } = null!;
    public List<Guid> GroupsToShare { get; set; } = null!;
    public Guid? GroupToSetDefaultDisplay { get; set; } = null;
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Layout { get; set; } = string.Empty;
    public string DisplayComponent { get; set; } = string.Empty;
}


