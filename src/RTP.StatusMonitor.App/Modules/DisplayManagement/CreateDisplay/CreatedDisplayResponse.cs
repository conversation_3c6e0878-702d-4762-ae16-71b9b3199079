namespace RTP.StatusMonitor.App.Modules.DisplayManagement.CreateDisplay;

public record CreatedDisplayResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Layout { get; set; } = null!;
    public string DisplayComponent { get; set; } = null!;
    public List<CreatedGroupViewPermissionDto> GroupViewPermissions { get; set; } = null!;
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
}

public record CreatedGroupViewPermissionDto
{
    public Guid UserGroupId { get; set; }
    public bool IsDefault { get; set; }
}
