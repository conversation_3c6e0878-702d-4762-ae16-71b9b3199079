using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Domain.Shared.Constants;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.CreateDisplay;

public class CreateDisplayHandler(IDataContext context)
    : IQueryHandler<CreateDisplayCommand, CreatedDisplayResponse>
{
    private readonly IDataContext _context = context;

    public async Task<ErrorOr<CreatedDisplayResponse>> Handle(CreateDisplayCommand request, CancellationToken cancellationToken)
    {
        // Given the request to create a display to share with other groups

        // First check that user is allowed to share the display with all the groups they want to share it with
        // NOTE - only RTP user group (admin roles) can create and share display with others for now
        // NOTE - other groups can only create display for themselves with no option of sharing
        bool isAuthorizedToShare = request.AppRoles.Contains(AppRole.Admin);

        // When the user is not allowed to share the display 
        // But they try to share it with other groups
        // Then return an unauthorized error
        if (isAuthorizedToShare is false && request.GroupsToShare.Count > 0)
            return DisplayErrors.ShareForbidden;

        // When the display has the same name as an existing display
        // Then return an error
        var isNameExists = await _context.Displays
            .AnyAsync(d => d.Name == request.Name, cancellationToken);
        if (isNameExists)
            return DisplayErrors.NameAlreadyExists;

        // Then we should find all the groups that would have permission to view this display
        var groupsWithViewPermissionsId = request.UserGroups.Concat(request.GroupsToShare).Distinct().ToList();

        // When the user wants to set the default display for a group
        // Then we need to verify that the group has permission to view this display
        if (request.GroupToSetDefaultDisplay is not null && groupsWithViewPermissionsId.Contains(request.GroupToSetDefaultDisplay.Value) is false)
        {
            return DisplayErrors.SetDefaultForbidden;
        }

        // When the group we want to set default display for already has a default display
        // Then return an error
        if (request.GroupToSetDefaultDisplay.HasValue)
        {
            var defaultDisplaysForGroups = await _context.Displays
                .Where(
                    d => d.DisplayViewPermissions
                    .Any(dvp => request.GroupToSetDefaultDisplay == dvp.UserGroupId && dvp.IsDefault))
                .ToListAsync(cancellationToken);
            if (defaultDisplaysForGroups.Any())
            {
                return DisplayErrors.DefaultDisplayAlreadyExists;
            }
        }

        using var transaction = await _context.BeginTransactionAsync(cancellationToken);

        try
        {
            // Then create the display for the groups
            var newDisplay = new Display
            {
                Name = request.Name,
                Alias = request.Alias,
                Description = request.Description,
                Layout = request.Layout,
                DisplayComponent = request.DisplayComponent,
            };

            // First we query all the groups that have permission to view the display
            var groupsWithViewPermissions = await _context.UserGroups
                .Where(gp => groupsWithViewPermissionsId.Contains(gp.UserGroupId))
                .Include(gp => gp.DisplayViewPermissions)
                .ToListAsync(cancellationToken);

            // Then we add the view permission for these group
            foreach (var group in groupsWithViewPermissions)
            {
                // Check if the group is the group to set default display for
                bool isDefault = request.GroupToSetDefaultDisplay.HasValue &&
                  request.GroupToSetDefaultDisplay.Value == group.UserGroupId;

                // Add the permission to the display
                group.AddPermissionToDisplay(
                  newDisplay, isDefault);
            }

            // Then we add the display to the database
            await _context.Displays.AddAsync(newDisplay, cancellationToken);

            // And save the changes to the database
            await _context.SaveChangesAsync(cancellationToken);

            // When all changes are saved to the database
            // Then commit the transaction
            await transaction.CommitAsync();

            // Then we need to query the newly created display and its view permissions to return to the client

            return new CreatedDisplayResponse
            {
                Id = newDisplay.Id,
                Name = newDisplay.Name,
                Alias = newDisplay.Alias,
                Description = newDisplay.Description,
                Layout = newDisplay.Layout,
                DisplayComponent = newDisplay.DisplayComponent,
                GroupViewPermissions = groupsWithViewPermissions.Select(gp => new CreatedGroupViewPermissionDto
                {
                    UserGroupId = gp.UserGroupId,
                    IsDefault = gp.DisplayViewPermissions.FirstOrDefault(dvp => dvp.DisplayId == newDisplay.Id)?.IsDefault ?? false,
                }).ToList(),
            };
        }
        catch
        {
            await transaction.RollbackAsync();
            return DisplayErrors.UnexpectedError;
        }
    }
}
