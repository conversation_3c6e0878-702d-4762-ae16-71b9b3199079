using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.DisplayManagement.GetDisplays.Dto;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.GetDisplays;

public class GetDisplayHandler(IDataContext context)
    : IQueryHandler<GetDisplaysQuery, List<DisplayDto>>
{
    private readonly IDataContext _context = context;
    public async Task<ErrorOr<List<DisplayDto>>> Handle(
        GetDisplaysQuery request,
        CancellationToken cancellationToken)
    {
        // Query all the displays for the groups
        List<Domain.Entities.Display> displays = await _context.Displays
            .AsNoTracking()
            .Include(d => d.DisplayViewPermissions)
            .Where(
                d => d.DisplayViewPermissions.Any(
                    dvp => request.UserGroups.Contains(dvp.UserGroupId)))
            .ToListAsync(cancellationToken);

        // Then map the displays to the display dto
        return displays.Select(d => new DisplayDto
        {
            Id = d.Id,
            Name = d.Name,
            Alias = d.Alias,
            Description = d.Description,
            Layout = d.Layout,
            DisplayComponent = d.DisplayComponent,
            DateCreated = d.DateCreated,
            DateModified = d.DateModified,
            GroupViewPermissions = d.DisplayViewPermissions
                .Select(dvp => new GroupViewPermissionDto
                {
                    UserGroupId = dvp.UserGroupId,
                    IsDefault = dvp.IsDefault
                })
                .ToList()
        }).OrderBy(d => d.Name).ToList();
    }
}
