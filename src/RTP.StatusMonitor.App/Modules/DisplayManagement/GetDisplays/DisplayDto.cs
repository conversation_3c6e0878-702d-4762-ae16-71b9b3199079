namespace RTP.StatusMonitor.App.Modules.DisplayManagement.GetDisplays.Dto;

public record DisplayDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Layout { get; set; } = null!;
    public string DisplayComponent { get; set; } = null!;
    public List<GroupViewPermissionDto> GroupViewPermissions { get; set; } = null!;
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
}

public record GroupViewPermissionDto
{
    public Guid UserGroupId { get; set; }
    public bool IsDefault { get; set; }
}
