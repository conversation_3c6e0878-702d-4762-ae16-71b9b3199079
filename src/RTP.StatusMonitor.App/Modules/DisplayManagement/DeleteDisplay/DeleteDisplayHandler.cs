using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Entities;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.DeleteDisplay;

public class DeleteDisplayHandler(IDataContext context) : ICommandHandler<DeleteDisplayCommand>
{
    private readonly IDataContext _dataContext = context;

    public async Task<ErrorOr<Unit>> Handle(DeleteDisplayCommand request, CancellationToken ct)
    {
        // First we need to find the display to delete from db
        Display? display = await _dataContext.Displays
            .Include(d => d.DisplayViewPermissions)
            .FirstOrDefaultAsync(d => d.Id == request.DisplayId, ct);

        // When the display is not found
        // Then return a not found error
        if (display == null)
            return DisplayErrors.NotFound;

        // TODO - need to refactor access control
        // First we need to check if the user has permission to delete the display
        bool isAuthorized = display.DisplayViewPermissions
          .Any(dvp => request.UserGroups.Contains(dvp.UserGroupId));

        // When the user is not authorized to delete the display
        // Then return an unauthorized error
        if (!isAuthorized)
            return DisplayErrors.Unauthorized;

        // Then we remove the display from db
        _dataContext.Displays.Remove(display);

        // Then we save the changes to db
        var isSuccess = await _dataContext.SaveChangesAsync(ct) > 0;
        if (!isSuccess)
            return DisplayErrors.UnexpectedError;

        return Unit.Value;
    }
}
