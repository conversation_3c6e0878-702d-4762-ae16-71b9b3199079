using ErrorOr;
using MediatR;
using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.UpdateDisplay;

public record UpdateDisplayCommand : IQuery<UpdatedDisplayResponse>
{
    public Guid DisplayId { get; set; }
    public List<Guid> UserGroups { get; set; } = null!;
    public List<Guid> GroupsToShare { get; set; } = null!;
    public Guid? GroupToSetDefaultDisplay { get; set; } = null;
    public List<string> AppRoles { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Alias { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Layout { get; set; } = string.Empty;
    public string DisplayComponent { get; set; } = string.Empty;
}
