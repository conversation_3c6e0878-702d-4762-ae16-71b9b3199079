using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.DisplayManagement.UpdateDisplay;

public class UpdateDisplayHandler(IDataContext context) : IQueryHandler<UpdateDisplayCommand, UpdatedDisplayResponse>
{
    private readonly IDataContext _context = context;

    public async Task<ErrorOr<UpdatedDisplayResponse>> Handle(UpdateDisplayCommand request, CancellationToken cancellationToken)
    {
        // Give the request to create a view display for the groups

        // First check user has permission to set the display for all the groups they want to set it for

        // First check that user is allowed to share the display with all the groups they want to share it with
        // NOTE - only RTP user group (admin roles) can create and share display with others for now
        // NOTE - other groups can only create display for themselves with no option of sharing
        bool isAuthorizedToShare = request.AppRoles.Contains("Admin.Write");

        // When the user is not allowed to share the display 
        // But they try to share it with other groups
        // Then return an unauthorized error
        if (isAuthorizedToShare is false && request.GroupsToShare.Count > 0)
            return DisplayErrors.ShareForbidden;


        // Then we need to find the display to update from db
        Domain.Entities.Display? displayToUpdate = await _context.Displays
            .Include(d => d.DisplayViewPermissions)
            .FirstOrDefaultAsync(d => d.Id == request.DisplayId, cancellationToken);

        // When the display is not found
        // Then return a not found error
        if (displayToUpdate is null)
            return DisplayErrors.NotFound;

        // Before we update the display view permission
        // We need to remove all the old permissions
        displayToUpdate.DisplayViewPermissions.Clear();

        // First we should find all the groups that have permission to view the display
        var groupsWithViewPermissionId = request.UserGroups.Concat(request.GroupsToShare).ToList();

        // When the user wants to set the display as default for a group
        // Then we need to verify that the group is among the groups that have view permission
        if (request.GroupToSetDefaultDisplay is not null && groupsWithViewPermissionId.Contains(request.GroupToSetDefaultDisplay.Value) is false)
        {
            return DisplayErrors.SetDefaultForbidden;
        }

        // Then we query all the groups that have permission to view the display
        var groupsWithViewPermission = await _context.UserGroups
            .Where(gp => groupsWithViewPermissionId.Contains(gp.UserGroupId))
            .Include(gp => gp.DisplayViewPermissions)
            .ToListAsync(cancellationToken);

        // When we want to update the default display for a group
        // Then we need to remove the default flag from that group
        if (request.GroupToSetDefaultDisplay is not null)
        {
            var groupToSetDefaultDisplay = groupsWithViewPermission.FirstOrDefault(gp => gp.UserGroupId == request.GroupToSetDefaultDisplay.Value);
            if (groupToSetDefaultDisplay is not null)
            {
                foreach (var dvp in groupToSetDefaultDisplay.DisplayViewPermissions)
                {
                    dvp.IsDefault = false;
                }
            }
        }

        // Then we add the view permission for these group
        // And set the default flag if the user wants to set the display as default
        foreach (var group in groupsWithViewPermission)
        {

            // Check if the group is the group to set default display for
            bool isDefault = request.GroupToSetDefaultDisplay.HasValue && request.GroupToSetDefaultDisplay.Value == group.UserGroupId;

            // Add the permission to the display
            group.AddPermissionToDisplay(
              display: displayToUpdate,
              isDefault: isDefault);
        }

        // Then we update the display with the new values if they are not null
        displayToUpdate.Name = request.Name ?? displayToUpdate.Name;
        displayToUpdate.Alias = request.Alias ?? displayToUpdate.Alias;
        displayToUpdate.Description = request.Description ?? displayToUpdate.Description;
        displayToUpdate.Layout = request.Layout ?? displayToUpdate.Layout;
        displayToUpdate.DisplayComponent = request.DisplayComponent ?? displayToUpdate.DisplayComponent;


        // And save the changes to the database
        bool isSuccess = await _context.SaveChangesAsync(cancellationToken) > 0;
        if (!isSuccess)
            return DisplayErrors.UnexpectedError;

        // return MediatR.Unit.Value;
        return new UpdatedDisplayResponse
        {
            Id = displayToUpdate.Id,
            Name = displayToUpdate.Name,
            Alias = displayToUpdate.Alias,
            Description = displayToUpdate.Description,
            Layout = displayToUpdate.Layout,
            DisplayComponent = displayToUpdate.DisplayComponent,
            GroupViewPermissions = groupsWithViewPermission.Select(gp => new UpdatedGroupViewPermissionDto
            {
                UserGroupId = gp.UserGroupId,
                IsDefault = gp.DisplayViewPermissions.FirstOrDefault(dvp => dvp.DisplayId == displayToUpdate.Id)?.IsDefault ?? false,
            }).ToList()
        };
    }
}
