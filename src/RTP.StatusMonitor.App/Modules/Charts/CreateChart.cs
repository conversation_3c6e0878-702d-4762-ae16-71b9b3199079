using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Chart;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Charts;

public record CreateChartCommand(
    List<string> AppRoles,
    Guid SiteId,
    List<ChartCommand> Charts) : ICommand;

public record ChartCommand(
    Guid Id,
    string Name,
    string Notes,
    bool IsDefault,
    string Type,
    object[] ChartStatuses,
    object[] ChartAreas
);
internal sealed class CreateChartCommandHandler(IDataContext dataContext)
        : ICommandHandler<CreateChartCommand>
{
    private readonly IDataContext _dataContext = dataContext;

    public async Task<ErrorOr<Unit>> Handle(
        CreateChartCommand request,
        CancellationToken ct)
    {
        // Get site data 
        Site? site = await _dataContext
            .Sites
            .Where(s => s.Id == request.SiteId)
            .Include(s => s.Charts)
            .Include(s => s.GroupPermissions)
            .FirstOrDefaultAsync(ct);

        if (site is null)
            return SiteErrors.NotFound;

        JsonSerializerSettings jsonSettings = new()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
        };

        // Attempt to save the charts to the site
        List<Chart> charts = [];
        foreach (ChartCommand item in request.Charts)
        {
            ErrorOr<Chart> chart = site.SaveChart(
                appRoles: request.AppRoles,
                id: item.Id,
                name: item.Name,
                notes: item.Notes,
                isDefault: item.IsDefault,
                type: item.Type,
                chartStatuses: JsonConvert
                    .SerializeObject(item.ChartStatuses, jsonSettings),
                chartAreas: JsonConvert
                    .SerializeObject(item.ChartAreas, jsonSettings));

            if (chart.IsError)
                return chart.Errors;

            charts.Add(chart.Value);

            // Make sure there can only be one default chart
            if (charts.Count(chart => chart.IsDefault) > 1)
            {
                return ChartErrors.DefaultChartConflict;
            }
        }

        // Remove all charts of the site and replace them with the new ones
        _dataContext.Charts.RemoveRange(site.Charts);

        _dataContext.Charts.AddRange(charts);

        await _dataContext.SaveChangesAsync(ct);

        return Unit.Value;
    }
}
