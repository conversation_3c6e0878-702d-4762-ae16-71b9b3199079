using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Charts;
public record GetChartsQuery(
    List<Guid> UserGroupsId,
    Guid SiteId) : IQuery<List<ChartsResponse>>;

public record ChartsResponse(
    Guid Id,
    string Name,
    string Notes,
    bool IsDefault,
    string Type,
    object[] ChartStatuses,
    object[] ChartAreas);

internal sealed class GetChartsQueryHandler(
    IDataContext dataContext,
    IAuthorizationService authorizationService)
        : IQueryHandler<GetChartsQuery, List<ChartsResponse>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly IAuthorizationService _authorizationService = authorizationService;

    public async Task<ErrorOr<List<ChartsResponse>>> Handle(
        GetChartsQuery request,
        CancellationToken ct)
    {
        // Check if the user is authorized to access the site
        bool isAuthorized = await _authorizationService.CheckUserAccessByGroup(
            userGroupsId: request.UserGroupsId,
            siteId: request.SiteId);

        if (isAuthorized is false)
            return SiteDomainErrors.Unauthorized;

        // Get site data
        var siteWithCharts = await _dataContext
            .Sites
            .Include(s => s.Charts)
            .FirstOrDefaultAsync(s => s.Id == request.SiteId, ct);

        if (siteWithCharts is null)
            return SiteDomainErrors.NotFound;

        // Otherwise, return the charts
        return siteWithCharts.Charts
            .Select(c => new ChartsResponse(
                Id: c.Id,
                Name: c.Name,
                Notes: c.Notes,
                IsDefault: c.IsDefault,
                Type: c.Type,
                ChartStatuses: JsonConvert.DeserializeObject<object[]>(c.ChartStatuses) ?? Array.Empty<object>(),
                ChartAreas: JsonConvert.DeserializeObject<object[]>(c.ChartAreas) ?? Array.Empty<object>()))
            .OrderBy(c => c.Name)
            .ToList();
    }
}


// REFERENCE
// using ErrorOr;
// using Microsoft.EntityFrameworkCore;
// using Newtonsoft.Json;
// using RTP.StatusMonitor.App.Shared.Authorization;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.Domain.Site;
// using RTP.StatusMonitor.Persistence;

// namespace RTP.StatusMonitor.App.Charts.GetCharts;

// internal sealed class GetChartsQueryHandler
//     : IQueryHandler<GetChartsQuery, List<ChartsResponse>>
// {
//     private readonly IDataContext _dataContext;
//     private readonly IAuthorizationService _authorizationService;

//     public GetChartsQueryHandler(
//         IDataContext dataContext,
//         IAuthorizationService authorizationService)
//     {
//         _dataContext = dataContext;
//         _authorizationService = authorizationService;
//     }

//     public async Task<ErrorOr<List<ChartsResponse>>> Handle(
//         GetChartsQuery request,
//         CancellationToken ct)
//     {
//         // Check if the user is authorized to access the site
//         bool isAuthorized = await _authorizationService.CheckUserAccessByGroup(
//             userGroupsId: request.UserGroupsId,
//             siteId: request.SiteId);

//         if (isAuthorized is false)
//         {
//             return SiteDomainErrors.Unauthorized;
//         }

//         // Get site data
//         var siteWithCharts = await _dataContext
//             .Sites
//             .Include(s => s.Charts)
//             .FirstOrDefaultAsync(s => s.Id == request.SiteId, ct);

//         if (siteWithCharts is null)
//         {
//             return SiteDomainErrors.NotFound;
//         }


//         // Otherwise, return the charts
//         return siteWithCharts.Charts
//             .Select(c => new ChartsResponse(
//                 Id: c.Id,
//                 Name: c.Name,
//                 Notes: c.Notes,
//                 Type: c.Type,
//                 IsDefault: c.IsDefault,
//                 ChartStatuses: MapToChartStatusResponse(c.ChartStatuses),
//                 ChartAreas: MapToChartAreaResponse(c.ChartAreas)))
//             .OrderBy(c => c.Name)
//             .ToList();
//     }

//     private List<ChartStatusResponse> MapToChartStatusResponse(string chartStatuses)
//     {
//         List<ChartStatusResponse>? chartStatusesObject = JsonConvert.DeserializeObject<List<ChartStatusResponse>>(chartStatuses);

//         if (chartStatusesObject is null)
//         {
//             return new List<ChartStatusResponse>();
//         }

//         return chartStatusesObject;
//     }

//     private List<ChartAreaResponse> MapToChartAreaResponse(string chartAreas)
//     {
//         List<ChartAreaResponse>? chartAreasObject = JsonConvert.DeserializeObject<List<ChartAreaResponse>>(chartAreas);

//         if (chartAreasObject is null)
//         {
//             return new List<ChartAreaResponse>();
//         }

//         return chartAreasObject;
//     }
// }
