using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Analytics;
public record AnalyticsSeries(
    Guid Id,
    string Name,
    ComputedExpression Calculation,
    FilterExpression Filter);

public record GetAnalyticsQuery(
    List<AnalyticsSeries> Series,
    DateTime StartTime,
    DateTime EndTime,
    TimeSeriesResamplingInterval Interval)
    : IAuthorizedQuery<List<AnalyticsResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? BlockId { get; set; }

    // NOTE - analytics at block level
    public Guid? SiteId { get; set; }
    public Guid? UnitId { get; set; }
}

public record AnalyticsResponse(
    Guid Id,
    string Name,
    List<double> Values,
    List<string> Timestamps);

internal sealed class GetAnalyticsQueryHandler(
    IDataContext dataContext,
    ForecastRepository forecastService,
    DataClientReadRepository dataClientDataService)
        : IQueryHandler<GetAnalyticsQuery, List<AnalyticsResponse>>
{
    private readonly IDataContext _dataContext = dataContext;
    private readonly ForecastRepository _forecastRepository = forecastService;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientDataService;
    readonly string ACTUAL_SOURCE_PREFIX = "ACT";
    readonly string DA_SOURCE_PREFIX = "DA";
    readonly string RT_SOURCE_PREFIX = "RT";

    public async Task<ErrorOr<List<AnalyticsResponse>>> Handle(
        GetAnalyticsQuery request,
        CancellationToken ct = default)
    {
        // Get block data from db
        Block block = await _dataContext.Blocks
            .Where(b => b.Id == request.BlockId)
            .Include(b => b.Site)
                .ThenInclude(s => s.Customer)
            .SingleAsync(ct);

        // Parse and group the filter tag by their prefix (ACT/DA/RT)
        Dictionary<string, List<string>> tagsInFilter = request.Series
            .SelectMany(s => ExpressionParser.ParseWithPrefix(s.Filter))
            .GroupBy(x => x.Key, x => x.Value)
            .ToDictionary(x => x.Key, x => x.SelectMany(y => y).Distinct().ToList());

        // Parse and group the calculation tag by their prefix (ACT/DA/RT)
        Dictionary<string, List<string>> tagsInCalculation = request.Series
            .SelectMany(s => ExpressionParser.ParseWithPrefix(s.Calculation))
            .GroupBy(x => x.Key, x => x.Value)
            .ToDictionary(x => x.Key, x => x.SelectMany(y => y).Distinct().ToList());

        // Combine the tags from filter and calculation and group them by their prefix
        Dictionary<string, List<string>> tagsGroupedByPrefix = tagsInFilter
            .Concat(tagsInCalculation)
            .GroupBy(x => x.Key, x => x.Value)
            .ToDictionary(x => x.Key, x => x.SelectMany(y => y).Distinct().ToList());

        // Query data for each source in parallel (if there is any variable for that source)
        List<TimeSeriesData> actualData = [];
        List<TimeSeriesData> dayAheadData = [];
        List<TimeSeriesData> realTimeData = [];

        List<Task> tasks =
        [
            Task.Run(async () =>
            {
                // Otherwise, query data client for actual data
                List<TimeSeriesData> historicalDataClient = [];
                if (request.Interval == TimeSeriesResamplingInterval.Hour)
                {
                    // Get the statistical data from data client
                    List<StatisticsDataClientDto> results = await _dataClientReadRepository
                        .GetHistoricalHourlyDataAsync(
                            block: block,
                            uniqueTags: tagsGroupedByPrefix
                                .GetValueOrDefault(ACTUAL_SOURCE_PREFIX, []),
                            startDate: request.StartTime,
                            endDate: request.EndTime,
                            ct: ct);

                    // Transform to time series data for analytics
                    historicalDataClient = [..results.ToTimeSeriesData()];
                }
                else
                {
                    historicalDataClient = await _dataClientReadRepository
                        .GetHistoricalDataAsync(
                            block: block,
                            uniqueVariables: tagsGroupedByPrefix
                                .GetValueOrDefault(ACTUAL_SOURCE_PREFIX, []),
                            startTime: request.StartTime,
                            endTime: request.EndTime,
                            ct: ct);
                }

                // Prefix the tag with ACT- to indicate that this is actual data
                // and downsampling to minute data for actual
                actualData = [.. historicalDataClient
                    .Select(x => new TimeSeriesData(
                            Tag: $"{ACTUAL_SOURCE_PREFIX}-{x.Tag}",
                            Values: x.Values,
                            Timestamps: x.Timestamps)
                        .Downsample(
                            request.Interval, TimeSeriesDownsampling.Average))];
            }, ct),

            // Query day ahead data (if any requested)
            // and interpolate the data to nth minute interval for more frequent data points
            Task.Run(async () =>
            {
                List<ForecastDataDto> dayAheadForecast = await _forecastRepository.GetDayAheadForecastAsync(
                    block: block,
                    tags: tagsGroupedByPrefix.GetValueOrDefault(DA_SOURCE_PREFIX, []),
                    startDate: DateOnly.FromDateTime(request.StartTime),
                    // NOTE - this is a hack to get the next day's data
                    endDate: DateOnly.FromDateTime(request.EndTime).AddDays(1),
                    ct: ct);

                // Interpolate the forecast data to get more minute data points (forecast is hourly data)
                dayAheadData = [.. dayAheadForecast
                    .Select(x => new TimeSeriesData(
                            Tag: $"{DA_SOURCE_PREFIX}-{x.Tag}",
                            Values: [.. x.Values],
                            Timestamps: [.. x.Timestamps])
                        .LinearInterpolate(request.Interval))];
            }, ct),

            Task.Run(async () =>
            {
                // Get current date time on site
                SiteLocalTime siteLocalTime = SiteLocalTime.Create(block.Site, DateTime.UtcNow);

                // If the end time is in the past, then no need to query real time data
                if (siteLocalTime.Value > request.EndTime) return;

                // Get the real time forecast
                List<ForecastDataDto> realtimeForecast =  await _forecastRepository.GetLatestForecastAsync(
                    tags: tagsGroupedByPrefix.GetValueOrDefault(RT_SOURCE_PREFIX, []),
                    block: block,
                    ct: ct);

                // Interpolate the forecast data to get more frequent data points
                realTimeData = realtimeForecast
                    .ConvertAll(x => new TimeSeriesData(
                            Tag: $"{RT_SOURCE_PREFIX}-{x.Tag}",
                            Values: [.. x.Values],
                            Timestamps: [.. x.Timestamps])
                        .LinearInterpolate(request.Interval));
            }, ct)
        ];

        // Wait for all tasks to complete
        await Task.WhenAll(tasks);

        // Dump all data into data table for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable
            .Create([.. actualData, .. dayAheadData, .. realTimeData]);

        // Then iterate through each series 
        // And perform analytics on each series using the data from the 3 sources
        // Evaluate the expression for each chart series
        return request.Series
            .Select(x =>
            {
                // NOTE - Analytics series only has filter and calculation, no tag
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: string.Empty,
                    calculation: x.Calculation,
                    filter: x.Filter);

                return new AnalyticsResponse(
                    Id: x.Id,
                    Name: x.Name,
                    Values: [.. data.Values.Select(x => x.ToDoubleOrDefault())],
                    Timestamps: [.. data.Timestamps.Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))]);
            })
            .ToList();
    }
}
