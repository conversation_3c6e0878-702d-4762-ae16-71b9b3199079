using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;

/// <summary>
/// The result of a query to the table storage for airsonic historical data
/// </summary>
public record AirsonicHistoricalTableDto : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public DateTimeOffset? Timestamp { get; set; }
    public string Index { get; set; } = string.Empty;
    public string Tag { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string LocalTimestamp { get; set; } = string.Empty;
    public bool Quality { get; set; }
    public string EngUnits { get; set; } = string.Empty;
    public ETag ETag { get; set; }
}
