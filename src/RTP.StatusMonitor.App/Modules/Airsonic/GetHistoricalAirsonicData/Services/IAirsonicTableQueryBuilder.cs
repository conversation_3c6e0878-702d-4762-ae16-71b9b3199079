namespace RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData.Services;

public interface IAirsonicTableQueryBuilder
{
    public string GetSnapshotQuery(
      string siteName,
      string blockName,
      string unitName,
      List<string> tags);

    public List<List<string>> GetHistoricalQuery(
      string siteName,
      string blockName,
      string unitName,
      List<string> aliases,
      string filter,
      long startTime,
      long endTime,
      long rangeLength,
      int queryBatchSize);

    public string GetTableName(string siteName);
}