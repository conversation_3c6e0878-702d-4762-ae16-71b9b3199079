using System.Text;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData.Services;

public class AirsonicTableQueryBuilder : IAirsonicTableQueryBuilder
{
    /// <summary>
    /// Get the query for snapshot partition of airsonic
    /// </summary>
    public string GetSnapshotQuery(
        string siteName,
        string blockName,
        string unitName,
        List<string> tags
    )
    {
        Tuple<string, string, string> siteBlockUnit = DetermineSiteBlockUnit(
            siteName: siteName,
            blockName: blockName,
            unitName: unitName
        );

        siteName = siteBlockUnit.Item1;
        blockName = siteBlockUnit.Item2;
        unitName = siteBlockUnit.Item3;

        string query = $"PartitionKey eq '{siteName}-{blockName}-{unitName}-Snapshot'";

        // Add query conditional to filter by each tag
        if (tags.Count != 0)
        {
            List<string> rowKeyQueries = [];
            foreach (string tag in tags)
            {
                rowKeyQueries.Add($"RowKey eq '{EscapeODataString(tag)}'");
            }

            query += $" and ({string.Join(" or ", rowKeyQueries)})";
        }

        return query;
    }

    public List<List<string>> GetHistoricalQuery(
        string siteName,
        string blockName,
        string unitName,
        List<string> aliases,
        string filter,
        long startTime,
        long endTime,
        long rangeLength,
        int queryGroupSize
    )
    {
        Tuple<string, string, string> siteBlockUnit = DetermineSiteBlockUnit(
            siteName: siteName,
            blockName: blockName,
            unitName: unitName
        );

        siteName = siteBlockUnit.Item1;
        blockName = siteBlockUnit.Item2;
        unitName = siteBlockUnit.Item3;

        // Split the time range into smaller ranges
        List<Tuple<long, long>> timeRanges = SplitTimeRanges(startTime, endTime, rangeLength);

        List<string> queries = [];
        foreach (Tuple<long, long> i in timeRanges)
        {
            // Get the partition key range to filter
            StringBuilder query = new();
            string startPartition = $"{siteName}-{blockName}-{unitName}-{i.Item1}";
            string endPartition = $"{siteName}-{blockName}-{unitName}-{i.Item2}";
            query
                .Append("(PartitionKey gt '")
                .Append(startPartition)
                .Append("' and PartitionKey lt '")
                .Append(endPartition)
                .Append("' and (RowKey eq '")
                .Append(EscapeODataString(aliases[0].Replace("#", string.Empty)))
                .Append('\'');

            // Add query conditional to filter by each tag
            foreach (string tag in aliases.Skip(1))
            {
                query
                    .Append(" or RowKey eq '")
                    .Append(EscapeODataString(tag.Replace("#", string.Empty)))
                    .Append('\'');
            }
            query.Append("))");

            // Add additional filter if any
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query.Append(" and ").Append(filter);
            }

            // Add the time range query to the list
            queries.Add(query.ToString());
            query.Clear();
        }

        // Group the queries into batches
        List<List<string>> queryGroups = [];
        for (int i = 0; i < queries.Count; i += queryGroupSize)
        {
            queryGroups.Add([.. queries.Skip(i).Take(queryGroupSize)]);
        }

        return queryGroups;
    }

    /// <summary>
    /// Given a start time and end time and the desired length of each range
    /// Then we split the time range into smaller ranges
    /// With each range equal to the desired length
    /// Then we return the list of time ranges
    ///
    /// Example: [1625097600, 1625097600 + 86400, 86400] => [[1625097600, 1625184000], [1625184000, 1625270400], ...
    /// </summary>
    private static List<Tuple<long, long>> SplitTimeRanges(
        long startTime,
        long endTime,
        long rangeLength
    )
    {
        // Then we split the time range into {intervalRange} interval to get a list of time ranges
        List<Tuple<long, long>> timeRanges = new();
        while (startTime < endTime)
        {
            long unixNextTime = startTime + rangeLength;

            if (unixNextTime > endTime)
            {
                unixNextTime = endTime;
            }

            timeRanges.Add(new Tuple<long, long>(startTime, unixNextTime));

            startTime = unixNextTime;
        }

        // Then we return the list of time ranges
        return timeRanges;
    }

    private static Tuple<string, string, string> DetermineSiteBlockUnit(
        string siteName,
        string blockName,
        string unitName
    )
    {
        if (siteName == "GT24")
        {
            siteName = "Lake Road";
            blockName = "3";
            unitName = "1";
        }
        else if (siteName == "W501g")
        {
            siteName = "Ennis";
            blockName = "1";
            unitName = "1";
        }
        else if (siteName == "7FA")
        {
            siteName = "Vandolah";
            blockName = "4";
            unitName = "1";
        }

        return new Tuple<string, string, string>(siteName, blockName, unitName);
    }

    public string GetTableName(string siteName)
    {
        siteName = siteName.Replace(" ", string.Empty);

        return siteName switch
        {
            "7FA" => "airsonicVandolah",
            "GT24" => "airsonicLakeRoad",
            "W501g" => "airsonicEnnis",
            _ => $"airsonic{siteName}",
        };
    }

    /// <summary>
    /// Escapes single quotes in strings for OData queries used by Azure Table Storage.
    /// In OData, single quotes within string literals must be escaped by doubling them.
    /// </summary>
    /// <param name="value">The string value to escape</param>
    /// <returns>The escaped string safe for use in OData queries</returns>
    private static string EscapeODataString(string value) => value.Replace("'", "''");
}
