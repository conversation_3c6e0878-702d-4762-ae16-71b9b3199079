using System.Collections.Concurrent;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Persistence;
using Microsoft.Extensions.Caching.Memory;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData.Services;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;

public class GetHistoricalAirsonicHandler(
    IDataContext context,
    IAirsonicService airsonicService,
    IAirsonicTableQueryBuilder airsonicQueryBuilder,
    IMemoryCache memoryCache,
    IAuthorizationService authorizationService)
    : IRequestHandler<GetHistoricalAirsonicQuery, ErrorOr<IEnumerable<AirsonicHistoricalGroupResponse>>>
{
    private readonly IDataContext _context = context;
    private readonly IAuthorizationService _authorizationService = authorizationService;
    private readonly IAirsonicService _airsonicService = airsonicService;
    private readonly IAirsonicTableQueryBuilder _airsonicQueryBuilder = airsonicQueryBuilder;
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly TimeSpan CACHE_EXPIRATION = TimeSpan.FromMinutes(15);

    public async Task<ErrorOr<IEnumerable<AirsonicHistoricalGroupResponse>>> Handle(
    GetHistoricalAirsonicQuery request,
    CancellationToken ct)
    {
        // Then we find unit by id
        Domain.Units.Unit? unit = await _context.Units
      .Include(u => u.Block)
        .ThenInclude(b => b.Site)
          .ThenInclude(s => s.Customer)
      .FirstOrDefaultAsync(u => u.Id == request.UnitId, ct);

        // When the unit is not found
        // Then we return the error not found
        if (unit is null)
            return UnitErrors.NotFound;

        // Given the request for historical data with the unit id
        bool isAllowedAccess = await _authorizationService.CheckUserAccessByGroup(
          userGroupsId: request.UserGroups.ToList(),
          siteId: unit.Block.Site.Id,
          ct: ct);

        string siteName = unit.Block.Site.Name;
        string blockName = unit.Block.Name.ToLower();
        string unitName = unit.Name.ToLower();

        string cacheKey = $"{siteName}-{blockName}-{siteName}-{request.StartDate}-{request.EndDate}";
        List<AirsonicHistoricalGroupResponse> data = await _memoryCache
          .GetOrCreateAsync(
            cacheKey,
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = CACHE_EXPIRATION;

                return await GetDataFromTable(request, siteName, blockName, unitName, ct);
            });


        // When data is in the cached 
        // Then we return the cached data
        // When data is not in the cached
        // Then we have to query the data from table storage
        return data;
    }

    private async Task<List<AirsonicHistoricalGroupResponse>> GetDataFromTable(
      GetHistoricalAirsonicQuery request,
      string siteName,
      string blockName,
      string unitName,
      CancellationToken ct)
    {
        // Then we start building the query for the data
        const int QUERY_GROUP_SIZE = 8; // The number of queries to run in parallel
        const int QUERY_TIME_INTERVAL = 60 * 60 * 1000; // Each query contains 1 hour of data
        string tableName = "";
        switch (siteName.ToLower().Replace(" ", string.Empty))
        {
            case "7fa":
                tableName = "airsonicVandolah";
                siteName = "Vandolah";
                blockName = "4";
                unitName = "1";
                break;
            case "gt24":
                tableName = "airsonicLakeRoad";
                siteName = "Lake Road";
                blockName = "3";
                unitName = "1";
                break;
            case "w501g":
                tableName = "airsonicEnnis";
                siteName = "Ennis";
                blockName = "1";
                unitName = "1";
                break;
            default:
                tableName = $"airsonic{siteName.Replace(" ", string.Empty)}";
                break;
        }


        // First we build the queries
        List<List<string>> queryGroups = _airsonicQueryBuilder.GetHistoricalQuery(
          siteName,
          blockName,
          unitName,
          request.Aliases,
          request.Filter,
          request.StartDate,
          request.EndDate,
          QUERY_TIME_INTERVAL,
          QUERY_GROUP_SIZE);

        // Then we send the queries to table storage in batches
        ConcurrentDictionary<string, List<AirsonicHistoricalDataDto>> historicals = [];
        await Parallel.ForEachAsync(queryGroups, async (query, _) =>
          {
              // When we get the data for each batch
              List<AirsonicHistoricalTableDto> data = await _airsonicService
                .GetHistoricalAsync(tableName, query, request.SkipInterval, ct);

              // Categorize data by alias and add to the dictionary
              foreach (AirsonicHistoricalTableDto item in data)
              {
                  if (!historicals.ContainsKey(item.Alias))
                  {
                      historicals.TryAdd(item.Alias, []);
                  }

                  historicals[item.Alias].Add(new AirsonicHistoricalDataDto
                  {
                      Index = long.Parse(item.PartitionKey.Split('-').Last()),
                      Alias = item.Alias,
                      Value = item.Value,
                      Quality = item.Quality,
                      EngUnits = item.EngUnits
                  });
              }
          });

        // After we get the data for all batches
        // Then we synchronize the data by getting the common timestamps among all tags
        Dictionary<string, List<AirsonicHistoricalDataDto>> synchronizedHistoricals = SynchronizedHistoricalData(historicals);

        // Then we return the data by mapping the data to the dto
        return synchronizedHistoricals
            .Select(x => new AirsonicHistoricalGroupResponse
            {
                Alias = x.Key,
                Data = x.Value
            })
            .ToList();
    }

    /// <summary>
    /// Given historical data for multiple tags
    /// Then we synchronize the data by getting the common timestamps among all tags
    /// </summary>
    /// <param name="data">Historical data of airsonic</param>
    /// <returns>The dictionary for each tag containing the records with common timestamp</returns>
    private static Dictionary<string, List<AirsonicHistoricalDataDto>> SynchronizedHistoricalData(ConcurrentDictionary<string, List<AirsonicHistoricalDataDto>> data)
    {
        // Given the historical data for multiple tags

        // When the data is empty
        // Then we return empty dictionary
        if (data.IsEmpty)
            return [];

        // Then we use the first tag as the base
        HashSet<long> commonTimestamps = [.. data.First().Value.Select(h => h.Index)];

        // Then we find the common timestamps among all tags
        foreach (List<AirsonicHistoricalDataDto> historical in data.Values)
        {
            HashSet<long> tagTimestamps = [.. historical.Select(h => h.Index)];
            commonTimestamps.IntersectWith(tagTimestamps);
        }

        // Then we filter the data to include only items with common timestamps
        Dictionary<string, List<AirsonicHistoricalDataDto>> synchronizedHistoricals = [];
        foreach (var pair in data)
        {
            synchronizedHistoricals[pair.Key] = pair.Value
                .Where(h => commonTimestamps.Contains(h.Index))
                .ToList();
        }

        return synchronizedHistoricals;
    }
}
