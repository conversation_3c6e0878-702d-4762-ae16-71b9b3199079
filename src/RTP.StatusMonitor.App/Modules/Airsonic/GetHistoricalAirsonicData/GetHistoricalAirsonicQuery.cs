using ErrorOr;
using MediatR;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;

public record GetHistoricalAirsonicQuery : IRequest<ErrorOr<IEnumerable<AirsonicHistoricalGroupResponse>>>
{
    public Guid UnitId { get; set; }
    public IEnumerable<Guid> UserGroups { get; set; } = null!;
    public List<string> Aliases { get; set; } = null!; // Alias of the tags to query
    public string Filter { get; set; } = string.Empty; // Filter to apply 
    public int SkipInterval { get; set; } // Skip interval to skip data points
    public long StartDate { get; set; } // Start date of the data
    public long EndDate { get; set; } // End date of the data
}
