using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.App.Shared.Authorization;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicConfig;
public record GetAirsonicConfigQuery(
    Guid UnitId,
    List<Guid> UserGroups) : IQuery<AirSpeedModelDto>;
public class GetAirsonicConfigHandler(
    IDataContext context,
    IAirsonicService airsonicService,
    IAuthorizationService authorizationService) : IQueryHandler<GetAirsonicConfigQuery, AirSpeedModelDto>
{
    private readonly IDataContext _context = context;
    private readonly IAirsonicService _airsonicService = airsonicService;
    private readonly IAuthorizationService _authorizationService = authorizationService;

    public async Task<ErrorOr<AirSpeedModelDto>> Handle(GetAirsonicConfigQuery request, CancellationToken cancellationToken)
    {
        // Find the unit by id
        Unit? unit = await _context.Units
            .AsNoTracking()
            .Where(u => u.Id == request.UnitId)
            .Include(u => u.Block)
                .ThenInclude(b => b.Site)
                    .ThenInclude(s => s.Customer)
            .FirstOrDefaultAsync(cancellationToken);

        // Return errors when unit is not found
        if (unit is null)
            return UnitErrors.NotFound;

        // Check if the user is authorized to access the site
        bool isAllowedAccess = await _authorizationService
            .CheckUserAccessByGroup(
                userGroupsId: [.. request.UserGroups],
                siteId: unit.Block.SiteId,
                ct: cancellationToken);

        string siteName = unit.Block.Site.Name.ToLower().Replace(" ", string.Empty);
        string path = "";
        string container = "";
        switch (siteName)
        {
            case "7fa":
                path = "vandolah/block4/airsonic/config/unit1/AirSpeedModel.xml";
                container = "nsgen";
                break;
            case "gt24":
                path = "lakeroad/block3/airsonic/config/unit1/AirSpeedModel.xml";
                container = "vistra";
                break;
            case "w501g":
                path = "ennis/block1/airsonic/config/unit1/AirSpeedModel.xml";
                container = "vistra";
                break;
            default:
                path = $"{siteName}/block{unit.Block.Name}/airsonic/config/unit{unit.Name}/AirSpeedModel.xml";
                container = unit.Block.Site.Customer.Name.ToLower().Replace(" ", string.Empty);
                break;
        }

        // Build options for blob request
        AirsonicServiceBlobOptions option = new()
        {
            Container = container,
            DirectoryPath = path,
            CancellationToken = cancellationToken
        };

        // Get config from blob
        AirSpeedModelDto? config = await _airsonicService.GetConfigAsync(option);

        // Return errors when config is not found
        return config ?? (ErrorOr<AirSpeedModelDto>)AirsonicErrors.ConfigNotFound;
    }
}
