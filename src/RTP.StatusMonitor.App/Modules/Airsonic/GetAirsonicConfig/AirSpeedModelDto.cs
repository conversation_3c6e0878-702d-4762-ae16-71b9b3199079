using System.Xml.Serialization;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicConfig;

[XmlRoot(ElementName = "SiteInfo")]
public class SiteInfo
{
    [XmlElement(ElementName = "Enabled")]
    public string Enabled { get; set; } = string.Empty;
    [XmlElement(ElementName = "Site")]
    public string Site { get; set; } = string.Empty;
    [XmlElement(ElementName = "Block")]
    public string Block { get; set; } = string.Empty;
    [XmlElement(ElementName = "Unit")]
    public string Unit { get; set; } = string.Empty;
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "Longitude")]
    public string Longitude { get; set; } = string.Empty;
    [XmlElement(ElementName = "Latitude")]
    public string Latitude { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeZoneId")]
    public string TimeZoneId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Altitude")]
    public string Altitude { get; set; } = string.Empty;
    [XmlElement(ElementName = "IpAddress")]
    public string IpAddress { get; set; } = string.Empty;
    [XmlElement(ElementName = "Port")]
    public string Port { get; set; } = string.Empty;
    [XmlElement(ElementName = "LocalDirectory")]
    public string LocalDirectory { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "DuctCentroid")]
public class DuctCentroid
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "NominalFlowAngles")]
public class NominalFlowAngles
{
    [XmlElement(ElementName = "XY")]
    public string XY { get; set; } = string.Empty;
    [XmlElement(ElementName = "ZX")]
    public string ZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "YZ")]
    public string YZ { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Vector3D")]
public class Vector3D
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Intersects")]
public class Intersects
{
    [XmlElement(ElementName = "Vector3D")]
    public List<Vector3D> Vector3D { get; set; } = Enumerable.Empty<Vector3D>().ToList();
}

[XmlRoot(ElementName = "NominalFlowDirection")]
public class NominalFlowDirection
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "OCentroid")]
public class OCentroid
{
    [XmlElement(ElementName = "Vector3D")]
    public List<Vector3D> Vector3D { get; set; } = Enumerable.Empty<Vector3D>().ToList();
}

[XmlRoot(ElementName = "Vectors")]
public class Vectors
{
    [XmlElement(ElementName = "Vector3D")]
    public List<Vector3D> Vector3D { get; set; } = Enumerable.Empty<Vector3D>().ToList();
}

[XmlRoot(ElementName = "Centroid")]
public class Centroid
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Point3D")]
public class Point3D
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Corners")]
public class Corners
{
    [XmlElement(ElementName = "Point3D")]
    public List<Point3D> Point3D { get; set; } = Enumerable.Empty<Point3D>().ToList();
}

[XmlRoot(ElementName = "Normal")]
public class Normal
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Wall")]
public class Wall
{
    [XmlElement(ElementName = "WallId")]
    public string WallId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Vectors")]
    public Vectors? Vectors { get; set; }
    [XmlElement(ElementName = "Centroid")]
    public Centroid? Centroid { get; set; }
    [XmlElement(ElementName = "Corners")]
    public Corners? Corners { get; set; }
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "Normal")]
    public Normal? Normal { get; set; }
    [XmlElement(ElementName = "Area")]
    public string Area { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Walls")]
public class Walls
{
    [XmlElement(ElementName = "Wall")]
    public List<Wall> Wall { get; set; } = Enumerable.Empty<Wall>().ToList();
}

[XmlRoot(ElementName = "Position")]
public class Position
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Direction")]
public class Direction
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "UpDirection")]
public class UpDirection
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "CoordinateTranslation")]
public class CoordinateTranslation
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "CameraViews")]
public class CameraViews
{
    [XmlElement(ElementName = "Position")]
    public Position? Position { get; set; }
    [XmlElement(ElementName = "Direction")]
    public Direction? Direction { get; set; }
    [XmlElement(ElementName = "FieldOfView")]
    public string FieldOfView { get; set; } = string.Empty;
    [XmlElement(ElementName = "UpDirection")]
    public UpDirection? UpDirection { get; set; }
    [XmlElement(ElementName = "CoordinateTranslation")]
    public CoordinateTranslation? CoordinateTranslation { get; set; }
}

[XmlRoot(ElementName = "Inlet")]
public class Inlet
{
    [XmlElement(ElementName = "WallId")]
    public string WallId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Vectors")]
    public Vectors? Vectors { get; set; }
    [XmlElement(ElementName = "Centroid")]
    public Centroid? Centroid { get; set; }
    [XmlElement(ElementName = "Corners")]
    public Corners? Corners { get; set; }
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "Normal")]
    public Normal? Normal { get; set; }
    [XmlElement(ElementName = "Area")]
    public string Area { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Outlet")]
public class Outlet
{
    [XmlElement(ElementName = "WallId")]
    public string WallId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Vectors")]
    public Vectors? Vectors { get; set; }
    [XmlElement(ElementName = "Centroid")]
    public Centroid? Centroid { get; set; }
    [XmlElement(ElementName = "Corners")]
    public Corners? Corners { get; set; }
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "Normal")]
    public Normal? Normal { get; set; }
    [XmlElement(ElementName = "Area")]
    public string Area { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Duct")]
public class Duct
{
    [XmlElement(ElementName = "DuctCentroid")]
    public DuctCentroid? DuctCentroid { get; set; }
    [XmlElement(ElementName = "DuctAreaAtCentroid")]
    public string DuctAreaAtCentroid { get; set; } = string.Empty;
    [XmlElement(ElementName = "DuctAreaMeasured")]
    public string DuctAreaMeasured { get; set; } = string.Empty;
    [XmlElement(ElementName = "InternalBracingArea")]
    public string InternalBracingArea { get; set; } = string.Empty;
    [XmlElement(ElementName = "NominalFlowAngles")]
    public NominalFlowAngles? NominalFlowAngles { get; set; }
    [XmlElement(ElementName = "DuctInletType")]
    public string DuctInletType { get; set; } = string.Empty;
    [XmlElement(ElementName = "Intersects")]
    public Intersects? Intersects { get; set; }
    [XmlElement(ElementName = "NominalFlowDirection")]
    public NominalFlowDirection? NominalFlowDirection { get; set; }
    [XmlElement(ElementName = "NominalMassFlowRate")]
    public string NominalMassFlowRate { get; set; } = string.Empty;
    [XmlElement(ElementName = "NominalDensity")]
    public string NominalDensity { get; set; } = string.Empty;
    [XmlElement(ElementName = "OCentroid")]
    public OCentroid? OCentroid { get; set; }
    [XmlElement(ElementName = "Walls")]
    public Walls? Walls { get; set; }
    [XmlElement(ElementName = "CameraViews")]
    public CameraViews? CameraViews { get; set; }
    [XmlElement(ElementName = "Inlet")]
    public Inlet? Inlet { get; set; }
    [XmlElement(ElementName = "Outlet")]
    public Outlet? Outlet { get; set; }
}

[XmlRoot(ElementName = "Coordinate")]
public class Coordinate
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Probe")]
public class Probe
{
    [XmlElement(ElementName = "ProbeId")]
    public string ProbeId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Coordinate")]
    public Coordinate? Coordinate { get; set; }
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Probes")]
public class Probes
{
    [XmlElement(ElementName = "Probe")]
    public List<Probe> Probe { get; set; } = Enumerable.Empty<Probe>().ToList();
}

[XmlRoot(ElementName = "Calculations")]
public class Calculations
{
    [XmlElement(ElementName = "ResultantId")]
    public string ResultantId { get; set; } = string.Empty;
    [XmlElement(ElementName = "DateId")]
    public string DateId { get; set; } = string.Empty;
    [XmlElement(ElementName = "IsSimulatedValue")]
    public string IsSimulatedValue { get; set; } = string.Empty;
    [XmlElement(ElementName = "Timestamp")]
    public string Timestamp { get; set; } = string.Empty;
    [XmlElement(ElementName = "Measurement")]
    public string Measurement { get; set; } = string.Empty;
    [XmlElement(ElementName = "MovingAverage")]
    public string MovingAverage { get; set; } = string.Empty;
    [XmlElement(ElementName = "MovingAverageCounter")]
    public string MovingAverageCounter { get; set; } = string.Empty;
    [XmlElement(ElementName = "MovingAverageGood")]
    public string MovingAverageGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "Voltage")]
    public string Voltage { get; set; } = string.Empty;
    [XmlElement(ElementName = "Current")]
    public string Current { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirFlowAngle")]
    public AirFlowAngle? AirFlowAngle { get; set; }
    [XmlElement(ElementName = "Velocity3D")]
    public Velocity3D? Velocity3D { get; set; }
    [XmlElement(ElementName = "Velocity3DRaw")]
    public Velocity3DRaw? Velocity3DRaw { get; set; }
    [XmlElement(ElementName = "AngleXY")]
    public string AngleXY { get; set; } = string.Empty;
    [XmlElement(ElementName = "AngleYZ")]
    public string AngleYZ { get; set; } = string.Empty;
    [XmlElement(ElementName = "AngleZX")]
    public string AngleZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "AvgDuctPressure")]
    public string AvgDuctPressure { get; set; } = string.Empty;
    [XmlElement(ElementName = "AvgDuctPressureGood")]
    public string AvgDuctPressureGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirDensity")]
    public string AirDensity { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirDensityGood")]
    public string AirDensityGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirMassFlowRate")]
    public string AirMassFlowRate { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirMassFlowRateRaw")]
    public string AirMassFlowRateRaw { get; set; } = string.Empty;
    [XmlElement(ElementName = "AirMassFlowRateGood")]
    public string AirMassFlowRateGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "IGV")]
    public string IGV { get; set; } = string.Empty;
    [XmlElement(ElementName = "ShaftSpeed")]
    public string ShaftSpeed { get; set; } = string.Empty;
    [XmlElement(ElementName = "Area")]
    public string Area { get; set; } = string.Empty;
    [XmlElement(ElementName = "DuctEstimatedTemp")]
    public string DuctEstimatedTemp { get; set; } = string.Empty;
    [XmlElement(ElementName = "ExecutionTime")]
    public string ExecutionTime { get; set; } = string.Empty;
    [XmlElement(ElementName = "FlowFactor")]
    public string FlowFactor { get; set; } = string.Empty;
    [XmlElement(ElementName = "MeasurementId")]
    public string MeasurementId { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathWithWindTofMA")]
    public string PathWithWindTofMA { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathAgainstWindTofMA")]
    public string PathAgainstWindTofMA { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathDistanceAvg")]
    public string PathDistanceAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathVelocityAvg")]
    public string PathVelocityAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathVelocityAvgGood")]
    public string PathVelocityAvgGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundAvg")]
    public string SpeedOfSoundAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundStdDev")]
    public string SpeedOfSoundStdDev { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundAvgGood")]
    public string SpeedOfSoundAvgGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundMA")]
    public string SpeedOfSoundMA { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityGood")]
    public string VelocityGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityX")]
    public string VelocityX { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityY")]
    public string VelocityY { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityZ")]
    public string VelocityZ { get; set; } = string.Empty;
    [XmlElement(ElementName = "Velocity")]
    public string Velocity { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityRaw")]
    public string VelocityRaw { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityRawMA")]
    public string VelocityRawMA { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityMA")]
    public string VelocityMA { get; set; } = string.Empty;
    [XmlElement(ElementName = "ResultantMACounter")]
    public string ResultantMACounter { get; set; } = string.Empty;
    [XmlElement(ElementName = "VolumetricFlowRateRaw")]
    public string VolumetricFlowRateRaw { get; set; } = string.Empty;
    [XmlElement(ElementName = "VolumetricFlowRate")]
    public string VolumetricFlowRate { get; set; } = string.Empty;
    [XmlElement(ElementName = "VolumetricFlowRateGood")]
    public string VolumetricFlowRateGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathId")]
    public string PathId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Delta")]
    public string Delta { get; set; } = string.Empty;
    [XmlElement(ElementName = "Distance")]
    public string Distance { get; set; } = string.Empty;
    [XmlElement(ElementName = "RatioMovingAvg")]
    public string RatioMovingAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "RatioMovingAvgCounter")]
    public string RatioMovingAvgCounter { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundEstimate")]
    public string SpeedOfSoundEstimate { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpeedOfSoundEstimateGood")]
    public string SpeedOfSoundEstimateGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "StaticTempEstimate")]
    public string StaticTempEstimate { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityError")]
    public string VelocityError { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityEst")]
    public string VelocityEst { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityEstGood")]
    public string VelocityEstGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "Signal")]
    public Signal? Signal { get; set; }
    [XmlElement(ElementName = "Input")]
    public Input? Input { get; set; }
    [XmlElement(ElementName = "AInput")]
    public AInput? AInput { get; set; }
    [XmlElement(ElementName = "AOutput")]
    public AOutput? AOutput { get; set; }
    [XmlElement(ElementName = "PingGood")]
    public string PingGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "Correlation")]
    public string Correlation { get; set; } = string.Empty;
    [XmlElement(ElementName = "CorrelationMovingAvg")]
    public string CorrelationMovingAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "CorrelationFIFO")]
    public string CorrelationFIFO { get; set; } = string.Empty;
    [XmlElement(ElementName = "CorrelationGood")]
    public string CorrelationGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "Strength")]
    public string Strength { get; set; } = string.Empty;
    [XmlElement(ElementName = "StrengthMovingAvg")]
    public string StrengthMovingAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "StrengthFIFO")]
    public string StrengthFIFO { get; set; } = string.Empty;
    [XmlElement(ElementName = "StrengthGood")]
    public string StrengthGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "SOSRaw")]
    public string SOSRaw { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlightRaw")]
    public string TimeOfFlightRaw { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlightMovingAvg")]
    public string TimeOfFlightMovingAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlightMovingAvgGood")]
    public string TimeOfFlightMovingAvgGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "PingMovAvgCounter")]
    public string PingMovAvgCounter { get; set; } = string.Empty;
    [XmlElement(ElementName = "PingReTryAttempts")]
    public string PingReTryAttempts { get; set; } = string.Empty;
    [XmlElement(ElementName = "Clipping")]
    public string Clipping { get; set; } = string.Empty;
    [XmlElement(ElementName = "DelayTime")]
    public string DelayTime { get; set; } = string.Empty;
    [XmlElement(ElementName = "LagMax")]
    public string LagMax { get; set; } = string.Empty;
    [XmlElement(ElementName = "LastTimePinged")]
    public string LastTimePinged { get; set; } = string.Empty;
    [XmlElement(ElementName = "LastGood")]
    public string LastGood { get; set; } = string.Empty;
    [XmlElement(ElementName = "NumCorrValues")]
    public string NumCorrValues { get; set; } = string.Empty;
    [XmlElement(ElementName = "StdDevMax")]
    public string StdDevMax { get; set; } = string.Empty;
    [XmlElement(ElementName = "StdDev")]
    public string StdDev { get; set; } = string.Empty;
    [XmlElement(ElementName = "StrengthWindowAvg")]
    public string StrengthWindowAvg { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlight")]
    public string TimeOfFlight { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeMin")]
    public string TimeMin { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeMax")]
    public string TimeMax { get; set; } = string.Empty;
    [XmlElement(ElementName = "TOFChange")]
    public string TOFChange { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlightMin")]
    public string TimeOfFlightMin { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlightMax")]
    public string TimeOfFlightMax { get; set; } = string.Empty;
    [XmlElement(ElementName = "IsSimulatedPing")]
    public string IsSimulatedPing { get; set; } = string.Empty;
    [XmlElement(ElementName = "WindowGood")]
    public string WindowGood { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Serial")]
public class Serial
{
    [XmlElement(ElementName = "SensorId")]
    public string SensorId { get; set; } = string.Empty;
    [XmlElement(ElementName = "BaudRate")]
    public string BaudRate { get; set; } = string.Empty;
    [XmlElement(ElementName = "Data")]
    public string Data { get; set; } = string.Empty;
    [XmlElement(ElementName = "Parit")]
    public string Parit { get; set; } = string.Empty;
    [XmlElement(ElementName = "Port")]
    public string Port { get; set; } = string.Empty;
    [XmlElement(ElementName = "ReadIntervalTimeout")]
    public string ReadIntervalTimeout { get; set; } = string.Empty;
    [XmlElement(ElementName = "ReadTotalTimeoutConstant")]
    public string ReadTotalTimeoutConstant { get; set; } = string.Empty;
    [XmlElement(ElementName = "ReadTotalTimeoutMultiplier")]
    public string ReadTotalTimeoutMultiplier { get; set; } = string.Empty;
    [XmlElement(ElementName = "Stop")]
    public string Stop { get; set; } = string.Empty;
    [XmlElement(ElementName = "WriteTotalTimeoutConstant")]
    public string WriteTotalTimeoutConstant { get; set; } = string.Empty;
    [XmlElement(ElementName = "WriteTotalTimeoutMultiplier")]
    public string WriteTotalTimeoutMultiplier { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Setting")]
public class Setting
{
    [XmlElement(ElementName = "SensorId")]
    public string SensorId { get; set; } = string.Empty;
    [XmlElement(ElementName = "CurrentHigh")]
    public string CurrentHigh { get; set; } = string.Empty;
    [XmlElement(ElementName = "CurrentLow")]
    public string CurrentLow { get; set; } = string.Empty;
    [XmlElement(ElementName = "MeasurementHigh")]
    public string MeasurementHigh { get; set; } = string.Empty;
    [XmlElement(ElementName = "MeasurementLow")]
    public string MeasurementLow { get; set; } = string.Empty;
    [XmlElement(ElementName = "Resistor")]
    public string Resistor { get; set; } = string.Empty;
    [XmlElement(ElementName = "TauP")]
    public string TauP { get; set; } = string.Empty;
    [XmlElement(ElementName = "TauT")]
    public string TauT { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Sensor")]
public class Sensor
{
    [XmlElement(ElementName = "Calculations")]
    public Calculations? Calculations { get; set; }
    [XmlElement(ElementName = "Coordinate")]
    public Coordinate? Coordinate { get; set; }
    [XmlElement(ElementName = "Location")]
    public string Location { get; set; } = string.Empty;
    [XmlElement(ElementName = "MeasurementType")]
    public string MeasurementType { get; set; } = string.Empty;
    [XmlElement(ElementName = "Model")]
    public string Model { get; set; } = string.Empty;
    [XmlElement(ElementName = "Protocol")]
    public string Protocol { get; set; } = string.Empty;
    [XmlElement(ElementName = "Serial")]
    public Serial? Serial { get; set; }
    [XmlElement(ElementName = "Setting")]
    public Setting? Setting { get; set; }
    [XmlElement(ElementName = "Use")]
    public string Use { get; set; } = string.Empty;
    [XmlElement(ElementName = "ChannelAddress")]
    public string ChannelAddress { get; set; } = string.Empty;
    [XmlElement(ElementName = "SensorId")]
    public string SensorId { get; set; } = string.Empty;
    [XmlElement(ElementName = "IsConnected")]
    public string IsConnected { get; set; } = string.Empty;
    [XmlElement(ElementName = "LastTimeChecked")]
    public string LastTimeChecked { get; set; } = string.Empty;
    [XmlElement(ElementName = "MovingAverageInterval")]
    public string MovingAverageInterval { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "SerialNum")]
    public string SerialNum { get; set; } = string.Empty;
    [XmlElement(ElementName = "UnitsOfMeasure")]
    public string UnitsOfMeasure { get; set; } = string.Empty;
    [XmlElement(ElementName = "Filter")]
    public string Filter { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Sensors")]
public class Sensors
{
    [XmlElement(ElementName = "Sensor")]
    public List<Sensor> Sensor { get; set; } = Enumerable.Empty<Sensor>().ToList();
}

[XmlRoot(ElementName = "AirFlowAngle")]
public class AirFlowAngle
{
    [XmlElement(ElementName = "XY")]
    public string XY { get; set; } = string.Empty;
    [XmlElement(ElementName = "ZX")]
    public string ZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "YZ")]
    public string YZ { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Velocity3D")]
public class Velocity3D
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Velocity3DRaw")]
public class Velocity3DRaw
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "DefaultMax")]
public class DefaultMax
{
    [XmlElement(ElementName = "XY")]
    public string XY { get; set; } = string.Empty;
    [XmlElement(ElementName = "ZX")]
    public string ZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "YZ")]
    public string YZ { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "DefaultMin")]
public class DefaultMin
{
    [XmlElement(ElementName = "XY")]
    public string XY { get; set; } = string.Empty;
    [XmlElement(ElementName = "ZX")]
    public string ZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "YZ")]
    public string YZ { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Angle")]
public class Angle
{
    [XmlElement(ElementName = "XY")]
    public string XY { get; set; } = string.Empty;
    [XmlElement(ElementName = "ZX")]
    public string ZX { get; set; } = string.Empty;
    [XmlElement(ElementName = "YZ")]
    public string YZ { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Center")]
public class Center
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "FilteredTransmittedSignal")]
public class FilteredTransmittedSignal
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "FilteredReceivedSignal")]
public class FilteredReceivedSignal
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "FilteredReceivedSpectrum")]
public class FilteredReceivedSpectrum
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "ReceivedSignal")]
public class ReceivedSignal
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "TransmittedSignal")]
public class TransmittedSignal
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "FilteredTransmittedSpectrum")]
public class FilteredTransmittedSpectrum
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "UnfilteredReceivedSpectrum")]
public class UnfilteredReceivedSpectrum
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Signal")]
public class Signal
{
    [XmlElement(ElementName = "FilteredTransmittedSignal")]
    public FilteredTransmittedSignal? FilteredTransmittedSignal { get; set; }
    [XmlElement(ElementName = "FilteredReceivedSignal")]
    public FilteredReceivedSignal? FilteredReceivedSignal { get; set; }
    [XmlElement(ElementName = "FilteredReceivedSpectrum")]
    public FilteredReceivedSpectrum? FilteredReceivedSpectrum { get; set; }
    [XmlElement(ElementName = "ReceivedSignal")]
    public ReceivedSignal? ReceivedSignal { get; set; }
    [XmlElement(ElementName = "TransmittedSignal")]
    public TransmittedSignal? TransmittedSignal { get; set; }
    [XmlElement(ElementName = "FilteredTransmittedSpectrum")]
    public FilteredTransmittedSpectrum? FilteredTransmittedSpectrum { get; set; }
    [XmlElement(ElementName = "UnfilteredReceivedSpectrum")]
    public UnfilteredReceivedSpectrum? UnfilteredReceivedSpectrum { get; set; }
    [XmlElement(ElementName = "IndexFound")]
    public string IndexFound { get; set; } = string.Empty;
    [XmlElement(ElementName = "IndexLost")]
    public string IndexLost { get; set; } = string.Empty;
    [XmlElement(ElementName = "LagStart")]
    public string LagStart { get; set; } = string.Empty;
    [XmlElement(ElementName = "LagEnd")]
    public string LagEnd { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpectrumVariance")]
    public string SpectrumVariance { get; set; } = string.Empty;
    [XmlElement(ElementName = "Timestamp")]
    public string Timestamp { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Input")]
public class Input
{
    [XmlElement(ElementName = "short")]
    public string Short { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "AInput")]
public class AInput
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "AOutput")]
public class AOutput
{
    [XmlElement(ElementName = "double")]
    public string Double { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "UltrasonicSettings")]
public class UltrasonicSettings
{
    [XmlElement(ElementName = "WaveForm")]
    public string WaveForm { get; set; } = string.Empty;
    [XmlElement(ElementName = "Amplitude")]
    public string Amplitude { get; set; } = string.Empty;
    [XmlElement(ElementName = "Correlation")]
    public string Correlation { get; set; } = string.Empty;
    [XmlElement(ElementName = "CorrelationThreshold")]
    public string CorrelationThreshold { get; set; } = string.Empty;
    [XmlElement(ElementName = "Decay")]
    public string Decay { get; set; } = string.Empty;
    [XmlElement(ElementName = "DtMax")]
    public string DtMax { get; set; } = string.Empty;
    [XmlElement(ElementName = "Filter")]
    public string Filter { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "Number")]
    public string Number { get; set; } = string.Empty;
    [XmlElement(ElementName = "Ping")]
    public string Ping { get; set; } = string.Empty;
    [XmlElement(ElementName = "PriortizeTOFWindow")]
    public string PriortizeTOFWindow { get; set; } = string.Empty;
    [XmlElement(ElementName = "RxDamping")]
    public string RxDamping { get; set; } = string.Empty;
    [XmlElement(ElementName = "RxFreq")]
    public string RxFreq { get; set; } = string.Empty;
    [XmlElement(ElementName = "Sleep")]
    public string Sleep { get; set; } = string.Empty;
    [XmlElement(ElementName = "SpectralVariance")]
    public string SpectralVariance { get; set; } = string.Empty;
    [XmlElement(ElementName = "Strength")]
    public string Strength { get; set; } = string.Empty;
    [XmlElement(ElementName = "StrengthThreshold")]
    public string StrengthThreshold { get; set; } = string.Empty;
    [XmlElement(ElementName = "Timestamp")]
    public string Timestamp { get; set; } = string.Empty;
    [XmlElement(ElementName = "TimeOfFlight")]
    public string TimeOfFlight { get; set; } = string.Empty;
    [XmlElement(ElementName = "TxDamping")]
    public string TxDamping { get; set; } = string.Empty;
    [XmlElement(ElementName = "TxFreq")]
    public string TxFreq { get; set; } = string.Empty;
    [XmlElement(ElementName = "TxSweep")]
    public string TxSweep { get; set; } = string.Empty;
    [XmlElement(ElementName = "UpdateInterval")]
    public string UpdateInterval { get; set; } = string.Empty;
    [XmlElement(ElementName = "UseMatchFilter")]
    public string UseMatchFilter { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "PingOne")]
public class PingOne
{
    [XmlElement(ElementName = "PingId")]
    public string PingId { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathId")]
    public string PathId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Calculations")]
    public Calculations? Calculations { get; set; }
    [XmlElement(ElementName = "MaxRetries")]
    public string MaxRetries { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "Selector")]
    public string Selector { get; set; } = string.Empty;
    [XmlElement(ElementName = "Distance")]
    public string Distance { get; set; } = string.Empty;
    [XmlElement(ElementName = "Rx")]
    public string Rx { get; set; } = string.Empty;
    [XmlElement(ElementName = "Tx")]
    public string Tx { get; set; } = string.Empty;
    [XmlElement(ElementName = "Type")]
    public string Type { get; set; } = string.Empty;
    [XmlElement(ElementName = "UltrasonicSettings")]
    public UltrasonicSettings? UltrasonicSettings { get; set; }
    [XmlElement(ElementName = "WithFlow")]
    public string WithFlow { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "PingTwo")]
public class PingTwo
{
    [XmlElement(ElementName = "PingId")]
    public string PingId { get; set; } = string.Empty;
    [XmlElement(ElementName = "PathId")]
    public string PathId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Calculations")]
    public Calculations? Calculations { get; set; }
    [XmlElement(ElementName = "MaxRetries")]
    public string MaxRetries { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "Selector")]
    public string Selector { get; set; } = string.Empty;
    [XmlElement(ElementName = "Distance")]
    public string Distance { get; set; } = string.Empty;
    [XmlElement(ElementName = "Rx")]
    public string Rx { get; set; } = string.Empty;
    [XmlElement(ElementName = "Tx")]
    public string Tx { get; set; } = string.Empty;
    [XmlElement(ElementName = "Type")]
    public string Type { get; set; } = string.Empty;
    [XmlElement(ElementName = "UltrasonicSettings")]
    public UltrasonicSettings? UltrasonicSettings { get; set; }
    [XmlElement(ElementName = "WithFlow")]
    public string WithFlow { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "SoundPath")]
public class SoundPath
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "UnitVector")]
public class UnitVector
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Path")]
public class Path
{
    [XmlElement(ElementName = "PathId")]
    public string PathId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Angle")]
    public Angle? Angle { get; set; }
    [XmlElement(ElementName = "Calculations")]
    public Calculations? Calculations { get; set; }
    [XmlElement(ElementName = "Center")]
    public Center? Center { get; set; }
    [XmlElement(ElementName = "Enabled")]
    public string Enabled { get; set; } = string.Empty;
    [XmlElement(ElementName = "PingOne")]
    public PingOne? PingOne { get; set; }
    [XmlElement(ElementName = "PingTwo")]
    public PingTwo? PingTwo { get; set; }
    [XmlElement(ElementName = "SoundPath")]
    public SoundPath? SoundPath { get; set; }
    [XmlElement(ElementName = "Type")]
    public string Type { get; set; } = string.Empty;
    [XmlElement(ElementName = "UnitVector")]
    public UnitVector? UnitVector { get; set; }
    [XmlElement(ElementName = "Distance")]
    public string Distance { get; set; } = string.Empty;
    [XmlElement(ElementName = "DistanceFrom3DDisto")]
    public string DistanceFrom3DDisto { get; set; } = string.Empty;
    [XmlElement(ElementName = "DistanceFrom2dDisto")]
    public string DistanceFrom2dDisto { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "NominalVelocity")]
    public string NominalVelocity { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Paths")]
public class Paths
{
    [XmlElement(ElementName = "Path")]
    public List<Path> Path { get; set; } = Enumerable.Empty<Path>().ToList();
}

[XmlRoot(ElementName = "ProbeCentroid")]
public class ProbeCentroid
{
    [XmlElement(ElementName = "X")]
    public string X { get; set; } = string.Empty;
    [XmlElement(ElementName = "Y")]
    public string Y { get; set; } = string.Empty;
    [XmlElement(ElementName = "Z")]
    public string Z { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "Resultant")]
public class Resultant
{
    [XmlElement(ElementName = "ResultantId")]
    public string ResultantId { get; set; } = string.Empty;
    [XmlElement(ElementName = "Calculations")]
    public Calculations? Calculations { get; set; }
    [XmlElement(ElementName = "Config")]
    public string Config { get; set; } = string.Empty;
    [XmlElement(ElementName = "DefaultMax")]
    public DefaultMax? DefaultMax { get; set; }
    [XmlElement(ElementName = "DefaultMin")]
    public DefaultMin? DefaultMin { get; set; }
    [XmlElement(ElementName = "Paths")]
    public Paths? Paths { get; set; }
    [XmlElement(ElementName = "DuctArea")]
    public string DuctArea { get; set; } = string.Empty;
    [XmlElement(ElementName = "ProbeCentroid")]
    public ProbeCentroid? ProbeCentroid { get; set; }
    [XmlElement(ElementName = "OCentroid")]
    public OCentroid? OCentroid { get; set; }
    [XmlElement(ElementName = "EnableDualProbe")]
    public string EnableDualProbe { get; set; } = string.Empty;
    [XmlElement(ElementName = "FlowFactor")]
    public string FlowFactor { get; set; } = string.Empty;
    [XmlElement(ElementName = "IsEnabled")]
    public string IsEnabled { get; set; } = string.Empty;
    [XmlElement(ElementName = "Name")]
    public string Name { get; set; } = string.Empty;
    [XmlElement(ElementName = "VelocityMAFilter")]
    public string VelocityMAFilter { get; set; } = string.Empty;
}

[XmlRoot(ElementName = "AirSpeedModel")]
public class AirSpeedModelDto
{
    [XmlElement(ElementName = "SiteInfo")]
    public SiteInfo? SiteInfo { get; set; }
    [XmlElement(ElementName = "Duct")]
    public Duct? Duct { get; set; }
    [XmlElement(ElementName = "Probes")]
    public Probes? Probes { get; set; }
    [XmlElement(ElementName = "Sensors")]
    public Sensors? Sensors { get; set; }
    [XmlElement(ElementName = "Resultant")]
    public Resultant? Resultant { get; set; }
    [XmlAttribute(AttributeName = "xsi", Namespace = "http://www.w3.org/2000/xmlns/")]
    public string Xsi { get; set; } = string.Empty;
    [XmlAttribute(AttributeName = "xsd", Namespace = "http://www.w3.org/2000/xmlns/")]
    public string Xsd { get; set; } = string.Empty;
}
