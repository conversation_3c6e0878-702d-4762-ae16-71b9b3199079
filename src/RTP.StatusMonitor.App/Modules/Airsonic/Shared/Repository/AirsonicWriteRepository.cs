using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;

public class AirsonicWriteRepository(IOptions<TableStorageOptions> tableStorageOptions)
    : TableRepository(tableStorageOptions)
{

    /// <summary>
    /// Migrate historical data from the existing schema to the new schema
    /// </summary>
    /// <param name="unit">The unit to migrate the data</param>
    /// <param name="entities">The list of historical entities from old schema to migrate</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public async Task MigrateHistoricalDataAsync(
        Unit unit,
        List<AirsonicHistoricalWriteSchemaDto> entities,
        CancellationToken ct)
    {
        // Create table client instance
        TableClient tableClient = CreateTableClient(
            $"airsonic{unit.Block.Site.Name.Replace(" ", string.Empty)}");

        // Map the existing schema to the new schema
        List<AirsonicHistoricalReadSchemaDto> newEntities = entities
            .ToNewAirsonicTableDto(unit);

        // Create batches of the new entities (100 items in each batch)
        List<List<TableTransactionAction>> batches = newEntities
            .CreateBatches(TableTransactionActionType.UpsertReplace);

        // Submit to the table storage in parallel
        await Parallel.ForEachAsync(
            batches,
            async (batch, _) => await tableClient.SubmitTransactionAsync(batch, ct));
    }

    /// <summary>
    /// Given the time series data, compute the statistics then transform to schema for statistics and upload to the table storage
    /// </summary>
    /// <param name="unit">The unit to migrate the data</param>
    /// <param name="entities">The list of time series data to derive statistics for</param>
    /// <param name="interval">The resampling interval</param>
    /// <param name="ct"></param>
    public async Task UploadStatisticsDataAsync(
        Unit unit,
        List<StatisticsAirsonicDto> data,
        CancellationToken ct)
    {
        // Create table client instance
        string tableName = $"airsonic{unit.Block.Site.Name.Replace(" ", string.Empty)}";
        TableClient tableClient = CreateTableClient(tableName);

        // Create batches of the statistics entities (100 items in each batch)
        List<List<TableTransactionAction>> batches = data
            .CreateBatches(TableTransactionActionType.UpsertReplace);

        // Submit to the table storage 
        foreach (List<TableTransactionAction> batch in batches)
        {
            await tableClient.SubmitTransactionAsync(batch, ct);
        }
    }
}
