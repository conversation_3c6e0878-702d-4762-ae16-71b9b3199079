using System.Collections.Concurrent;
using Azure;
using Azure.Data.Tables;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData.Services;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Repository;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;

public class AirsonicReadRepository(
    IOptions<TableStorageOptions> tableStorageOptions,
    IAirsonicTableQueryBuilder queryBuilder,
    IMemoryCache cache) : TableRepository(tableStorageOptions)
{
    private readonly IMemoryCache _cache = cache;
    private readonly IAirsonicTableQueryBuilder _queryBuilder = queryBuilder;
    /// <summary>
    /// Group the queries into batch of n queries
    /// </summary>
    const int QUERY_BATCH_SIZE = 8;

    /// <summary>
    /// The time interval each query will span in milliseconds
    /// </summary>
    const int QUERY_TIME_INTERVAL_IN_MILISECONDS = 60 * 30 * 1000;

    /// <summary>
    /// The duration to cache the historical data
    /// </summary>
    public readonly TimeSpan HISTORICAL_CACHE_DURATION_IN_MINS = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Get the latest Airsonic data from table storage
    /// </summary>
    /// 
    /// <param name="block">The block to get the latest data</param>
    /// <param name="aliases">The list of tags to needed</param>
    /// 
    /// <returns>The list of data client entity</returns>
    public List<AirsonicHistoricalWriteSchemaDto> GetLatestData(
        Unit unit, List<string> aliases)
        => CreateTableClient(
            _queryBuilder.GetTableName(siteName: unit.Block.Site.Name))
        .Query<AirsonicHistoricalWriteSchemaDto>(
            filter: _queryBuilder.GetSnapshotQuery(
                siteName: unit.Block.Site.Name,
                blockName: unit.Block.Name,
                unitName: unit.Name,
                tags: aliases),
            cancellationToken: default)
        .ToList();

    /// <summary>
    /// Get historical data from the table storage for a specific unit
    /// </summary>
    /// 
    /// <param name="table"></param>
    /// <param name="queries"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<List<AirsonicHistoricalWriteSchemaDto>> GetCurrentDayHistoricalAsync(
        Unit unit,
        List<string> aliases,
        long unixStartTimeInMs,
        long unixEndTimeInMs,
        CancellationToken ct)
    {
        try
        {
            // Create table client instance
            TableClient tableClient = CreateTableClient(
                _queryBuilder.GetTableName(unit.Block.Site.Name));

            // Build the queries
            List<List<string>> queryBatches = _queryBuilder.GetHistoricalQuery(
                siteName: unit.Block.Site.Name,
                blockName: unit.Block.Name.ToLower(),
                unitName: unit.Name.ToLower(),
                aliases: aliases,
                filter: string.Empty,
                startTime: unixStartTimeInMs,
                endTime: unixEndTimeInMs,
                rangeLength: QUERY_TIME_INTERVAL_IN_MILISECONDS,
                queryBatchSize: QUERY_BATCH_SIZE);

            // Query the table in parallel
            ConcurrentBag<AirsonicHistoricalWriteSchemaDto> entities = new();
            await Parallel.ForEachAsync(queryBatches, async (queriesInBatch, _) =>
            {
                await Parallel.ForEachAsync(queriesInBatch, async (query, _) =>
                {
                    AsyncPageable<AirsonicHistoricalWriteSchemaDto> data = tableClient.QueryAsync<AirsonicHistoricalWriteSchemaDto>(
                        filter: query,
                        select: ["Index", "RowKey", "Tag", "Value", "Quality"],
                        cancellationToken: ct);

                    await foreach (AirsonicHistoricalWriteSchemaDto record in data)
                    {
                        entities.Add(record);
                    }
                });
            });

            return [.. entities];
        }
        catch (Exception e)
        { throw new Exception(e.Message); }
    }

    /// <summary>
    /// Query the new schema table storage for historical data client data (any day that is not current day since current day data has not been migrated over yet)
    /// </summary>
    /// 
    /// <param name="block">The block to get the data</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    /// <param name="ct"></param>
    /// 
    /// <returns>The list of data client entity</returns>
    private async Task<List<AirsonicHistoricalReadSchemaDto>> GetPreviousDayHistoricalAsync(
        Unit unit,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct) => await _cache.GetOrCreateAsync(
            key: $"airsonic-{unit.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            factory: async (entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                // Create table client instance
                string tableName = $"airsonic{unit.Block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                ConcurrentBag<AirsonicHistoricalReadSchemaDto> entities = [];
                int days = (int)(endDate - startDate).TotalDays;

                // Divide the unique tags into groups of 3
                List<List<string>> uniqueTagsGroups = uniqueTags
                    .Select((value, index) => new { Index = index, Value = value })
                    .GroupBy(x => x.Index / 3)
                    .Select(x => x.Select(v => v.Value).ToList())
                    .ToList();

                // Query the table in parallel for each group of tags and
                // each tag in the group
                await Parallel.ForEachAsync(uniqueTagsGroups, async (tagGroup, _) =>
                {
                    await Parallel.ForEachAsync(tagGroup, async (tag, _) =>
                    {
                        for (int i = 0; i <= days; i++)
                        {
                            // Build the filter to query
                            string filter = $"PartitionKey eq '{unit.Alias}-{tag}-{startDate.AddDays(i):yyyy-MM-dd}'";

                            // Query data from table storage
                            AsyncPageable<AirsonicHistoricalReadSchemaDto> data = tableClient
                                .QueryAsync<AirsonicHistoricalReadSchemaDto>(
                                    filter,
                                    select: ["Alias", "LocalTimestamp", "Value", "Quality"],
                                    cancellationToken: ct);

                            // Select good quality data only
                            await foreach (AirsonicHistoricalReadSchemaDto record in data)
                            {
                                if (record.Quality == true)
                                {
                                    entities.Add(record);
                                }
                            }
                        }
                    });
                });

                return entities.ToList();
            }
        );

    /// <summary>
    /// Query historical data from the table storage
    /// If the date range spans across the today and previous day, query both the new and old schema and combine the results.
    /// If the date range is before the current day, query the new schema.
    /// If the date range is after the current day (today only), query the old schema.
    /// </summary>
    /// <param name="unit">The unit to get the data</param>
    /// <param name="uniqueVariables">The list of unique variables to query</param>
    /// <param name="startTime">The start time of the query</param>
    /// <param name="endTime">The end time of the query</param>
    /// <param name="splitTime">The split time to determine if the query is for current day or not</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public async Task<List<Domain.TimeSeries.Types.TimeSeriesData>> GetHistoricalDataAsync(
        Unit unit,
        List<string> uniqueVariables,
        DateTime startTime,
        DateTime endTime,
        CancellationToken ct)
    {
        TimeZoneInfo siteTzInfo = TimeZoneInfo
            .FindSystemTimeZoneById(unit.Block.Site.TimeZone);

        // Convert UTC time to local site time using timezone
        SiteLocalTime currentLocalTime = SiteLocalTime.Create(
            unit.Block.Site, DateTime.UtcNow);

        // Set the split time to start of today in local time
        DateTime splitTime = new(
            currentLocalTime.Value.Year,
            currentLocalTime.Value.Month,
            currentLocalTime.Value.Day, 0, 0, 0);

        // Not current day => query using new schema
        if (endTime < splitTime)
        {
            List<AirsonicHistoricalReadSchemaDto> entities = await
                GetPreviousDayHistoricalAsync(
                    unit: unit,
                    uniqueTags: uniqueVariables,
                    startDate: startTime,
                    endDate: endTime,
                    ct: ct);

            return [.. entities.ToTimeSeriesData()];
        }
        // Current day => query using old schema
        else if (startTime >= splitTime)
        {
            List<AirsonicHistoricalWriteSchemaDto> entities = await
                GetCurrentDayHistoricalAsync(
                    unit: unit,
                    aliases: uniqueVariables,
                    unixStartTimeInMs: new DateTimeWithZone(
                        startTime,
                        siteTzInfo).UnixTimeInMs,
                    unixEndTimeInMs: new DateTimeWithZone(
                        endTime,
                        siteTzInfo).UnixTimeInMs,
                    ct: ct);

            return entities.ToTimeSeriesData(siteTzInfo);
        }
        // Both current day and not current day => query using both schema
        else
        {
            List<AirsonicHistoricalReadSchemaDto> previousDayEntities = await GetPreviousDayHistoricalAsync(
                unit: unit,
                uniqueTags: uniqueVariables,
                startDate: startTime,
                endDate: splitTime
                    .AddDays(-1)
                    .AddHours(23)
                    .AddMinutes(59)
                    .AddSeconds(59),
                ct: ct);

            List<AirsonicHistoricalWriteSchemaDto> currentDayEntities = await GetCurrentDayHistoricalAsync(
                unit: unit,
                aliases: uniqueVariables,
                unixStartTimeInMs: new DateTimeWithZone(
                    splitTime,
                    siteTzInfo).UnixTimeInMs,
                unixEndTimeInMs: new DateTimeWithZone(
                    endTime,
                    siteTzInfo).UnixTimeInMs,
                ct: ct);

            return previousDayEntities
                .ToTimeSeriesData()
                .Concat(currentDayEntities.ToTimeSeriesData(siteTzInfo))
                .ToList();
        }
    }

    /// <summary>
    /// Query the new schema table storage for historical data client data (any day that is not current day since current day data has not been migrated over yet)
    /// </summary>
    /// <param name="block">The block to get the data</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    ///  <param name="ct"></param>
    /// <returns>The list of data client entity</returns>
    public async Task<List<StatisticsAirsonicDto>> GetHistoricalHourlyDataAsync(
        Unit unit,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct)
        => await _cache.GetOrCreateAsync(
            key: $"airsonic-hourly-{unit.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                // Create table client instance
                string tableName = $"airsonic{unit.Block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                ConcurrentBag<StatisticsAirsonicDto> entities = new();
                int days = (int)(endDate - startDate).TotalDays;

                // Divide the unique tags into groups of 3
                List<List<string>> uniqueTagsGroups = uniqueTags
                    .Select(
                        (value, index)
                        => new { Index = index, Value = value })
                    .GroupBy(x => x.Index / 3)
                    .Select(x => x.Select(v => v.Value).ToList())
                    .ToList();

                // Get the start and end time of the local site timezone
                TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone);
                DateTimeWithZone startTime = new(startDate, siteTzInfo);
                DateTimeWithZone endTime = new(endDate, siteTzInfo);

                // Query the table in parallel for each group of tags and
                // each tag in the group
                await Parallel.ForEachAsync(uniqueTagsGroups, async (tagGroup, _)
                    => await Parallel.ForEachAsync(tagGroup, async (tag, _) =>
                    {
                        // Build the filter to query
                        string query = $"PartitionKey eq '{unit.Alias}-{tag}-Hour' and RowKey ge '{startTime.UnixTimeInSeconds}' and RowKey le '{endTime.UnixTimeInSeconds + 1}'";

                        // Query data from table storage
                        AsyncPageable<StatisticsAirsonicDto> data = tableClient
                            .QueryAsync<StatisticsAirsonicDto>(
                                filter: query,
                                select: ["Tag", "LocalTimestamp", "Avg", "Max", "Min", "StdDev", "Points"],
                                cancellationToken: ct);

                        await foreach (StatisticsAirsonicDto record in data)
                        { entities.Add(record); }
                    }));

                return entities.ToList();
            });

    /// <summary>
    /// Query table storage for daily airsonic data (any day that is not current day since current day data has not been migrated over yet)
    /// </summary>
    /// <param name="unit">The unit to get the data</param>
    /// <param name="uniqueTags">The list of tags to query</param>
    /// <param name="startDate">The start date of the query</param>
    /// <param name="endDate">The end date of the query</param>
    ///  <param name="ct"></param>
    /// 
    /// <returns>The list of data client entity</returns>
    public async Task<List<StatisticsAirsonicDto>> GetHistoricalDailyDataAsync(
        Unit unit,
        List<string> uniqueTags,
        DateTime startDate,
        DateTime endDate,
        CancellationToken ct)
        => await _cache.GetOrCreateAsync(
            key: $"airsonic-daily-{unit.Id}-{JsonConvert.SerializeObject(uniqueTags)}-{startDate}-{endDate}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = HISTORICAL_CACHE_DURATION_IN_MINS;

                // Create table client instance
                string tableName = $"airsonic{unit.Block.Site.Name.Replace(" ", string.Empty)}";
                TableClient tableClient = CreateTableClient(tableName);

                ConcurrentBag<StatisticsAirsonicDto> entities = new();
                int days = (int)(endDate - startDate).TotalDays;

                // Divide the unique tags into groups of 3
                List<List<string>> uniqueTagsGroups = uniqueTags
                    .Select(
                        (value, index)
                        => new { Index = index, Value = value })
                    .GroupBy(x => x.Index / 3)
                    .Select(x => x.Select(v => v.Value).ToList())
                    .ToList();

                // Get the start and end time of the local site timezone
                TimeZoneInfo siteTzInfo = TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone);
                DateTimeWithZone startTime = new(startDate, siteTzInfo);
                DateTimeWithZone endTime = new(endDate, siteTzInfo);

                // Query the table in parallel for each group of tags and
                // each tag in the group
                await Parallel.ForEachAsync(uniqueTagsGroups, async (tagGroup, _) =>
                {
                    await Parallel.ForEachAsync(tagGroup, async (tag, _) =>
                    {
                        // Build the filter to query
                        string query = $"PartitionKey eq '{unit.Alias}-{tag}-Day' and RowKey ge '{startTime.UnixTimeInSeconds}' and RowKey le '{endTime.UnixTimeInSeconds + 1}'";

                        // Query data from table storage
                        AsyncPageable<StatisticsAirsonicDto> data = tableClient
                            .QueryAsync<StatisticsAirsonicDto>(
                                filter: query,
                                select: new[] { "Tag", "LocalTimestamp", "Avg", "Max", "Min", "StdDev", "Points" },
                                cancellationToken: ct);

                        await foreach (StatisticsAirsonicDto record in data)
                        { entities.Add(record); }
                    });
                });

                return entities.ToList();
            });
}
