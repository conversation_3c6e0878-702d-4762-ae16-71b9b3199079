using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;

/// <summary>
/// The result of a query to the table storage for airsonic historical data
/// </summary>
public record AirsonicHistoricalWriteSchemaDto : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public DateTimeOffset? Timestamp { get; set; }
    public string LocalTimestamp { get; set; } = string.Empty;

    /// <summary>
    /// This is the batch id => unix timestamp in milliseconds when the data was uploaded
    /// </summary>
    public string Index { get; set; } = null!;
    public string Tag { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public bool Quality { get; set; }
    public string EngUnits { get; set; } = string.Empty;
    public ETag ETag { get; set; }
}
