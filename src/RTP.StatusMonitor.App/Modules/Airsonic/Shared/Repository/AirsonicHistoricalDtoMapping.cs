using RTP.StatusMonitor.Domain.TimeSeries.Types;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;

public static class AirsonicHistoricalDtoMapping
{
    /// <summary>
    /// Converts the given collection of AirsonicHistoricalWriteSchemaDto objects to a list of TimeSeriesData objects.
    /// </summary>
    /// 
    /// <param name="data">The collection of AirsonicHistoricalWriteSchemaDto objects to convert.</param>
    /// <param name="siteTimeZone">The time zone information for the site.</param>
    /// 
    /// <returns>A list of TimeSeriesData objects representing the converted data.</returns>
    public static List<TimeSeriesData> ToTimeSeriesData(
        this IEnumerable<AirsonicHistoricalWriteSchemaDto> data,
        TimeZoneInfo siteTimeZone) => data
            .GroupBy(x => x.<PERSON>Key) // row key here is the alias
            .Select(x => new TimeSeriesData
            (
                Tag: x.Key,
                Values: x
                    .Select(v => (object)v.Value)
                    .ToArray(),
                Timestamps: x
                    .Select(
                        v => TimeZoneInfo.ConvertTimeFromUtc(
                            DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(v.Index)).DateTime,
                        siteTimeZone))
                    .ToArray()
            ))
            .ToList();
}
