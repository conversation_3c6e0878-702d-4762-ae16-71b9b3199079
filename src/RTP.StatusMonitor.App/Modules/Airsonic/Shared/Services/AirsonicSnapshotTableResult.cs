using Azure;
using Azure.Data.Tables;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

public record AirsonicSnapshotTableResult : ITableEntity
{
    public string PartitionKey { get; set; } = null!;
    public string RowKey { get; set; } = null!;
    public long Index { get; set; }
    public string LocalTimestamp { get; set; } = string.Empty;
    public DateTimeOffset? Timestamp { get; set; }
    public string Tag { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public string EngUnits { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public bool Quality { get; set; }
    public ETag ETag { get; set; }
}
