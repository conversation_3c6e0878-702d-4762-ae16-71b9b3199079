using RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicConfig;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

public interface IAirsonicService
{
    /// <summary>
    /// Given an option object with container name, directory path, and cancellation token
    /// Then get the latest airsonic data from blob storage
    /// </summary>
    /// <param name="option">an object containing container name and directory path for the blob and the cancellation token</param>
    /// <returns>Airsonic snapshot data from Output.xml file</returns>
    Task<AirsonicSnapshotListResponse?> GetSnapshotAsync(AirsonicServiceBlobOptions option);

    Task<List<AirsonicSnapshotTableResult>> GetSnapshotAsync(
      string table,
      string query,
      CancellationToken ct);

    /// <summary>
    /// Given an option object with container name, directory path, and cancellation token
    /// Then get the configuration model for the Airsonic from blob storage
    /// </summary>
    /// <param name="option">an object containing container name and directory path for the blob and the cancellation token</param>
    /// <returns>Airsonic configuration from AirSpeedModel.xml</returns>
    Task<AirSpeedModelDto?> GetConfigAsync(AirsonicServiceBlobOptions option);

    /// <summary>
    /// Given an option object with table name, tags, skip interval, filter, and cancellation token
    /// Then get historical data from table storage for the tags with the filter and skip interval applied
    /// </summary>
    /// <param name="option">an object containing the table to get data, the tags you want to query historical data for, the filter to apply, the skip interval, and the cancellation token</param>
    /// <returns>a list of airsonic historical data</returns>
    Task<List<AirsonicHistoricalTableDto>> GetHistoricalAsync(
      string table, List<string> queries, int skipInterval, CancellationToken ct);
}
