using System.Xml.Linq;
using System.Xml.Serialization;
using Azure.Data.Tables;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData;
using RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicConfig;

namespace RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

public class AirsonicService(
    IOptions<BlobStorageOptions> blobStorageOptions,
    IOptions<TableStorageOptions> tableStorageOptions) : IAirsonicService
{
    private readonly BlobStorageOptions _blobStorageOptions = blobStorageOptions.Value;
    private readonly TableStorageOptions _tableStorageOptions = tableStorageOptions.Value;

    private async Task<T?> GetXmlFromBlobStorageAsync<T>(
        string container,
        string directoryPath,
        CancellationToken ct) where T : class
    {
        try
        {
            var blobClient = new BlobClient(
                _blobStorageOptions.ConnectionString,
                container,
                directoryPath);

            // Download blob data into memory
            var res = await blobClient.DownloadAsync(ct);
            if (res.Value.Content is null)
                return null;

            // Get the blob content as string
            using var streamReader = new StreamReader(res.Value.Content);

            var content = await streamReader.ReadToEndAsync();
            if (content is null)
                return null;

            // Parse the string content into XML document
            XDocument xdoc = XDocument.Parse(content);
            var xmlSerializer = new XmlSerializer(typeof(T));

            // Deserialize the XML document into an object of type T
            using var reader = xdoc.Root?.CreateReader();
            if (reader is null)
                return null;

            return xmlSerializer.Deserialize(reader) as T;
        }
        catch (Exception ex)
        {
            throw new Exception($"Error getting XML data from blob storage: {ex.Message}");
        }
    }

    public async Task<AirsonicSnapshotListResponse?> GetSnapshotAsync(AirsonicServiceBlobOptions option)
    {
        return await GetXmlFromBlobStorageAsync<AirsonicSnapshotListResponse>(option.Container,
        option.DirectoryPath,
        option.CancellationToken);
    }

    public async Task<List<AirsonicSnapshotTableResult>> GetSnapshotAsync(
        string table,
        string query,
        CancellationToken ct)
    {
        try
        {
            var tableClient = new TableClient(
                new Uri(_tableStorageOptions.Uri),
                table,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            // Query the airsonic snapshot
            var data = tableClient.QueryAsync<AirsonicSnapshotTableResult>(
                filter: query,
                maxPerPage: 1000,
                select: new[] { "Tag", "RowKey", "Alias", "Value", "Quality", "Index", "EngUnits", "LocalTimestamp" },
                cancellationToken: ct);

            var snapshots = new List<AirsonicSnapshotTableResult>();
            await foreach (var page in data.AsPages())
            {
                snapshots.AddRange(page.Values);
            }

            return snapshots;
        }
        catch
        {
            return new List<AirsonicSnapshotTableResult>();
        }
    }

    public async Task<AirSpeedModelDto?> GetConfigAsync(
        AirsonicServiceBlobOptions option)
    {
        return await GetXmlFromBlobStorageAsync<AirSpeedModelDto>(
            option.Container, option.DirectoryPath, option.CancellationToken);
    }

    public async Task<List<AirsonicHistoricalTableDto>> GetHistoricalAsync(
        string table,
        List<string> queries,
        int skipInterval,
        CancellationToken ct)
    {
        try
        {
            // Given a table name and a list of queries

            // Then we create a client to connect to table storage
            TableClient tableClient = new(
                new Uri(_tableStorageOptions.Uri),
                table,
                new TableSharedKeyCredential(
                    _tableStorageOptions.AccountName,
                    _tableStorageOptions.AccountKey));

            // Then we should query the table in parallel
            var historicals = new ConcurrentBag<AirsonicHistoricalTableDto>();
            await Parallel.ForEachAsync(queries, async (query, _) =>
            {
                // When we query the table for each query
                var res = tableClient.QueryAsync<AirsonicHistoricalTableDto>(
            filter: query,
            maxPerPage: 1000,
            select: new[] { "PartitionKey", "Alias", "Value", "Quality", "EngUnits" },
            cancellationToken: ct);

                // Then we should add the results to the list of historicals
                await foreach (var page in res.AsPages())
                {
                    // Group the historicals by aliases into AirsonicHistoricalGroupDto
                    foreach (var record in page.Values)
                    {
                        historicals.Add(record);
                    }
                }
            });

            return historicals.ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }
}
