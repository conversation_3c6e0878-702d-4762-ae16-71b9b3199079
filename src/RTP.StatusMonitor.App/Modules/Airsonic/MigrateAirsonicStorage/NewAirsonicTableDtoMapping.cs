using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;

public static class NewAirsonictableDtoMapping
{
    /// <summary>
    /// Map from the existing schema <see cref="AirsonicHistoricalWriteSchemaDto"/> to new <see cref="AirsonicHistoricalReadSchemaDto"/>
    /// </summary>
    /// 
    /// <param name="data">A list of airsonic historical DTO from existing schema</param>
    /// <param name="unit">The unit that the data belongs to</param>
    /// 
    /// <returns>A list of new airsonic table DTO</returns>
    public static List<AirsonicHistoricalReadSchemaDto> ToNewAirsonicTableDto(
        this List<AirsonicHistoricalWriteSchemaDto> data,
        Unit unit)
    {
        List<AirsonicHistoricalReadSchemaDto> results = new();
        for (int i = 0; i < data.Count; i++)
        {
            // Get the date time utc from batch id (index)
            SiteLocalTime siteLocalTime = SiteLocalTime.Create(
                site: unit.Block.Site,
                utcTime: DateTimeOffset
                .FromUnixTimeMilliseconds(long.Parse(data[i].Index))
                .DateTime);

            results.Add(new AirsonicHistoricalReadSchemaDto
            {
                // NOTE - Need to use RowKey instead of Tag (formatted for table storage RowKey and PK)
                PartitionKey = $"{unit.Alias}-{data[i].RowKey}-{siteLocalTime.Value:yyyy-MM-dd}",
                RowKey = data[i].Index.ToString(),
                Alias = data[i].RowKey,
                Tag = data[i].Tag,
                LocalTimestamp = siteLocalTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                Value = data[i].Value,
                Quality = data[i].Quality
            });
        }

        return results
            .DistinctBy(r => new { r.PartitionKey, r.RowKey })
            .ToList();
    }

    /// <summary>
    /// Map from the new airsonic table DTO to the time series data.
    /// </summary>
    /// 
    /// <param name="data">A list of new airsonic table DTO</param>
    /// 
    /// <returns>A list of time series data</returns>
    public static List<Domain.TimeSeries.Types.TimeSeriesData> ToTimeSeriesData(
        this IEnumerable<AirsonicHistoricalReadSchemaDto> data)
        // NOTE - Group by the Alias
        => data.GroupBy(d => d.Alias)
            .Select(g => new Domain.TimeSeries.Types.TimeSeriesData(
                Tag: g.Key,
                Values: g.Select(v => v.Value).ToArray(),
                Timestamps: g.Select(v => DateTime.Parse(v.LocalTimestamp)).ToArray()))
            .ToList();
}
