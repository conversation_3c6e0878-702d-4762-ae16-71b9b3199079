using RTP.StatusMonitor.App.Shared.Storage;

namespace RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;

public class AirsonicHistoricalReadSchemaDto : BaseTableEntity
{
    public string Tag { get; init; } = string.Empty;
    public string Alias { get; init; } = string.Empty;
    public string LocalTimestamp { get; init; } = string.Empty;
    public string Value { get; init; } = string.Empty;
    public bool Quality { get; init; }
}