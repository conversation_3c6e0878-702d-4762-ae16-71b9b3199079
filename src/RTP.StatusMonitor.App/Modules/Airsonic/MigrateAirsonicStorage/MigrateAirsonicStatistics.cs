using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;

public record MigrateAirsonicStatisticsCommand(
    Guid UnitId,
    List<string> Tags,
    DateOnly Date) : ICommand;

internal sealed class MigrateAirsonicStatisticsCommandHandler
    : ICommandHandler<MigrateAirsonicStatisticsCommand>
{
    private readonly IDataContext _dataContext;
    private readonly AirsonicReadRepository _airsonicReadRepository;
    private readonly AirsonicWriteRepository _airsonicWriteRepository;

    public MigrateAirsonicStatisticsCommandHandler(
        IDataContext dataContext,
        AirsonicReadRepository airsonicReadRepository,
        AirsonicWriteRepository airsonicWriteRepository)
    {
        _dataContext = dataContext;
        _airsonicReadRepository = airsonicReadRepository;
        _airsonicWriteRepository = airsonicWriteRepository;
    }

    public async Task<ErrorOr<MediatR.Unit>> Handle(
        MigrateAirsonicStatisticsCommand request,
        CancellationToken ct = default)
    {
        // Get the unit info
        Domain.Units.Unit unit = await _dataContext.Units
            .Include(b => b.Block)
                .ThenInclude(s => s.Site)
                    .ThenInclude(c => c.Customer)
            .SingleAsync(b => b.Id == request.UnitId, ct);

        // Get the local start and end time
        TimeZoneInfo siteTzInfo = TimeZoneInfo
            .FindSystemTimeZoneById(unit.Block.Site.TimeZone);
        DateTimeWithZone localStartTime = new(
            new(request.Date.Year, request.Date.Month, request.Date.Day, 0, 0, 0),
            siteTzInfo);
        DateTimeWithZone localEndTime = new(
            new(request.Date.Year, request.Date.Month, request.Date.Day, 23, 59, 59),
            siteTzInfo);

        // Get the historical data from existing data client
        List<AirsonicHistoricalWriteSchemaDto> historicalEntities = await _airsonicReadRepository
            .GetCurrentDayHistoricalAsync(
                unit: unit,
                aliases: request.Tags,
                unixStartTimeInMs: localStartTime.UnixTimeInMs,
                unixEndTimeInMs: localEndTime.UnixTimeInMs,
                ct: ct);

        // Filter bad data before computing the statistics
        // NOTE - also filter data not within the date 
        // Airsonic machine have time sync issue so data timestamp will be slightly off
        List<Domain.TimeSeries.Types.TimeSeriesData> timeSeriesData = historicalEntities
            .Where(e => e.Quality == true &&
                DateOnly.FromDateTime(SiteLocalTime.Create(
                    unit.Block.Site,
                    DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(e.Index)).DateTime).Value) == request.Date)
            .ToTimeSeriesData(siteTzInfo);

        // Compute and upload hourly statistics
        var hourlyStatistics = timeSeriesData
            .ToStatisticsAirsonicDto(unit, TimeSeriesResamplingInterval.Hour);
        await _airsonicWriteRepository.UploadStatisticsDataAsync(
            unit: unit,
            data: hourlyStatistics,
            ct: ct);

        // Compute and upload daily statistics
        var dailyStatistics = timeSeriesData
            .ToStatisticsAirsonicDto(unit, TimeSeriesResamplingInterval.Day);
        await _airsonicWriteRepository.UploadStatisticsDataAsync(
            unit: unit,
            data: dailyStatistics,
            ct: ct);

        return MediatR.Unit.Value;
    }
}
