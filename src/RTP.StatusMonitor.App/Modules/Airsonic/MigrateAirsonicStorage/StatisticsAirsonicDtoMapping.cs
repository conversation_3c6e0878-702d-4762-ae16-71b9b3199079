using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.Units;

namespace RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;

public static class StatisticsAirsonicDtoMapping
{
    /// <summary>
    /// Transfrom time series to statistics airsonic DTO by computing the statistics for the time series data based on the specified resampling interval.
    /// </summary>
    /// 
    /// <param name="data"></param>
    /// <param name="unit"></param>
    /// <param name="interval"></param>
    /// <returns></returns>
    public static List<StatisticsAirsonicDto> ToStatisticsAirsonicDto(
        this List<Domain.TimeSeries.Types.TimeSeriesData> data,
        Unit unit,
        TimeSeriesResamplingInterval interval)
    {
        List<StatisticsAirsonicDto> entities = new();
        TimeZoneInfo siteTzInfo = TimeZoneInfo
            .FindSystemTimeZoneById(unit.Block.Site.TimeZone);

        // Compute statistics for each time series data
        foreach (var timeSeriesData in data)
        {
            List<(DateTime, double[])> stats = timeSeriesData.Downsample(
                interval,
                new Func<IEnumerable<double>, double>[]
                {
                    TimeSeriesDownsampling.Min,
                    TimeSeriesDownsampling.Max,
                    TimeSeriesDownsampling.Average,
                    TimeSeriesDownsampling.StdDev
                });

            entities.AddRange(
                stats.Select(s => new StatisticsAirsonicDto()
                {
                    PartitionKey = $"{unit.Alias}-{timeSeriesData.Tag}-{interval}",
                    RowKey = new DateTimeWithZone(s.Item1, siteTzInfo)
                        .UnixTimeInSeconds.ToString(),
                    Tag = timeSeriesData.Tag,
                    LocalTimestamp = s.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
                    Min = s.Item2[0],
                    Max = s.Item2[1],
                    Avg = s.Item2[2],
                    StdDev = s.Item2[3],
                    // Depending on the interval => get the number of points for the interval (day/hour/etc...)
                    Points = interval switch
                    {
                        TimeSeriesResamplingInterval.Day
                            => timeSeriesData.Timestamps
                            .Where(t => t.Date == s.Item1.Date)
                            .Count(),
                        TimeSeriesResamplingInterval.Hour
                            => timeSeriesData.Timestamps
                            .Where(t => t.Date == s.Item1.Date &&
                                t.Hour == s.Item1.Hour)
                            .Count(),
                        _ => throw new NotImplementedException()
                    }
                }));
        }

        return entities;
    }

    /// <summary>
    /// Converts a list of <see cref="StatisticsAirsonicDto"/> objects to a list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.
    /// </summary>
    /// 
    /// <param name="data">The list of <see cref="StatisticsAirsonicDto"/> objects to convert.</param>
    /// 
    /// <returns>A list of <see cref="Domain.TimeSeries.Types.TimeSeriesData"/> objects.</returns>
    public static List<Domain.TimeSeries.Types.TimeSeriesData> ToTimeSeriesData(this List<StatisticsAirsonicDto> data)
    {
        // Group the statistics data by tag first
        Dictionary<string, List<StatisticsAirsonicDto>> groupedData = data
            .GroupBy(x => x.Tag)
            .ToDictionary(x => x.Key, x => x.ToList());

        // Get the hourly time series data for each tag 
        // NOTE - for now only get the average (will refactor for more flexibility later etc min/max/etc...)
        List<Domain.TimeSeries.Types.TimeSeriesData> timeSeriesData = new();
        foreach (var item in groupedData)
        {
            timeSeriesData.Add(new Domain.TimeSeries.Types.TimeSeriesData(
                Tag: item.Key,
                Values: item.Value
                    .Select(v => (object)v.Avg)
                    .ToArray(),
                Timestamps: item.Value
                    .Select(v => DateTime.Parse(v.LocalTimestamp))
                    .ToArray()));
        }

        return timeSeriesData;
    }
}