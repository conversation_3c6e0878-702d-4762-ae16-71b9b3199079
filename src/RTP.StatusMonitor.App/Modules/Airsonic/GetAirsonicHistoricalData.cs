using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Airsonic.MigrateAirsonicStorage;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Dto;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Airsonic;
public record GetAirsonicHistoricalQuery(
    List<AirsonicSeries> Series,
    DateTime StartDate,
    DateTime EndDate,
    TimeSeriesResamplingInterval Interval)
    : IAuthorizedQuery<List<AirsonicHistoricalDataResponse>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? UnitId { get; set; }

    // NOTE - not applicable for airsonic
    public Guid? SiteId { get; set; } = null;
    public Guid? BlockId { get; set; } = null;
}

public record AirsonicHistoricalDataResponse(
    Guid Id,
    string Name,
    string Alias,
    List<double> Values,
    List<string> Timestamps);

internal sealed class GetAirsonicHistoricalQueryHandler(
    AirsonicReadRepository airsonicRepository,
    IDataContext context)
        : IQueryHandler<GetAirsonicHistoricalQuery, List<AirsonicHistoricalDataResponse>>
{
    private readonly IDataContext _context = context;
    private readonly AirsonicReadRepository _airsonicRepository = airsonicRepository;

    public async Task<ErrorOr<List<AirsonicHistoricalDataResponse>>> Handle(
        GetAirsonicHistoricalQuery request,
        CancellationToken ct)
    {
        // Get the unit data
        Unit? unit = await _context.Units
            .AsNoTracking()
            .Include(b => b.Block)
                .ThenInclude(b => b.Site)
                .ThenInclude(s => s.Customer)
            .SingleOrDefaultAsync(b => b.Id == request.UnitId, ct);

        if (unit is null)
            return UnitErrors.NotFound;

        // Extract all unique variables from the series
        List<string> uniqueVariables = request.Series
            .Select(s => s.Alias)
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Filter)))
            .Distinct()
            .ToList();

        // Query historical data from the table storage
        List<TimeSeriesData> timeSeriesData = [];
        if (request.Interval == TimeSeriesResamplingInterval.Hour)
        {
            List<StatisticsAirsonicDto> results = await _airsonicRepository.GetHistoricalHourlyDataAsync(
                unit: unit,
                uniqueTags: uniqueVariables,
                startDate: request.StartDate,
                endDate: request.EndDate,
                ct: ct);

            timeSeriesData = results.ToTimeSeriesData();
        }
        else
        {
            timeSeriesData = await _airsonicRepository.GetHistoricalDataAsync(
                unit: unit,
                uniqueVariables: uniqueVariables,
                startTime: request.StartDate,
                endTime: request.EndDate,
                ct: ct);
        }

        // Downsample the data by the requested interval
        List<TimeSeriesData> downsampledData = timeSeriesData
            .Select(ts => ts.Downsample(
                request.Interval,
                TimeSeriesDownsampling.Average))
            .ToList();

        // Create a data table from the entities
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable
            .Create(downsampledData);

        // Evaluate the expression for each chart series
        return request.Series
            .Select(x =>
            {
                // Apply the filter and calculation to the data table for the current series
                TimeSeriesData data = dataTable.TryEvaluateExpression(
                    tag: x.Alias,
                    calculation: x.Calculation,
                    filter: x.Filter);

                return new AirsonicHistoricalDataResponse(
                    Id: x.Id,
                    Name: x.Name,
                    Alias: x.Alias,
                    Values: data.Values
                        .Select(x => x.ToDoubleOrDefault())
                        .ToList(),
                    Timestamps: data.Timestamps
                        .Select(x => x.ToString("yyyy-MM-dd HH:mm:ss"))
                        .ToList());
            })
            .ToList();
    }
}
