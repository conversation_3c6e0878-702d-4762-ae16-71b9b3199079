using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.App.Modules.Airsonic.GetHistoricalAirsonicData.Services;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;
using RTP.StatusMonitor.App.Shared.Api.Airsonic;

namespace RTP.StatusMonitor.App.Modules.Airsonic;

public static class DependencyInjection
{
    public static IServiceCollection AddAirsonic(this IServiceCollection services)
    {
        services.AddTransient<AirsonicApi>();
        services.AddScoped<AirsonicReadRepository>();
        services.AddScoped<AirsonicWriteRepository>();

        services.AddScoped<IAirsonicTableQueryBuilder, AirsonicTableQueryBuilder>();

        // TODO - deprecated, refactor to remove
        services.AddScoped<IAirsonicService, AirsonicService>();

        return services;
    }
}
