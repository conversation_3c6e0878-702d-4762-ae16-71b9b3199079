namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicSummary;

public static class AirsonicAliasConstant
{
    public const string ClientConnected = "Client Connected";
    public const string PanelTemp = "Panel Temp";
    public const string AirSpeed = "Air Speed";
    public const string AirSpeedRaw = "Air Speed Raw";
    public const string AmbIntakeTemp = "Intake Temp";
    public const string AmbIntakeRH = "Intake RH";
    public const string AmbTemp = "Ambient Temp";
    public const string AmbRH = "Ambient RH";
    public const string AmbPress = "Ambient Press";
    public const string IGV = "IGV";
    public const string CurrentMessage = "Current Message Log";
    public const string VFR = "Vol Flow Rate";
    public const string MFR = "Air Mass Flow Rate";
    public const string AngleXY = "Air Flow Angle XY";
    public const string AngleYZ = "Air Flow Angle YZ";
    public const string AngleZX = "Air Flow Angle ZX";
    public const string CompPressRatio = "CompPressRatio";
    public const string InletDrop = "Inlet Press Drop";
    public const string DuctTemp = "Duct Static Temp";
    public const string SOS = "Speed of Sound";
    public const string InletDensity = "Inlet Density";
    public const string GTShaftSpeed = "GTShaftSpeed";
    public const string Disturbance = "Turbulence Combined";
    public const string DiskSpace = "Available Drive Space";
    public const string ProcessTime = "Execution Time Total";
    public const string EngineUpTime = "Engine Up Time";
    public const string LastTuned = "Last Probe Tuning";
    public const string Listen = "Listen Data";
    public const string StatusCode = "Status Code";
    public const string MaxCompFreq = "Max Comp Frequency";
    public const string MaxFreqMagnitude = "Max Freq Magnitude";
    public const string MW = "MW";
}