using System.Collections.Concurrent;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.App.Shared.Messaging;
using ErrorOr;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicSummary;
public record GetAirsonicSummaryQuery(List<Guid> UserGroups)
    : IQuery<AirsonicSummaryResponse[]>;
internal sealed class GetAirsonicSummaryHandler(
    DataContext context,
    IAirsonicService airsonicService,
    IWeatherServiceFactory weatherServiceFactory)
        : IQueryHandler<GetAirsonicSummaryQuery, AirsonicSummaryResponse[]>
{
    private readonly DataContext _context = context;
    private readonly IAirsonicService _airsonicService = airsonicService;
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;

    public async Task<ErrorOr<AirsonicSummaryResponse[]>> Handle(
        GetAirsonicSummaryQuery request,
        CancellationToken cancellationToken)
    {
        List<Unit> units = await _context.GroupPermissions
            .AsNoTracking()
            .Where(gp => request.UserGroups.Contains(gp.GroupId))
            .SelectMany(gp => gp.Site.Blocks)
            .SelectMany(block => block.Units)
            .Include(u => u.Block)
                .ThenInclude(b => b.Site)
            .Include(u => u.Block.Site.Customer)
            .ToListAsync(cancellationToken);

        ConcurrentBag<AirsonicSummaryResponse> summaries = [];
        await Parallel.ForEachAsync(units, async (unit, ct) =>
        {
            try
            {
                string table = unit.GetAirsonicHistoricalTable();
                string partitionKey = unit.GetAirsonicSnapshotPartition();

                // Get airsonic snapshot from table storage
                List<AirsonicSnapshotTableResult> snapshots = await _airsonicService.GetSnapshotAsync(
                    table: table,
                    query: $"PartitionKey eq '{partitionKey}'",
                    ct: cancellationToken);

                AirsonicSnapshot[] airsonicSnapshots = snapshots.Select(x => new AirsonicSnapshot
                (
                    Index: x.Index,
                    Timestamp: DateTime.Parse(
                    x.LocalTimestamp.Substring(0, x.LocalTimestamp.Length - 4)),
                    Tag: x.Tag,
                    Alias: x.Alias,
                    Value: x.Value,
                    Quality: x.Quality,
                    EngUnits: x.EngUnits
                )).ToArray();

                // If fail to get snapshot 
                // Then skip parallel loop iteration for this unit
                if (snapshots is null)
                    return;

                // Get weather condition for site from cached first if not expired
                WeatherConditionResult? weather = await _weatherServiceFactory
                    .Get(WeatherService.AW)
                    .GetCurrentConditionsAsync(
                        latitude: unit.Block.Site.Latitude,
                        longitude: unit.Block.Site.Longitude,
                        altitude: unit.Block.Site.Altitude,
                        ct: cancellationToken
                    );

                if (weather is null)
                    return;

                // Return the airsonic summary response based on the snapshots
                summaries.Add(airsonicSnapshots.ToAirsonicSummaryResponse(
                    customerName: unit.Block.Site.Customer.Name,
                    unitId: unit.Id,
                    siteName: unit.Block.Site.Name,
                    blockName: unit.Block.Name,
                    unitName: unit.Name,
                    timeZoneId: unit.Block.Site.TimeZone,
                    currentWeather: weather));
            }
            catch
            {
                return;
            }
        });

        return summaries
            .OrderByDescending(x => x.AirsonicStatus == "Bad")
            .ThenByDescending(x => x.AirsonicStatus == "Warning")
            .ThenBy(x => x.Site)
            .ThenBy(x => x.Block)
            .ThenBy(x => x.Unit)
            .ToArray();
    }
}
