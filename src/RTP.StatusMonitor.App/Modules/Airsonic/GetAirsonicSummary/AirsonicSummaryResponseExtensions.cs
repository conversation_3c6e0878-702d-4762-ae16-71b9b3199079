using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;
using RTP.StatusMonitor.Domain.EngUnits;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicSummary;

public static class AirsonicSummaryResponseExtensions
{
    public static AirsonicSummaryResponse ToAirsonicSummaryResponse(
        this AirsonicSnapshot[] snapshots,
        string customerName,
        Guid unitId,
        string siteName,
        string blockName,
        string unitName,
        string timeZoneId,
        WeatherConditionResult currentWeather)
    {
        // Get airspeed tag
        AirsonicSnapshot? airSpeed = snapshots
            .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AirSpeed);

        // Get connection tag and default to good if not available
        AirsonicSnapshot connection = snapshots
            .FirstOrDefault(
                tag => tag.Alias == AirsonicAliasConstant.ClientConnected,
                new AirsonicSnapshot
                (
                    Index: 0,
                    Tag: "Client Connected",
                    Alias: "Client Connected",
                    Value: "1",
                    EngUnits: string.Empty,
                    Timestamp: DateTime.Now,
                    Quality: true
                ));

        // Get a list of diagnostic results for airsonic
        List<MonitoringResponse> monitorySummary = GetMonitoringSummaryResponse(
            connectionStatus: connection.Value == "1" ? "Good" : "Bad",
            lastUpdated: airSpeed?.Timestamp,
            mfr: snapshots.FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.MFR),
            timeZoneId: timeZoneId);

        // Get airsonic status 
        string airsonicStatus = CheckAirsonicStatus(
            lastUpdated: airSpeed?.Timestamp,
            connectionStatus: connection.Value == "1" ? "Good" : "Bad",
            mfr: snapshots.FirstOrDefault(
                x => x?.Alias == AirsonicAliasConstant.MFR, null),
            timeZoneId: timeZoneId);

        Domain.EngUnits.Types.Temperature? metricTemp = currentWeather.Temp?.ToCelcius();
        Domain.EngUnits.Types.Temperature? imperialTemp = currentWeather.Temp?.ToFahrenheit();

        Domain.EngUnits.Types.Pressure? metricPressure = currentWeather.MeanSeaLevelPressure?.ToMBar();
        Domain.EngUnits.Types.Pressure? imperialPressure = currentWeather.MeanSeaLevelPressure?.ToInHg();

        // Map WeatherConditionResult to WeatherConditionDto
        WeatherConditionDto weatherResponse = new(
            LocalObservationDateTime: DateTime.Parse(
                currentWeather.LocalTime),
            WeatherText: currentWeather.Description,
            WeatherIcon: currentWeather.WeatherIcon,
            Temperature: new Temperature(
                Metric: new Metric(
                    Value: metricTemp?.Value ?? 0,
                    Unit: metricTemp?.EngUnits.Name ?? string.Empty,
                    UnitType: 0,
                    Phrase: string.Empty),
                Imperial: new Imperial(
                    Value: imperialTemp?.Value ?? 0,
                    Unit: imperialTemp?.EngUnits.Name ?? string.Empty,
                    UnitType: 0,
                    Phrase: string.Empty)),
            RelativeHumidity: currentWeather.RelativeHumidity,
            Pressure: new Pressure(
                Metric: new Metric(
                    Value: metricPressure?.Value ?? 0,
                    Unit: metricPressure?.EngUnits.Name ?? string.Empty,
                    UnitType: 0,
                    Phrase: string.Empty),
                Imperial: new Imperial(
                    Value: imperialPressure?.Value ?? 0,
                    Unit: imperialPressure?.EngUnits.Name ?? string.Empty,
                    UnitType: 0,
                    Phrase: string.Empty)));

        return new AirsonicSummaryResponse
        {
            Id = unitId,
            Customer = customerName,
            Site = siteName,
            Block = blockName,
            Unit = unitName,
            CurrentWeather = weatherResponse,
            PanelTemp = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.PanelTemp),
            AirSpeed = airSpeed,
            AirSpeedRaw = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AirSpeedRaw),
            AmbIntakeTemp = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AmbIntakeTemp),
            AmbIntakeRH = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AmbIntakeRH),
            AmbTemp = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AmbTemp),
            AmbRH = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AmbRH),
            AmbPress = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AmbPress),
            IGV = snapshots.FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.IGV),
            AirsonicStatus = airsonicStatus,
            MonitorSummary = monitorySummary,
            CurrentMessage = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.CurrentMessage),
            // NOTE - Use airspeed as metric to track last updated time
            LastUpdated = airSpeed?.Timestamp,
            VFR = snapshots.FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.VFR),
            MFR = snapshots.FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.MFR),
            AngleXY = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AngleXY),
            AngleYZ = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AngleYZ),
            AngleZX = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.AngleZX),
            CompPressRatio = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.CompPressRatio),
            InletDrop = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.InletDrop),
            DuctTemp = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.DuctTemp),
            SOS = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.SOS),
            InletDensity = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.InletDensity),
            ShaftSpeed = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.GTShaftSpeed),
            Disturbance = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.Disturbance),
            DiskSpace = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.DiskSpace),
            ProcessTime = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.ProcessTime),
            EngineUpTime = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.EngineUpTime),
            LastTuned = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.LastTuned),
            Listen = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.Listen),
            StatusCode = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.StatusCode),
            MaxCompFreq = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.MaxCompFreq),
            MaxFreqMagnitude = snapshots
                .FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.MaxFreqMagnitude),
            MW = snapshots.FirstOrDefault(x => x?.Alias == AirsonicAliasConstant.MW),
        };
    }

    /// <summary>
    /// Given the connection status, the last updated time, and MFR tag
    /// Then return a list of diagnostic results for airsonic
    /// 
    /// 1. Connection is not ok -> bad
    /// 2. No last updated time or updated more than a minute ago -> warning
    /// 3. no MFR or MFR is bad -> warning
    /// Otherwise, good
    private static List<MonitoringResponse> GetMonitoringSummaryResponse(
        string connectionStatus,
        DateTime? lastUpdated,
        AirsonicSnapshot? mfr,
        string timeZoneId)
    {
        // 1. Connection is not ok -> bad
        List<MonitoringResponse> results = new()
        { CheckAirsonicConnection(connectionStatus) };

        // 2. no last update time -> warning
        if (lastUpdated is null)
        {
            results.Add(new MonitoringResponse(
                Status: "Warning",
                Message: "No last updated time"));
        }
        // or last updated time is more than a minute ago -> warning
        else
        {
            // Convert last updated time to utc 
            DateTime lastUpdatedUTC = TimeZoneInfo.ConvertTimeToUtc(
                lastUpdated.Value,
                TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));

            // Get current utc time
            DateTime currentUtcTime = DateTime.Now.ToUniversalTime();

            // Check if last updated is more than a minute behind current utc time
            DateTime? timeToCheck = lastUpdatedUTC.AddMinutes(1);
            bool isBehind = timeToCheck < currentUtcTime;
            // warning if MFR is not good 
            if (isBehind)
            {
                results.Add(new MonitoringResponse(
                    Status: "Warning",
                    Message: "Data not updated for the last minute"));
            }
            else
            {
                results.Add(new MonitoringResponse(
                    Status: "Good",
                    Message: "Data up to date"));
            }
        }

        // 3. no MFR info => warning
        if (mfr is null)
        {
            results.Add(new MonitoringResponse(
                Status: "Warning",
                Message: "Mass Flow Rate data not available"));
        }
        else
        {
            if (mfr.Quality == false)
            {
                results.Add(new MonitoringResponse(
                    Status: "Warning",
                    Message: "Mass Flow Rate data is bad"));
            }
            else
            {
                results.Add(new MonitoringResponse(
                    Status: "Good",
                    Message: "Mass Flow Rate data is good"));
            }
        }

        return results;
    }

    /// <summary>
    /// If connection is bad -> Bad
    /// Otherwise, Good
    /// </summary>
    /// 
    /// <param name="connection">The connection status of Airsonic either "Bad" or "Good"</param>
    /// 
    /// <returns></returns>
    private static MonitoringResponse CheckAirsonicConnection(string connection)
        => connection == "Bad"
            ? new(Status: "Bad", Message: "Connection is bad")
            : new(Status: "Good", Message: "Connection is good");

    /// <summary>
    /// Given the connection status, the last updated time, and MFR tag
    /// 
    /// If connection is bad -> Bad
    /// If MFR is bad or last update is more than 1 mins behind current time -> Warning
    /// Else -> Good
    /// </summary>
    private static string CheckAirsonicStatus(
        DateTime? lastUpdated,
        string connectionStatus,
        AirsonicSnapshot? mfr,
        string timeZoneId)
    {
        // No last update time -> warning
        if (lastUpdated is null)
            return "Warning";

        // Connection is bad -> then status is bad
        if (connectionStatus == "Bad")
            return "Bad";

        // No MFR info => warning
        if (mfr is null)
            return "Warning";

        // Convert last updated time to utc 
        var lastUpdatedUTC = TimeZoneInfo.ConvertTimeToUtc(
            lastUpdated.Value,
            TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));

        // Check if airsonic snapshot has been updated within the last minute
        var isBehind = lastUpdatedUTC.AddMinutes(1) < DateTime.Now.ToUniversalTime();

        // Warning if MFR is not good or last updated time is more than a minute behind local time
        if (isBehind || mfr.Quality == false)
            return "Warning";

        // otherwise, status is good
        return "Good";
    }
}
