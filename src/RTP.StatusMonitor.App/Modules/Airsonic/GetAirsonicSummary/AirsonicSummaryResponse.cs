using RTP.StatusMonitor.App.Modules.Weather.Shared.Services.AccuWeather;

namespace RTP.StatusMonitor.App.Modules.Airsonic.GetAirsonicSummary;
public record MonitoringResponse(string Status, string Message);

public record AirsonicSnapshot(
    double Index,
    DateTime Timestamp,
    string Tag,
    string Alias,
    string EngUnits,
    string Value,
    bool Quality
);

public record WeatherConditionDto
(
    DateTime LocalObservationDateTime,
    string WeatherText,
    int WeatherIcon,
    Temperature Temperature,
    int RelativeHumidity,
    Pressure Pressure
);

public record AirsonicSummaryResponse
{
    public Guid Id { get; set; }
    public string Customer { get; set; } = string.Empty;
    public string Site { get; set; } = string.Empty;
    public string Block { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    // The status of airsonic 
    public string AirsonicStatus { get; set; } = string.Empty;
    public List<MonitoringResponse> MonitorSummary { get; set; } = new();
    public AirsonicSnapshot? AirSpeed { get; set; }
    public AirsonicSnapshot? AirSpeedRaw { get; set; }
    public AirsonicSnapshot? AmbTemp { get; set; }
    public AirsonicSnapshot? AmbRH { get; set; }
    public AirsonicSnapshot? AmbIntakeTemp { get; set; }
    public AirsonicSnapshot? AmbIntakeRH { get; set; }
    public AirsonicSnapshot? AmbPress { get; set; }
    public AirsonicSnapshot? AngleXY { get; set; }
    public AirsonicSnapshot? AngleYZ { get; set; }
    public AirsonicSnapshot? AngleZX { get; set; }
    public WeatherConditionDto CurrentWeather { get; set; } = null!;
    public AirsonicSnapshot? CurrentMessage { get; set; }
    // use air speed as metric to track last updated time
    public DateTime? LastUpdated { get; set; }
    public AirsonicSnapshot? VFR { get; set; }
    public AirsonicSnapshot? MFR { get; set; }
    public AirsonicSnapshot? DuctTemp { get; set; }
    public AirsonicSnapshot? DuctPress { get; set; }
    public AirsonicSnapshot? InletDrop { get; set; }
    public AirsonicSnapshot? SOS { get; set; }
    public AirsonicSnapshot? InletDensity { get; set; }
    public AirsonicSnapshot? PanelTemp { get; set; }
    public AirsonicSnapshot? IGV { get; set; }
    public AirsonicSnapshot? ShaftSpeed { get; set; }
    public AirsonicSnapshot? CompPressRatio { get; set; }
    public AirsonicSnapshot? MaxFreqMagnitude { get; set; }
    public AirsonicSnapshot? MW { get; set; }
    public AirsonicSnapshot? Disturbance { get; set; }
    public AirsonicSnapshot? DiskSpace { get; set; }
    public AirsonicSnapshot? ProcessTime { get; set; }
    public AirsonicSnapshot? EngineUpTime { get; set; }
    public AirsonicSnapshot? MaxCompFreq { get; set; }
    public AirsonicSnapshot? LastTuned { get; set; }
    public AirsonicSnapshot? Listen { get; set; }
    public AirsonicSnapshot? StatusCode { get; set; }
}

