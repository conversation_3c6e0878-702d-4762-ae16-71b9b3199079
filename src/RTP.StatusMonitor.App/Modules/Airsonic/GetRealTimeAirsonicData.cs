using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.App.Shared.Errors;
using RTP.StatusMonitor.Domain.Units;
using System.Xml.Serialization;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Services;

namespace RTP.StatusMonitor.App.Modules.Airsonic;
public record GetRealTimeAirsonicDataQuery(
    Guid UnitId,
    IEnumerable<Guid> UserGroups) : IQuery<AirsonicSnapshotResponse[]>;

[XmlRoot(ElementName = "ArrayOfVariable")]
public record AirsonicSnapshotListResponse
{
    [XmlElement(ElementName = "Variable")]
    public AirsonicSnapshotResponse[] Variable { get; set; } = [];

    [XmlAttribute(AttributeName = "xsi")]
    public string Xsi { get; set; } = string.Empty;

    [XmlAttribute(AttributeName = "xsd")]
    public string Xsd { get; set; } = string.Empty;

    [XmlText]
    public string Text { get; set; } = string.Empty;
}

public record AirsonicSnapshotResponse
(
    double Index,
    DateTime Timestamp,
    string Tag,
    string Alias,
    string EngUnits,
    string Value,
    bool Quality
);

public class GetRealTimeAirsonicDataHandler(
  IDataContext context,
  IAirsonicService airsonicService)
    : IQueryHandler<GetRealTimeAirsonicDataQuery, AirsonicSnapshotResponse[]>
{
    private readonly IDataContext _context = context;

    private readonly IAirsonicService _airsonicService = airsonicService;

    public async Task<ErrorOr<AirsonicSnapshotResponse[]>> Handle(
        GetRealTimeAirsonicDataQuery request,
        CancellationToken ct)
    {
        // Find the unit by id
        var unit = await _context.Units
          .Include(x => x.Block)
            .ThenInclude(x => x.Site)
              .ThenInclude(x => x.Customer)
          .Include(x => x.Block)
            .ThenInclude(x => x.Site)
              .ThenInclude(x => x.GroupPermissions)
          .FirstOrDefaultAsync(x => x.Id == request.UnitId, ct);

        // Return NotFound error when unit is not found
        if (unit is null)
            return UnitErrors.NotFound;

        // Check group permissions for access and return Unauthorized error when user is not authorized
        if (unit.Block.Site.HasAccessToSite(request.UserGroups.ToList()) is false)
        {
            return SiteErrors.Unauthorized;
        }

        var table = unit.GetAirsonicHistoricalTable();

        var partitionKey = unit.GetAirsonicSnapshotPartition();

        // Get latest snapshot for the site
        var data = await _airsonicService.GetSnapshotAsync(
            table: table,
            query: $"PartitionKey eq '{partitionKey}'",
            ct: ct
        );

        // Return an empty data if no snapshot is found
        if (data is null)
        { return Array.Empty<AirsonicSnapshotResponse>(); }

        return data.Select(x => new AirsonicSnapshotResponse
        (
            Tag: x.Tag,
            Alias: x.RowKey,
            Value: x.Value,
            Quality: x.Quality,
            Index: x.Index,
            EngUnits: x.EngUnits,
            Timestamp: DateTime.Parse(
            x.LocalTimestamp.Substring(0, x.LocalTimestamp.Length - 4))
        )).ToArray();
    }
}
