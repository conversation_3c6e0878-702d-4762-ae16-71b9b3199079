using System.Data;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Dto;
using RTP.StatusMonitor.App.Modules.Airsonic.Shared.Repository;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Airsonic;

public record GetRealTimeAirsonicQueryVersion2(List<AirsonicSeries> Series)
    : IAuthorizedQuery<List<AirsonicSnapshotResponseVersion2>>
{
    public bool IsAuthorizationEnabled { get; set; } = true;
    public List<Guid> UserGroupsId { get; set; } = [];
    public Guid? UnitId { get; set; }

    // NOTE - only unit id is relevant for this query
    public Guid? SiteId { get; set; } = null;
    public Guid? BlockId { get; set; } = null;
}

public record AirsonicSnapshotResponseVersion2(
    Guid Id,
    string Name,
    string Timestamp,
    double Value);

internal sealed class GetRealTimeAirsonicQueryHandlerVersion2(
    IDataContext context,
    AirsonicReadRepository airsonicReadRepository)
        : IQueryHandler<GetRealTimeAirsonicQueryVersion2, List<AirsonicSnapshotResponseVersion2>>
{
    private readonly AirsonicReadRepository _airsonicReadRepository = airsonicReadRepository;
    private readonly IDataContext _context = context;

    public async Task<ErrorOr<List<AirsonicSnapshotResponseVersion2>>> Handle(
        GetRealTimeAirsonicQueryVersion2 request,
        CancellationToken ct)
    {
        // Find the unit by id
        Unit? unit = await _context.Units
            .Include(x => x.Block)
            .ThenInclude(x => x.Site)
                .ThenInclude(x => x.Customer)
            .SingleOrDefaultAsync(x => x.Id == request.UnitId, ct);

        // When the unit is not found
        // Then we return the error not found
        if (unit is null)
            return UnitErrors.NotFound;

        // Extract all unique variables from the series
        List<string> uniqueVariables = request.Series
            .Select(s => s.Alias)
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Calculation)))
            .Concat(request.Series
                .SelectMany(s => ExpressionParser.Parse(s.Filter)))
            .Distinct()
            .ToList();

        // Get the latest snapshot for the unit
        List<AirsonicHistoricalWriteSchemaDto> entities = _airsonicReadRepository.GetLatestData(
            unit: unit,
            aliases: uniqueVariables);

        // Convert the entities to time series data for analytics
        List<TimeSeriesData> timeSeriesData = entities
            .ToTimeSeriesData(TimeZoneInfo.FindSystemTimeZoneById(unit.Block.Site.TimeZone));

        // Create the data table and load time series data for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(timeSeriesData);

        // Process data for each series
        List<AirsonicSnapshotResponseVersion2> responses = [];
        foreach (AirsonicSeries series in request.Series)
        {
            TimeSeriesData data = dataTable.TryEvaluateExpression(
                tag: series.Alias,
                calculation: series.Calculation,
                filter: series.Filter);

            // Make sure there is data after analytics
            if (!data.Timestamps.Any())
                continue;

            // If there is real time data => should only be 1 value and timestamp
            responses.Add(new AirsonicSnapshotResponseVersion2(
                Id: series.Id,
                Name: series.Name,
                Value: data.Values
                    .Select(x => double.TryParse(
                        x.ToString(), out double value)
                        ? value
                        : double.NaN)
                    .First(),
                Timestamp: data.Timestamps
                    .First().ToString("yyyy-MM-dd HH:mm:ss")));
        }

        return responses;
    }
}
