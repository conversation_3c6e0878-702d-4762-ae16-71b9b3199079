using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Repository.Weather;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Repository.InputData;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Persistence;
using System.Collections.Concurrent;

namespace RTP.StatusMonitor.App.Modules.Monitoring.MonitorStatus;

internal sealed class MonitorStatusQueryHandler
    : IRequestHandler<MonitorStatusQuery, ErrorOr<List<MonitorStatusResponse>>>
{
    private readonly TimeSpan INPUT_DATA_STALE_TIME = TimeSpan.FromMinutes(5);
    private readonly TimeSpan WEATHER_FILE_STALE_TIME = TimeSpan.FromMinutes(60);
    private readonly IDataContext _context;
    private readonly WeatherReadRepository _weatherBiasReadRepository;
    private readonly InputDataRepository _inputDataRepository;
    private readonly IDateTimeProvider _dateTimeProvider;

    public MonitorStatusQueryHandler(
        IDataContext context,
        WeatherReadRepository weatherBiasReadRepository,
        InputDataRepository inputDataRepository,
        IDateTimeProvider dateTimeProvider)
    {
        _context = context;
        _weatherBiasReadRepository = weatherBiasReadRepository;
        _inputDataRepository = inputDataRepository;
        _dateTimeProvider = dateTimeProvider;
    }

    public async Task<ErrorOr<List<MonitorStatusResponse>>> Handle(
        MonitorStatusQuery request,
        CancellationToken ct)
    {
        // Query data from all units
        List<Domain.Units.Unit> units = await _context
            .Units
            .AsNoTracking()
            .Include(u => u.Block)
            .ThenInclude(b => b.Site)
                .ThenInclude(s => s.Customer)
            .ToListAsync(ct);

        // Check input data and weather data for each unit
        ConcurrentBag<MonitorStatusResponse> results = new();
        List<Task> tasks = units
            .Where(u => u.Block.Site.Customer.Name != "Demo")
            .Select(unit => Task.Run(async () =>
            {
                results.Add(new MonitorStatusResponse(
                    UnitId: unit.Id,
                    Customer: unit.Block.Site.Customer.Name,
                    Site: unit.Block.Site.Name,
                    Block: unit.Block.Name,
                    Unit: unit.Name,
                    InputData: CheckInputData(unit.Block),
                    CurrentWeather: await CheckWeatherData(unit),
                    WeatherForecast: CheckWeatherFile(unit, ct)));
            })).ToList();

        await Task.WhenAll(tasks);

        // Sort the results by site, block, and unit name
        return results.OrderBy(x => x.Site)
            .ThenBy(x => x.Block)
            .ThenBy(x => x.Unit)
            .ToList();
    }

    /// <summary>
    /// Check if the weather forecast file is updated (considered bad if not updated in 60 minutes)
    /// </summary>
    /// 
    /// <param name="unit"></param>
    /// 
    /// <returns></returns>
    private Details CheckWeatherFile(
        Domain.Units.Unit unit,
        CancellationToken ct)
    {
        DateTimeOffset? lastModifed = _weatherBiasReadRepository.GetWeatherFileLastModifiedTime(unit, ct);

        if (lastModifed is null)
        {
            return new(
                Status: false,
                LastUpdated: null,
                Message: "Weather file is missing");
        }
        else
        {
            return _dateTimeProvider.UtcNow - lastModifed > WEATHER_FILE_STALE_TIME
                ? new(
                    Status: false,
                    LastUpdated: SiteLocalTime.Create(unit.Block.Site, lastModifed.Value.DateTime).Value,
                    Message: "Weather file is stale")
                : new(
                    Status: true,
                    LastUpdated: SiteLocalTime.Create(unit.Block.Site, lastModifed.Value.DateTime).Value,
                    Message: "Weather file is updated");
        }
    }

    /// <summary>
    /// This method will query the input data snapshot from
    /// table storage and check the timestamp of the first record
    /// to see if data has been stale
    /// </summary>
    private Details CheckInputData(Block block)
    {
        // Query input data for the unit
        List<InputDataDto> inputEntities = _inputDataRepository.GetInputData(block);
        InputDataDto? firstItem = inputEntities.FirstOrDefault();

        if (firstItem is null || firstItem.Timestamp is null)
        {
            return new(
                Status: false,
                LastUpdated: null,
                Message: "Input data is frozen");
        }

        // Check if input data is frozen
        DateTime? lastUpdated = DateTime.TryParse(
            firstItem.LocalTimestamp, out DateTime localTime)
            ? localTime : null;
        return (DateTimeOffset)_dateTimeProvider.UtcNow - firstItem.Timestamp > INPUT_DATA_STALE_TIME
            ? new(
                Status: false,
                LastUpdated: lastUpdated,
                Message: "Input data is frozen")
            : new(
                Status: true,
                LastUpdated: lastUpdated,
                Message: "Input data is updated");
    }

    /// <summary>
    /// This method will check the current weather data for each unit across
    /// all partitions to see if data has been stale
    /// </summary>
    private async Task<List<Details>> CheckWeatherData(Domain.Units.Unit unit)
    {
        ConcurrentBag<Details> statuses = new();
        List<Task> tasks = Enum.GetValues<WeatherService>()
        // NOTE - currently only AW is used
        .Where(weatherApi => weatherApi == WeatherService.AW)
        .Select(weatherApi =>
            Enum.GetValues<WeatherTagSource>().Select(tagSrc => Task.Run(() =>
                {
                    WeatherBiasDto? weatherBias = _weatherBiasReadRepository.GetLatestWeatherBias(
                        unit: unit,
                        weatherService: weatherApi,
                        tagSource: tagSrc);

                    if (weatherBias is null)
                    {
                        statuses.Add(new Details(
                            Status: false,
                            LastUpdated: null,
                            Message: $"{weatherApi} is stale"));
                    }
                    else
                    {
                        statuses.Add(new Details(
                            Status: true,
                            LastUpdated: DateTime.Parse(weatherBias.LocalTimestamp),
                            Message: $"{weatherApi} is updated"));
                    }
                })))
            .SelectMany(x => x)
            .ToList();

        await Task.WhenAll(tasks);

        return statuses.ToList();
    }
}
