using Azure.Data.Tables;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Storage;
using RTP.StatusMonitor.Domain.Units;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Monitoring.GetInputData;

internal sealed class GetInputDataQueryHandler
  : IRequestHandler<GetInputDataQuery, ErrorOr<List<InputDataResponse>>>
{
    private readonly IDataContext _dataContext;
    private readonly ITableStorage _tableStorage;

    public GetInputDataQueryHandler(
      IDataContext dataContext,
      ITableStorage tableStorage)
    {
        _dataContext = dataContext;
        _tableStorage = tableStorage;
    }

    public async Task<ErrorOr<List<InputDataResponse>>> Handle(
      GetInputDataQuery request,
      CancellationToken ct)
    {
        // Query unit data
        var unit = await _dataContext.Units
          .AsNoTracking()
          .Where(unit => unit.Id == request.UnitId)
          .Include(unit => unit.Block)
            .ThenInclude(block => block.Site)
              .ThenInclude(site => site.Customer)
          .FirstOrDefaultAsync(ct);

        if (unit is null)
            return UnitErrors.NotFound;

        // Query input data from table storage
        string tableName = $"{unit.Block.Site.Customer.Name.ToLower()}Input";

        string partitionKey = unit.Block.GetInputDataPartitionKey();

        string query = $"PartitionKey eq '{partitionKey}'";

        List<TableEntity> inputEntities = await _tableStorage.QueryEntitiesAsync(
          tableName: tableName,
          query: query,
          ct);

        // Map the input data (type TableEntity) to the response type
        return inputEntities.Select(item => new InputDataResponse(
          Tag: item["Tag"].ToString() ?? "",
          Value: item["Value"].ToString() ?? "",
          Timestamp: item["Timestamp"].ToString() ?? "",
          LocalTimestamp: item["LocalTimestamp"].ToString() ?? "",
          Quality: item["Quality"].ToString() == "Good"
        )).ToList();
    }
}
