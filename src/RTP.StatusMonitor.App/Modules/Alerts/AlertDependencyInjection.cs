using Microsoft.Extensions.DependencyInjection;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Templates;

namespace RTP.StatusMonitor.App.Modules.Alerts;

public static class AlertDependencyInjection
{
    public static IServiceCollection AddAlertServices(this IServiceCollection services)
    {
        services.AddTransient<ForecastAlertTemplateRenderer>();
        services.AddTransient<ReportProcessor>();
        // services.AddTransient<AlertProcessor>();
        services.AddTransient<EquipmentContentRenderer>();
        services.AddTransient<WeatherContentRenderer>();
        services.AddTransient<AlertRenderer>();

        return services;
    }
}
