using ErrorOr;
using Dapper;
using RTP.StatusMonitor.App.Modules.Alerts.GetAlertBeta;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence.Data;
using RTP.StatusMonitor.Persistence.Models;
using System.Data;
using Newtonsoft.Json;

namespace RTP.StatusMonitor.App.Modules.Alerts.GetAlertSummariesBeta;

public class AlertQueryResult
{
    public Guid AlertId { get; init; }
    public string Name { get; init; } = string.Empty;
    public bool IsEnabled { get; init; }
    public string SiteDisplayName { get; init; } = string.Empty;
    public string AlertTypes { get; init; } = string.Empty;
    public string Recipients { get; init; } = string.Empty;
    public AlertTriggerType TriggerType { get; init; }
    public int ExpirationTimeInMinutes { get; init; }
    public string CriteriaCombinationRule { get; init; } = string.Empty;
    public string RunTimes { get; init; } = string.Empty;
    public string DaysOfWeek { get; init; } = string.Empty;
    public string DaysOfMonth { get; init; } = string.Empty;
    public string DaysOfYear { get; init; } = string.Empty;
    public DateTime? LastSentUtc { get; init; }
    public Guid SiteId { get; init; }
}

public class GetAlertBetaSummariesQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    : IQueryHandler<GetAlertBetaSummariesQuery, List<AlertBetaSummaryResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory = sqlConnectionFactory;

    public async Task<ErrorOr<List<AlertBetaSummaryResponse>>> Handle(
        GetAlertBetaSummariesQuery request,
        CancellationToken cancellationToken)
    {
        const string sql = @"
            SELECT 
                a.AlertId,
                a.Name,
                a.IsEnabled,
                CONCAT(c.Name, '-', s.Name) as SiteDisplayName,
                a.AlertTypes,
                a.Recipients,
                a.TriggerType,
                a.ExpirationTimeInMinutes,
                a.CriteriaCombinationRule,
                a.RunTimes,
                a.DaysOfWeek,
                a.DaysOfMonth,
                a.DaysOfYear,
                a.LastSentUtc,
                a.SiteId
            FROM AlertsBeta a
            INNER JOIN Sites s ON a.SiteId = s.Id
            INNER JOIN Customers c ON s.CustomerId = c.Id";

        using IDbConnection connection = _sqlConnectionFactory.CreateConnection();
        IEnumerable<AlertQueryResult> alerts = await connection.QueryAsync<AlertQueryResult>(sql);

        return alerts.Select(alert => new AlertBetaSummaryResponse(
            AlertId: alert.AlertId,
            Name: alert.Name,
            IsEnabled: alert.IsEnabled,
            Trigger: alert.TriggerType switch
            {
                AlertTriggerType.Criteria => new CriteriaTriggerResponse(
                    alert.ExpirationTimeInMinutes, [], alert.CriteriaCombinationRule),
                AlertTriggerType.Daily => new DailyScheduleTriggerResponse(
                    alert.ExpirationTimeInMinutes,
                    JsonConvert.DeserializeObject<List<TimeOnly>>(alert.RunTimes) ?? []),
                AlertTriggerType.Weekly => new WeeklyScheduleTriggerResponse(
                    alert.ExpirationTimeInMinutes,
                    JsonConvert.DeserializeObject<List<TimeOnly>>(alert.RunTimes) ?? [],
                    JsonConvert.DeserializeObject<List<DayOfWeek>>(alert.DaysOfWeek)?.ConvertAll(d => d.ToString()) ?? []),
                AlertTriggerType.Monthly => new MonthlyScheduleTriggerResponse(
                    alert.ExpirationTimeInMinutes,
                    JsonConvert.DeserializeObject<List<TimeOnly>>(alert.RunTimes) ?? [],
                    JsonConvert.DeserializeObject<List<int>>(alert.DaysOfMonth) ?? []),
                AlertTriggerType.Yearly => new YearlyScheduleTriggerResponse(
                    alert.ExpirationTimeInMinutes,
                    JsonConvert.DeserializeObject<List<TimeOnly>>(alert.RunTimes) ?? [],
                    JsonConvert.DeserializeObject<List<int>>(alert.DaysOfYear) ?? []),
                _ => throw new InvalidOperationException($"Trigger type {alert.TriggerType} not supported")
            },
            SiteDisplayName: alert.SiteDisplayName,
            AlertTypes: JsonConvert.DeserializeObject<List<string>>(alert.AlertTypes) ?? [],
            Recipients: JsonConvert.DeserializeObject<List<string>>(alert.Recipients) ?? [],
            LastSentUtc: alert.LastSentUtc?.ToString("yyyy-MM-dd HH:mm") ?? string.Empty,
            SiteId: alert.SiteId))
        .ToList();
    }
}
