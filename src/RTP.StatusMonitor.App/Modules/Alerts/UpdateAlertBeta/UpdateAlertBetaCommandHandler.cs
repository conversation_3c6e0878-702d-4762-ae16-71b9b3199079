using MediatR;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;

namespace RTP.StatusMonitor.App.Modules.Alerts.UpdateAlertBeta;

internal sealed class UpdateAlertBetaCommandHandler(DataContext dataContext)
        : ICommandHandler<UpdateAlertBetaCommand>
{
    private readonly DataContext _context = dataContext;

    public async Task<ErrorOr<Unit>> Handle(
        UpdateAlertBetaCommand request, CancellationToken ct = default)
    {
        // Query the existing alert from db
        AlertModel? existingAlertModel = await _context.AlertModels
            .Include(a => a.Contents)
            .Include(a => a.Criterias)
            .SingleOrDefaultAsync(a => a.AlertId == request.AlertId, cancellationToken: ct);
        if (existingAlertModel is null)
        {
            return AlertErrors.NotFound;
        }

        AlertBeta existingAlert = existingAlertModel.ToDomain();
        // todo - Check if the site exists

        AlertBeta updatedAlert = new(
            id: request.AlertId,
            name: request.UpdateAlert.Name ?? existingAlert.Name,
            description: request.UpdateAlert.Description ?? existingAlert.Description,
            isEnabled: request.UpdateAlert.IsEnabled ?? existingAlert.IsEnabled,
            trigger: request.UpdateAlert.Trigger is not null ? request.UpdateAlert.Trigger switch
            {
                CriteriaTriggerCommand criteria => new CriteriaTrigger(
                    criteria.ExpirationTimeInMinutes,
                    [.. criteria.Criterias.Select(c => new AlertCriteria(Guid.NewGuid(), c.BlockId, new(c.Filter)))],
                    Enum.Parse<CombinationRule>(criteria.CombinationRule)),
                DailyScheduleTriggerCommand daily => new DailyFrequency(
                    daily.ExpirationTimeInMinutes,
                    daily.RunTimes.ConvertAll(TimeOnly.Parse)),
                WeeklyScheduleTriggerCommand weekly => new WeeklyFrequency(
                    weekly.ExpirationTimeInMinutes,
                    weekly.RunTimes.ConvertAll(TimeOnly.Parse),
                    weekly.DaysOfWeek.ConvertAll(Enum.Parse<DayOfWeek>)),
                MonthlyScheduleTriggerCommand monthly => new MonthlyFrequency(
                    monthly.ExpirationTimeInMinutes,
                    monthly.RunTimes.ConvertAll(TimeOnly.Parse),
                    monthly.DaysOfMonth),
                YearlyScheduleTriggerCommand yearly => new YearlyFrequency(
                    yearly.ExpirationTimeInMinutes,
                    yearly.RunTimes.ConvertAll(TimeOnly.Parse),
                    yearly.DaysOfYear),
                _ => throw new InvalidOperationException($"Alert trigger {request.UpdateAlert.Trigger?.GetType().Name} not supported")
            } : existingAlert.Trigger,
            title: request.UpdateAlert.Title ?? existingAlert.Title,
            alertTypes: request.UpdateAlert.AlertTypes is not null
                ? [.. request.UpdateAlert.AlertTypes.Select(Enum.Parse<AlertType>)]
                : [.. existingAlert.AlertTypes],
            recipients: request.UpdateAlert.Recipients is not null
                ? request.UpdateAlert.Recipients
                .ConvertAll(EmailAddress.From)
                .Where(r => r is not null)
                .ToList()!
                : existingAlert.Recipients!,
            contents: request.UpdateAlert.Contents is not null
                ? [.. request.UpdateAlert.Contents
                    .Select<AlertContentCommand, AlertContent>(c => c switch
                    {
                        AlertNoteContentCommand note => new AlertNoteContent(Guid.NewGuid(), note.Note),
                        AlertContactContentCommand contact => new AlertContactContent(
                            Guid.NewGuid(), contact.Contact),
                        AlertReportContentCommand report => new AlertReportContent(Guid.NewGuid(), report.Reports),
                        AlertEquipmentContentCommand equipment => new AlertEquipmentContent(
                            Guid.NewGuid(),
                            equipment.IncludeAvailability,
                            equipment.IncludeConstraint,
                            equipment.NumberOfEventsToInclude),
                        AlertWeatherContentCommand weather => new AlertWeatherContent(
                            Guid.NewGuid(),
                            weather.SiteId,
                            Enum.Parse<WeatherService>(weather.WeatherService),
                            Enum.Parse<ForecastInterval>(weather.ForecastInterval),
                            weather.NumberOfDaysForecast),
                        AlertSqlContentCommand sql => new AlertSqlContent(Guid.NewGuid(), sql.Query),
                        _ => throw new InvalidOperationException($"Note content {c.GetType().Name} not supported")
                    })]
                : [.. existingAlert.AlertContents],
            attachments: request.UpdateAlert.Attachments is not null
                ? request.UpdateAlert.Attachments.ConvertAll(a => new AlertAttachment(a))
                : [.. existingAlert.Attachments],
            siteId: request.UpdateAlert.SiteId ?? existingAlert.SiteId,
            lastSentUtc: existingAlert.LastSentUtc);

        // Replace the current alert with the updated one
        _context.AlertModels.Remove(existingAlertModel);

        _context.AlertModels.Add(updatedAlert.ToPersistence());

        await _context.SaveChangesAsync(ct);

        return Unit.Value;
    }
}
