using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;
using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Alerts.UpdateAlertBeta;

public record UpdateAlertBetaCommand(
    Guid AlertId,
    AlertBetaUpdateRequest UpdateAlert) : ICommand;

public record AlertBetaUpdateRequest(
    string? Name,
    string? Description,
    bool? IsEnabled,
    AlertTriggerCommand? Trigger,
    string? Title,
    List<string>? AlertTypes,
    List<string>? Recipients,
    List<AlertContentCommand>? Contents,
    List<Guid>? Attachments,
    Guid? SiteId);
