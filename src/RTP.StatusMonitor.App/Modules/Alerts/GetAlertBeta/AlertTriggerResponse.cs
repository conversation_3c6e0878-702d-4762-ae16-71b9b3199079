using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.GetAlertBeta;

public abstract record AlertTriggerResponse(string Type);
public record CriteriaTriggerResponse(
    int ExpirationTimeInMinutes,
    List<AlertCriteriaResponse> Criterias,
    string CombinationRule)
    : AlertTriggerResponse(AlertTriggerType.Criteria.ToString());

public record AlertCriteriaResponse(Guid BlockId, string Filter);

public record DailyScheduleTriggerResponse(int ExpirationTimeInMinutes, List<TimeOnly> RunTimes)
    : AlertTriggerResponse(AlertTriggerType.Daily.ToString());

public record WeeklyScheduleTriggerResponse(
    int ExpirationTimeInMinutes,
    List<TimeOnly> RunTimes,
    List<string> DaysOfWeek)
    : AlertTriggerResponse(AlertTriggerType.Weekly.ToString());

public record MonthlyScheduleTriggerResponse(
    int ExpirationTimeInMinutes,
    List<TimeOnly> RunTimes,
    List<int> DaysOfMonth)
    : AlertTriggerResponse(AlertTriggerType.Monthly.ToString());

public record YearlyScheduleTriggerResponse(
    int ExpirationTimeInMinutes,
    List<TimeOnly> RunTimes,
    List<int> DaysOfYear)
    : AlertTriggerResponse(AlertTriggerType.Yearly.ToString());

