using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.GetAlertBeta;

public abstract record AlertContentResponse(Guid AlertContentId, string Type);
public record AlertNoteReponse(Guid AlertContentId, string Note) : AlertContentResponse(AlertContentId, AlertContentType.Note.ToString());
public record AlertContactResponse(Guid AlertContentId, string Contact) : AlertContentResponse(AlertContentId, AlertContentType.Contact.ToString());
public record AlertReportReponse(Guid AlertContentId, List<Guid> Reports) : AlertContentResponse(AlertContentId, AlertContentType.Report.ToString());
public record AlertEquipmentResponse(
    Guid AlertContentId,
    bool IncludeAvailability,
    bool IncludeConstraint,
    int NumberOfEventsToInclude) : AlertContentResponse(AlertContentId, AlertContentType.Equipment.ToString());

public record AlertWeatherResponse(Guid AlertContentId, Guid SiteId, string WeatherService, string ForecastInterval, int NumberOfDaysForecast)
    : AlertContentResponse(AlertContentId, AlertContentType.Weather.ToString());

public record AlertSqlResponse(Guid AlertContentId, string Query) : AlertContentResponse(AlertContentId, AlertContentType.SqlQuery.ToString());
