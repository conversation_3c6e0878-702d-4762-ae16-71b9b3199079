using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.GetAlertBeta;

public class GetAlertBetaQueryHandler(DataContext context)
        : IQueryHandler<GetAlertBetaQuery, AlertBetaResponse>
{
    private readonly DataContext _context = context;
    public async Task<ErrorOr<AlertBetaResponse>> Handle(
        GetAlertBetaQuery request,
        CancellationToken ct)
    {
        AlertModel? alertModel = await _context.AlertModels
            .AsNoTracking()
            .Include(a => a.Contents)
            .Include(a => a.Criterias)
            .AsSplitQuery()
            .SingleOrDefaultAsync(a => a.AlertId == request.AlertId, ct);

        if (alertModel is null)
        {
            return AlertErrors.NotFound;
        }

        AlertBeta alert = alertModel.ToDomain();

        return alert is null
            ? AlertErrors.NotFound
            : new AlertBetaResponse(
                AlertId: alert.Id,
                Name: alert.Name,
                Description: alert.Description,
                IsEnabled: alert.IsEnabled,
                Title: alert.Title,
                AlertTypes: [.. alert.AlertTypes.Select(a => a.ToString())],
                Recipients: [.. alert.Recipients.Select(r => r.Value)],
                Trigger: alert.Trigger switch
                {
                    CriteriaTrigger criteriaTrigger => new CriteriaTriggerResponse(
                        criteriaTrigger.ExpirationTimeInMinutes,
                        [.. criteriaTrigger.Criterias.Select(
                            c => new AlertCriteriaResponse(c.BlockId, c.Filter.Value))],
                        criteriaTrigger.CombinationRule.ToString()),
                    DailyFrequency daily => new DailyScheduleTriggerResponse(
                        daily.ExpirationTimeInMinutes, daily.RunTimes),
                    WeeklyFrequency weekly => new WeeklyScheduleTriggerResponse(
                        weekly.ExpirationTimeInMinutes,
                        weekly.RunTimes,
                        weekly.DaysOfWeeks.ConvertAll(d => d.ToString())),
                    MonthlyFrequency monthly => new MonthlyScheduleTriggerResponse(
                        monthly.ExpirationTimeInMinutes, monthly.RunTimes, monthly.DaysOfMonth),
                    YearlyFrequency yearly => new YearlyScheduleTriggerResponse(
                        yearly.ExpirationTimeInMinutes, yearly.RunTimes, yearly.DaysOfYear),
                    _ => throw new InvalidOperationException($"Trigger type {alert.Trigger.GetType().Name} is not supported"),
                },
                Contents: alert.AlertContents
                    .Select<AlertContent, AlertContentResponse>(content => content switch
                    {
                        AlertNoteContent note => new AlertNoteReponse(content.AlertContentId, note.Note),
                        AlertContactContent contact => new AlertContactResponse(content.AlertContentId, contact.Contact),
                        AlertReportContent report => new AlertReportReponse(content.AlertContentId, report.Reports),
                        AlertEquipmentContent equipment => new AlertEquipmentResponse(
                            content.AlertContentId,
                            equipment.IncludeAvailability,
                            equipment.IncludeConstraint,
                            equipment.NumberOfEventsToInclude),
                        AlertWeatherContent weather => new AlertWeatherResponse(
                            content.AlertContentId,
                            weather.SiteId,
                            weather.WeatherService.ToString(),
                            weather.ForecastInterval.ToString(),
                            weather.NumberOfDaysForecast),
                        AlertSqlContent sql => new AlertSqlResponse(
                            content.AlertContentId, sql.Query),
                        _ => throw new InvalidOperationException($"Content type {content.GetType().Name} is not supported")
                    })
                    .ToList(),
                Attachments: [.. alert.Attachments.Select(a => a.ReportId)],
                SiteId: alert.SiteId,
                LastSentUtc: alert.LastSentUtc.HasValue
                    ? alert.LastSentUtc.Value.ToString("YY/mm/dd H:mm:ss")
                    : null
            );
    }
}
