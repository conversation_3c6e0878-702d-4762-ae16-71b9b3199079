using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.Site;
using RTP.StatusMonitor.Domain.SiteWeatherSettings;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.CreateAlertBeta;

internal sealed class CreateAlertBetaCommandHandler(DataContext dataContext)
    : ICommandHandler<AlertBetaCommand>
{
    private readonly DataContext _context = dataContext;

    public async Task<ErrorOr<Unit>> Handle(
        AlertBetaCommand request,
        CancellationToken ct = default
    )
    {
        // Find the site (alert must be associated with a site)
        Site? site = await _context.Sites.FirstOrDefaultAsync(s => s.Id == request.SiteId, ct);

        // Check if the site exists
        if (site is null)
        {
            return SiteDomainErrors.NotFound;
        }

        AlertBeta alert = new(
            id: Guid.NewGuid(),
            name: request.Name,
            description: request.Description,
            isEnabled: request.IsEnabled,
            trigger: request.Trigger switch
            {
                CriteriaTriggerCommand criteria => new CriteriaTrigger(
                    criteria.ExpirationTimeInMinutes,
                    [
                        .. criteria.Criterias.Select(c => new AlertCriteria(
                            Guid.NewGuid(),
                            c.BlockId,
                            new(c.Filter)
                        )),
                    ],
                    Enum.Parse<CombinationRule>(criteria.CombinationRule)
                ),
                DailyScheduleTriggerCommand daily => new DailyFrequency(
                    daily.ExpirationTimeInMinutes,
                    [.. daily.RunTimes.Select(TimeOnly.Parse)]
                ),
                WeeklyScheduleTriggerCommand weekly => new WeeklyFrequency(
                    weekly.ExpirationTimeInMinutes,
                    [.. weekly.RunTimes.Select(TimeOnly.Parse)],
                    weekly.DaysOfWeek.ConvertAll(Enum.Parse<DayOfWeek>)
                ),
                MonthlyScheduleTriggerCommand monthly => new MonthlyFrequency(
                    monthly.ExpirationTimeInMinutes,
                    [.. monthly.RunTimes.Select(TimeOnly.Parse)],
                    monthly.DaysOfMonth
                ),
                YearlyScheduleTriggerCommand yearly => new YearlyFrequency(
                    yearly.ExpirationTimeInMinutes,
                    [.. yearly.RunTimes.Select(TimeOnly.Parse)],
                    yearly.DaysOfYear
                ),
                _ => throw new InvalidOperationException(
                    $"Alert trigger {request.Trigger.GetType().Name} not supported"
                ),
            },
            title: request.Title,
            alertTypes: [.. request.AlertTypes.Select(Enum.Parse<AlertType>)],
            recipients: request
                .Recipients.ConvertAll(EmailAddress.From)
                .Where(r => r is not null)
                .ToList()!,
            contents:
            [
                .. request.Contents.Select<AlertContentCommand, AlertContent>(c =>
                    c switch
                    {
                        AlertNoteContentCommand note => new AlertNoteContent(
                            Guid.NewGuid(),
                            note.Note
                        ),
                        AlertContactContentCommand contact => new AlertContactContent(
                            Guid.NewGuid(),
                            contact.Contact
                        ),
                        AlertReportContentCommand report => new AlertReportContent(
                            Guid.NewGuid(),
                            report.Reports
                        ),
                        AlertEquipmentContentCommand equipment => new AlertEquipmentContent(
                            Guid.NewGuid(),
                            equipment.IncludeConstraint,
                            equipment.IncludeAvailability,
                            equipment.NumberOfEventsToInclude
                        ),
                        AlertWeatherContentCommand weather => new AlertWeatherContent(
                            Guid.NewGuid(),
                            weather.SiteId,
                            Enum.Parse<WeatherService>(weather.WeatherService),
                            Enum.Parse<ForecastInterval>(weather.ForecastInterval),
                            weather.NumberOfDaysForecast
                        ),
                        AlertSqlContentCommand sql => new AlertSqlContent(
                            Guid.NewGuid(),
                            sql.Query
                        ),
                        _ => throw new InvalidOperationException(
                            $"Note content {c.GetType().Name} not supported"
                        ),
                    }
                ),
            ],
            attachments: request.Attachments.ConvertAll(a => new AlertAttachment(a)),
            siteId: site.Id,
            lastSentUtc: null
        );

        // Map from domain model to persistence model
        AlertModel alertModel = alert.ToPersistence();

        _context.AlertModels.Add(alertModel);

        await _context.SaveChangesAsync(ct);
        return Unit.Value;
    }
}
