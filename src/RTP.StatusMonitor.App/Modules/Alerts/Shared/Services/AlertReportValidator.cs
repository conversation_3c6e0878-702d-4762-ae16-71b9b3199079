using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;

public static class AlertReportValidator
{
    /// <summary>
    /// Creates a function that checks if all the reports included in the alert exist
    /// </summary>
    /// <param name="context">The database context</param>
    /// <returns>A function that takes a list of report IDs and returns whether they all exist</returns>
    public static Func<List<Guid>, Task<bool>> CheckReportExist(DataContext context)
        => async (reportIds) => await context.Reports
            .Where(r => reportIds.Contains(r.Id))
            .CountAsync() == reportIds.Count;
}
