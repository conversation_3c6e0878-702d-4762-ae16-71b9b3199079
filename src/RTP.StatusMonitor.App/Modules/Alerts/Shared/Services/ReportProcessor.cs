using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Reporting.Shared;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Processing;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;

public abstract record ReportProcessingResult(Guid ReportId, ReportType ReportType);

public record ForecastProcessingResult(Guid ReportId, List<ReportColumnData> Data)
    : ReportProcessingResult(ReportId, ReportType.Forecast);

public record HistoricalProcessingResult(Guid ReportId, List<ReportColumnData> Data)
    : ReportProcessingResult(ReportId, ReportType.Historical);
public record AccuracyProcessingResult(Guid ReportId, List<ReportColumnData> Data)
    : ReportProcessingResult(ReportId, ReportType.Accuracy);

public record LookupProcessingResult(Guid ReportId, List<LookupReportRow> Data)
    : ReportProcessingResult(ReportId, ReportType.Lookup);

public class ReportProcessor(
    ForecastReportProcessor forecastReportProcessor,
    HistoricalReportProcessor historicalReportProcessor,
    LookupReportProcessor lookupReportProcessor,
    DataContext context
)
{
    /// <summary>
    /// Query the reports and process them according to their type (forecast, historical, etc...) to get the data
    /// </summary>
    /// <param name="reportIds">The ids of the reports to process</param>
    /// <param name="ct"></param>
    /// <returns>A dictionary of the report and their processed data column</returns>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task<Dictionary<Report, ReportProcessingResult>> ProcessReportsAsync(
        List<Guid> reportIds,
        CancellationToken ct
    )
    {
        // Get the reports to be processed for the alert
        List<Report> reports = await context
            .Reports.AsNoTracking()
            .Where(r => reportIds.Contains(r.Id))
            .Include(r => r.ReportColumns.OrderBy(c => c.Position))
            .Include(r => r.DateRangeInfo)
            .ToListAsync(ct);

        // Order the report by its index in the original list
        List<Report> orderedReports =
        [
            .. reports.OrderBy(r => reportIds.FindIndex(reportId => reportId == r.Id)),
        ];

        // Process the reports to get the data
        Dictionary<Report, ReportProcessingResult> reportData = [];
        foreach (Report report in orderedReports)
        {
            switch (report.ReportType)
            {
                case ReportType.Forecast:
                    {
                        List<ReportColumnData> forecastData =
                            await forecastReportProcessor.ProcessReportAsync(report, ct);
                        reportData.Add(report, new ForecastProcessingResult(report.Id, forecastData));
                        break;
                    }
                // *NOTE: Historical and Accuracy shares the same processing logic
                case ReportType.Historical:
                    {
                        List<ReportColumnData> historicalData =
                            await historicalReportProcessor.ProcessReportAsync(report, ct);
                        reportData.Add(report, new HistoricalProcessingResult(report.Id, historicalData));
                        break;
                    }
                case ReportType.Accuracy:
                    {
                        List<ReportColumnData> historicalData =
                            await historicalReportProcessor.ProcessReportAsync(report, ct);
                        reportData.Add(report, new AccuracyProcessingResult(report.Id, historicalData));
                        break;
                    }
                case ReportType.Lookup:
                    {
                        List<LookupReportRow> lookupData = await lookupReportProcessor.ProcessReportAsync(report, ct);
                        reportData.Add(report, new LookupProcessingResult(report.Id, lookupData));
                        break;
                    }
                default:
                    throw new InvalidOperationException("Report type not supported");
            }
        }

        return reportData;
    }

    // /// <summary>
    // /// Get the formatted reports for the attachments
    // /// </summary>
    // /// <param name="attachmentReports">The reports being used in the attachments</param>
    // /// <returns>The formatted reports</returns>
    // /// <exception cref="InvalidOperationException">Thrown when the report type is not supported</exception>
    // private static List<ReportFormatOutput> FormatAlertAttachments(
    //     Dictionary<Report, List<ReportColumnData>> attachmentReports
    // )
    // {
    //     List<ReportFormatOutput> formattedReports = [];
    //     foreach (var (report, reportData) in attachmentReports)
    //     {
    //         ReportFormatOutput formattedReport = report.ReportType switch
    //         {
    //             ReportType.Forecast => ForecastReportFormatter.FormatReport(
    //                 report,
    //                 Enum.Parse<ForecastLayoutType>(report.ReportLayout.Value),
    //                 report.FileFormat,
    //                 reportData
    //             ),
    //             ReportType.Historical => HistoricalReportFormatter.FormatReport(
    //                 report,
    //                 Enum.Parse<HistoricalLayoutType>(report.ReportLayout.Value),
    //                 report.FileFormat,
    //                 reportData
    //             ),
    //             ReportType.Accuracy => AccuracyReportFormatter.FormatReport(
    //                 report,
    //                 ReportFileFormat.Excel,
    //                 reportData
    //             ),
    //             _ => throw new InvalidOperationException("Report type not supported"),
    //         };
    //
    //         formattedReports.Add(formattedReport);
    //     }
    //
    //     return formattedReports;
    // }
    //
    // /// <summary>
    // /// Get the formatted reports for the report body
    // /// </summary>
    // /// <param name="reportsInBody">The reports being used in the report body</param>
    // /// <returns>The formatted reports</returns>
    // /// <exception cref="InvalidOperationException">Thrown when the report type is not supported</exception>
    // private static List<ReportFormatOutput> FormatAlertReports(
    //     Dictionary<Report, List<ReportColumnData>> reportsInBody
    // )
    // {
    //     List<ReportFormatOutput> formattedReports = [];
    //
    //     // Format the reports to HTML to include in the email
    //     foreach ((Report report, List<ReportColumnData> reportData) in reportsInBody)
    //     {
    //         ReportFormatOutput formattedReport = report.ReportType switch
    //         {
    //             ReportType.Forecast => ForecastReportFormatter.FormatReport(
    //                 report,
    //                 Enum.Parse<ForecastLayoutType>(report.ReportLayout.Value),
    //                 ReportFileFormat.HTML,
    //                 reportData
    //             ),
    //             ReportType.Historical => HistoricalReportFormatter.FormatReport(
    //                 report,
    //                 Enum.Parse<HistoricalLayoutType>(report.ReportLayout.Value),
    //                 ReportFileFormat.HTML,
    //                 reportData
    //             ),
    //             ReportType.Accuracy => AccuracyReportFormatter.FormatReport(
    //                 report,
    //                 ReportFileFormat.HTML,
    //                 reportData
    //             ),
    //             _ => throw new InvalidOperationException("Report type not supported"),
    //         };
    //
    //         formattedReports.Add(formattedReport);
    //     }
    //
    //     return formattedReports;
    // }
}
// public async Task<(
//     List<ReportFormatOutput> reportsInBody,
//     List<ReportFormatOutput> reportsInAttachments
// )> ProcessReportsAsync(Alert alert, CancellationToken ct)
// {
//     // Get the report ids of the attachments and the report body
//     IEnumerable<Guid> attachmentReportIds = alert.AlertDetails.Attachments.Select(a =>
//         a.ReportId
//     );
//     IEnumerable<Guid> bodyReportIds = alert.AlertDetails.Body.ReportRenderConfigs.Select(rrc =>
//         rrc.ReportId
//     );
//
//     // Get the reports to be processed for the alert
//     List<Report> reports = await context
//         .Reports.AsNoTracking()
//         .Where(r => bodyReportIds.Contains(r.Id) || attachmentReportIds.Contains(r.Id))
//         .Include(r => r.ReportColumns.OrderBy(c => c.Position))
//         .Include(r => r.DateRangeInfo)
//         .ToListAsync(ct);
//
//     // Process the reports to get the data
//     Dictionary<Guid, List<ReportColumnData>> reportData = [];
//     foreach (Report report in reports)
//     {
//         // Process report to get the data
//         List<ReportColumnData> data = report.ReportType switch
//         {
//             ReportType.Forecast => await forecastReportProcessor.ProcessReportAsync(
//                 report,
//                 ct
//             ),
//             ReportType.Historical or ReportType.Accuracy =>
//                 await historicalReportProcessor.ProcessReportAsync(report, ct),
//             ReportType.Lookup => await lookupReportProcessor.ProcessReportAsync(report, ct),
//             _ => throw new InvalidOperationException("Report type not supported"),
//         };
//
//         reportData.Add(report.Id, data);
//     }
//
//     // Get the reports that are being used in the attachments and the report body
//     Dictionary<Report, List<ReportColumnData>> reportsInBody = reportData
//         .Where(kvp => bodyReportIds.Contains(kvp.Key))
//         .ToDictionary(kvp => reports.Find(r => r.Id == kvp.Key)!, kvp => kvp.Value);
//
//     Dictionary<Report, List<ReportColumnData>> reportsInAttachments = reportData
//         .Where(kvp => attachmentReportIds.Contains(kvp.Key))
//         .ToDictionary(kvp => reports.Find(r => r.Id == kvp.Key)!, kvp => kvp.Value);
//
//     // Get the formatted reports for the attachments and the report body
//     List<ReportFormatOutput> formattedReportsInBody = FormatAlertReports(reportsInBody);
//
//     // Order the reports based on the render config
//     List<ReportFormatOutput> orderedReportsInBody =
//     [
//         .. formattedReportsInBody.OrderBy(x =>
//             alert
//                 .AlertDetails.Body.ReportRenderConfigs.Find(y => y.ReportId == x.ReportId)
//                 ?.Position
//         ),
//     ];
//
//     List<ReportFormatOutput> formattedReportsInAttachments = FormatAlertAttachments(
//         reportsInAttachments
//     );
//
//     return (orderedReportsInBody, formattedReportsInAttachments);
// }
