// using RTP.StatusMonitor.App.Modules.Alerts.Shared.Templates;
// using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
// using RTP.StatusMonitor.Domain.Alerts;
//
// namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
// public class AlertProcessor(ForecastAlertTemplateRenderer alertRenderer)
// {
//     private readonly ForecastAlertTemplateRenderer _alertRenderer = alertRenderer;
//
//     public async Task<string> ProcessAlertAsync(
//         Alert alert, List<ReportFormatOutput> reportsInBody, CancellationToken ct)
//             => alert.AlertType switch
//             {
//                 AlertType.Forecast => await _alertRenderer
//                     .RenderAlertAsync(reportsInBody, alert, ct),
//                 AlertType.Historical => HistoricalAlertTemplateRenderer.RenderAlert(reportsInBody, alert),
//                 AlertType.Accuracy => AccuracyAlertTemplateRenderer.RenderAlert(reportsInBody, alert),
//                 _ => throw new InvalidOperationException("Alert type not supported")
//             };
// }
