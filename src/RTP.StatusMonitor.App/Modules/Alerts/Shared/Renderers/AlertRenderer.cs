using System.Data;
using System.Text;
using Dapper;
using HandlebarsDotNet;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence.Data;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;

public class AlertRenderer(
    ISqlConnectionFactory sqlConnectionFactory,
    EquipmentContentRenderer equipmentContentRenderer,
    WeatherContentRenderer weatherContentRenderer,
    ReportProcessor reportProcessor)
{
    private const string _footerTemplate = @"
      <table cellspacing='0' cellpadding='0' border='0' width='100%' style='background-color: #1d7fb8;'>
        <tr>
          <td align='center' style='padding: 20px;'>
            <table cellspacing='0' cellpadding='0' border='0' width='600'>
              <!-- Logo and Company Name -->
              <tr>
                <td align='center' style='padding-bottom: 10px;'>
                  <table cellspacing='0' cellpadding='0' border='0'>
                    <tr>
                      <td valign='middle' style='padding-right: 10px;'>
                        <img src='https://realtimepower.blob.core.windows.net/assets/rtp-pwa-192x192.png' 
                             alt='RTP Analytics' 
                             width='48' 
                             height='48' 
                             style='display: block; border: 0;'>
                      </td>
                      <td valign='middle'>
                        <span style='
                          font-size: 28px;
                          font-weight: 600;
                          color: #ffffff;
                          font-family: Arial, sans-serif;
                        '>RTP Analytics</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Links Section -->
              <tr>
                <td>
                  <table cellspacing='0' cellpadding='0' border='0' width='100%'>
                    <tr>
                      <td style='border-top: 1px solid #ffffff33; font-size: 1px; height: 1px;'>&nbsp;</td>
                    </tr>
                    <tr>
                      <td align='center' style='padding: 20px 0;'>
                        <p style='
                          font-family: Arial, sans-serif;
                          font-size: 16px;
                          color: #e2e8f0;
                          margin: 0 0 20px;
                          line-height: 1.6;
                        '>
                          <strong>We win when you win</strong> — our
                          success is measured by the enhanced efficiency and performance we
                          bring to your operations.
                        </p>
                        <table cellspacing='0' cellpadding='0' border='0'>
                          <tr>
                            <td style='padding-right: 20px;'>
                              <table cellspacing='0' cellpadding='0' border='0'>
                                <tr>
                                  <td bgcolor='#324b6e' style='border-radius: 6px;'>
                                    <a href='https://www.rtpanalytics.com'
                                       style='
                                         font-family: Arial, sans-serif;
                                         color: #e2e8f0;
                                         text-decoration: none;
                                         font-size: 14px;
                                         padding: 12px 24px;
                                         border: 0;
                                         display: inline-block;
                                       '>Learn More</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                            <td>
                              <table cellspacing='0' cellpadding='0' border='0'>
                                <tr>
                                  <td bgcolor='#324b6e' style='border-radius: 6px;'>
                                    <a href='https://www.rtpanalytics.com/contact'
                                       style='
                                         font-family: Arial, sans-serif;
                                         color: #e2e8f0;
                                         text-decoration: none;
                                         font-size: 14px;
                                         padding: 12px 24px;
                                         border: 0;
                                         display: inline-block;
                                       '>Contact Us</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td style='border-bottom: 1px solid #ffffff33; font-size: 1px; height: 1px;'>&nbsp;</td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Copyright -->
              <tr>
                <td align='center' style='padding-top: 20px;'>
                  <span style='
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    color: #a0aec0;
                  '>© 2024 RTP Analytics. All rights reserved.</span>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>";

    private const string _alertTemplate = @"
        <div 
            style='
                font-family: Inter, Arial, sans-serif;
                letter-spacing: 0.5px;
                margin: -10px;'
        >
            <!-- SECTION - Main Content -->
            <div style='color: black; padding: 10px;'>
                <div style='margin: 0 auto;'>

                    <!-- Alert Title -->
                    <h1 style='
                        margin: 0 0 20px;
                        text-align: center;
                        color: black;
                        font-size: 32px;
                        font-weight: 600;
                    '>
                        {{title}}
                    </h1>

                    {{#each alertContents}}
                    <div style='margin-bottom: 0.5rem;'>
                        {{{this}}}
                    </div>
                    {{/each}}
                </div>
            </div>

            {{{FooterTemplate}}}
        </div>";

    public record SiteDto(
        Guid Id,
        string Name,
        double Latitude,
        double Longitude,
        double Altitude,
        bool IsMetric,
        string TimeZone);

    public async Task<string> Render(AlertBeta alert, CancellationToken ct)
    {
        using IDbConnection connection = sqlConnectionFactory.CreateConnection();

        const string sql = """
                           SELECT 
                               Id,
                               Name,
                               Latitude,
                               Longitude, 
                               Altitude,
                               IsMetric,
                               TimeZone
                           FROM Sites
                           WHERE Id IN @SiteIds;
                           """;

        // Get the all sites info needed (including all sites from weather)
        Guid[] siteIds = [alert.SiteId, .. alert.AlertContents.OfType<AlertWeatherContent>().Select(w => w.SiteId)];
        IEnumerable<SiteDto> siteInfos = await connection.QueryAsync<SiteDto>(sql, new { SiteIds = siteIds });

        // Get the report ids for the alert contents and attachments
        Dictionary<int, string> alertContentByPosition = [];
        for (int i = 0; i < alert.AlertContents.Count; i++)
        {
            AlertContent alertContent = alert.AlertContents[i];

            switch (alertContent)
            {
                case AlertNoteContent alertNoteContent:
                    alertContentByPosition.Add(i, NoteContentRenderer.Render(alertNoteContent));
                    break;
                case AlertContactContent alertContactContent:
                    alertContentByPosition.Add(i, ContactContentRenderer.Render(alertContactContent));
                    break;
                case AlertReportContent alertReportContent: // Process the reports to get data
                    Dictionary<Report, ReportProcessingResult> reportData =
                        await reportProcessor.ProcessReportsAsync(alertReportContent.Reports, ct);

                    // Render the report as HTML
                    List<ReportFormatOutput> reportContents = FormatReports(reportData);
                    alertContentByPosition.Add(i,
                        string.Join("</br",
                            reportContents.ConvertAll(r => Encoding.Default.GetString(r.Content))));
                    break;
                case AlertEquipmentContent alertEquipmentContent:
                    // Equipment will use the site data set in alert
                    alertContentByPosition.Add(i,
                        await equipmentContentRenderer.Render(alertEquipmentContent,
                            siteInfos.First(s => s.Id == alert.SiteId), ct));
                    break;
                case AlertWeatherContent alertWeatherContent:
                    alertContentByPosition.Add(i, alertWeatherContent.ForecastInterval switch
                    {
                        ForecastInterval.Daily => await weatherContentRenderer.RenderDailyForecast(
                            alertWeatherContent, siteInfos.First(s => s.Id == alertWeatherContent.SiteId), ct),
                        ForecastInterval.Hourly => await weatherContentRenderer.RenderHourlyForecast(
                            alertWeatherContent, siteInfos.First(s => s.Id == alertWeatherContent.SiteId), ct),
                        _ => throw new InvalidOperationException("Invalid forecast interval")
                    });
                    break;
                // TODO - working on it
                // case AlertSqlContent alertSqlContent:
                //     break;
                default:
                    throw new InvalidOperationException($"Unsupported alert content type: {alertContent.GetType()}");
            }
        }

        return Handlebars.Compile(_alertTemplate)(new Dictionary<string, object>()
        {
            ["title"] = alert.Title,
            ["currentYear"] = DateTime.UtcNow.Year,
            ["alertContents"] = alertContentByPosition.OrderBy(x => x.Key).Select(x => x.Value),
            ["FooterTemplate"] = _footerTemplate,
        });
    }

    /// <summary>
    /// Get the formatted reports for the report body
    /// </summary>
    /// <param name="reportsInBody">The reports being used in the report body</param>
    /// <returns>The formatted reports</returns>
    /// <exception cref="InvalidOperationException">Thrown when the report type is not supported</exception>
    private static List<ReportFormatOutput> FormatReports(Dictionary<Report, ReportProcessingResult> reportsInBody)
    {
        List<ReportFormatOutput> formattedReports = [];

        // Format the reports to HTML to include in the email
        foreach ((Report report, ReportProcessingResult reportData) in reportsInBody)
        {
            ReportFormatOutput formattedReport = report.ReportType switch
            {
                ReportType.Forecast => ForecastReportFormatter.FormatReport(
                    report,
                    Enum.Parse<ForecastLayoutType>(report.ReportLayout.Value),
                    ReportFileFormat.HTML,
                    (ForecastProcessingResult)reportData),
                ReportType.Historical => HistoricalReportFormatter.FormatReport(
                    report,
                    Enum.Parse<HistoricalLayoutType>(report.ReportLayout.Value),
                    ReportFileFormat.HTML,
                    (HistoricalProcessingResult)reportData),
                ReportType.Accuracy => AccuracyReportFormatter.FormatReport(
                    report,
                    ReportFileFormat.HTML,
                    (AccuracyProcessingResult)reportData),
                ReportType.Lookup => LookupReportFormatter.FormatReport(report, ReportFileFormat.HTML,
                    (LookupProcessingResult)reportData),
                _ => throw new InvalidOperationException("Report type not supported")
            };

            formattedReports.Add(formattedReport);
        }

        return formattedReports;
    }
}
