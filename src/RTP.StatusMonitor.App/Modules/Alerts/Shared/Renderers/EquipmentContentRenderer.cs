using System.Data;
using Dapper;
using HandlebarsDotNet;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Templates;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.EquipmentContent;
using RTP.StatusMonitor.Domain.EquipmentContent.Events;
using RTP.StatusMonitor.Domain.EquipmentSection;
using RTP.StatusMonitor.Domain.IntegrationEventLogEntry;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Data;
using System.Text;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Abstractions.Extensions;
using static RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers.AlertRenderer;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;
public record BlockEquipmentStatus(
    Guid BlockId,
    string Block,
    string Tag,
    string Alias,
    ContentFormat Format,
    SectionType SectionType);

public class EquipmentContentRenderer(
    DataContext context,
    ISqlConnectionFactory sqlConnectionFactory,
    DataClientReadRepository dataClientReadRepository)
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory = sqlConnectionFactory;
    private readonly DataContext _context = context;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;
    private const string _logsSectionTemplate = @"
        {{#if eventLogs}}
        <div style='
            display: inline-block;
            vertical-align: top;
            border-radius: 8px;
            width: 100%;
            padding: 1rem;
            background-color: #f8fafc;
        '>
            <h3 style='margin: 0 0 15px; color: #4299e1; font-size: 20px'>LOGS</h3>

            <table>
                {{#each eventLogs}}
                <tr>
                    <td>
                        <strong style='color: #2d3748;'>{{createdAt}}</strong>
                        <span>by</span>
                        <span style='color: #3182ce'>{{createdBy}}</span>
                        <span>{{note}}</span>
                    </td>
                </tr>
                {{/each}}
            </table>
        </div>
        {{/if}}";

    public async Task<string> Render(
        AlertEquipmentContent equipmentContent,
        SiteDto site,
        CancellationToken ct)
    {
        // Get the equipment changelog
        List<ForecastUpdateLog> forecastUpdateLogs = await GetEquipmentChangelog(
            site.Name, equipmentContent.NumberOfEventsToInclude, ct);

        const string sql = """
        SELECT 
            b.Id as BlockId,
            b.Alias as Block, 
            ec.Tag, 
            ec.Alias, 
            ec.Format,
            es.SectionType
        FROM EquipmentContents ec
        JOIN Blocks b ON ec.BlockId = b.Id
        JOIN EquipmentSections es ON ec.EquipmentSectionId = es.Id
        WHERE ec.EquipmentSectionId in 
        (
            SELECT Id
            FROM EquipmentSections
            WHERE EquipmentGroupId in
            (
                SELECT Id
                FROM EquipmentGroups 
                WHERE SiteId = @SiteId
            ) and SectionType in @SectionTypes
        )
        ORDER BY es.SectionType, Block, Format, Alias
        """;

        StringBuilder html = new();

        // Add mobile-specific styles
        html.AppendLine(@"
            <style>
                @media screen and (max-width: 600px) {
                    .equipment-table {
                        display: block;
                        overflow-x: auto;
                        -webkit-overflow-scrolling: touch;
                    }
                }
            </style>");

        // Start the main container
        html.AppendLine(@"<div style='width: 100%;'>");

        // Add logs section at the top
        html.AppendLine(RenderLogsSection(forecastUpdateLogs));

        using IDbConnection connection = _sqlConnectionFactory.CreateConnection();

        // Get both availability and constraints data in one query
        List<int> sectionTypes = [];
        if (equipmentContent.IncludeAvailability)
            sectionTypes.Add((int)SectionType.Availability);
        if (equipmentContent.IncludeConstraint)
            sectionTypes.Add((int)SectionType.Constraints);

        if (sectionTypes.Any())
        {
            List<BlockEquipmentStatus> blockEquipmentData = [.. connection
                .Query<BlockEquipmentStatus>(sql, new { SiteId = site.Id, SectionTypes = sectionTypes.ToArray() })];

            if (blockEquipmentData.Any())
            {
                html.AppendLine(await RenderEquipmentStatusAsync(blockEquipmentData, ct));
            }
        }

        // Close main container
        html.AppendLine("</div>");

        return html.ToString();
    }

    /// <summary>
    /// Renders the equipment content table in HTML format
    /// </summary>
    /// <returns>HTML string for the equipment content table</returns>
    private async Task<string> RenderEquipmentStatusAsync(
        List<BlockEquipmentStatus> equipmentStatus,
        CancellationToken ct)
    {
        if (equipmentStatus.Count == 0)
        {
            return string.Empty;
        }

        // Get unique blocks (columns) and tags (rows)
        List<string> blockAliases = [.. equipmentStatus.Select(x => x.Block).Distinct()];
        List<string> tags = [.. equipmentStatus.Select(x => x.Tag).Distinct()];

        List<Guid> blockIds = [.. equipmentStatus.Select(x => x.BlockId).Distinct()];
        List<Block> blocks = await _context.Blocks
            .AsNoTracking()
             .Include(b => b.Site)
                 .ThenInclude(s => s.Customer)
            .Where(b => blockIds.Contains(b.Id))
            .ToListAsync(ct);

        Dictionary<string, double> tagValueLookup = [];
        foreach (Guid blockId in blockIds)
        {
            Block? block = blocks.FirstOrDefault(b => b.Id == blockId);
            if (block is null)
            {
                continue;
            }

            List<DataClientHistoricalWriteSchemaDto> blockSnapshotData = _dataClientReadRepository
                .GetLatestData(block, tags);
            foreach (DataClientHistoricalWriteSchemaDto i in blockSnapshotData)
            {
                string key = $"{block.Alias}-{i.Tag}";
                if (!tagValueLookup.ContainsKey(key))
                {
                    tagValueLookup[key] = i.Value;
                }
            }
        }

        Dictionary<(string Block, string Tag), (ContentFormat Format, SectionType SectionType)> formatLookup = equipmentStatus
            .ToDictionary(
                x => (x.Block, x.Tag),
                x => (x.Format, x.SectionType));

        StringBuilder html = new();
        html.AppendLine(@"
            <div style='
                color: black;
                border-radius: 8px;
                padding: 1rem;
                background-color: white;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            '>");

        html.AppendLine(@"
            <div class='equipment-table' style='
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            '>");

        html.AppendLine(@"
            <table style='
                width: 100%; 
                border-collapse: collapse;
                border: 1px solid #E6E6E6;
                line-height: 1.4;
            '>");

        // Add header row
        html.AppendLine("<thead style='color: #1d7fb8'>");
        html.AppendLine("<tr style='border: 1px dotted #E6E6E6;'>");

        // First column headers
        html.AppendLine(@"
            <th style='
                text-align: left;
                border: 1px dotted #E6E6E6;
                padding: 8px;
                background-color: #f8fafc;
            '>Tag</th>");

        // Add block names as column headers
        foreach (string b in blockAliases)
        {
            html.AppendLine(@$"
                <th style='
                    border: 1px dotted #E6E6E6;
                    padding: 8px;
                    background-color: #f8fafc;
                    text-align: center;
                '>{b}</th>");
        }

        html.AppendLine("</tr>");
        html.AppendLine("</thead>");

        // Add data rows
        html.AppendLine("<tbody>");

        foreach (string tag in tags)
        {
            var firstBlockTag = formatLookup.First(x => x.Key.Tag == tag);
            var sectionType = firstBlockTag.Value.SectionType;

            html.AppendLine("<tr>");

            // Add tag cell
            html.AppendLine(@$"
                <td style='
                    text-align: left;
                    border: 1px dotted #E6E6E6;
                    padding: 8px;
                '>{tag}</td>");

            // Add values for each block
            foreach (string b in blockAliases)
            {
                if (formatLookup.TryGetValue((b, tag), out var format))
                {
                    double? value = tagValueLookup.ToLookUp($"{b}-{tag}");
                    string cellContent = format.Format switch
                    {
                        ContentFormat.OnOff => RenderOnOffButton(value),
                        ContentFormat.Percentage => value is not null ? $"{value * 100:F1}%" : "-",
                        _ => value?.ToString() ?? string.Empty
                    };

                    html.AppendLine(@$"
                        <td style='
                            border: 1px dotted #E6E6E6;
                            padding: 8px;
                            text-align: center;
                        '>{cellContent}</td>");
                }
                else
                {
                    html.AppendLine(@"
                        <td style='
                            border: 1px dotted #E6E6E6;
                            padding: 8px;
                            text-align: center;
                        '>-</td>");
                }
            }

            html.AppendLine("</tr>");
        }

        html.AppendLine("</tbody>");
        html.AppendLine("</table>");
        html.AppendLine("</div>");
        html.AppendLine("</div>");

        return html.ToString();
    }

    /// <summary>
    /// Renders an ON/OFF button based on the value
    /// </summary>
    private static string RenderOnOffButton(double? value)
    {
        string text = value == 0 ? "OFF" : "ON";
        string backgroundColor = value == 0 ? "#EF4444" : "#10B981"; // Red for OFF, Green for ON

        return @$"
            <div style='
                display: inline-block;
                padding: 4px 12px;
                border-radius: 5px;
                background-color: {backgroundColor};
                color: white;
                font-weight: 600;
                min-width: 60px;
            '>{text}</div>";
    }

    /// <summary>
    /// Renders the logs section in HTML format
    /// </summary>
    private static string RenderLogsSection(List<ForecastUpdateLog> forecastUpdateLogs)
    {
        Dictionary<string, object> templateData = new() { ["eventLogs"] = forecastUpdateLogs };
        return Handlebars.Compile(_logsSectionTemplate)(templateData);
    }

    /// <summary>
    /// Query the equipment changelog of the site 
    /// </summary>
    private async Task<List<ForecastUpdateLog>> GetEquipmentChangelog(
        string siteName,
        int numberOfEventsToInclude,
        CancellationToken ct)
    {
        // Get the event logs of forecast updates for the site
        List<IntegrationEventLogEntry> selectedSiteEventLogs = await _context
            .IntegrationEventLogEntries
                .AsNoTracking()
                .Where(el => el.Content.Contains(siteName))
                .OrderByDescending(el => el.CreationDate)
                .Take(numberOfEventsToInclude)
                .ToListAsync(ct);

        // Get the forecast update logs
        return [.. selectedSiteEventLogs
            .ToDictionary(el => el.CreationDate, el => el.Content)
            .Select(el =>
            {
                ForecastUpdateEvent forecastUpdateEvent = JsonConvert
                    .DeserializeObject<ForecastUpdateEvent>(el.Value)!;

                return new ForecastUpdateLog(
                    CreatedAt: el.Key.ToString("yyyy/MM/dd"),
                    Note: forecastUpdateEvent.Note,
                    CreatedBy: forecastUpdateEvent.CreatedBy);
            })
            .Where(log => !string.IsNullOrEmpty(log.Note) && !log.Note.ToLower().Contains("test"))];
    }
}

