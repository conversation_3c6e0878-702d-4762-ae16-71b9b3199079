using HandlebarsDotNet;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;

public static class ContactContentRenderer
{
    private const string _contactSectionTemplate = @"
    <div style='
        display: inline-block;
        vertical-align: top;
        border-radius: 8px;
        width: 100%;
        padding: 1rem;
        background-color: #f8fafc;
    '>
        <h3 style='
            margin: 0 0 15px; 
            color: #4299e1;
            font-size: 20px;'>SUPPORT
        </h3>
            <table style='
                color: black;
                padding-left: 10px;'>
                    {{#each contacts}}
                    <tr>
                        <td>{{this}}</td>
                    </tr>
                    {{/each}}
            </table>
    </div>";

    public static string Render(AlertContactContent content)
        => Handlebars.Compile(_contactSectionTemplate)(new Dictionary<string, object>()
        {
            ["contacts"] = content.Contact.Split('\n').ToList()
        });
}
