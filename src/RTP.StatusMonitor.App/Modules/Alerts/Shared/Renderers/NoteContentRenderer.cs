using HandlebarsDotNet;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;

public static class NoteContentRenderer
{
    private const string _noteSectionTemplate = @"
        <div style='
            display: inline-block;
            vertical-align: top;
            border-radius: 8px;
            width: 100%;
            padding: 1rem;
            background-color: #f8fafc;
        '>
            <h3 style='
                margin-bottom: 15px; 
                color: #4299e1; 
                font-size: 20px
            '>NOTES</h3>

            <table style='
                margin: 0;
                color: black;
                font-weight: 500;
            '>
                {{#each notes}}
                <tr>
                    <td>{{{this}}}</td>
                </tr>
                {{/each}}
            </table>
        </div>";

    public static string Render(AlertNoteContent noteContent) => Handlebars.Compile(_noteSectionTemplate)(new Dictionary<string, object>()
    {
        ["notes"] = noteContent.Note.Split('\n').ToList()
    });
}

