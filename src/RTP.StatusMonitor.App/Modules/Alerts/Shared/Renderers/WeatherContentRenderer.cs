using System.Data;
using System.Text;
using RTP.StatusMonitor.App.Modules.Weather.GetCurrentWeatherConditions.Dto;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api;
using RTP.StatusMonitor.App.Modules.Weather.Shared.Api.Response;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.EngUnits.Types;
using RTP.StatusMonitor.Domain.Shared;
using static RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers.AlertRenderer;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;

public class WeatherContentRenderer(IWeatherServiceFactory weatherServiceFactory)
{
    private const string _assetStorage = "https://realtimepower.blob.core.windows.net";
    private readonly IWeatherServiceFactory _weatherServiceFactory = weatherServiceFactory;
    public async Task<string> RenderDailyForecast(AlertWeatherContent weatherContent, SiteDto site, CancellationToken ct)
    {
        List<DailyForecastResponse> forecastData = await _weatherServiceFactory
            .Get(weatherContent.WeatherService)
            .GetDailyForecastAsync(
                latitude: site.Latitude,
                longitude: site.Longitude,
                unitSystem: site.IsMetric
                    ? UnitSystem.Metric
                    : UnitSystem.Imperial,
                ct: ct);

        // Get the end time of the forecast
        DateTime startOfTomorrow = new(
            DateTime.Now.AddDays(1).Year,
            DateTime.Now.AddDays(1).Month,
            DateTime.Now.AddDays(1).Day,
            0, 0, 0);
        DateTime forecastEndTime = startOfTomorrow.AddDays(weatherContent.NumberOfDaysForecast);

        // Filter the forecast based on the days requested
        List<DailyForecastResponse> filteredForecast = [.. forecastData
            .Where(f =>
                TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                    TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone))
                    <= forecastEndTime)];

        StringBuilder html = new();

        html.AppendLine(@"
            <div style='
                color: black;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 20px;
            '>");

        html.AppendLine(@$"
            <h3 style='
                color: #4299e1;
                font-size: 24px;
                margin-bottom: 0px;
            '>{site.Name} Daily Weather Forecast</h3>");

        html.AppendLine(@$"
            <strong style='
                color: #4299e1;
                font-size: 14px;
                margin-bottom: 15px;
            '>({site.Latitude:F6}°, {site.Longitude:F6}°) Ele: {site.Altitude:F0}</strong>");

        // Add wrapper for horizontal scroll
        html.AppendLine(@"
            <div style='
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 10px;
                overflow-y: hidden;
            '>");

        html.AppendLine(@"
            <table style='
                width: 100%;
                border-collapse: collapse;
                border: 1px solid #E6E6E6;
                line-height: 1.4;
            '>");

        // Add header row
        html.AppendLine("<thead style='color: #1d7fb8'>");
        html.AppendLine("<tr style='border: 1px dotted #E6E6E6;'>");

        // Add column headers
        string[] headers =
        [
            "Date",
            "Day",
            "High/Low",
            "Condition",
            "RH",
            "Precip",
            "Wind",
            "UV",
        ];

        foreach (string header in headers)
        {
            html.AppendLine(@$"
                <th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{header}</th>");
        }

        html.AppendLine("</tr>");
        html.AppendLine("</thead>");

        // Add data rows
        html.AppendLine("<tbody>");

        foreach (DailyForecastResponse forecast in filteredForecast)
        {
            html.AppendLine("<tr>");

            // Date and Day Part
            DateTime localTime = DateTimeOffset.FromUnixTimeSeconds(forecast.UnixTimeInSeconds)
                .UtcDateTime;
            string formattedDate = localTime.ToString("MMM dd, yyyy");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>
                    {formattedDate}
                </td>");

            // Day of the week (Mon, Tue, etc.)
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>
                    {localTime.ToString("ddd")}
                </td>");

            // Temperature
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{forecast.TemperatureMax?.Value.ToString("F1")} °{(forecast.TemperatureMax?.EngUnits == TemperatureEngUnit.Celsius ? "C" : "F")}
                    /
                    {forecast.TemperatureMin?.Value.ToString("F1")} °{(forecast.TemperatureMin?.EngUnits == TemperatureEngUnit.Celsius ? "C" : "F")}
                </td>");

            // Weather Icon and description
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>
                    <img src='{_assetStorage}/assets/weather/{weatherContent.WeatherService.ToString().ToLower()}/{forecast.IconCode}.png' 
                         alt=''
                         width='32'
                         height='32'
                         style='vertical-align: middle;'
                    />
                    {forecast.ShortDescription}
                </td>");

            // Relative Humidity
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{forecast.RelativeHumid?.Value:F0}%</td>");

            // Precipitation
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{forecast.PrecipProbability:F0}%</td>");

            // Wind Phrase
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{forecast.WindPhrase}</td>");

            // UV Description
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: left;
                    padding: 8px;
                '>{forecast.UvDescription}</td>");

            html.AppendLine("</tr>");
        }

        html.AppendLine("</tbody>");
        html.AppendLine("</table>");
        html.AppendLine("</div>");
        html.AppendLine("</div>");

        return html.ToString();
    }

    public async Task<string> RenderHourlyForecast(AlertWeatherContent weatherContent, SiteDto site, CancellationToken ct)
    {
        List<WeatherConditionResult> forecastData = await _weatherServiceFactory
            .Get(weatherContent.WeatherService)
            .GetHourlyForecastAsync(
                latitude: site.Latitude,
                longitude: site.Longitude,
                unitSystem: site.IsMetric
                    ? UnitSystem.Metric
                    : UnitSystem.Imperial,
                altitude: site.Altitude,
                ct: ct);

        // Get the end time of the forecast 
        // NOTE - 0-index => 2 days means 2 days from now - today, tomorrow and the day after
        DateTime startOfTomorrow = new(
            DateTime.Now.AddDays(1).Year,
            DateTime.Now.AddDays(1).Month,
            DateTime.Now.AddDays(1).Day,
            0, 0, 0);
        DateTime forecastEndTime = startOfTomorrow.AddDays(weatherContent.NumberOfDaysForecast);

        // Filter the forecast based on the days requested
        List<WeatherConditionResult> filteredForecast = [.. forecastData
            .Where(f =>
                TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeSeconds(f.UnixTimeInSeconds).UtcDateTime,
                    TimeZoneInfo.FindSystemTimeZoneById(site.TimeZone))
                    <= forecastEndTime)];

        StringBuilder html = new();

        html.AppendLine(@"
            <div style='
                color: black;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 20px;
            '>");

        html.AppendLine(@$"
            <h3 style='
                color: #4299e1;
                font-size: 24px;
                margin-bottom: 0px;
            '>{site.Name} Hourly Weather Forecast</h3>");

        html.AppendLine(@$"
            <strong style='
                color: #4299e1;
                font-size: 14px;
                margin-bottom: 15px;
            '>({site.Latitude:F6}°, {site.Longitude:F6}°) Ele: {site.Altitude:F0}</strong>");

        // Add wrapper for horizontal scroll
        html.AppendLine(@"
            <div style='
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 10px;
                overflow-y: hidden;
            '>");

        html.AppendLine(@"
            <table style='
                width: 100%;
                border-collapse: collapse;
                border: 1px solid #E6E6E6;
                line-height: 1.4;
            '>");

        // Add header row
        html.AppendLine("<thead style='color: #1d7fb8'>");
        html.AppendLine("<tr style='border: 1px dotted #E6E6E6;'>");

        // Add column headers
        string[] headers =
        [
            "Local Time",
            "Hour",
            "Icon",
            "Desc",
            "T",
            "RH",
            "PBaro",
            "P",
            "WS",
            "WD",
            "Precip",
            "UV"
        ];

        foreach (string header in headers)
        {
            html.AppendLine(@$"
                <th style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{header}</th>");
        }

        html.AppendLine("</tr>");
        html.AppendLine("</thead>");

        // Add data rows
        html.AppendLine("<tbody>");

        foreach (WeatherConditionResult forecast in filteredForecast)
        {
            html.AppendLine("<tr>");

            DateTime localTime = DateTime.Parse(forecast.LocalTime);
            int hourEnding = localTime.Hour == 0 ? 24 : localTime.Hour;
            string formattedTime = hourEnding == 24 ? localTime.AddDays(-1).ToString("yyyy-MM-dd") : localTime.ToString("yyyy-MM-dd");

            // Local time
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{formattedTime}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{hourEnding}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>
                    <img src='{_assetStorage}/assets/weather/{weatherContent.WeatherService.ToString().ToLower()}/{forecast.WeatherIcon}.png' 
                         alt='{forecast.Description}'
                         width='32'
                         height='32'
                         style='vertical-align: middle; margin-right: 8px;'
                    />
                </td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>
                    {forecast.Description}
                </td>");

            // Temperature
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.Temp?.Value}</td>");

            // Relative Humidity
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.RelativeHumidity}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.MeanSeaLevelPressure?.Value}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.AbsolutePressure?.Value}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.WindSpd?.Value}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.WindDirection?.Localized}</td>");

            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{forecast.Precipitation?.Value}</td>");

            // UV Index
            string uvText = forecast.UvIndex.HasValue ? forecast.UvIndex.Value.ToString() : string.Empty;
            html.AppendLine(@$"
                <td style='
                    border: 1px dotted #E6E6E6;
                    border-collapse: separate;
                    white-space: nowrap;
                    text-align: center;
                '>{uvText}</td>");

            html.AppendLine("</tr>");

            // Add empty row after hour ending 24 for visual separation between days
            if (hourEnding == 24)
            {
                html.AppendLine(@"<tr style='height: 32px;'><td colspan='10'></td></tr>");
            }
        }

        html.AppendLine("</tbody>");
        html.AppendLine("</table>");
        html.AppendLine("</div>");
        html.AppendLine("</div>");

        return html.ToString();
    }
}
