namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;

public static class AlertTriggerConstants
{
    public const string Criteria = "Criteria";
    public const string Daily = "Daily";
    public const string Weekly = "Weekly";
    public const string Monthly = "Monthly";
    public const string Yearly = "Yearly";
}

public abstract record AlertTriggerCommand(string Type, int ExpirationTimeInMinutes);

public record CriteriaTriggerCommand(int ExpirationTimeInMinutes, List<AlertCriteriaCommand> Criterias, string CombinationRule)
    : AlertTriggerCommand(AlertTriggerConstants.Criteria, ExpirationTimeInMinutes);

public record AlertCriteriaCommand( Guid BlockId, string Filter);

public abstract record ScheduleTriggerCommand(string Type, List<string> RunTimes, int ExpirationTimeInMinutes)
    : AlertTriggerCommand(Type, ExpirationTimeInMinutes);

public record DailyScheduleTriggerCommand(List<string> RunTimes, int ExpirationTimeInMinutes)
    : ScheduleTriggerCommand(AlertTriggerConstants.Daily, RunTimes, ExpirationTimeInMinutes);

public record WeeklyScheduleTriggerCommand(
    List<string> DaysOfWeek, // ["Monday", "Tuesday", etc...]
    List<string> RunTimes,
    int ExpirationTimeInMinutes)
    : ScheduleTriggerCommand(AlertTriggerConstants.Weekly, RunTimes, ExpirationTimeInMinutes);

public record MonthlyScheduleTriggerCommand(
    List<int> DaysOfMonth, // [1, 2, etc... 31]
    List<string> RunTimes,
    int ExpirationTimeInMinutes)
    : ScheduleTriggerCommand(AlertTriggerConstants.Monthly, RunTimes, ExpirationTimeInMinutes);

public record YearlyScheduleTriggerCommand(
    List<int> DaysOfYear, // [1, 2, etc... 31]
    List<string> RunTimes,
    int ExpirationTimeInMinutes)
    : ScheduleTriggerCommand(AlertTriggerConstants.Yearly, RunTimes, ExpirationTimeInMinutes);
