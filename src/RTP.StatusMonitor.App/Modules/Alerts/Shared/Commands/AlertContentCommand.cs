namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Commands;

public static class AlertContentConstants
{
    public const string Note = "Note";
    public const string Contact = "Contact";
    public const string Report = "Report";
    public const string Equipment = "Equipment";
    public const string Weather = "Weather";
    public const string SqlQuery = "SqlQuery";
}

public abstract record AlertContentCommand(string Type);
public record AlertNoteContentCommand(string Note) : Alert<PERSON>ontentCommand(AlertContentConstants.Note);
public record AlertContactContentCommand(string Contact) : AlertContentCommand(AlertContentConstants.Contact);
public record AlertReportContentCommand(List<Guid> Reports) : <PERSON><PERSON><PERSON><PERSON>nt<PERSON>ommand(AlertContentConstants.Report);
public record AlertEquipmentContentCommand(
    bool IncludeAvailability,
    bool IncludeConstraint,
    int NumberOfEventsToInclude) : Alert<PERSON>ontentCommand(AlertContentConstants.Equipment);
public record AlertWeatherContentCommand(
    Guid SiteId,
    int NumberOfDaysForecast,
    string ForecastInterval,
    string WeatherService) : AlertContentCommand(AlertContentConstants.Weather);
public record AlertSqlContentCommand(string Query) : AlertContentCommand(AlertContentConstants.SqlQuery);
