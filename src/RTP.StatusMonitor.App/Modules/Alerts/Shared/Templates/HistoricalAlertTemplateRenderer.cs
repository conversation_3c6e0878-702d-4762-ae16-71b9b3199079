using System.Text;
using HandlebarsDotNet;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.App.Modules.Alerts.Shared.Templates;

public class HistoricalAlertTemplateRenderer
{
    private const string NoteSectionTemplate = @"
        <div style='
            display: inline-block;
            width: 600px;
            vertical-align: top;
            padding-right: 10px;
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 8px;
        '>
            <h3 style='margin: 0 0 15px; color: #4299e1; font-size: 20px'>NOTES</h3>

            <table style='
                margin: 0;
                color: black;
                font-weight: 500;
            '>
                {{#each notes}}
                <tr>
                    <td>{{{this}}}</td>
                </tr>
                {{/each}}
            </table>
        </div>";
    private const string FooterTemplate = @"
      <table cellspacing='0' cellpadding='0' border='0' width='100%' style='background-color: #1d7fb8;'>
        <tr>
          <td align='center' style='padding: 20px;'>
            <table cellspacing='0' cellpadding='0' border='0' width='600'>
              <!-- Logo and Company Name -->
              <tr>
                <td align='center' style='padding-bottom: 10px;'>
                  <table cellspacing='0' cellpadding='0' border='0'>
                    <tr>
                      <td valign='middle' style='padding-right: 10px;'>
                        <img src='https://realtimepower.blob.core.windows.net/assets/rtp-pwa-192x192.png' 
                             alt='RTP Analytics' 
                             width='48' 
                             height='48' 
                             style='display: block; border: 0;'>
                      </td>
                      <td valign='middle'>
                        <span style='
                          font-size: 28px;
                          font-weight: 600;
                          color: #ffffff;
                          font-family: Arial, sans-serif;
                        '>RTP Analytics</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Links Section -->
              <tr>
                <td>
                  <table cellspacing='0' cellpadding='0' border='0' width='100%'>
                    <tr>
                      <td style='border-top: 1px solid #ffffff33; font-size: 1px; height: 1px;'>&nbsp;</td>
                    </tr>
                    <tr>
                      <td align='center' style='padding: 20px 0;'>
                        <p style='
                          font-family: Arial, sans-serif;
                          font-size: 16px;
                          color: #e2e8f0;
                          margin: 0 0 20px;
                          line-height: 1.6;
                        '>
                          <strong>We win when you win</strong> — our
                          success is measured by the enhanced efficiency and performance we
                          bring to your operations.
                        </p>
                        <table cellspacing='0' cellpadding='0' border='0'>
                          <tr>
                            <td style='padding-right: 20px;'>
                              <table cellspacing='0' cellpadding='0' border='0'>
                                <tr>
                                  <td bgcolor='#324b6e' style='border-radius: 6px;'>
                                    <a href='https://www.rtpanalytics.com'
                                       style='
                                         font-family: Arial, sans-serif;
                                         color: #e2e8f0;
                                         text-decoration: none;
                                         font-size: 14px;
                                         padding: 12px 24px;
                                         border: 0;
                                         display: inline-block;
                                       '>Learn More</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                            <td>
                              <table cellspacing='0' cellpadding='0' border='0'>
                                <tr>
                                  <td bgcolor='#324b6e' style='border-radius: 6px;'>
                                    <a href='https://www.rtpanalytics.com/contact'
                                       style='
                                         font-family: Arial, sans-serif;
                                         color: #e2e8f0;
                                         text-decoration: none;
                                         font-size: 14px;
                                         padding: 12px 24px;
                                         border: 0;
                                         display: inline-block;
                                       '>Contact Us</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td style='border-bottom: 1px solid #ffffff33; font-size: 1px; height: 1px;'>&nbsp;</td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Copyright -->
              <tr>
                <td align='center' style='padding-top: 20px;'>
                  <span style='
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    color: #a0aec0;
                  '>© 2024 RTP Analytics. All rights reserved.</span>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>";
    private const string HistoricalAlertTemplate = @"
        <div 
            style='
                font-family: Inter, Arial, sans-serif;
                letter-spacing: 0.5px;
                margin: -10px;'
        >
            <!-- SECTION - Main Content -->
            <div style='color: #ffffff; padding: 10px;'>
                <div style='margin: 0 auto;'>

                    <!-- Alert Title -->
                    <h1 style='
                        margin: 0 0 20px;
                        text-align: center;
                        color: black;
                        font-size: 32px;
                        font-weight: 600;
                    '>
                        {{title}}
                    </h1>

                    <div style='color: black; line-height: 1.3;'>
                        <!-- SECTION - Notes Section -->
                        {{{NotesTemplate}}}
                    </div>

                    <!-- SECTION - Report Data -->
                    {{#each reports}}
                    <div>
                        {{{this}}}
                    </div>
                    {{/each}}

                    <!-- SECTION - Support -->
                    <div style='padding: 10px; border-radius: 8px;'>
                        <h3 style='margin: 0 0 15px; color: #4299e1; font-size: 20px;'>SUPPORT</h3>

                        <table style='
                            color: black;
                            padding-left: 10px;
                        '>
                            {{#each contacts}}
                            <tr>
                                <td>{{this}}</td>
                            </tr>
                            {{/each}}
                        </table>
                    </div>
                </div>
            </div>

            <!-- SECTION - Footer -->
            {{{FooterTemplate}}}
        </div>";

    public static string RenderAlert(List<ReportFormatOutput> reports, Alert alert)
    {
        // Create the notes template
        string notesTemplate = alert.AlertDetails.Body.Parameters.ContainsKey("notes") && alert.AlertDetails.Body.Parameters["notes"] is not null
        ? Handlebars.Compile(NoteSectionTemplate)(new Dictionary<string, object>()
        {
            ["notes"] = alert.AlertDetails.Body.Parameters["notes"]
            .ToString()
            ?.Split('\n')
            .ToList() ?? new()
        }) : string.Empty;

        // Combine all parameters
        Dictionary<string, object> templateData = new(alert.AlertDetails.Body.Parameters)
        {
            ["title"] = alert.AlertDetails.Title,
            // Decode the byte array to HTML string
            ["reports"] = reports.Select(r => Encoding.Default.GetString(r.Content)).ToList(),
            ["currentYear"] = DateTime.UtcNow.Year,
            ["contacts"] = alert.AlertDetails.Body.Parameters.ContainsKey("contacts") && alert.AlertDetails.Body.Parameters["contacts"] is not null
            ? alert.AlertDetails.Body.Parameters["contacts"].ToString()
              ?.Split('\n')
              .ToList() ?? new()
            : new(),
            ["NotesTemplate"] = notesTemplate,
            ["FooterTemplate"] = FooterTemplate,
        };

        // Render final HTML
        return Handlebars.Compile(HistoricalAlertTemplate)(templateData);
    }
}

