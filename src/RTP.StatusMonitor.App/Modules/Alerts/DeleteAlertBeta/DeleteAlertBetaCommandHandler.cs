using MediatR;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;
using RTP.StatusMonitor.Domain.Alerts;

namespace RTP.StatusMonitor.App.Modules.Alerts.DeleteAlertBeta;

internal sealed class DeleteAlertBetaCommandHandler(DataContext dataContext)
        : ICommandHandler<DeleteAlertBetaCommand>
{
    private readonly DataContext _context = dataContext;
    public async Task<ErrorOr<Unit>> Handle(
        DeleteAlertBetaCommand request, CancellationToken ct = default)
    {
        AlertModel? alertModel = await _context.AlertModels
            .SingleOrDefaultAsync(alert => request.AlertId == alert.AlertId, ct);

        if (alertModel is null)
        {
            return AlertErrors.NotFound;
        }

        _context.AlertModels.Remove(alertModel);
        await _context.SaveChangesAsync(ct);

        return Unit.Value;
    }
}
