using ErrorOr;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.PreviewAlertBeta;

public class PreviewAlertBetaCommandHandler(
    DataContext context,
    AlertRenderer alertRenderer) : ICommandHandler<PreviewAlertBetaCommand, string>
{
    private readonly DataContext _context = context;
    private readonly AlertRenderer _alertRenderer = alertRenderer;
    public async Task<ErrorOr<string>> Handle(
        PreviewAlertBetaCommand request,
        CancellationToken ct = default)
    {
        // Get the alert
        AlertModel? alertModel = await _context.AlertModels
            .AsNoTracking()
            .AsSplitQuery()
            .Include(a => a.Contents.OrderBy(content => content.Position))
            .FirstOrDefaultAsync(a => a.AlertId == request.AlertId, ct);

        // Check if the alert exists
        if (alertModel is null)
        {
            return AlertErrors.NotFound;
        }

        // Map from peristence model to domain modal
        AlertBeta alert = alertModel.ToDomain();

        return await _alertRenderer.Render(alert, ct);
    }
}
