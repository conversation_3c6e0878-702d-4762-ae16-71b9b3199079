// using System.Net.Mail;
// using ErrorOr;
// using MediatR;
// using Microsoft.EntityFrameworkCore;
// using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
// using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
// using RTP.StatusMonitor.App.Shared.Emails;
// using RTP.StatusMonitor.App.Shared.Messaging;
// using RTP.StatusMonitor.Domain.Alerts;
// using RTP.StatusMonitor.Domain.Reports;
// using RTP.StatusMonitor.Persistence;
//
// namespace RTP.StatusMonitor.App.Modules.Alerts.ProcessAlert;
//
// public class ProcessAlertCommandHandler(
//     DataContext context,
//     EmailService emailService,
//     ReportProcessor reportProcessor,
//     AlertProcessor alertProcessor
// ) : ICommandHandler<ProcessAlertCommand, Unit>
// {
//     private readonly DataContext _context = context;
//     private readonly EmailService _emailService = emailService;
//     private readonly ReportProcessor _reportProcessor = reportProcessor;
//     private readonly AlertProcessor _alertProcessor = alertProcessor;
//
//     public async Task<ErrorOr<Unit>> Handle(
//         ProcessAlertCommand request,
//         CancellationToken cancellationToken
//     )
//     {
//         // Get the alert
//         Alert? alert = await _context
//             .Alerts.Include(a => a.Site)
//             .FirstOrDefaultAsync(a => a.Id == request.AlertId, cancellationToken);
//
//         // Check if the alert exists
//         if (alert is null)
//         {
//             return AlertErrors.NotFound;
//         }
//
//         // Make sure it is enabled before processing
//         if (!alert.IsEnabled)
//         {
//             return AlertErrors.CannotProcessDisabledAlert;
//         }
//
//         // Process the reports for this alert (get the data for the reports and format them to be used in the email)
//         (List<ReportFormatOutput> reportsInBody, List<ReportFormatOutput> reportsInAttachments) =
//             await _reportProcessor.ProcessReportsAsync(alert, cancellationToken);
//
//         // Process the alert and get the html content
//         string alertContent = await _alertProcessor.ProcessAlertAsync(
//             alert,
//             reportsInBody,
//             cancellationToken
//         );
//
//         // Send the email
//         _emailService.SendEmail(
//             recipients: request.SendTo is null
//                 ? alert.AlertDetails.Recipients
//                 : [request.SendTo.Value],
//             subject: request.Note is null
//                 ? $"{DateTime.Now.AddDays(1):yyyy-MM-dd}-{alert.AlertDetails.Title}"
//                 : $"{DateTime.Now.AddDays(1):yyyy-MM-dd}-{alert.AlertDetails.Title} - {request.Note}",
//             body: alertContent,
//             isBodyHtml: true,
//             // Create the attachments for the email
//             attachments:
//             [
//                 .. reportsInAttachments
//                     .Select(r => new Attachment(
//                         new MemoryStream(r.Content),
//                         $"{r.ReportName}.{r.FileFormat.GetFileExtension()}",
//                         r.FileFormat.GetMimeType()
//                     ))
//                     .OrderBy(a => a.Name),
//             ]
//         );
//
//         // Record when the alert was sent
//         ErrorOr<Updated> recordAlertSentResult = alert.RecordAlertSent(DateTime.UtcNow);
//         if (recordAlertSentResult.IsError)
//         {
//             return recordAlertSentResult.Errors;
//         }
//
//         // Save the changes to the database
//         await _context.SaveChangesAsync(cancellationToken);
//
//         return Unit.Value;
//     }
// }
