using MediatR;
using RTP.StatusMonitor.App.Shared.Messaging;

namespace RTP.StatusMonitor.App.Modules.Alerts.ProcessAlertBeta;

public record SendAlertBetaCommand(Guid AlertId, BaseAlertRequest AlertRequest)
    : ICommand<Unit>;

public abstract record BaseAlertRequest(string? Note = null);
public record TestAlertRequest(string SendTo, string? Note) : BaseAlertRequest(Note);
public record SendAlertRequest(string? Note) : BaseAlertRequest(Note);

