using System.Data;
using System.Net.Mail;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Renderers;
using RTP.StatusMonitor.App.Modules.Alerts.Shared.Services;
using RTP.StatusMonitor.App.Modules.Reporting.Shared;
using RTP.StatusMonitor.App.Modules.Reporting.Shared.Formatting;
using RTP.StatusMonitor.App.Shared.Emails;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Reports;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.ProcessAlertBeta;

public class SendAlertBetaCommandHandler(
    DataContext context,
    ReportProcessor reportProcessor,
    EmailService emailService,
    AlertRenderer alertRenderer) : ICommandHandler<SendAlertBetaCommand, Unit>
{
    public async Task<ErrorOr<Unit>> Handle(SendAlertBetaCommand request, CancellationToken ct = default)
    {
        // Get the alert
        AlertModel? existingAlertModel = await context.AlertModels
            .Include(a => a.Contents.OrderBy(content => content.Position))
            .Include(a => a.Criterias)
            .SingleOrDefaultAsync(a => a.AlertId == request.AlertId, ct);
        if (existingAlertModel is null)
        {
            return AlertErrors.NotFound;
        }

        // Map from persistence model to domain modal
        AlertBeta existingAlert = existingAlertModel.ToDomain();

        // Record when the alert was sent if this is not a test
        //* NOTE - need to do this first before processing the alert because the processing may take a long time and may fail
        if (request.AlertRequest is SendAlertRequest)
        {
            ErrorOr<Updated> recordAlertSentResult = existingAlert.RecordTimeSent(DateTime.UtcNow);
            if (recordAlertSentResult.IsError)
            {
                return recordAlertSentResult.Errors;
            }

            // Update by replacing current alert with new one (update last sent utc)
            context.AlertModels.Remove(existingAlertModel);
            context.AlertModels.Add(existingAlert.ToPersistence());

            await context.SaveChangesAsync(ct);
        }

        // Make sure it is enabled before processing (allow to send a test)
        if (request.AlertRequest is SendAlertRequest && !existingAlert.IsEnabled)
        {
            return AlertErrors.CannotProcessDisabledAlert;
        }

        // Process the reports in the attachments
        Dictionary<Report, ReportProcessingResult> reportsInAttachments = await reportProcessor.ProcessReportsAsync(
            [.. existingAlert.Attachments.Select(a => a.ReportId)], ct);

        List<ReportFormatOutput> formattedAttachements = FormatReports(reportsInAttachments);
        List<string> emailAddresses = request.AlertRequest switch
        {
            TestAlertRequest testRequest => [testRequest.SendTo],
            SendAlertRequest _ => [.. existingAlert.Recipients.Select(c => c.Value)],
            _ => throw new InvalidOperationException("Invalid request type")
        };

        // Send the email
        emailService.SendEmail(
            recipients: emailAddresses,
            subject: string.IsNullOrEmpty(request.AlertRequest.Note) ?
                $"{DateTime.Now.AddDays(1):yyyy-MM-dd}-{existingAlert.Title}" :
                $"{DateTime.Now.AddDays(1):yyyy-MM-dd}-{existingAlert.Title} - {request.AlertRequest.Note}",
            body: await alertRenderer.Render(existingAlert, ct),
            isBodyHtml: true,
            attachments: [..formattedAttachements
                .Select(r => new Attachment(
                    new MemoryStream(r.Content),
                    $"{r.ReportName}.{r.FileFormat.GetFileExtension()}",
                    r.FileFormat.GetMimeType()))
                .OrderBy(a => a.Name)]);

        return Unit.Value;
    }

    /// <summary>
    /// Get the formatted reports for the report body
    /// </summary>
    /// <param name="reportsInBody">The reports being used in the report body</param>
    private static List<ReportFormatOutput> FormatReports(Dictionary<Report, ReportProcessingResult> reportsInBody)
    {
        List<ReportFormatOutput> formattedReports = [];

        // Format the reports to HTML to include in the email
        foreach ((Report report, ReportProcessingResult reportData) in reportsInBody)
        {
            ReportFormatOutput formattedReport = report.ReportType switch
            {
                ReportType.Forecast => ForecastReportFormatter.FormatReport(
                    report,
                    Enum.Parse<ForecastLayoutType>(report.ReportLayout.Value),
                    report.FileFormat,
                    (ForecastProcessingResult)reportData),
                ReportType.Historical => HistoricalReportFormatter.FormatReport(
                    report,
                    Enum.Parse<HistoricalLayoutType>(report.ReportLayout.Value),
                    report.FileFormat,
                    (HistoricalProcessingResult)reportData),
                ReportType.Accuracy => AccuracyReportFormatter.FormatReport(
                    report,
                    report.FileFormat,
                    (AccuracyProcessingResult)reportData),
                ReportType.Lookup => LookupReportFormatter.FormatReport(
                    report,
                    report.FileFormat,
                    (LookupProcessingResult)reportData),
                _ => throw new InvalidOperationException("Report type not supported")
            };

            formattedReports.Add(formattedReport);
        }

        return formattedReports;
    }
}
