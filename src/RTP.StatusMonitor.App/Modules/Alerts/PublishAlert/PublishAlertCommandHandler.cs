using System.Data;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using RTP.StatusMonitor.App.Shared.Clock;
using RTP.StatusMonitor.App.Shared.Messaging;
using RTP.StatusMonitor.App.Shared.Options;
using RTP.StatusMonitor.App.Shared.Repository.DataClientHistorical;
using RTP.StatusMonitor.App.Shared.Repository.Forecast;
using RTP.StatusMonitor.Domain.Alerts;
using RTP.StatusMonitor.Domain.Blocks;
using RTP.StatusMonitor.Domain.Shared;
using RTP.StatusMonitor.Domain.TimeSeries;
using RTP.StatusMonitor.Domain.TimeSeries.Types;
using RTP.StatusMonitor.Persistence;
using RTP.StatusMonitor.Persistence.Models;

namespace RTP.StatusMonitor.App.Modules.Alerts.PublishAlert;

internal class PublishAlertCommandHandler(
    IDateTimeProvider dateTimeProvider,
    DataContext dataContext,
    IOptions<AzureServiceBusOptions> options,
    DataClientReadRepository dataClientReadRepository,
    ForecastRepository forecastRepository,
    IEventBus eventBus
) : ICommandHandler<PublishAlertCommand>
{
    private readonly IDateTimeProvider _dateTimeProvider = dateTimeProvider;
    private readonly DataContext _dataContext = dataContext;
    private readonly IEventBus _eventBus = eventBus;
    private readonly AzureServiceBusOptions _azureServiceBusOptions = options.Value;
    private readonly DataClientReadRepository _dataClientReadRepository = dataClientReadRepository;
    private readonly ForecastRepository _forecastRepository = forecastRepository;
    private readonly string ACTUAL_SOURCE_PREFIX = "ACT";
    private readonly string DA_SOURCE_PREFIX = "DA";
    private readonly string RT_SOURCE_PREFIX = "RT";

    public async Task<ErrorOr<Unit>> Handle(PublishAlertCommand request, CancellationToken ct)
    {
        // Get all enabled alerts
        List<AlertBeta> alerts = await _dataContext
            .AlertModels.AsNoTracking()
            .Where(x => x.IsEnabled)
            .Include(x => x.Criterias)
            .Select(x => x.ToDomain())
            .ToListAsync(ct);

        // Create a list of events to be published
        List<AlertProcessingEvent> events = [];
        foreach (AlertBeta alert in alerts)
        {
            switch (alert.Trigger)
            {
                case CriteriaTrigger criteriaTrigger:
                    if (
                        await IsCriteriaTriggerMet(
                            alert.LastSentUtc,
                            criteriaTrigger,
                            _dateTimeProvider.UtcNow,
                            ct
                        )
                    )
                    {
                        events.Add(
                            new AlertProcessingEvent(alert.Id, "PublishAlertBetaTimerFunction")
                        );
                    }
                    break;
                case ScheduleTrigger:
                    if (alert.ShouldBeSent(_dateTimeProvider.UtcNow))
                    {
                        events.Add(
                            new AlertProcessingEvent(alert.Id, "PublishAlertBetaTimerFunction")
                        );
                    }
                    break;
                default:
                    break;
            }
        }

        if (events.Count > 0)
        {
            await _eventBus.PublishMessageBatchAsync(
                events,
                _azureServiceBusOptions.AlertProcessingQueueBeta,
                ct
            );
        }

        return Unit.Value;
    }

    public async Task<bool> IsCriteriaTriggerMet(
        DateTime? lastSentUtc,
        CriteriaTrigger alertTrigger,
        DateTime currentUtcTime,
        CancellationToken ct
    )
    {
        // Check if the interval has elapsed
        if (lastSentUtc.HasValue)
        {
            TimeSpan sinceLastAlert = currentUtcTime - lastSentUtc.Value;
            if (sinceLastAlert.TotalMinutes < alertTrigger.ExpirationTimeInMinutes)
            {
                return false;
            }
        }

        // If no condition is set, then do not trigger
        if (alertTrigger.Criterias.Count == 0)
        {
            return false;
        }

        List<bool> evaluationResults = [];
        foreach (AlertCriteria criteria in alertTrigger.Criterias)
        {
            evaluationResults.Add(await EvaluateAlertTrigger(criteria, currentUtcTime, ct));
        }

        return alertTrigger.CombinationRule switch
        {
            CombinationRule.And => evaluationResults.All(r => r),
            CombinationRule.Or => evaluationResults.Any(r => r),
            _ => throw new InvalidOperationException(
                "Invalid combination rule for alert criteria triggers"
            ),
        };
    }

    /// <summary>
    /// Evaluate the alert criteria trigger
    /// </summary>
    /// <param name="criteria">The criteria to evaluate</param>
    /// <param name="currentUtcTime">The current UTC time</param>
    /// <param name="ct">The cancellation token</param>
    /// <returns>True if the criteria is met, false otherwise</returns>
    private async Task<bool> EvaluateAlertTrigger(
        AlertCriteria criteria,
        DateTime currentUtcTime,
        CancellationToken ct
    )
    {
        // Get block data from db
        Block block = await _dataContext
            .Blocks.Where(b => b.Id == criteria.BlockId)
            .Include(b => b.Site)
            .ThenInclude(s => s.Customer)
            .SingleAsync(ct);

        TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(block.Site.TimeZone);

        // Get the local time
        DateTime localTime = TimeZoneInfo.ConvertTimeFromUtc(currentUtcTime, siteTimeZone);

        // Parse and group the filter tag by their prefix (ACT/DA/RT)
        Dictionary<string, List<string>> tagsInFilter = ExpressionParser
            .ParseWithPrefix(criteria.Filter)
            .GroupBy(x => x.Key, x => x.Value)
            .ToDictionary(x => x.Key, x => x.SelectMany(y => y).Distinct().ToList());

        // Get actual snapshot data
        List<DataClientHistoricalWriteSchemaDto> actuals = _dataClientReadRepository.GetLatestData(
            block,
            tagsInFilter.GetValueOrDefault(ACTUAL_SOURCE_PREFIX, [])
        );

        // Convert to time series data
        List<TimeSeriesData> actualTimeSeries = actuals.ToTimeSeriesData(siteTimeZone);

        // Prefix the tag with ACT- to indicate that this is actual data
        List<TimeSeriesData> actualData =
        [
            .. actualTimeSeries.Select(x => new TimeSeriesData(
                Tag: $"{ACTUAL_SOURCE_PREFIX}-{x.Tag}",
                Values: x.Values,
                Timestamps: [localTime]
            )),
        ];

        // Get the day ahead forecast
        List<ForecastDataDto> dayAheadForecast =
            await _forecastRepository.GetDayAheadForecastForDateTimeAsync(
                block: block,
                tags: tagsInFilter.GetValueOrDefault(DA_SOURCE_PREFIX, []),
                date: DateOnly.FromDateTime(localTime),
                hourEnding: localTime.Hour,
                ct: ct
            );

        List<TimeSeriesData> dayAheadData =
        [
            .. dayAheadForecast.Select(x => new TimeSeriesData(
                Tag: $"{DA_SOURCE_PREFIX}-{x.Tag}",
                Values: [.. x.Values],
                Timestamps: [localTime]
            )),
        ];

        // Get the real time forecast
        List<ForecastDataDto> realtimeForecast =
            await _forecastRepository.GetLatestForecastForDateTimeAsync(
                block: block,
                tags: tagsInFilter.GetValueOrDefault(RT_SOURCE_PREFIX, []),
                dateTime: localTime,
                ct: ct
            );

        List<TimeSeriesData> realtimeData =
        [
            .. realtimeForecast.Select(x => new TimeSeriesData(
                Tag: $"{RT_SOURCE_PREFIX}-{x.Tag}",
                Values: [.. x.Values],
                Timestamps: [localTime]
            )),
        ];

        // Dump all data into data table for analytics
        SortedTimeSeriesTable dataTable = SortedTimeSeriesTable.Create(
            [.. actualData, .. dayAheadData, .. realtimeData]
        );
        ErrorOr<SortedTimeSeriesTable> result = dataTable.TryApplyFilter(criteria.Filter);

        // If criteria can't be evaluated, then conditions are considered not met.
        // Otherwise, return true if conditions are met (non-empty row)
        return result.Match(value => value.DataTable.Rows.Count > 0, error => false);
    }
}
