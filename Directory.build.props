<?xml version="1.0" encoding="utf-8"?>
<Project>
  <!-- Common project properties -->
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>


  <!-- Roslyn Analyzers -->
  <ItemGroup>
    <PackageReference Include="Roslynator.Analyzers" Version="4.13.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <!-- Sonar Analyzer -->
  <ItemGroup>
    <PackageReference Include="SonarAnalyzer.CSharp" Version="10.7.0.110445">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <!-- Include .editorconfig as an AdditionalFile -->
  <ItemGroup Condition="Exists('$(MSBuildThisFileDirectory)\.editorconfig')">
    <AdditionalFiles Include="$(MSBuildThisFileDirectory)\.editorconfig" />
  </ItemGroup>

</Project> 