# Introduction

Azure Function API for RTP Analytics

## Getting Started

### Prerequisites

- [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Azure Functions Core Tools](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local#install-the-azure-functions-core-tools)
- [Azurite](https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage)
- [Visual Studio 2022](https://visualstudio.microsoft.com/downloads/) (recommended) or [VS Code](https://code.visualstudio.com/)
- An active Azure subscription

### Installation Steps

1. Clone this repository:

   ```bash
   git clone https://<EMAIL>/rtpenergy/RTP%20Status%20Monitor/_git/RTP.StatusMonitor.Api
   cd RTP.StatusMonitor.Api
   ```

2. Open the solution in Visual Studio 2022 or VS Code

3. Configure local settings:
   - Navigate to `src/RTP.StatusMonitor.Function`
   - Rename `local.settings.template.json` to `local.settings.json`
   - Update the connection strings and other settings as needed

4. Run locally:
   - In Visual Studio: Press F5 or click the Run button
   - In VS Code/Terminal: At the root of the repository, run

     ```bash
     func start --script-root src/RTP.StatusMonitor.Function
     ```

5. Deploy to Azure:
   - The project uses Azure DevOps Pipelines for continuous integration and deployment (CI/CD):
     - When code is merged into the main branch, it triggers an automated build and deployment
     - The pipeline builds the .NET project and packages it as a Function App
     - It then deploys to the Azure Function App named 'status-monitor-api-premium-net8'
     - Pipeline configuration can be found in `src/RTP.StatusMonitor.Function/azure-pipelines.yml`
     - Deployment uses production variables from the 'StatusMonitor-Production' variable group

### Software Dependencies

- .NET 8.0
- Azure Functions Runtime v4
- Azure Entra Id for authentication
- Azure Application Insights for monitoring

### API Documentation
