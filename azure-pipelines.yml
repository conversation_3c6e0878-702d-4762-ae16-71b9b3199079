# .NET Core Function App to Linux on Azure
# Build a .NET Core function app and deploy it to Azure as a Linux function App.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/en-us/azure/devops/pipelines/languages/dotnet-core

trigger:
  - main

variables:
  - group: StatusMonitor-Production
  - name: azureSubscription
    value: "21185077-54e7-4667-875e-da1fc5d523c7"

  - name: resourceGroupName
    value: "status-monitor-api-premium"

  - name: functionAppName
    value: "status-monitor-api-premium-net8"

  - name: vmImageName
    value: "ubuntu-latest"
  
  - name: workingDirectory
    value: "$(System.DefaultWorkingDirectory)/src/RTP.StatusMonitor.Function"

stages:
  - stage: Build
    displayName: Build stage

    jobs:
      - job: Build
        displayName: Build
        pool:
          vmImage: $(vmImageName)

        steps:
          - task: DotNetCoreCLI@2
            displayName: Build
            inputs:
              command: "build"
              projects: |
                $(workingDirectory)/*.csproj
              arguments: --output $(System.DefaultWorkingDirectory)/publish_output --configuration Release

          - task: ArchiveFiles@2
            displayName: "Archive files"
            inputs:
              rootFolderOrFile: "$(System.DefaultWorkingDirectory)/publish_output"
              includeRootFolder: false
              archiveType: zip
              archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
              replaceExistingArchive: true

          - publish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
            artifact: drop

  - stage: Deploy
    displayName: Deploy stage
    dependsOn: Build
    condition: succeeded()

    jobs:
      - deployment: Deploy
        displayName: Deploy
        environment: "development"
        pool:
          vmImage: $(vmImageName)

        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureFunctionApp@2
                  displayName: "Azure Functions App Deploy"
                  inputs:
                    connectedServiceNameARM: "$(azureSubscription)"
                    appType: functionApp
                    appName: $(functionAppName)
                    package: "$(Pipeline.Workspace)/drop/$(Build.BuildId).zip"
                    deploymentMethod: auto

                - task: AzureAppServiceSettings@1
                  displayName: "Update App Settings"
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    appName: $(functionAppName)
                    resourceGroupName: $(resourceGroupName)
                    appSettings: |
                      [
                        {
                          "name": "AccuWeatherApiConfig__ApiKey",
                          "value": "$(AccuWeatherApiConfig__ApiKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "AppEnvironment",
                          "value": "$(AppEnvironment)",
                          "slotSetting": false
                        },
                        {
                          "name": "AzureAd__Instance",
                          "value": "$(AzureAd__Instance)",
                          "slotSetting": false
                        },
                        {
                          "name": "AzureAd__Domain",
                          "value": "$(AzureAd__Domain)",
                          "slotSetting": false
                        },
                        {
                          "name": "AzureAd__Client",
                          "value": "$(AzureAd__Client)",
                          "slotSetting": false
                        },
                        {
                          "name": "AzureAd__TenantId",
                          "value": "$(AzureAd__TenantId)",
                          "slotSetting": false
                        },
                        {
                          "name": "AzureAd__Scope",
                          "value": "$(AzureAd__Scope)",
                          "slotSetting": false
                        },
                        {
                          "name": "BlobStorageConfig__ConnectionString",
                          "value": "$(BlobStorageConfig__ConnectionString)",
                          "slotSetting": false
                        },
                        {
                          "name": "GoogleMapApiConfig__ApiKey",
                          "value": "$(GoogleMapApiConfig__ApiKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "IBMWeatherApiConfig__ApiKey",
                          "value": "$(IBMWeatherApiConfig__ApiKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "InternalApiConfig__BaseUrl",
                          "value": "$(InternalApiConfig__BaseUrl)",
                          "slotSetting": false
                        },
                        {
                          "name": "InternalApiConfig__FunctionKey",
                          "value": "$(InternalApiConfig__FunctionKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "ServiceBusConfig__ConnectionString",
                          "value": "$(ServiceBusConfig__ConnectionString)",
                          "slotSetting": false
                        },
                        {
                          "name": "ServiceBusConfig__ForecastUpdatesTopic",
                          "value": "$(ServiceBusConfig__ForecastUpdatesTopic)",
                          "slotSetting": false
                        },
                       {
                          "name": "ServiceBusConfig__AlertProcessingQueue",
                          "value": "$(ServiceBusConfig__AlertProcessingQueue)",
                          "slotSetting": false
                        },
                        {
                          "name": "TableStorageConfig__AccountKey",
                          "value": "$(TableStorageConfig__AccountKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "TableStorageConfig__AccountName",
                          "value": "$(TableStorageConfig__AccountName)",
                          "slotSetting": false
                        },
                        {
                          "name": "TableStorageConfig__Uri",
                          "value": "$(TableStorageConfig__Uri)",
                          "slotSetting": false
                        },
                        {
                          "name": "TableStorageConfig__WeatherReadTable",
                          "value": "$(TableStorageConfig__WeatherReadTable)",
                          "slotSetting": false
                        },
                        {
                          "name": "TableStorageConfig__WeatherWriteTable",
                          "value": "$(TableStorageConfig__WeatherWriteTable)",
                          "slotSetting": false
                        },
                        {
                          "name": "TomorrowWeatherApiConfig__ApiKey",
                          "value": "$(TomorrowWeatherApiConfig__ApiKey)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__Host",
                          "value": "$(EmailConfig__Host)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__Port",
                          "value": "$(EmailConfig__Port)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__Address",
                          "value": "$(EmailConfig__Address)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__DefaultCredentials",
                          "value": "$(EmailConfig__DefaultCredentials)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__Password",
                          "value": "$(EmailConfig__Password)",
                          "slotSetting": false
                        },
                        {
                          "name": "EmailConfig__UserName",
                          "value": "$(EmailConfig__UserName)",
                          "slotSetting": false
                        },
                        {
                          "name": "SqlConnectionString",
                          "value": "$(SqlConnectionString)",
                          "slotSetting": false
                        }
                      ]
